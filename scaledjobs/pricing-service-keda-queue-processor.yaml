apiVersion: keda.sh/v1alpha1
kind: ScaledJob
metadata:
  name: pricing-service-keda-queue-processor
  namespace: VAR_KUBE_ENV
  labels: {}
spec:
  successfulJobsHistoryLimit: 2
  failedJobsHistoryLimit: 1
  pollingInterval: 20
  maxReplicaCount: 5
  scalingStrategy:
    strategy: "accurate"
  jobTargetRef:
    parallelism: 1
    completions: 1
    activeDeadlineSeconds: 3000
    backoffLimit: 1
    template:
      metadata:
        labels:
          app: pricing-service-keda-queue-processor
      spec:
        containers:
          - name: pricing-service-keda-queue-processor
            image: udaan.azurecr.io/pricing-jobs:latest
            resources:
              requests:
                memory: "4096Mi"
                cpu: "500m"
              limits:
                memory: "8192Mi"
                cpu: "1000m"
            args:
              - java
              - -cp
              - /jars/*
              - com.udaan.pricing.jobs.keda.JobProcessor
              - pricing-svc-job-queue
              - VAR_KUBE_ENV
            envFrom:
              - secretRef:
                  name: first-party-jobs
            readinessProbe:
              initialDelaySeconds: 500
              failureThreshold: 3
              periodSeconds: 10
              successThreshold: 1
              timeoutSeconds: 240
              httpGet:
                path: /
                port: 80
                scheme: HTTP
            startupProbe:
              initialDelaySeconds: 1500
              failureThreshold: 3
              periodSeconds: 10
              successThreshold: 1
              timeoutSeconds: 240
              httpGet:
                path: /
                port: 80
                scheme: HTTP
        restartPolicy: Never
        tolerations:
          - key: topology.kubernetes.io/region
            value: xp-maa
            effect: NoSchedule
          - key: topology.kubernetes.io/region
            value: xp-azure-cen0
            effect: NoSchedule
  triggers:
    - type: azure-queue
      metadata:
        direction: in
        queueName: pricing-svc-job-queue
        queueLength: '1'
        connectionFromEnv: udfirstparty_storage