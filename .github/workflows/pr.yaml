name: pr
'on':
  pull_request:
    branches:
    - '**'
jobs:
  setup:
    runs-on:
    - self-hosted
    - runner-controller
    outputs:
      runner_name: ${{ steps.start_runner.outputs.runner_name }}
    steps:
    - id: start_runner
      env:
        WORKER_TYPE: kotlin-jdk21
      run: start-runner
  main:
    needs:
    - setup
    runs-on: ${{ needs.setup.outputs.runner_name }}
    env:
      MAVEN_OPTS: -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN
        -Dorg.slf4j.simpleLogger.showDateTime=true -Djava.awt.headless=true -DinstallAtEnd=true
        -DdeployAtEnd=true
      MAVEN_CLI_OPTS: -U -B -e -fae -V
    steps:
    - name: checkout
      uses: actions/checkout@v2
      with:
        fetch-depth: 0
    - name: flags
      run: |
        msg="$(git log --format=%B -1 ${{ github.event.pull_request.head.sha }})"
        msgContains() {
          echo "$msg" | grep -q "$1" && echo true || echo false
        }
        echo "SKIP_ABI_TEST=$(msgContains '#skip-abi-test')" | tee -a $GITHUB_ENV
    - name: Compile and Test Models
      run: |
        echo "Building and running tests for model"
        mvn-cd-build mvn -P '!default,repo-proxy,abi-check' ${MAVEN_CLI_OPTS} -Djapicmp.skip=${SKIP_ABI_TEST} clean test install
      working-directory: pricing-model
    - name: Compile and Test Client
      run: |
        echo "Building and running tests for client"
        mvn-cd-build mvn -P '!default,repo-proxy,abi-check' ${MAVEN_CLI_OPTS} -Djapicmp.skip=${SKIP_ABI_TEST} clean test install
      working-directory: pricing-client
    - name: Test
      run: |
        echo "Building and running tests"
        mvn-cd-build mvn -P '!default,repo-proxy' ${MAVEN_CLI_OPTS} clean test
    timeout-minutes: 25
  teardown:
    needs:
    - setup
    - main
    if: always()
    runs-on:
    - self-hosted
    - runner-controller
    steps:
    - name: teardown runner
      run: stop-runner ${{ needs.setup.outputs.runner_name }}
