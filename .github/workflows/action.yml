name: Linters Action

on:
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]

jobs:
  setup:
    runs-on:
      - self-hosted
      - runner-controller
    outputs:
      runner_name: ${{ steps.start_runner.outputs.runner_name }}
    steps:
      - id: start_runner
        env:
          WORKER_TYPE: linter-kotlin
        run: start-runner
  main:
    name: <PERSON><PERSON> Detekt
    needs:
      - setup
    runs-on: ${{ needs.setup.outputs.runner_name }}
    steps:
      # checkout this repo
      - name: Checkout Repo
        uses: actions/checkout@v2
        with:
          fetch-depth: 0
      # checkout the private repo containing the action to run
      - name: Checkout GitHub Action Repo
        uses: actions/checkout@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          repository: udaan-com/linters
          ref: main
          token: ${{ secrets.LINTERS_TOKEN }}
          path: action.yml
      - name: Run My Action
        uses: ./action.yml
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          reviewdog_level: 'error'
          reviewdog_reporter: github-pr-check
    timeout-minutes: 25
  teardown:
    needs:
      - setup
      - main
    if: always()
    runs-on:
      - self-hosted
      - runner-controller
    steps:
      - name: teardown runner
        run: stop-runner ${{ needs.setup.outputs.runner_name }}
