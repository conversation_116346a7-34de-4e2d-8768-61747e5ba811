name: master
'on':
  push:
    branches:
      - master
      - main
jobs:
  setup:
    runs-on:
      - self-hosted
      - runner-controller
    outputs:
      runner_name: ${{ steps.start_runner.outputs.runner_name }}
    steps:
      - id: start_runner
        env:
          WORKER_TYPE: kotlin-jdk21
        run: start-runner
  main:
    needs:
      - setup
      - sonar
    runs-on: ${{ needs.setup.outputs.runner_name }}
    env:
      MAVEN_OPTS: -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN
        -Dorg.slf4j.simpleLogger.showDateTime=true -Djava.awt.headless=true -DinstallAtEnd=true
        -DdeployAtEnd=true
      MAVEN_CLI_OPTS: -U -B -e -fae -V
      BUILDAH_FORMAT: docker
      SKIP_ABI_TEST: ${{ contains(github.event.head_commit.message, '#skip-abi-test') }}
    steps:
      - name: checkout
        uses: actions/checkout@v2
        with:
          fetch-depth: 0
      - name: Set outputs
        id: vars
        run: echo "::set-output name=sha_short::$(git rev-parse --short HEAD)"
      - name: Compile and Test Models
        run: |
          echo "Building and running tests for model"
          mvn-cd-build mvn -P '!default,repo-proxy,abi-check' ${MAVEN_CLI_OPTS} -Djapicmp.skip=${SKIP_ABI_TEST} clean test install
        working-directory: pricing-model
      - name: Compile and Test Client
        run: |
          echo "Building and running tests for client"
          mvn-cd-build mvn -P '!default,repo-proxy,abi-check' ${MAVEN_CLI_OPTS} -Djapicmp.skip=${SKIP_ABI_TEST} clean test install
        working-directory: pricing-client
      - name: archive build artifacts
        uses: actions/upload-artifact@v4
        if: ${{ (success() || failure()) }}
        with:
          name: japicmp
          path: "**/target/japicmp"
          retention-days: 7
      - name: Deploy Models
        if: ${{ !contains(github.event.head_commit.message, '#no_build') }}
        run: |
          echo "Deploying models"
          mvn-cd-build mvn -P '!default,repo-proxy' ${MAVEN_CLI_OPTS} deploy -DskipTests
        working-directory: pricing-model
      - name: Deploy Client
        if: ${{ !contains(github.event.head_commit.message, '#no_build') }}
        run: |
          echo "Deploying client"
          mvn-cd-build mvn -P '!default,repo-proxy' ${MAVEN_CLI_OPTS} deploy -DskipTests
        working-directory: pricing-client
      - name: Build, Test and Package
        if: ${{ !contains(github.event.head_commit.message, '#no_build')  }}
        run: |
          echo "Building, running tests and packaging"
          mvn-cd-build mvn -P '!default,repo-proxy' ${MAVEN_CLI_OPTS} -DexcludeScope=provided clean package dependency:copy-dependencies
      - name: Build and Push Images
        if: ${{ !contains(github.event.head_commit.message, '#no_build') }}
        run: |
          echo "Building and Pushing Images to Registry"
          mvn-cd-build 'cd pricing-service/target && MODULE=pricing-service buildctl image'
          mvn-cd-build 'cd pricing-events/target && MODULE=pricing-events buildctl image'
          mvn-cd-build 'cd pricing-jobs/target && MODULE=pricing-jobs buildctl image'
      - name: Deploy Dev K8S Service
        run: |-
          if ${{ contains(github.event.head_commit.message, '#no_build') }} ; then
          echo "Deploying pricing-service to dev k8s"
          mvn-cd-build 'cd pricing-service/src && buildctl deploy dev'
          echo "Deployed pricing-service to dev k8s"

          else
          echo "Deploying pricing-service to dev k8s"
          mvn-cd-build 'cd pricing-service/target && buildctl deploy dev'
          echo "Deployed pricing-service to dev k8s"

          fi
      - name: Deploy Dev K8S signal consumer
        run: |-
          if ${{ contains(github.event.head_commit.message, '#no_build') }} ; then
          echo "Deploying pricing-events to dev k8s"
          mvn-cd-build 'cd pricing-events/src && buildctl deploy dev'
          else

          echo "Deploying pricing-events to dev k8s"
          mvn-cd-build 'cd pricing-events/target && buildctl deploy dev'

          fi
      - name: Deploy Loadtest
        if: ${{ contains(github.event.head_commit.message, '#deploy-load-test') }}
        run: |
          echo "Deploying Load Test to dev k8s"
          MODULE=pricing-service mvn-cd-build 'cd pricing-service/src && buildctl load-test pricing'
          echo "Deployed Load Test to dev k8s"
      - name: Deploy Prod K8S Service(sin0)
        if: ${{ contains(github.event.head_commit.message, '#deploy-to-prod') || contains(github.event.head_commit.message,
          '#canary') }}
        run: |-
          if ${{ contains(github.event.head_commit.message, '#no_build') }} ; then
          echo "Deploying pricing-service to prod k8s(sin0)"
          mvn-cd-build 'cd pricing-service/src && buildctl deploy prod'
          FORCE_CLUSTER_ID=k8s0 mvn-cd-build 'cd pricing-service/src && FORCE_CLUSTER_ID=k8s0 buildctl deploy prod'
          else

          echo "Deploying fp-pricing-service to prod k8s(sin0)"
          mvn-cd-build 'cd pricing-service/target && buildctl deploy prod'
          FORCE_CLUSTER_ID=k8s0 mvn-cd-build 'cd pricing-service/target && FORCE_CLUSTER_ID=k8s0 buildctl deploy prod'

          fi
      - name: Deploy Signal event consumer Service to prod k8s(sin0)
        if: ${{ contains(github.event.head_commit.message, '#deploy-to-prod') || contains(github.event.head_commit.message,
          '#canary') }}
        run: |-
          if ${{ contains(github.event.head_commit.message, '#no_build') }} ; then
          echo "Deploying pricing-event-consumer-service to prod k8s(sin0)"
          mvn-cd-build 'cd pricing-events/src && buildctl deploy prod'
          else

          echo "Deploying pricing-event-consumer-service to prod k8s(sin0)"
          mvn-cd-build 'cd pricing-events/target && buildctl deploy prod'

          fi
      - name: Deploy-Prod-K8S-cronjobs(sin0)
        if: ${{ contains(github.event.head_commit.message, '#deploy-to-prod') ||
                contains(github.event.head_commit.message, '#deploy-crons-to-prod')
          }}
        run: |-
          echo "Deploying cronjobs to prod k8s(sin0)"
          MODULE=pricing-jobs mvn-cd-build 'udaan-k8s-build-cronjob prod'
      - name: Deploy-Prod-Keda
        if: ${{ contains(github.event.head_commit.message, '#deploy-to-prod') ||
          contains(github.event.head_commit.message, '#deploy-crons-to-prod')
          }}
        run: |
          echo "Applying jobs to prod k8s"
          CLUSTER_ID=k8s0 MODULE=pricing-jobs mvn-cd-build 'udaan-k8s-build-cronjob keda scale'
          echo "Deployed prod keda scaled jobs"
      - name: Trim-mvn-versions
        if: ${{ !contains(github.event.head_commit.message, '#no_build') }}
        run: |-
          echo "Trimming all versions other than the last 5"
          mvn-cd-build 'trim-pom-versions pom.xml 5'
      - name: Sonarqube Analysis
        if: ${{ !contains(github.event.head_commit.message, '#no_build') }}
        run: |
          echo ${{ needs.sonar.outputs.SONAR_URL }}
          mvn sonar:sonar -Dsonar.host.url=${{ needs.sonar.outputs.SONAR_URL }} -Dsonar.login=${{ secrets.SONAR_KEY }} -Dsonar.projectVersion=${{ steps.vars.outputs.sha_short }} -Dsonar.sources=src/main -Dsonar.java.binaries=.
    timeout-minutes: 100
  teardown:
    needs:
      - setup
      - main
      - sonar
    if: always()
    runs-on:
      - self-hosted
      - runner-controller
    steps:
      - name: teardown runner
        run: stop-runner ${{ needs.setup.outputs.runner_name }}
  sonar:
    needs:
      - setup
    uses: udaan-com/shared-workflows/.github/workflows/sonarqube-config.yml@master
    with:
      runner_name: ${{ needs.setup.outputs.runner_name }}
