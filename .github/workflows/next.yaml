name: next
'on':
  push:
    branches:
    - next-*
jobs:
  setup:
    runs-on:
    - self-hosted
    - runner-controller
    outputs:
      runner_name: ${{ steps.start_runner.outputs.runner_name }}
    steps:
    - id: start_runner
      env:
        WORKER_TYPE: kotlin-jdk21
      run: start-runner
  main:
    needs:
    - setup
    runs-on: ${{ needs.setup.outputs.runner_name }}
    env:
      MAVEN_OPTS: -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN
        -Dorg.slf4j.simpleLogger.showDateTime=true -Djava.awt.headless=true -DinstallAtEnd=true
        -DdeployAtEnd=true
      MAVEN_CLI_OPTS: -U -B -e -fae -V
      BUILDAH_FORMAT: docker
      SKIP_ABI_TEST: ${{ contains(github.event.head_commit.message, '#skip-abi-test') }}
    steps:
    - name: checkout
      uses: actions/checkout@v2
      with:
        fetch-depth: 0
    - name: Compile and Test Models
      run: |
        echo "Building and running tests for model"
        mvn-cd-build mvn -P '!default,repo-proxy,abi-check' ${MAVEN_CLI_OPTS} -Djapicmp.skip=${SKIP_ABI_TEST} clean test install
      working-directory: pricing-model
    - name: Compile and Test Client
      run: |
        echo "Building and running tests for client"
        mvn-cd-build mvn -P '!default,repo-proxy,abi-check' ${MAVEN_CLI_OPTS} -Djapicmp.skip=${SKIP_ABI_TEST} clean test install
      working-directory: pricing-client
    - name: Build, Test and Package
      if: ${{ !contains(github.event.head_commit.message, '#no_build')  }}
      run: |
        echo "Building, running tests and packaging"
        mvn-cd-build mvn -P '!default,repo-proxy' ${MAVEN_CLI_OPTS} -DexcludeScope=provided clean package dependency:copy-dependencies
    - name: Deploy-Prod-K8S-Service(sin0)
      if: ${{ contains(github.event.head_commit.message, '#next') && contains(github.event.head_commit.message, '#deploy-to-prod') }}
      run: |-
        if ${{ contains(github.event.head_commit.message, '#no_build') }} ; then
        echo "Deploying service to prod k8s(sin0)"
        CLUSTER_ID=sin0 mvn-cd-build 'cd pricing-service/src && MODULE=pricing-service buildctl image && buildctl deploy prod'
        
        else
        echo "Deploying service to prod k8s(sin0)"
        CLUSTER_ID=sin0 mvn-cd-build 'cd pricing-service/target && MODULE=pricing-service buildctl image && buildctl deploy prod'
        
        fi
    timeout-minutes: 25
  teardown:
    needs:
    - setup
    - main
    if: always()
    runs-on:
    - self-hosted
    - runner-controller
    steps:
    - name: teardown runner
      run: stop-runner ${{ needs.setup.outputs.runner_name }}
