package com.udaan.pricing.events.listeners

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.getCurrentMillis
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.core.constants.EventHubConfig
import com.udaan.pricing.events.SignalTaskType
import com.udaan.pricing.signals.SignalEvent
import com.udaan.pricing.variable.VariableId
import com.udaan.sourcing.async.listeners.eventhub.EventHubListener
import com.udaan.sourcing.async.models.TaskRequest
import java.util.concurrent.TimeUnit

@Singleton
class TradingSignalEventListener @Inject constructor(
    private val mapper: ObjectMapper
) : EventHubListener(
    resourceId = EventHubConfig.EVENT_HUB_RESOURCE_ID,
    checkpointEvery = 5
) {

    private val log by logger()

    private val tradingPriceVariablesToProcess = listOf(
        VariableId.STAPLES_LIP_WOT_PAISA_UNIT,
        VariableId.GRANARY_INVENTORY_FLAG,
        VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT,
        VariableId.STAPLES_LPP_WOT_RUPEES_UNIT,
        VariableId.BMT_GID_BPS,
        VariableId.BMT_MULTIPLIER,
        VariableId.TRADING_PRICE_WOT_PAISA_UNIT,
        VariableId.KP_ABS_MARKUP_WOT_PAISA_UNIT,
        VariableId.OFFLINE_COMPETITION_WT_PAISA_UNIT,
        VariableId.JUMBOTAIL_COMP_LADDER_PRICE_WT_PAISA_UNIT
    )

    override fun transform(data: ByteArray): List<TaskRequest<*>> {
        val signalEvent = mapper.readValue(data, SignalEvent::class.java)

        if (tradingPriceVariablesToProcess.contains(signalEvent.variableId).not()) {
            log.info("Ignoring signal event for : {}", signalEvent)
            return emptyList()
        }

        return listOf(
            TaskRequest(
                type = SignalTaskType.TRADING_REPRICE_ON_SIGNAL_EVENT.toString(),
                body = signalEvent
            )
        )
    }
}
