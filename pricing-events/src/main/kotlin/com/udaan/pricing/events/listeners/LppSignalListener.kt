package com.udaan.pricing.events.listeners

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.firstpartyproc.models.PurchaseOrderEvent
import com.udaan.pricing.events.LPP_SIGNAL_CONSUMER
import com.udaan.pricing.events.SignalTaskType
import com.udaan.pricing.signalcreation.PurchaseOrderInput
import com.udaan.sourcing.async.listeners.eventhub.EventHubListener
import com.udaan.sourcing.async.models.TaskRequest

@Singleton
class LppSignalListener @Inject constructor(
    private val mapper: ObjectMapper
) : EventHubListener(
    resourceId = LPP_SIGNAL_CONSUMER,
    checkpointEvery = 5
) {

    override fun transform(data: ByteArray): List<TaskRequest<*>> {
        val purchaseOrderEvent = mapper.readValue(data, PurchaseOrderEvent::class.java)

        return listOf(
            TaskRequest(
                type = SignalTaskType.CREATE_SIGNAL.toString(),
                body = PurchaseOrderInput(
                    orgId = purchaseOrderEvent.orgId,
                    orgUnitId = purchaseOrderEvent.orgUnitId,
                    purchaseOrderId = purchaseOrderEvent.poId,
                    productId = purchaseOrderEvent.productId,
                    unitPrice = purchaseOrderEvent.unitPrice,
                    vendorId = purchaseOrderEvent.vendorId,
                    vendorUnitId = purchaseOrderEvent.vendorUnit
                )
            )
        )
    }
}
