package com.udaan.pricing.events.handlers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.core.managers.signals.SignalWriteManager
import com.udaan.pricing.events.SignalTaskType
import com.udaan.pricing.signaldeletion.DeleteSignalRequest
import com.udaan.sourcing.async.handler.TaskHandler
import com.udaan.sourcing.async.handler.retry.RetryConfig
import com.udaan.sourcing.async.models.Task
import com.udaan.sourcing.async.models.TaskRequest

@Singleton
class DeleteSignalTaskHandler @Inject constructor(
    private val signalWriteManager: SignalWriteManager
) : TaskHandler<DeleteSignalRequest>(
    SignalTaskType.DELETE_SIGNAL.toString(),
    DeleteSignalRequest::class.java,
    RetryConfig(persistOnFailure = true, maxAttempts = 1)
) {

    private val log by logger()

    override suspend fun handle(task: Task<DeleteSignalRequest>): List<TaskRequest<*>> {
        deleteSignal(task.body)
        return emptyList()
    }

    private suspend fun deleteSignal(deleteSignalRequest: DeleteSignalRequest) {
        log.info("DeleteSignalRequest received {}", deleteSignalRequest)
        signalWriteManager.parseDeleteSignalRequestAndDelete(deleteSignalRequest)
        log.info("DeleteSignalRequest is completed {}", deleteSignalRequest)
    }
}
