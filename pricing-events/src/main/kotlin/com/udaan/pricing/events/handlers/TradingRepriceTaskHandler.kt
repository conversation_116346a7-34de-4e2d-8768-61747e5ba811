package com.udaan.pricing.events.handlers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.core.controller.automation.TradingRepriceController
import com.udaan.pricing.events.SignalTaskType
import com.udaan.pricing.signals.SignalEvent
import com.udaan.sourcing.async.handler.TaskHandler
import com.udaan.sourcing.async.handler.retry.RetryConfig
import com.udaan.sourcing.async.models.Task
import com.udaan.sourcing.async.models.TaskRequest

@Singleton
class TradingRepriceTaskHandler @Inject constructor(
    private val tradingRepriceController: TradingRepriceController
) : TaskHandler<SignalEvent>(
    SignalTaskType.TRADING_REPRICE_ON_SIGNAL_EVENT.toString(),
    SignalEvent::class.java,
    RetryConfig(persistOnFailure = true, maxAttempts = 3)
) {

    private val log by logger()

    override suspend fun handle(task: Task<SignalEvent>): List<TaskRequest<*>> {
        log.info("Received task to reprice trading items for {}", task.body)
        tradingRepriceController.repriceItems(
            locationValue = task.body.location.locationValue,
            locationType = task.body.location.locationType,
            catalogEntity = task.body.catalogEntity,
            referenceId = task.body.referenceId
        )
        log.info("Processed task to reprice trading items for {}", task.body)
        return emptyList()
    }

}
