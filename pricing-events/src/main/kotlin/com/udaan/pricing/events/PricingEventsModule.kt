package com.udaan.pricing.events

import com.google.inject.AbstractModule
import com.google.inject.Provides
import com.google.inject.Singleton
import com.udaan.config.Configuration
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.core.constants.CosmosDbConfig
import com.udaan.sourcing.async.messaging.servicebus.ServiceBusTopicConsumerClient
import com.udaan.sourcing.async.storage.cosmos.CosmosMessageStorageClient
import javax.ws.rs.NotFoundException

class PricingEventsModule : AbstractModule() {
    override fun configure() {
        install(PricingCoreModule())
    }

    @Provides
    @Singleton
    fun getCosmosMessageStorageClient(): CosmosMessageStorageClient {
        return CosmosMessageStorageClient(
            configKey = CosmosDbConfig.DB_ACCOUNT_CONFIG,
            dbName = CosmosDbConfig.PRICING_COMMON_DB,
            containerName = CosmosDbConfig.PRICING_ASYNC_TASKS_COLLECTION
        )
    }

    @Provides
    @Singleton
    fun getServiceBusTopicConsumerClient(): ServiceBusTopicConsumerClient {
        return ServiceBusTopicConsumerClient(
            connectionString = Configuration.get(SERVICE_BUS_RESOURCE_ID)
                ?: throw NotFoundException("Configuration not found for ServiceBus"),
            topicName = SIGNALS_TOPIC_NAME,
            subscriptionName = SIGNALS_TOPIC_SUBSCRIBER_NAME,
            maxConcurrentCalls = 40
        )
    }
}
