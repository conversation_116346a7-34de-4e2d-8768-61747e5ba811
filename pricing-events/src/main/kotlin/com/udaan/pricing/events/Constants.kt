package com.udaan.pricing.events

const val SERVICE_BUS_RESOURCE_ID = "pricing/servicebus/connection"
const val SIGNALS_TOPIC_NAME = "signal-events"
const val COMPETITIVE_EVENTS_TOPIC_NAME = "competitive-events"
const val COMPETITIVE_EVENTS_SUBSCRIPTION_NAME = "competitive-events-signal-consumer"
const val SIGNALS_TOPIC_SUBSCRIBER_NAME = "signal-event-consumer"
const val LPP_SIGNAL_CONSUMER = "po-creation-pricing-signals-v2-consumer"
