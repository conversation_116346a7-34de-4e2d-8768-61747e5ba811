package com.udaan.pricing.events.listeners

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.config.Configuration
import com.udaan.pricing.events.COMPETITIVE_EVENTS_SUBSCRIPTION_NAME
import com.udaan.pricing.events.COMPETITIVE_EVENTS_TOPIC_NAME
import com.udaan.pricing.events.SERVICE_BUS_RESOURCE_ID
import com.udaan.pricing.events.SignalTaskType
import com.udaan.pricing.signalcreation.RawSignalInput
import com.udaan.sourcing.async.listeners.servicebus.ServiceBusTopicListener
import com.udaan.sourcing.async.models.TaskRequest

// todo: Evaluate if we need this listener?
@Singleton
class CompetitiveEventsSignalListener @Inject constructor(
    private val mapper: ObjectMapper
) : ServiceBusTopicListener(
    connectionString = Configuration.get(SERVICE_BUS_RESOURCE_ID) ?: error("No resource found for $SERVICE_BUS_RESOURCE_ID"),
    topicName = COMPETITIVE_EVENTS_TOPIC_NAME,
    subscriptionName = COMPETITIVE_EVENTS_SUBSCRIPTION_NAME
) {

    override fun transform(data: ByteArray): List<TaskRequest<*>> {
        val rawSignalInput = mapper.readValue(data, RawSignalInput::class.java)

        return listOf(
            TaskRequest(
                type = SignalTaskType.CREATE_SIGNAL.toString(),
                body = rawSignalInput
            )
        )
    }
}
