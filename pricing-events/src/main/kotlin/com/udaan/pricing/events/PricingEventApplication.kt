package com.udaan.pricing.events

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.udaan.common.server.DropwizardApplication
import com.udaan.common.server.register
import com.udaan.resources.lifecycle.LifeCycleObjectRepo
import com.udaan.sourcing.async.AsyncTaskServer
import com.udaan.sourcing.async.handler.TaskHandler
import com.udaan.sourcing.async.listeners.AbstractListener
import com.udaan.sourcing.async.listeners.eventhub.EventHubListener
import com.udaan.sourcing.async.listeners.servicebus.ServiceBusTopicListener
import com.udaan.sourcing.async.messaging.servicebus.ServiceBusTopicConsumerClient
import com.udaan.sourcing.async.storage.cosmos.CosmosMessageStorageClient
import io.dropwizard.jersey.jackson.JsonProcessingExceptionMapper
import io.dropwizard.setup.Environment
import org.reflections.Reflections

class PricingEventApplication: DropwizardApplication<PricingEventsConfiguration>() {
    companion object {
        private const val TASK_HANDLERS_MODULE = "com.udaan.pricing.events.handlers"
        private const val LISTENERS_MODULE = "com.udaan.pricing.events.listeners"
    }

    override fun getGuiceModules(configuration: PricingEventsConfiguration, environment: Environment) = listOf(
        PricingEventsModule()
    )

    override fun getJacksonModules() = listOf(JavaTimeModule())

    override fun runAdditional(configuration: PricingEventsConfiguration, environment: Environment) {
        environment.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        environment.register(LifeCycleObjectRepo.global())

        environment.jersey().register(JsonProcessingExceptionMapper(true))

        registerAndStartAsyncTaskServer()
    }

    private fun registerAndStartAsyncTaskServer() {
        val cosmosMessageStorageClient = injector.getInstance(CosmosMessageStorageClient::class.java)
        val serviceBusTopicConsumerClient = injector.getInstance(ServiceBusTopicConsumerClient::class.java)

        val asyncTaskServer = AsyncTaskServer(
            handlers = getHandlers(),
            consumer = serviceBusTopicConsumerClient,
            storage = cosmosMessageStorageClient,
            listeners = getListeners()
        )

        asyncTaskServer.startInBackground()
    }

    private fun getHandlers(): List<TaskHandler<*>> {
        val handlersReflections = Reflections(TASK_HANDLERS_MODULE)
        return handlersReflections.getSubTypesOf(TaskHandler::class.java).toList().map {
            injector.getInstance(it)
        }
    }

    private fun getListeners(): List<AbstractListener> {
        val listenersReflections = Reflections(LISTENERS_MODULE)
        val eventHubListeners = listenersReflections.getSubTypesOf(EventHubListener::class.java).toList().map {
            injector.getInstance(it)
        }
        val serviceBusListeners = listenersReflections.getSubTypesOf(ServiceBusTopicListener::class.java).toList().map {
            injector.getInstance(it)
        }
        return eventHubListeners + serviceBusListeners
    }
}

fun main(args: Array<String>) {
    PricingEventApplication().startWithArgs(args)
}
