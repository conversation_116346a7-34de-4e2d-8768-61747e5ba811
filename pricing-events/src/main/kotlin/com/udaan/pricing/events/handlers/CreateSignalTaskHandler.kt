package com.udaan.pricing.events.handlers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.core.managers.signals.SignalWriteManager
import com.udaan.pricing.events.SignalTaskType
import com.udaan.pricing.signalcreation.RawSignalInput
import com.udaan.sourcing.async.handler.TaskHandler
import com.udaan.sourcing.async.handler.retry.RetryConfig
import com.udaan.sourcing.async.models.Task
import com.udaan.sourcing.async.models.TaskRequest

@Singleton
class CreateSignalTaskHandler @Inject constructor(
    private val signalWriteManager: SignalWriteManager
) : TaskHandler<RawSignalInput>(
    SignalTaskType.CREATE_SIGNAL.toString(),
    RawSignalInput::class.java,
    RetryConfig(persistOnFailure = true, maxAttempts = 3)
) {

    private val log by logger()

    override suspend fun handle(task: Task<RawSignalInput>): List<TaskRequest<*>> {
        createSignal(task.body)
        return emptyList()
    }

    private suspend fun createSignal(rawSignalInput: RawSignalInput) {
        log.info("Received rawSignalInput for signal creation {}", rawSignalInput)
        signalWriteManager.createSignalFromRawInput(rawSignalInput)
        log.info("signal creation for rawSignalInput is completed {}", rawSignalInput)
    }


}
