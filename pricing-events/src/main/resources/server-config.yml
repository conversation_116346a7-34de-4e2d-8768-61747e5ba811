
##exclude-start
catalog-readpath-service:
  baseUrl: "http://catalog-readpath-service"
  httpClientConfig:
    connectTimeout: 1000
    readTimeout: 5000
    requestTimeout: 5000
    threadPoolName: AsyncHttpClient-catalog

listing-tags:
  baseUrl: "http://listing-tags-service"
  httpClientConfig:
    connectTimeout: 1000
    readTimeout: 5000
    requestTimeout: 5000
    threadPoolName: AsyncHttpClient-catalog

promotions-discovery-service:
  baseUrl: "http://promotions-discovery-service"
  httpClientConfig:
    requestTimeout: 10000

vertical:
  baseUrl: "http://vertical-service"
  httpClientConfig:
    connectTimeout: 1000
    readTimeout: 5000
    requestTimeout: 5000
    threadPoolName: AsyncHttpClient-vertical


promotions-service:
  baseUrl: "http://promotions-service"
  httpClientConfig:
    requestTimeout: 10000

user:
  baseUrl: "http://user-service"
  httpClientConfig:
    requestTimeout: 60000
    threadPoolName: AsyncHttpClient-userΩ

user-service:
  baseUrl: "http://user-service"
  httpClientConfig:
    requestTimeout: 60000

inventory:
  baseUrl: "http://inventory-service"
  httpClientConfig:
    requestTimeout: 60000

warehouse:
  baseUrl: "http://warehouse-service"
  httpClientConfig:
    requestTimeout: 60000

communication:
  baseUrl: "http://communication-service"
  httpClientConfig:
    requestTimeout: 60000

fulfilment:
  baseUrl: "http://fulfilment-service"
  httpClientConfig:
    requestTimeout: 60000

fulfilment-catalog:
  baseUrl: "http://fulfilment-catalog-service"
  httpClientConfig:
    requestTimeout: 60000

fulfilment-catalog-legacy:
  baseUrl: "http://fulfilment-catalog-legacy-service"
  httpClientConfig:
    requestTimeout: 10000

fulfilment-catalog-service:
  baseUrl: "http://fulfilment-catalog-service"
  httpClientConfig:
    requestTimeout: 60000

inbound-fulfilment:
  baseUrl: "http://inbound-fulfilment-service"
  httpClientConfig:
    requestTimeout: 60000

order:
  baseUrl: "http://order-mgt-svc"
  httpClientConfig:
    requestTimeout: 60000

job-mgmt-daemon:
  baseUrl: "http://job-mgmt-daemon"
  httpClientConfig:
    requestTimeout: 60000

storageService:
  baseUrl: "http://storage-service"
  httpClientConfig:
    requestTimeout: 60000

orderService:
  baseUrl: "http://order-mgt-svc"
  httpClientConfig:
    requestTimeout: 60000

logistics-console-service:
  baseUrl: "http://logistics-console-service"
  httpClientConfig:
    requestTimeout: 60000

dataplatform-service:
  baseUrl: "http://data-platform-file-service"
  httpClientConfig:
    requestTimeout: 300000

config-service:
  baseUrl: "http://config-service"
  httpClientConfig:
    requestTimeout: 5000

fulfilment-service:
  baseUrl: "http://fulfilment-service"
  httpClientConfig:
    requestTimeout: 6000

credit-service:
  baseUrl: "http://credit-service"
  httpClientConfig:
    requestTimeout: 6000

orchestrator-service:
  baseUrl: "http://orchestrator-service"
  httpClientConfig:
    requestTimeout: 6000
    keepAlive: false

tracking-service:
  baseUrl: "http://udaan-tracking-service"
  httpClientConfig:
    requestTimeout: 60000

first-party-service:
  baseUrl: "http://first-party-service"
  httpClientConfig:
    requestTimeout: 60000

planning-service:
  baseUrl: "http://planning-service"
  httpClientConfig:
    requestTimeout: 60000

sc-network-service:
  baseUrl: "http://sc-network-service"
  httpClientConfig:
    requestTimeout: 60000

warehouse-service:
  baseUrl: "http://warehouse-service"
  httpClientConfig:
    requestTimeout: 10000

trading-service:
  baseUrl: "http://trading-service"
  httpClientConfig:
    requestTimeout: 6000

first-party-catalog-service:
  baseUrl: "http://first-party-catalog-service"
  httpClientConfig:
    requestTimeout: 10000

pricing-signals-v2-service:
  baseUrl: "http://pricing-signals-v2-service"
  httpClientConfig:
    requestTimeout: 10000

catalog-service:
  baseUrl: "http://catalog-service"
  httpClientConfig:
    requestTimeout: 60000

product-master-catstack-service:
  baseUrl: "http://product-master-catstack-service"
  httpClientConfig:
    requestTimeout: 30000

vertical-service:
  baseUrl: "http://vertical-service"
  httpClientConfig:
    requestTimeout: 60000

pricing-network-service:
  baseUrl: "http://pricing-network-service"
  httpClientConfig:
    requestTimeout: 5000

server:
  adminContextPath: /
  applicationContextPath: /
  applicationConnectors:
    - type: http
      port: 7000
  adminConnectors:
    - type: http
      port: 7001
  requestLog:
    appenders: [ ]

logging:
  level: WARN
  loggers:
    "io.dropwizard": INFO
    "org.eclipse.jetty": INFO
    "com.udaan": INFO
    "com.udaan.pricing": DEBUG
  appenders:
    - type: console
      threshold: INFO
      timeZone: "UTC"
      logFormat: "[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%X{TRACE-ID}] [%level] [%logger] %msg %n"

errorCodeConfig:
  serviceName: PRICING_EVENT_SERVICE

eventhubResourceId: "signalsv2/trading-signals-v2-consumer"

dcos:
  cpus: 2
  mem: 4000
  servicePort: 10040  # Service port as found on https://sites.google.com/a/udaan.com/tech/services
  ephemeralStorage: 10240 #MBs
  instancesDev: 1
  instancesProd: 1 # Number of instances to be spawned during deployment
  jvmArgs: >-
    -server -XX:InitialRAMPercentage=70.0 -XX:MaxRAMPercentage=70.0
    -XX:MaxMetaspaceSize=214m -XX:+UseParallelGC
    -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=9002
    -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false
    -XX:StartFlightRecording
  mainClass: com.udaan.pricing.events.PricingEventApplicationKt
  env:
    dev:
      cluster: k8s0
      hpaSpec:
        maxReplicas: 2
        minReplicas: 1
        targetCPUUtilizationPercentage: 80
    prod:
      cluster: sin0
      hpaSpec:
        maxReplicas: 5
        minReplicas: 2
        targetCPUUtilizationPercentage: 80
