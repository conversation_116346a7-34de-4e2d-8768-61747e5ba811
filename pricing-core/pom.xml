<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.udaan.pricing</groupId>
        <artifactId>pricing-parent</artifactId>
        <version>2.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>pricing-core</artifactId>
    <packaging>jar</packaging>

    <name>Pricing core</name>

    <dependencies>
        <dependency>
            <groupId>com.udaan.pricing</groupId>
            <artifactId>pricing-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.incentives</groupId>
            <artifactId>promotions-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.vertical</groupId>
            <artifactId>vertical-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.warehouse</groupId>
            <artifactId>warehouse-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.warehouse</groupId>
            <artifactId>warehouse-models</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.listingtag</groupId>
            <artifactId>listing-tags-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.dataplatform</groupId>
            <artifactId>dataplatform-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.users</groupId>
            <artifactId>user-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.users</groupId>
            <artifactId>user-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.catalog</groupId>
            <artifactId>catalog-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.orchestrator</groupId>
            <artifactId>orchestrator-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.orchestrator</groupId>
            <artifactId>orchestrator-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.microsoft.azure</groupId>
            <artifactId>documentdb-bulkexecutor</artifactId>
        </dependency>

        <dependency>
            <groupId>fulfilment-catalog-legacy</groupId>
            <artifactId>fulfilment-catalog-legacy-client</artifactId>
        </dependency>

        <dependency>
            <groupId>io.dropwizard</groupId>
            <artifactId>dropwizard-core</artifactId>
        </dependency>

        <dependency>
            <groupId>io.dropwizard</groupId>
            <artifactId>dropwizard-testing</artifactId>
        </dependency>

        <dependency>
            <groupId>io.dropwizard</groupId>
            <artifactId>dropwizard-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.firstparty</groupId>
            <artifactId>sprinkle-utils</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.firstparty</groupId>
            <artifactId>first-party-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.firstpartycatalog</groupId>
            <artifactId>first-party-catalog-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.firstpartycatalog</groupId>
            <artifactId>first-party-catalog-models</artifactId>
        </dependency>

        <!-- Uber H3 -->
        <dependency>
            <groupId>com.uber</groupId>
            <artifactId>h3</artifactId>
        </dependency>


        <!--Kotlin-->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.fulfilmentcatalog</groupId>
            <artifactId>fulfilment-catalog-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.fulfilment</groupId>
            <artifactId>client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.cosmosdb</groupId>
            <artifactId>cosmosdb-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.microsoft.azure</groupId>
            <artifactId>azure-eventhubs</artifactId>
        </dependency>
        <dependency>
            <groupId>com.microsoft.azure</groupId>
            <artifactId>azure-eventhubs-eph</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.x25</groupId>
            <artifactId>ip-subnet-tree</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.common</groupId>
            <artifactId>common-utils</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.common</groupId>
            <artifactId>common-server</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.config</groupId>
            <artifactId>config-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.planning</groupId>
            <artifactId>planning-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.planning</groupId>
            <artifactId>planning-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.scnetwork</groupId>
            <artifactId>sc-network-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.scnetwork</groupId>
            <artifactId>sc-network-models</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.firstparty</groupId>
            <artifactId>trading-client</artifactId>
        </dependency>

        <dependency>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udojava</groupId>
            <artifactId>EvalEx</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.common</groupId>
            <artifactId>common-auth</artifactId>
        </dependency>

        <!-- test -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>io.mockk</groupId>
            <artifactId>mockk-jvm</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- temp netty dependency to unblock operations and functional threads from netty issue -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-resolver-dns</artifactId>
        </dependency>


    </dependencies>

    <build>
        <sourceDirectory>src/main/kotlin</sourceDirectory>
        <testSourceDirectory>src/test/kotlin</testSourceDirectory>
        <plugins>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <source>src/main/kotlin</source>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <phase>process-test-sources</phase>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
