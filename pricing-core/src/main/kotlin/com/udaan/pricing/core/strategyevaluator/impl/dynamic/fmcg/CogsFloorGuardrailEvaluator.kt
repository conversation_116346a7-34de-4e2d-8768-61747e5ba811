package com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg

import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.LadderUtils
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.variable.VariableId

internal object CogsFloorGuardrailEvaluator : Evaluator {
    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        require(data.previousOutput != null) {
            "Previous output is mandatory for cogs floor guardrail evaluator"
        }

        val outputMetadata = mutableMapOf<String, String>()
        ValidationUtils.validateInputs(data.strategy, data.inputs)

        val cogs = (VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.FMCG_COGS_WOT_PAISA_SET
        ) as? BigDecimalValue)?.value

        outputMetadata["FLOOR_GUARDRAIL_HIT"] = "false"

        val previousOutput = data.previousOutput.output
        outputMetadata["PRICE_WITHOUT_FLOOR_GUARDRAIL"] = previousOutput.toString()

        val guardrailedPrice = when (previousOutput) {
            is BigDecimalValue -> {
                if (cogs != null && previousOutput.value < cogs) {
                    outputMetadata["FLOOR_GUARDRAIL_HIT"] = "true"
                    BigDecimalValue(cogs)
                } else {
                    previousOutput
                }
            }
            is LadderValue -> {
                val updatedLadders = previousOutput.value.map { ladder ->
                    val overridedLadderValue = if (cogs != null && ladder.ladderValue < cogs) {
                        outputMetadata["FLOOR_GUARDRAIL_HIT"] = "true"
                        cogs
                    } else {
                        ladder.ladderValue
                    }

                    ladder.copy(
                        ladderValue = overridedLadderValue
                    )
                }
                LadderUtils.mergeLaddersWithSimilarValue(LadderValue(updatedLadders))
            }
            else -> {
                throw IllegalArgumentException("Expected previous output of type BigDecimalValue or " +
                        "LadderValue but got ${previousOutput::class.simpleName}")
            }
        }

        return EvaluatorOutput(guardrailedPrice, outputMetadata)
    }
}
