package com.udaan.pricing.core.strategyevaluator.impl.dynamic.meat

import com.udaan.catalog.model.VerticalCategory
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.CompMatchUtils
import com.udaan.pricing.core.utils.LadderUtils
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.variable.VariableId

internal object MeatHyperpureCompMatchEvaluator : Evaluator {
    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        require(data.previousOutput != null) {
            "Previous output is mandatory for cogs floor guardrail evaluator"
        }
        ValidationUtils.validateInputs(data.strategy, data.inputs)
        val outputMetadata = mutableMapOf<String, String>()

        // fetching conversion rate
        val conversionRate = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.CONVERSION_RATE
        ) as BigDecimalValue

        // previous input in ladder format (if not already)
        val ladderInputWithoutTaxPaisaAssortment = LadderUtils.convertToLadderValue(
            inputValue = data.previousOutput.output,
            outputMetadata = outputMetadata
        )

        // comp price without tax at assortment level
        val compPriceWithoutTaxPaisaAssortment = CompMatchUtils.getCompLadderPriceWithoutTaxAtAssortment(
            compVariableId = VariableId.HYPERPURE_COMP_LADDER_PRICE_WT_PAISA_UNIT,
            conversionRate = conversionRate.value,
            inputs = data.inputs,
            verticalCategory = VerticalCategory.MEAT,
            outputMetadata = outputMetadata
        ) ?: return EvaluatorOutput(ladderInputWithoutTaxPaisaAssortment, outputMetadata)
        outputMetadata["HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT"] = compPriceWithoutTaxPaisaAssortment.toString()

        // no comp match if qty type mismatches
        val qtyTypeMatchStatus = CompMatchUtils.isQuantityTypeMatching(
            compQtyTypeVariableId = VariableId.HYPERPURE_QUANTITY_TYPE,
            inputs = data.inputs,
            outputMetadata = outputMetadata
        )
        if (qtyTypeMatchStatus.not()) return EvaluatorOutput(ladderInputWithoutTaxPaisaAssortment, outputMetadata)

        /*
            if qty type matches, bring comp value to same level of qty per unit as our listing's
            ex:
            comp qty per unit = 10kg
            listing qty per unit = 5kg
            updated comp value for comparison = (comp value / 10) * 5
         */
        val packSizeCompatibleCompWotPaiseAssortment = CompMatchUtils.translateCompToListingPackSize(
            compLadderValue = compPriceWithoutTaxPaisaAssortment,
            compQuantityPerUnitVariable = VariableId.HYPERPURE_QUANTITY_PER_UNIT,
            inputs = data.inputs,
            outputMetadata = outputMetadata
        ) ?: return EvaluatorOutput(ladderInputWithoutTaxPaisaAssortment, outputMetadata)
        outputMetadata["HP_TRANSLATED_PACK_SIZE_WOT_PAISE_ASSORTMENT"] =
            packSizeCompatibleCompWotPaiseAssortment.toString()

        // comp floor guardrail price
        val floorGuardrailWithoutTaxPaiseAtAssortment = CompMatchUtils.getFloorGuardrailForCompInPaiseAtAssortment(
            inputs = data.inputs,
            variableId = VariableId.MEAT_COGS_WOT_PAISA_UNIT,
            conversionRate = conversionRate.value
        )
        outputMetadata["COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT"] =
            floorGuardrailWithoutTaxPaiseAtAssortment.toString()

        // deriving final ladders post comparison and merge
        val finalLadders = LadderUtils.deriveFinalLaddersPostComparisonAndMerge(
            compFloorGuardrailPrice = floorGuardrailWithoutTaxPaiseAtAssortment,
            ladderInputWithoutTaxPaisaAssortment = ladderInputWithoutTaxPaisaAssortment,
            compPriceWithoutTaxPaisaAssortment = packSizeCompatibleCompWotPaiseAssortment,
            outputMetadata = outputMetadata,
            applyLadderCountCap = VariableUtils.getApplyMaxLadderCountThresholdVariable(
                data.inputs
            )
        )
        return EvaluatorOutput(finalLadders, outputMetadata)
    }
}
