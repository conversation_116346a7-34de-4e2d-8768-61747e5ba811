package com.udaan.pricing.core.helpers.rider.impl

import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.FetchGeoPricingRequest
import com.udaan.pricing.GeoPricing
import com.udaan.pricing.GeoPricingState
import com.udaan.pricing.GeoPricingType
import com.udaan.pricing.PriceRiderForListing
import com.udaan.pricing.RiderCode
import com.udaan.pricing.core.controller.GeoPricingController
import com.udaan.pricing.core.helpers.BuyerCohortDetails
import com.udaan.pricing.core.helpers.BuyerTagHelper
import com.udaan.pricing.core.helpers.rider.PricingRequestContext
import com.udaan.pricing.core.helpers.rider.Rider
import com.udaan.pricing.core.models.CatalogEntityContext

class GeoPriceRider @Inject constructor(
    private val geoPricingController: GeoPricingController,
    private val buyerTagHelper: BuyerTagHelper
): Rider() {
    override val isAdditive :Boolean = false
    val log by logger()

    override suspend fun getPriceRider(
        catalogEntityContext: CatalogEntityContext,
        requestContext: PricingRequestContext,
        fetchRiderDetails: Boolean,
        cluster: List<String>?,
        preferredWarehouseId: String?,
        servingWarehouseId: String?
    ): PriceRiderForListing? {
        val buyerContext = requestContext.buyerContext
        return try {
            if (buyerContext?.pincode == null || catalogEntityContext.listingDetail.orgId.isEmpty()) {
                null
            } else {
                // deriving buyerTag for buyer level differentials
                val buyerOrgId = requestContext.buyerContext.orgId
                val derivedBuyerTagsDetails = buyerTagHelper.getBuyerTagsApplicable(buyerOrgId)
                val derivedBuyerTags = derivedBuyerTagsDetails.map { it.buyerCohort }
                val buyerCohortLocations = if (!preferredWarehouseId.isNullOrEmpty()) {
                    derivedBuyerTags.map { derivedBuyerTag ->
                        (preferredWarehouseId + "_" + derivedBuyerTag).lowercase()
                    }
                } else null

                log.info("Got pricing buyer tags as {} and buyer cohort location as {}",
                    derivedBuyerTags, buyerCohortLocations)

                val geoResponse = geoPricingController.getGeoPricing(
                    FetchGeoPricingRequest(
                        orgId = catalogEntityContext.listingDetail.orgId,
                        vertical = catalogEntityContext.vertical.name.lowercase(),
                        listingId = catalogEntityContext.listingId,
                        pincode = buyerContext.pincode!!,
                        state = listOf(GeoPricingState.ACTIVE),
                        cluster = cluster.orEmpty().plus(derivedBuyerTags).plus(buyerCohortLocations.orEmpty()).plus(preferredWarehouseId.orEmpty())
                    )
                ).filter { it.priceValue != 0L }

                if (geoResponse.isNotEmpty()) {
                    val areAllPercentDifferentials = geoResponse.all { it.priceType == GeoPricingType.PERCENTAGE }

                    if (areAllPercentDifferentials) {
                        val appliedRider = decideAppliedRider(
                            buyerCohortDetails = derivedBuyerTagsDetails,
                            geoResponse = geoResponse,
                            warehouseId = preferredWarehouseId
                        )

                        PriceRiderForListing(
                            listingId = catalogEntityContext.listingId,
                            saleUnitId = "",
                            bpsInPercentage = appliedRider?.priceValue?.toInt() ?: 0,
                            flatVal = 0,
                            isAdditive = true,
                            riderCode = RiderCode.GEO.name,
                            riderDetails = appliedRider
                        )
                    } else {
                        // This log is to identify quantum of calls where we have flat differentials applicable
                        // todo: @om.raj - Should be deprecated later on if it was built solely for marketplace
                        log.info("Flat differentials applicable: {}", geoResponse)
                        PriceRiderForListing(
                            listingId = catalogEntityContext.listingId,
                            saleUnitId = "",
                            bpsInPercentage = 0,
                            flatVal = geoResponse[0].priceValue.toInt(),
                            isAdditive = geoResponse.first().additive,
                            riderCode = RiderCode.GEO.name,
                            riderDetails = geoResponse.first()
                        )
                    }
                } else {
                    null
                }
            }

        } catch (ex: Exception) {
            log.error("Geo rider Exception in geo filter {} ", ex.message)
            null
        }
    }

    /**
     * Fun returns applicable geoDifferential basis buyer cohorts derived for buyer.
     * Below logic is applied:
     * 1. if buyer cohorts available, pick first differential at buyer cohort level looking in decreasing priority
     * 2. if no differential found at cohort level, check in same order for buyerCohortLocation level
     * 3. if none found, return first location based differential
     * 4. if no buyer cohorts available, return first location based differential
     */
    private fun decideAppliedRider(
        buyerCohortDetails: List<BuyerCohortDetails>,
        geoResponse: List<GeoPricing>,
        warehouseId: String?
    ): GeoPricing? {
        // return first differential if no buyer cohorts
        if (buyerCohortDetails.isEmpty()) return geoResponse.firstOrNull()

        // check for cohort level geoDifferential in decreasing priority order
        val cohortGeoDifferential = buyerCohortDetails
            .sortedByDescending { it.cohortPriority }
            .mapNotNull { cohortDetail ->
                geoResponse.firstOrNull { it.geoTypeId.equals(cohortDetail.buyerCohort, ignoreCase = true) }
            }.firstOrNull()
        if (cohortGeoDifferential != null) return cohortGeoDifferential

        // if no cohort level differential found, check for cohort based location level differential in same order
        val cohortLocationGeoDifferential = warehouseId?.let {
            buyerCohortDetails
                .sortedByDescending { it.cohortPriority }
                .mapNotNull { cohortDetail ->
                    val cohortLocation = warehouseId + "_" + cohortDetail.buyerCohort
                    geoResponse.firstOrNull { it.geoTypeId.equals(cohortLocation, ignoreCase = true) }
                }.firstOrNull()
        }
        return cohortLocationGeoDifferential ?: geoResponse.firstOrNull()
    }
}
