package com.udaan.pricing.core.utils

import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorConfigInput
import com.udaan.pricing.strategy.Strategy

internal object ValidationUtils {

    fun validateInputs(strategy: Strategy, inputs: List<EvaluatorConfigInput>) {
        val variablesNeeded = strategy.mandatoryVariables
        variablesNeeded.map { variableId ->
            val variableInput = inputs.firstOrNull { it.variableId ==  variableId }?.value
            require(variableInput != null) {
                "Mandatory $variableId is not passed in the input."
            }
        }
    }
}
