package com.udaan.pricing.core.helpers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.catalog.model.VerticalCategory
import com.udaan.catalog.models.ModelV2
import com.udaan.common.client.UdaanServiceException
import com.udaan.common.utils.kotlin.logger
import com.udaan.orchestrator.client.servingTenants.SearchServingWarehousesCacheClient
import com.udaan.orchestrator.models.search.OrderLineDetail
import com.udaan.orchestrator.models.search.SearchServingWarehouseReq
import com.udaan.orchestrator.models.search.ServingWarehouse
import com.udaan.pricing.ContextualPriceRequest
import kotlinx.coroutines.future.await

@Singleton
class ServingWarehouseHelper @Inject constructor(
    private val servingWarehouseCacheClient: SearchServingWarehousesCacheClient
) {
    companion object {
        private val logger by logger()
    }
    
    /**
     * This function returns the current serving warehouse for the listing, salesUnit and given buyer details.
     * We are getting served warehouse in iteration for salesUnit chunk: 1 -> 5 -> all chunk one by one
     * Also, if buyerOrgUnitId is not present, we are defaulting to ORUNZL1WEX5T2C5J4N0VKXRMFGM6V for now.
     * // todo - move away from these hacks of saleUnitId and buyerOrgUnitId
     */
    suspend fun getServingWarehouseId(
        listingId: String,
        salesUnitIds: List<String>,
        listingDetails: ModelV2.TradeListing,
        verticalCategory: VerticalCategory,
        contextualPriceRequest: ContextualPriceRequest
    ): String? {
        // search-serving warehouse is an operation-heavy call, so calling it only wherever required.
        if (verticalCategory != VerticalCategory.FMCG) {
            return null
        }

        var servingWarehouses = getServingWarehouseForSalesUnitChunk(
            listingId = listingId,
            salesUnitIds = salesUnitIds,
            listingDetails = listingDetails,
            contextualPriceRequest = contextualPriceRequest,
            chunkSize = 1
        )

        if (servingWarehouses.isEmpty() && salesUnitIds.size > 1) {
            logger.info("Getting serving warehouses for chunk size 5 for listing: {}", listingId)
            servingWarehouses = getServingWarehouseForSalesUnitChunk(
                listingId = listingId,
                salesUnitIds = salesUnitIds,
                listingDetails = listingDetails,
                contextualPriceRequest = contextualPriceRequest,
                chunkSize = 5
            )
        }

        if (servingWarehouses.isEmpty() && salesUnitIds.size > 5) {
            logger.info("Getting serving warehouses for 15 active salesUnit chunk of listing: {}", listingId)
            servingWarehouses = getServingWarehouseForSalesUnitChunk(
                listingId = listingId,
                salesUnitIds = salesUnitIds,
                listingDetails = listingDetails,
                contextualPriceRequest = contextualPriceRequest,
                chunkSize = 15
            )
        }

        if (servingWarehouses.isEmpty()) {
            logger.info("No serving warehouse for listingId: {} and saleUnits size: {}", listingId, salesUnitIds.size)
        }

        return servingWarehouses.firstOrNull()?.warehouseId
    }

    private suspend fun getServingWarehouseForSalesUnitChunk(
        listingId: String,
        salesUnitIds: List<String>,
        listingDetails: ModelV2.TradeListing,
        contextualPriceRequest: ContextualPriceRequest,
        chunkSize: Int
    ): List<ServingWarehouse> {
        val orderLineDetails = salesUnitIds.chunked(chunkSize).first().map {
            OrderLineDetail(
                orderLineId = contextualPriceRequest.refId ?: it,
                listingId = listingId,
                salesUnitId = it
            )
        }

        return getServingWarehouseIdForSalesUnitId(
            orderLineDetail = orderLineDetails,
            listingDetails = listingDetails,
            contextualPriceRequest = contextualPriceRequest
        )
    }

    private suspend fun getServingWarehouseIdForSalesUnitId(
        orderLineDetail: List<OrderLineDetail>,
        listingDetails: ModelV2.TradeListing,
        contextualPriceRequest: ContextualPriceRequest
    ): List<ServingWarehouse> {
        return try {
            val servingWarehouse = servingWarehouseCacheClient.searchServingWarehouses(
                request = SearchServingWarehouseReq(
                    orderLineListingDetails = orderLineDetail,
                    sellerOrgId = listingDetails.orgId,
                    buyerOrgUnitId = contextualPriceRequest.buyerContext?.orgUnitId ?: "ORUNZL1WEX5T2C5J4N0VKXRMFGM6V",
                    buyerPinCode = contextualPriceRequest.buyerContext?.pincode!!,
                    useCachedPreferredWarehouses = true
                ),
                retryCount = 1
            ).await()
            val servingWarehouseMap = servingWarehouse?.orderLineWarehousesMap ?: emptyMap()
            logger.info("OrderLineDetails size: {}, buyerContext: {}, serving warehouse map: {}",
                orderLineDetail.size, contextualPriceRequest.buyerContext, servingWarehouseMap)

            servingWarehouseMap.values.flatten()
        } catch (e: NullPointerException){
            logger.error("null pointer exception getServingWarehouseIdForSalesUnitId ${e.message}")
            emptyList()
        } catch (e: UdaanServiceException) {
            //HACK : Handling this for Vendor Prices
            if (contextualPriceRequest.buyerContext?.orgUnitId == null && e.httpStatusCode == 409) {
                logger.info(
                    "Ignoring Error getting serving warehouseId for OrderLineDetails: {}, buyerContext: {}, exception: {}, stacktrace: {}",
                    orderLineDetail, contextualPriceRequest.buyerContext, e.message, e.stackTrace)
                return emptyList()
            }else {
                logger.error(
                    "Error getting serving warehouseId for OrderLineDetails: {}, buyerContext: {}, exception: {}, stacktrace: {}",
                    orderLineDetail, contextualPriceRequest.buyerContext, e.message, e.stackTrace
                )
                throw e
            }
        } catch (ex: Exception) {
            logger.error(
                "Error getting serving warehouseId for OrderLineDetails: {}, buyerContext: {}, exception: {}, stacktrace: {}",
                orderLineDetail, contextualPriceRequest.buyerContext, ex.message, ex.stackTrace
            )
            throw ex
        }
    }
}
