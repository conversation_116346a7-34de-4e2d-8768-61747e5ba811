package com.udaan.pricing.core.models.contracts

import com.udaan.common.utils.getCurrentMillis
import com.udaan.pricing.contracts.ContractDurationResponse
import com.udaan.pricing.contracts.ContractLadderPriceRequest
import com.udaan.pricing.contracts.ContractLadderPriceResponse
import com.udaan.pricing.contracts.ContractLadderRequest
import com.udaan.pricing.contracts.ContractLadderResponse
import com.udaan.pricing.contracts.ContractPriceRequestType
import com.udaan.pricing.contracts.ContractQuoteResponse
import com.udaan.pricing.contracts.ContractRequest
import com.udaan.pricing.contracts.ContractResponse
import com.udaan.pricing.contracts.ExceptionContractResponse
import com.udaan.pricing.contracts.QuotePriceInfoResponse
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.concurrent.*

fun Contract.toContractPriceResponse(catalogTitle: String): ContractResponse {
    return ContractResponse(
        catalogEntity = this.contractCatalogEntity.toString(),
        catalogEntityId = this.catalogEntityId,
        duration = ContractDurationResponse(
            startTime = this.duration.startTime,
            endTime = this.duration.endTime,
            lockInTime = this.duration.lockInTime
        ),
        contractPrice = ContractLadderPriceResponse(
            value = when (this.price) {
                is ContractLadderPrice -> this.price.value.map {
                    ContractLadderResponse(
                        minQuantity = it.minQuantity,
                        maxQuantity = it.maxQuantity,
                        ladderValue = it.ladderValue
                    )
                }
                is ContractLadderMrpMarkdownBps -> this.price.value.map {
                    ContractLadderResponse(
                        minQuantity = it.minQuantity,
                        maxQuantity = it.maxQuantity,
                        ladderValue = it.ladderValue
                    )
                }
            }
        ),
        contractPriceType = when (this.price) {
            is ContractLadderPrice -> ContractPriceRequestType.ABSOLUTE_LADDER_PRICE
            is ContractLadderMrpMarkdownBps -> ContractPriceRequestType.MRP_MARKDOWN_BPS
        },
        volumeCommitted = this.metadata.volumeCommitted,
        volumeOrdered = this.metadata.volumeOrdered,
        volumeOrderedRefreshedTill = this.metadata.volumeOrderedRefreshedTill ?: 0,
        requestedBy = this.lastRefreshedBy,
        contractType = this.type.toString(),
        catalogTitle = catalogTitle,
        quotePriceInfo = this.quoteInfo?.toQuotePriceInfoResponse()
    )
}

fun ContractQuoteInfo.toQuotePriceInfoResponse(): QuotePriceInfoResponse {
    return QuotePriceInfoResponse(
        quotePriceInPaisa = this.quotePriceInPaisa,
        cogsUnitPriceInPaisa = this.cogsUnitPriceInPaisa,
        quoteMrpInMarkdownBps = this.quoteMrpInMarkdownBps,
        quoteRefreshedAt = this.quoteRefreshedAt,
        listingIdUsedForQuote = this.listingIdUsedForQuote,
        bestCompPriceInPaisa = this.bestCompPriceInPaisa,
        bestCompName = this.bestCompName
    )
}


fun ContractQuoteResponse.toContractRequest(
    requestedBy: String,
    lockInDays: Long,
    expiryInDays: Long
): ContractRequest {
    val priceType = if (this.mrpMarkDownBps == null) {
        ContractPriceRequestType.MRP_MARKDOWN_BPS
    } else if (this.quotePriceInPaisa != null) {
        ContractPriceRequestType.ABSOLUTE_LADDER_PRICE
    } else {
        error("No quote price found to convert to contract request")
    }

    return ContractRequest(
        buyerOrgId = this.buyerOrgId,
        contractCatalogEntityId = this.contractCatalogEntityId,
        contractCatalogEntity = this.contractCatalogEntity,
        price = ContractLadderPriceRequest(
            value = listOf(
                ContractLadderRequest(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE,
                    ladderValue = this.mrpMarkDownBps
                        ?: this.quotePriceInPaisa
                        ?: error("No quote price found to convert to contract request")
                )
            )

        ),
        priceType = priceType,
        type = if (lockInDays > 0) ContractType.LOCK_IN.name else ContractType.EXPIRY.name,
        expiryInEpoch = LocalDate.now()
            .plusDays(expiryInDays)
            .atStartOfDay()
            .toInstant(ZoneOffset.UTC)
            .toEpochMilli(),
        lockInEpoch = getCurrentMillis() + TimeUnit.DAYS.toMillis(lockInDays),
        requestedBy = requestedBy,
        volumeCommitted = this.volumeCommitted,
        reason = ContractReason.VOLUME.name
    )
}

fun ExceptionContract.toExceptionContractResponse(catalogTitle: String): ExceptionContractResponse {
    return ExceptionContractResponse(
        buyerOrgId = buyerOrgId,
        contract = contract.toContractPriceResponse(catalogTitle),
        cogsUnitPrice = cogsUnitPrice,
        mrpUnitPriceWithNoTax = mrpUnitPriceWithNoTax,
        message = message,
        requestedAt = requestedAt
    )
}