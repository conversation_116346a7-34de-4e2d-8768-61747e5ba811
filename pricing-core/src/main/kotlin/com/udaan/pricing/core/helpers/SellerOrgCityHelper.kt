package com.udaan.pricing.core.helpers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.firstparty.client.FirstPartyClient
import com.udaan.firstparty.client.SellerClient
import com.udaan.instrumentation.TelemetryScope

@Singleton
// todo: just for pilot, move this to redisCache2 once you have enough bandwidth
class SellerOrgCityHelper @Inject constructor(
    private val sellerClient: SellerClient,
    private val firstPartyClient: FirstPartyClient
) {
    companion object {
        private val logger by logger()
    }

    init {
        logger.info("OrgUtils initialised")
    }

    /**
     * A deferred property that asynchronously initializes a sellerOrgs to city map.
     *
     * The initialization is done using the [TelemetryScope.async] coroutine builder.
     * This ensures that the computation is non-blocking and can be awaited when needed.
     *
     * The actual initialization logic is defined in the [initialiseSellerOrgsToCityMap] function.
     *
     * @property deferredSellerOrgsToCityMap A Deferred object representing the asynchronous computation of sellerOrgs to city map.
     */
    private val deferredSellerOrgsToCityMap = TelemetryScope.async {
        initialiseSellerOrgsToCityMap()
    }

    /**
     * Retrieves the city for the specified sellerOrgId.
     *
     * This function waits for the asynchronous initialization of the deferredCityToSellerOrgMap map to complete,
     * and then returns the list of sellerOrgIds corresponding to the provided city name.
     * The city name is converted to uppercase to ensure a case-insensitive lookup.
     *
     * @param sellerOrgId The seller organization ID for which we want to retrieve the city.
     * @return the city, or `null` if the sellerOrgId is not found in the map.
     */
    suspend fun getCityForSellerOrgId(sellerOrgId: String): String? {
        return deferredSellerOrgsToCityMap.await()[sellerOrgId.uppercase()]
    }

    /**
     * Initializes a map of sellerOrgId to city map.
     *
     * This function retrieves all first-party sellers and filters them based on categories,
     * then retrieves all associated marketplace sellerOrgIds and then creates a sellerOrgId to city map.
     * The city names are converted to uppercase to ensure case-insensitive grouping.
     *
     * @return A map where each key is a sellerOrgId and value is the city name (in uppercase).
     */
    private suspend fun initialiseSellerOrgsToCityMap(): Map<String, String> {
        val allowedCategories = listOf("FMCG", "Staples")
        return sellerClient.getAllFirstPartySellers().executeAwait().filter { orgResponse ->
            allowedCategories.any { it.equals(orgResponse.category.name, ignoreCase = true) }
        }.filterNot {
            it.name.contains("Test Org")
        }.map { orgResponse ->
            val allSellerOrgIdsForFpOrgs = getAllMarketplaceSellerOrgsForFpOrgId(orgResponse.orgId).distinct()
            allSellerOrgIdsForFpOrgs.map {
                it to orgResponse.city.uppercase()
            }
        }.flatten().toMap()
    }

    /**
     * Retrieves all marketplace sellerOrgIds associated with a given fpOrgId.
     *
     * This function fetches the marketplace mappings for the specified fpOrgId,
     * filters the mappings to include only those for the "UDAAN_MP" marketplace, and returns a list of
     * sellerOrgIds, including the fpOrgId itself.
     *
     * @param fpOrgId The fpOrgId to retrieve marketplace sellerOrgIds.
     * @return A list of marketplace sellerOrgIds associated with the given fpOrgId.
     */
    private suspend fun getAllMarketplaceSellerOrgsForFpOrgId(fpOrgId: String): List<String> {
        val orgMarketplaceMappingsForFpOrg = firstPartyClient.getOrgMarketplaceMappingsForBuyOrg(fpOrgId).executeAwait(1)
        return orgMarketplaceMappingsForFpOrg.filter {
            it.marketPlaceId.equals("UDAAN_MP", ignoreCase = true)
        }.map {
            it.sellerOrgId
        }.plus(fpOrgId).distinct()
    }
}
