package com.udaan.pricing.core.utils.signals

import com.udaan.pricing.commons.Ladder
import java.math.BigDecimal
import java.math.RoundingMode

object BigDecimalUtil {
    fun BigDecimal.roundToDefaultScale(): BigDecimal {
        return this.setScale(4, RoundingMode.HALF_EVEN)
    }

    fun List<Ladder>.roundValues(): List<Ladder> {
        return this.map {
            it.copy(
                ladderValue = it.ladderValue.roundToDefaultScale()
            )
        }
    }

    /**
     * This extension fun:
     * - convert the Int value to BigDecimal
     * - rounds basis scale and RoundingMode given
     *
     * Default config for rounding is
     * - scale 4 (impacts left side of decimal)
     * - RoundingMode.HALF_EVEN (banker's rounding)
     *
     * It returns BigDecimal value.
     */
    fun Int.toBigDecimalWithScale(
        scale: Int = 4,
        roundingMode: RoundingMode = RoundingMode.HALF_EVEN
    ): BigDecimal {
        return this.toBigDecimal().setScale(scale, roundingMode)
    }

    /**
     * This extension fun:
     * - convert the Long value to BigDecimal
     * - rounds basis scale and RoundingMode given
     *
     * Default config for rounding is
     * - scale 4 (impacts left side of decimal)
     * - RoundingMode.HALF_EVEN (banker's rounding)
     *
     * It returns BigDecimal value.
     */
    fun Long.toBigDecimalWithScale(
        scale: Int = 4,
        roundingMode: RoundingMode = RoundingMode.HALF_EVEN
    ): BigDecimal {
        return this.toBigDecimal().setScale(scale, roundingMode)
    }

    /**
     * This extension fun:
     * - convert the Double value to BigDecimal
     * - rounds basis scale and RoundingMode given
     *
     * Default config for rounding is
     * - scale 4 (impacts left side of decimal)
     * - RoundingMode.HALF_EVEN (banker's rounding)
     *
     * It returns BigDecimal value.
     */
    fun Double.toBigDecimalWithScale(
        scale: Int = 4,
        roundingMode: RoundingMode = RoundingMode.HALF_EVEN
    ): BigDecimal {
        return this.toBigDecimal().setScale(scale, roundingMode)
    }

    /**
     * This extension fun:
     * - convert the String value to BigDecimal
     * - rounds basis scale and RoundingMode given
     *
     * Default config for rounding is
     * - scale 4 (impacts left side of decimal)
     * - RoundingMode.HALF_EVEN (banker's rounding)
     *
     * It returns BigDecimal value.
     */
    fun String.toBigDecimalWithScale(
        scale: Int = 4,
        roundingMode: RoundingMode = RoundingMode.HALF_EVEN
    ): BigDecimal {
        return this.toBigDecimal().setScale(scale, roundingMode)
    }
}
