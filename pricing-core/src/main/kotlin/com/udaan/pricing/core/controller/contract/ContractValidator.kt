package com.udaan.pricing.core.controller.contract

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.catalog.models.ModelV2
import com.udaan.common.utils.getCurrentMillis
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.contracts.ContractRequest
import com.udaan.pricing.core.models.common.getMrpWithOutTax
import com.udaan.pricing.core.helpers.CatalogHelper
import com.udaan.pricing.core.helpers.ConfigHelper
import com.udaan.pricing.core.helpers.PricingNetworkHelper
import com.udaan.pricing.core.models.contracts.Contract
import com.udaan.pricing.core.models.contracts.ContractCatalogEntity
import com.udaan.pricing.core.models.contracts.ContractDuration
import com.udaan.pricing.core.models.contracts.ContractLadderMrpMarkdownBps
import com.udaan.pricing.core.models.contracts.ContractLadderPrice
import com.udaan.pricing.core.models.contracts.ContractReason
import com.udaan.pricing.core.models.contracts.ContractType
import com.udaan.pricing.core.models.contracts.expiryDurationInDays
import com.udaan.pricing.core.models.contracts.lockInDurationInDays
import com.udaan.pricing.core.svcinterfaces.CatalogSvcInterface

@Singleton
class ContractValidator @Inject constructor(
    private val configHelper: ConfigHelper,
    private val catalogHelper: CatalogHelper,
    private val priceValidator: ContractPriceValidator,
    private val pricingNetworkHelper: PricingNetworkHelper,
    private val catalogSvcInterface: CatalogSvcInterface
) {

    companion object {
        val INVALID_LISTING_STATUS_FOR_CONTRACT_CREATION = listOf(
            ModelV2.TradeListing.Status.ARCHIVED,
            ModelV2.TradeListing.Status.DELETED
        )
        val VALID_SALES_UNIT_STATUS_FOR_CONTRACT_CREATION = listOf(
            ModelV2.EleStatus.ENABLED,
            ModelV2.EleStatus.INACTIVE
        )
    }

    suspend fun validateContract(contract: Contract, skipPriceValidation: Boolean = false) {
        validateContractCatalogEntity(contract)
        validateCity(contract)
        validateContractDuration(contract)
        enforceCategoryChecks(contract)
        validatePrice(contract)
        validateContractReason(contract)
        if (skipPriceValidation.not()) {
            priceValidator.validateContractPrice(contract)
        }
    }

    private suspend fun enforceCategoryChecks(contract: Contract) {
        val skipVerticalChecks = configHelper.isSkipVerticalExpiryLimitsEnabled(contract.catalogEntityId)
        if (skipVerticalChecks) {
            return
        }
        val activeListingDetails = when (contract.contractCatalogEntity) {
            ContractCatalogEntity.PRODUCT_GROUP_ID -> {
                catalogHelper.getActiveListingsForGroupId(
                    contract.catalogEntityId,
                    contract.metadata.city ?: error("City should not be empty.")
                )
            }
            ContractCatalogEntity.LISTING_ID -> {
                listOf(catalogSvcInterface.getTradeListingMinimal(contract.catalogEntityId))
            }
        }
        val (_, listingsWithoutMrp) = divideListingsByMrp(activeListingDetails)
        if (listingsWithoutMrp.isNotEmpty() && contract.price is ContractLadderMrpMarkdownBps) {
            error(
                "All listings of ${contract.catalogEntityId} should have valid MRP for contract creation. " +
                        "if contract is based on MRP_MARKDOWN_BPS. " +
                        "Please fix the MRP of these listings ${listingsWithoutMrp.map { it.listingId }}"
            )
        }
    }

    private fun divideListingsByMrp(activeListingDetails: List<ModelV2.TradeListing>): Pair<List<ModelV2.TradeListing>,
            List<ModelV2.TradeListing>> {
        return activeListingDetails.partition { listingDetails ->
            listingDetails.salesUnitList
                .filter { it.status in VALID_SALES_UNIT_STATUS_FOR_CONTRACT_CREATION }
                .any { salesUnit ->
                    catalogHelper.getListingTaxDetails(listingDetails, salesUnit.salesUnitId)
                        .getMrpWithOutTax()
                        ?.let { it > 0 } == true
                }
        }
    }

    private suspend fun validateCity(contract: Contract) {
        if (contract.metadata.city.isNullOrBlank()) {
            error("City should not be empty.")
        }
        val warehouses = pricingNetworkHelper.getWarehousesForAnchorCity(contract.metadata.city)
        require(warehouses.isNotEmpty()) {
            "No warehouses found for the city ${contract.metadata.city}. Please provide a valid city."
        }
    }

    private suspend fun validateContractCatalogEntity(contract: Contract) {
        when (contract.contractCatalogEntity) {
            ContractCatalogEntity.PRODUCT_GROUP_ID -> {
                /*
                * TODO: Uncomment this block, post the fix from catalog.
                val groupDetails = catalogHelper.getGroupIdDetails(groupId = contract.catalogEntityId)
                require(groupDetails != null) {
                    "${contract.catalogEntityId} is not a valid group id."
                }*/
            }
            ContractCatalogEntity.LISTING_ID -> {
                val listingDetails = catalogSvcInterface.getTradeListingMinimal(contract.catalogEntityId)
                if (INVALID_LISTING_STATUS_FOR_CONTRACT_CREATION.contains(listingDetails.status)) {
                    error(
                        "${contract.catalogEntityId} is " +
                                " ${listingDetails.status} for contract creation. Please provide a valid listing-id"
                    )
                }
            }
        }
    }

    /**
     * Validates a contract duration.
     * 1. Expiry limits of contract should be under limits configured at vertical level.
     * 2. Lock in limits of contract should be under limits configured at vertical level.
     */
    private suspend fun validateContractDuration(contract: Contract) {
        /**
         * Why this check ? sometimes catalog has incorrect definition of verticals for a gid or listingId.
         *  Even though this check maintains sanity of groupId or listingId.
         *  there will be callouts froms pricing team to update the contract.
         *  SOP should be followed as below.
         *  1. Tag catalog team to correct the verticals for the given gid or listingId.
         *  2. If step 1 is taking time and people want to update the contract irrespective of catalog fix.
         *  3. Enable the flag in config to skip vertical expiry check for the given catalogId.
         *  4. Ask the user to upload the config.
         *  5. Once the catalog is fixed, or no contracts upload is over, disable the flag in config.
         *
         *  TODO: Make sure we have this check via authz. where tech people can skip the checks on behalf of user.
         */
        val skipVerticalExpiryCheckEnabled = configHelper.isSkipVerticalExpiryLimitsEnabled(contract.catalogEntityId)
        val verticalId = if (skipVerticalExpiryCheckEnabled.not()) {
            val vertical = catalogHelper.fetchVerticalIdForCatalogId(
                CatalogEntityType.valueOf(contract.contractCatalogEntity.name),
                contract.catalogEntityId
            )
            require(vertical != null) {
                "There is no vertical found for ${contract.catalogEntityId}."
            }
            vertical
        } else {
            null
        }
        val isExpiryLimitsValid = configHelper.isContractExpiryLimitValid(
            startTime = contract.duration.startTime,
            endTime = contract.duration.endTime,
            verticalId = verticalId
        )
        require(isExpiryLimitsValid) {
            "Contract expiry time of ${contract.expiryDurationInDays()} days is not valid."
        }
        if (contract.type == ContractType.LOCK_IN) {
            require(contract.duration.lockInTime > 0) {
                "Contract lock-in should be greater than 0 days."
            }
            val isLockInLimitsValid = configHelper.isContractLockInLimitValid(
                startTime = contract.duration.startTime,
                lockInTime = contract.duration.lockInTime,
                verticalId = verticalId
            )
            require(isLockInLimitsValid) {
                "Contract lock-in time of ${contract.lockInDurationInDays()} days is not valid."
            }
            require(contract.duration.lockInTime <= contract.duration.endTime) {
                "Contract lock-in time of ${contract.lockInDurationInDays()} " +
                        "days should not exceed expiry time of ${contract.expiryDurationInDays()} days"
            }
        }
    }

    private fun validateContractReason(contract: Contract) {
        if (contract.metadata.reason == ContractReason.VOLUME) {
            require(contract.metadata.volumeCommitted > 0) {
                "Volume Committed should be greater than 0."
            }
        }
    }

    private fun validatePrice(contract: Contract) {
        when (contract.price) {
            /**
             * TODO: Add margin checks.
             */
            is ContractLadderPrice -> {
                val allLadderValuesValid = contract.price.value.all { it.ladderValue > 0 }
                require(allLadderValuesValid) {
                    "Price values should be greater than 0."
                }
                // Ensure ladder values are in decreasing order
                val allLadderValuesDecreasing = contract.price.value
                    .zipWithNext { a, b -> a.ladderValue > b.ladderValue }
                    .all { it }
                require(allLadderValuesDecreasing) {
                    "Price values should be in decreasing order."
                }
            }
            is ContractLadderMrpMarkdownBps -> {
                val allLadderValuesValid = contract.price.value.all { it.ladderValue > 0 }
                require(allLadderValuesValid) {
                    "MRP markdown values should be greater than 0."
                }
            }
        }
    }


    fun getContractDuration(contractRequest: ContractRequest): ContractDuration {
        val startTime = getCurrentMillis()
        return ContractDuration(
            startTime = startTime,
            endTime = contractRequest.expiryInEpoch,
            lockInTime = if (contractRequest.type == ContractType.LOCK_IN.name) {
                contractRequest.lockInEpoch
            } else {
                0L
            }
        )
    }
}
