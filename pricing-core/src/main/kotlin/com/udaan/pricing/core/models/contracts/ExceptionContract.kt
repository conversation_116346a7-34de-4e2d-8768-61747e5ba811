package com.udaan.pricing.core.models.contracts

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.common.utils.getCurrentMillis

@JsonIgnoreProperties(ignoreUnknown = true)
class ExceptionContract(
    val buyerOrgId: String,
    val contract: Contract,
    val cogsUnitPrice: Long? = null,
    val mrpUnitPriceWithNoTax: Long? = null,
    val message: String,
    val requestedAt: Long = getCurrentMillis()
) {
    val id = getContractId(buyerOrgId, contract.catalogEntityId)

    companion object {
        fun getContractId(buyerOrgId: String, catalogEntityId: String): String {
            return ("$buyerOrgId:$catalogEntityId").uppercase()
        }
    }
}