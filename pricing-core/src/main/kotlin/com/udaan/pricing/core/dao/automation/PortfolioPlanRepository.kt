package com.udaan.pricing.core.dao.automation

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.core.constants.CosmosDbConfig
import com.udaan.pricing.portfolioplan.PortfolioPlan
import com.udaan.pricing.portfolioplan.PortfolioPlanState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.withContext

@Singleton
class PortfolioPlanRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {
    private val portfolioPlanCosmosDao by lazy {
        CosmosDbDao(
            configKey = "pricing",
            databaseName = CosmosDbConfig.PRICING_AUTOMATION_DB,
            containerName = CosmosDbConfig.PORTFOLIO_PLAN_CONTAINER
        ) { builder ->
            builder.connectionSharingAcrossClientsEnabled(true)
        }
    }

    /**
     * Dummy function solely used for client initialisation while starting the service
     */
    suspend fun initialise() {
        portfolioPlanCosmosDao.findItem("ID1")
    }

    suspend fun createOrUpdatePortfolioPlan(
        portfolioPlan: PortfolioPlan
    ): PortfolioPlan {
        return portfolioPlanCosmosDao.createOrUpdateItem(portfolioPlan.toDocument()).toPortfolioPlan()
    }

    suspend fun getPortfolioPlanByPortfolioId(
        portfolioId: String
    ): List<PortfolioPlan> {
        return withContext(Dispatchers.IO) {
            portfolioPlanCosmosDao.queryItems(
                queryName = "get-portfolio-plan",
                querySpec = makeSqlQuerySpec(
                    """
                    select * from c where c.portfolioId = @portfolioId and c.state = @state
                    """.trimIndent(),
                    "@portfolioId" to portfolioId,
                    "@state" to PortfolioPlanState.ACTIVE
                )
            ).toList().map { it.toPortfolioPlan() }
        }
    }


    private fun PortfolioPlan.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toPortfolioPlan() = objectMapper.convertValue(this, PortfolioPlan::class.java)
}