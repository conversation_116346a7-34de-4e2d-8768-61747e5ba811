package com.udaan.pricing.core.helpers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.catalog.models.ModelV2
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.managers.signals.VariableManager
import com.udaan.pricing.variable.Variable
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.variable.requestreponse.GetResolvedValuesResponse
import com.udaan.pricing.variable.requestreponse.ResolvedValue
import java.math.BigDecimal

@Singleton
class PricingSignalsHelper @Inject constructor(
    private val variableManager: VariableManager,
    private val sellerOrgCityHelper: SellerOrgCityHelper
) {

    suspend fun getStaplesCogsForProductGroup(
        groupId: String,
        warehouseId: String
    ): BigDecimal? {
        val resolvedValuesMap = variableManager.resolveValuesForProductGroupCached(
            productGroupId = groupId,
            location = Location(LocationType.WAREHOUSE, warehouseId),
            variableIds = setOf(VariableId.STAPLES_LIP_WOT_PAISA_UNIT, VariableId.STAPLES_LPP_WOT_RUPEES_UNIT),
            inputTerritoryMap = null
        ).resolvedValuesMap

        // pick LIP if it is available (not null and not MAX INT) else pick LPP if available (not null and not MAX INT)
        val resolvedValue =
            (resolvedValuesMap[VariableId.STAPLES_LIP_WOT_PAISA_UNIT]?.value as? BigDecimalValue)
                ?.takeUnless { it.value == Int.MAX_VALUE.toBigDecimal() }
                ?: (resolvedValuesMap[VariableId.STAPLES_LPP_WOT_RUPEES_UNIT]?.value as? BigDecimalValue)
                    ?.takeUnless { it.value == Int.MAX_VALUE.toBigDecimal() }
        return resolvedValue?.value
    }

    // @todo - this method should only have orgId and listingId as params and not whole listing details
    suspend fun getFmcgCogsForListing(
        listingDetails: ModelV2.TradeListing,
        salesUnitId: String
    ): BigDecimal? {
        val city = sellerOrgCityHelper.getCityForSellerOrgId(listingDetails.orgId) ?: return null
        val resolvedValue = variableManager.resolveValuesForListingSalesUnitCached(
            listingId = listingDetails.listingId,
            salesUnitId = salesUnitId,
            location = Location(LocationType.CITY, city),
            variableIds = setOf(VariableId.FMCG_COGS_WOT_PAISA_SET),
            inputTerritoryMap = null
        )
        val cogsValue =
            (resolvedValue.resolvedValuesMap[VariableId.FMCG_COGS_WOT_PAISA_SET]?.value as? BigDecimalValue)?.value
        if (cogsValue == Int.MAX_VALUE.toBigDecimal()) {
            return null
        }
        return cogsValue
    }

    suspend fun resolveValuesForProductGroup(
        groupId: String,
        locationType: LocationType,
        locationValue: String,
        variableIds: Set<VariableId>,
        inputTerritoryMap: Map<String, String>?
    ): GetResolvedValuesResponse {
        return variableManager.resolveValuesForProductGroupCached(
            productGroupId = groupId,
            location = Location(locationType, locationValue),
            variableIds = variableIds,
            inputTerritoryMap = inputTerritoryMap
        )
    }

    suspend fun resolveValuesForListing(
        listingId: String,
        salesUnitId: String,
        locationType: LocationType,
        locationValue: String,
        variableIds: Set<VariableId>,
        inputTerritoryMap: Map<String, String>?
    ): GetResolvedValuesResponse {
        return variableManager.resolveValuesForListingSalesUnitCached(
            listingId = listingId,
            salesUnitId = salesUnitId,
            location = Location(locationType, locationValue),
            variableIds = variableIds,
            inputTerritoryMap = inputTerritoryMap
        )
    }

    suspend fun fetchAndValidateTradingManualPrice(
        groupId: String,
        warehouseId: String,
        priceValidTimeInMillis: Long
    ): Boolean {
        val resolvedValue = variableManager.resolveValuesForProductGroupCached(
            productGroupId = groupId,
            location = Location(LocationType.WAREHOUSE, warehouseId),
            variableIds = setOf(VariableId.TRADING_PRICE_WOT_PAISA_UNIT),
            inputTerritoryMap = null
        ).resolvedValuesMap[VariableId.TRADING_PRICE_WOT_PAISA_UNIT]
        return isManualPriceValid(resolvedValue, priceValidTimeInMillis)
    }

    private fun isManualPriceValid(
        resolvedValue: ResolvedValue?,
        priceValidTimeInMillis: Long
    ): Boolean {
        val tradingPrice = (resolvedValue?.value as? BigDecimalValue)?.value
        return (tradingPrice != null && resolvedValue.lastRefreshedAt != null
                && resolvedValue.lastRefreshedAt!! > priceValidTimeInMillis)
    }

    suspend fun getAllVariables(): List<Variable> {
        return variableManager.getAllVariables()
    }
}