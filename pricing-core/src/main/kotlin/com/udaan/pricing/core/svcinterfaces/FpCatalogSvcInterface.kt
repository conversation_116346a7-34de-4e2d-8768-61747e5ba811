package com.udaan.pricing.core.svcinterfaces

import awaitOrNull
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.firstparty.representations.MarketPlaceIdentifier
import com.udaan.firstpartycatalog.client.FirstPartyCatalogClient
import com.udaan.firstpartycatalog.client.FirstPartyCatalogRedisClient
import com.udaan.firstpartycatalog.client.FirstPartyCatalogRedisRepository
import com.udaan.firstpartycatalog.models.Product
import com.udaan.firstpartycatalog.models.ProductGroup
import com.udaan.vertical.model.CentralVertical
import kotlinx.coroutines.future.await

@Singleton
class FpCatalogSvcInterface @Inject constructor(
    private val firstPartyCatalogRedisRepository: FirstPartyCatalogRedisRepository,
    private val firstPartyCatalogRedisClient: FirstPartyCatalogRedisClient,
    private val firstPartyCatalogClient: FirstPartyCatalogClient
) {
    /**
     * This function assumes that there will be only one active salesUnit for a listing
     */
    suspend fun getProductForListingId(listingId: String): Product? {
        return firstPartyCatalogRedisRepository.getProductByListingId(
            listingId = listingId,
            retryCount = 1
        ).awaitOrNull()
    }

    suspend fun getAProductFromGroupId(groupId: String): Product? {
        return firstPartyCatalogRedisRepository.getProductsFromProductGroupId(
            productGroupId = groupId
        ).await().firstOrNull()?.let { productId ->
            getProductDetails(productId)
        }
    }

    suspend fun getProductDetails(productId: String): Product {
        return firstPartyCatalogRedisRepository.getProduct(productId, retryCount = 3).await()
    }

    suspend fun getProductGroup(groupId: String): ProductGroup? {
        return firstPartyCatalogClient.getGroupFromGroupId(groupId).executeAwaitOrNull()
    }

    suspend fun getGroupIdTitle(groupId: String): String? {
        return firstPartyCatalogRedisRepository.getProductsFromProductGroupId(
            groupId, retryCount = 1
        ).await().firstOrNull()?.let {
            firstPartyCatalogRedisRepository.getProduct(it, retryCount = 1).awaitOrNull()?.title
        }
    }


    suspend fun getListingMarketplaceMappingsForGid(productGroupId: String): List<ListingMarketPlaceMapping> {
        val productIds = firstPartyCatalogRedisRepository.getProductsFromProductGroupId(
            productGroupId, retryCount = 1
        ).await()
        return productIds.map { productId ->
            getListingMarketplaceMappingsForPid(productId)
        }.flatten()
    }

    suspend fun getGroupIdDetails(groupId: String): ProductGroup? {
        return firstPartyCatalogClient.getGroupFromGroupId(groupId).executeAwaitOrNull()
    }

    suspend fun fetchGroupIdFromListingId(
        listingId: String
    ): String? {
        val product = getProductForListingId(listingId)
        return product?.let {
            getProductGroupDetailsForProductId(it.productId)
        }
    }

    suspend fun getProductGroupDetailsForProductId(productId: String): String? {
        return firstPartyCatalogRedisClient.getGroupId(productId).awaitOrNull()?.groupId
    }

    private suspend fun getListingMarketplaceMappingsForPid(productId: String): List<ListingMarketPlaceMapping> {
        return firstPartyCatalogRedisRepository.fetchMarketplaceMappings(productId, retryCount = 1).await().filter {
            it.marketPlaceId == MarketPlaceIdentifier.Udaan_MP.name
        }.map {
            ListingMarketPlaceMapping(
                listingId = it.listingId,
                salesUnitId = it.saleUnitId ?: "",
                productId = it.productId,
                sellerOrgId = it.sellerOrgId
            )
        }
    }

    data class ListingMarketPlaceMapping(
        val listingId: String,
        val salesUnitId: String,
        val productId: String,
        val sellerOrgId: String
    )

    suspend fun getFpProductDetails(productId: String): Product {
        return firstPartyCatalogRedisRepository.getProduct(productId = productId, retryCount = 3).await()
    }

    suspend fun getMappedFpProductIdsFromGroupId(productGroupId: String): List<String> {
        return firstPartyCatalogRedisRepository.getProductsFromProductGroupId(
            productGroupId = productGroupId,
            retryCount = 3
        ).await()
    }

    suspend fun getCentralVerticalForProductGroupId(productGroupId: String): CentralVertical {
        return firstPartyCatalogRedisClient.getFPVerticalResponseFromGroupId(productGroupId).await()
    }
}
