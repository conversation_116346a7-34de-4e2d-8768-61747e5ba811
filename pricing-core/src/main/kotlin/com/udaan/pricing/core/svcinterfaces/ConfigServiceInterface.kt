package com.udaan.pricing.core.svcinterfaces

import com.google.inject.Inject
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.udaan.config.client.BusinessConfigClient
import com.udaan.pricing.core.constants.NamedConstants
import kotlinx.coroutines.future.await

@Singleton
class ConfigServiceInterface @Inject constructor(
    @Named(NamedConstants.Config.PRICING_SERVICE_CONFIG) private val pricingConfigClient: BusinessConfigClient
) {
    suspend fun <T> hGetAllAsync(hashMapName: String): Map<String, T>? {
        return pricingConfigClient.hGetAllAsync<T>(
            hashMapName = hashMapName
        ).await()
    }

    suspend fun <T> hGetValueAsync(hashMapName: String, key: String): T? {
        return pricingConfigClient.hGetValueAsync<T>(hashMapName, key).await()
    }

    suspend fun getIntAsync(key: String): Int? {
        return pricingConfigClient.getIntAsync(key).await()
    }

    suspend fun sIsMemberAsync(hashSetName: String, value: String): Boolean {
        return pricingConfigClient.sIsMemberAsync(
            hashSetName = hashSetName,
            value = value
        ).await()
    }

    suspend fun <T> sMembersAsync(hashSetName: String): Set<T>? {
        return pricingConfigClient.sMembersAsync<T>(key = hashSetName).await()
    }
}