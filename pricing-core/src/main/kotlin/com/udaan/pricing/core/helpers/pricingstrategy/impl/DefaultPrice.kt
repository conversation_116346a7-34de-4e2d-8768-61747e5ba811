package com.udaan.pricing.core.helpers.pricingstrategy.impl

import com.google.inject.Inject
import com.udaan.catalog.model.VerticalCategory
import com.udaan.catalog.models.ModelV2
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.ContextualPriceRequest
import com.udaan.pricing.PriceForListing
import com.udaan.pricing.core.controller.BasePriceController
import com.udaan.pricing.core.helpers.pricingstrategy.PricingStrategy
import com.udaan.pricing.core.helpers.pricingstrategy.Strategy
import com.udaan.pricing.core.models.CatalogEntityContext
import com.udaan.proto.models.ModelV1
import kotlinx.coroutines.Deferred

class DefaultPrice @Inject constructor(
    private val basePriceController: BasePriceController
) : Strategy() {
    companion object {
        private val logger by logger()
    }

    override suspend fun getPrice(
        catalogEntityContext: CatalogEntityContext,
        contextualPriceRequest: ContextualPriceRequest?,
        fetchInactive: Boolean,
        cluster: List<String>?,
        preferredWarehouseId: String?,
        servingWarehouseId: String?,
        buyerOrgUnit: ModelV1.OrgUnit?,
        mappedBenchmarkListingGuardrailPriceDeferred: Deferred<PriceForListing?>?
    ): List<PriceForListing> {

        val basePrices = if (catalogEntityContext.salesUnitId != null) {
            basePriceController.getBasePricesForListingSalesUnit(
                listingId = catalogEntityContext.listingId,
                salesUnitId = catalogEntityContext.salesUnitId
            )
        } else {
            basePriceController.getBasePricesForListing(catalogEntityContext.listingId)
        }

        val filteredBasePrices = if (fetchInactive.not()) {
            val activeSU = catalogEntityContext.listingDetail.salesUnitList.filter {
                it.status == ModelV2.EleStatus.ENABLED
            }.map { it.salesUnitId }.toSet()
            basePrices.filter { activeSU.contains(it.saleUnitId) }
        } else {
            basePrices
        }

        /**
         * Override base price in case of B2B staples
         */
        val overriddenBasePrices = if (catalogEntityContext.verticalCategory == VerticalCategory.STAPLES) {
            filteredBasePrices.map { it.copy(qtyBasedPrice = emptyList(), metaData = null) }
        } else {
            filteredBasePrices
        }

        /**
         * Override ends
         */

        val prices = overriddenBasePrices.groupBy { it.saleUnitId }.map { (salesUnitId, price) ->
            val latest = price.maxByOrNull { it.updatedAt } ?: error("No price found for salesUnitId $salesUnitId")
            PriceForListing(latest.listingId, latest.saleUnitId, latest.qtyBasedPrice, PricingStrategy.BASE.name, latest.metaData)
        }

        return prices
    }

}
