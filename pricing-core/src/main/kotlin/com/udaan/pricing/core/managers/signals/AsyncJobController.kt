package com.udaan.pricing.core.managers.signals

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.google.inject.Singleton
import com.microsoft.azure.storage.queue.CloudQueueClient
import com.microsoft.azure.storage.queue.CloudQueueMessage
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.core.constants.JobConstants.PRICING_SERVICE_JOB_QUEUE
import com.udaan.pricing.core.dao.signals.AsyncJobRepository
import com.udaan.pricing.core.helpers.BlobStorageHelper
import com.udaan.pricing.core.utils.signals.CosmosQueryConstants
import com.udaan.pricing.job.AsyncJob
import com.udaan.pricing.job.AsyncJobData
import com.udaan.pricing.job.AsyncJobEvent
import com.udaan.pricing.job.AsyncJobStatus
import com.udaan.pricing.job.AsyncJobType
import com.udaan.pricing.job.requests.CreateAsyncJobRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.InputStream

@Singleton
class AsyncJobController @Inject constructor(
    private val asyncJobRepository: AsyncJobRepository,
    private val blobStorageHelper: BlobStorageHelper,
    private val objectMapper: ObjectMapper,
    private val cloudQueueClient: CloudQueueClient
) {
    companion object {
        private val logger by logger()
    }

    suspend fun createAsyncJob(createAsyncJobRequest: CreateAsyncJobRequest): AsyncJob {
        return asyncJobRepository.createOrUpdateAsyncJob(createAsyncJobRequest.toAsyncJob())
    }

    suspend fun updateAsyncJobStatusAndMetadata(
        jobId: String,
        status: AsyncJobStatus,
        remarks: String? = null,
        inputFilePath: String? = null,
        outputFilePath: String? = null,
        attributesMap: Map<String, String>? = null,
        updatedBy: String
    ): AsyncJob {
        val asyncJob = asyncJobRepository.getAsyncJob(jobId)

        require(asyncJob != null) {
            "Async job with id $jobId not found"
        }

        val updatedRemarks = remarks ?: asyncJob.remarks
        val updatedFilePathReference = asyncJob.filePathReference.copy(
            inputFilePath = inputFilePath ?: asyncJob.filePathReference.inputFilePath,
            outputFilePath = outputFilePath ?: asyncJob.filePathReference.outputFilePath
        )

        val updatedJobData = attributesMap?.let {
            AsyncJobData(asyncJob.jobData.attributesMap + it)
        } ?: asyncJob.jobData

        val updatedAsyncJob = asyncJob.copy(
            status = status,
            filePathReference = updatedFilePathReference,
            jobData = updatedJobData,
            remarks = updatedRemarks,
            updatedBy = updatedBy
        )
        return asyncJobRepository.createOrUpdateAsyncJob(updatedAsyncJob)
    }

    suspend fun getAsyncJobs(
        asyncJobType: AsyncJobType,
        offSet: Int, limit: Int
    ): Collection<AsyncJob> {
        /**
         * Why this check ?
         *
         * Even if you provide offset and limit,
         * cosmos db reads all the documents internally and skips the document according to offset.
         * So your RU cost would be offset + limit.
         */
        require((offSet + limit) <= CosmosQueryConstants.MAX_FETCH_LIMIT) {
            "Total number of documents read (offset + limit) should not exceed ${CosmosQueryConstants.MAX_FETCH_LIMIT}"
        }
        return asyncJobRepository.getAsyncJobs(
            asyncJobType,
            offSet = offSet, limit = limit
        )
    }

    suspend fun getAsyncJob(
        jobId: String
    ): AsyncJob? {
        return asyncJobRepository.getAsyncJob(jobId)
    }

    suspend fun getJobFile(jobId: String, fileType: String): InputStream {
        val asyncJob = asyncJobRepository.getAsyncJob(jobId) ?: error("Async job with id $jobId not found")
        val fileName = asyncJob.id + "_" + "${fileType.lowercase()}.xlsx"
        return blobStorageHelper.getInputStreamFromBlobStorage(
            containerReference = asyncJob.type.getStorageReference(),
            directoryReference = asyncJob.type.getStorageReference(),
            fileName = fileName
        )
    }

    /**
     * TODO: push to different queue based on file size or job types.
     * For now, pushing to same queue for all job types
     */
    suspend fun pushAsyncJobToQueue(jobId: String) {
        val pricingServiceJobQueue = cloudQueueClient.getQueueReference(PRICING_SERVICE_JOB_QUEUE)
        withContext(Dispatchers.IO) {
            pricingServiceJobQueue.addMessage(
                CloudQueueMessage(
                    objectMapper.writeValueAsString(AsyncJobEvent(jobId))
                )
            )
            logger.info("added {} message to {}", jobId, PRICING_SERVICE_JOB_QUEUE)
        }
    }

    private fun AsyncJobType.getStorageReference(): String {
        return this.name.lowercase().replace("_", "-")
    }
}
