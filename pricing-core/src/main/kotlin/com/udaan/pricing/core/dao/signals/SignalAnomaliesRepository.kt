package com.udaan.pricing.core.dao.signals

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.core.constants.CosmosDbConfig
import com.udaan.pricing.signalanomaly.SignalAnomalyAuditState
import com.udaan.pricing.signalanomaly.SignalAnomalyEntry
import kotlinx.coroutines.flow.toList

@Singleton
class SignalAnomaliesRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {
    private val cosmosDbDao by lazy {
        CosmosDbDao(
            configKey = CosmosDbConfig.DB_ACCOUNT_CONFIG,
            databaseName = CosmosDbConfig.PRICING_AUTOMATION_DB,
            containerName = CosmosDbConfig.SIGNAL_ANOMALIES_COLLECTION
        ) { builder ->
            builder.connectionSharingAcrossClientsEnabled(true)
        }
    }

    /**
     * This method is used to reduce the connection time to cosmos, used at the time of warmup
     */
    suspend fun initialise() {
        getSignalByReferenceId("")
    }

    suspend fun createOrUpdateSignal(signalAnomalyEntry: SignalAnomalyEntry): SignalAnomalyEntry? {
        return cosmosDbDao.createOrUpdateItem(signalAnomalyEntry.toDocument()).toSignalAnomalyEntry()
    }

    suspend fun getSignalsPendingForReview(): List<SignalAnomalyEntry> {
        return cosmosDbDao.queryItems(
            "get-signals-in-review",
            makeSqlQuerySpec(
                queryText = """
                select * from c where c.state = 'TO_REVIEW'
            """.trimIndent()
            )
        )
            .toList()
            .map { it.toSignalAnomalyEntry() }
    }

    suspend fun getSignalByIdAndPartitionKey(
        id: String,
        partitionKey: String
    ): SignalAnomalyEntry? {
        return cosmosDbDao.getItem(id, partitionKey)?.toSignalAnomalyEntry()
    }

    suspend fun getSignalByReferenceId(
        referenceId: String
    ): SignalAnomalyEntry? {
        return cosmosDbDao.queryItems(
            queryName = "get-signal-by-reference-id",
            querySpec = makeSqlQuerySpec(
                """
                select * from c where c.referenceId = @referenceId
            """.trimIndent(),
                "@referenceId" to referenceId)
        ).toList().map { it.toSignalAnomalyEntry() }.firstOrNull()
    }

    suspend fun archiveEntry(signalAnomalyEntry: SignalAnomalyEntry): SignalAnomalyEntry? {
        return cosmosDbDao.createOrUpdateItem(
            signalAnomalyEntry.copy(
                state = SignalAnomalyAuditState.ARCHIVED,
                updatedAt = System.currentTimeMillis()
            ).toDocument()
        ).toSignalAnomalyEntry()
    }

    private fun SignalAnomalyEntry.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toSignalAnomalyEntry() = objectMapper.convertValue(this, SignalAnomalyEntry::class.java)
}
