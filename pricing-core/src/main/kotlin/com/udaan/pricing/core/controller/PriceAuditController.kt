package com.udaan.pricing.core.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.dataplatform.client.DPEventsIngestionClient
import com.udaan.instrumentation.TelemetryScope.Companion.launch
import com.udaan.pricing.PricingAudit

class PriceAuditController @Inject constructor(
    private val objectMapper: ObjectMapper
) {
    companion object {
        private val log by logger()
    }

    private fun sendAuditEvent(pricingAudit: PricingAudit) {
        launch {
            try {
                DPEventsIngestionClient.logEvent(
                    teamName = "pricing",
                    eventName = "price_audit_v2",
                    refId1 = pricingAudit.refId,
                    eventData = objectMapper.convertValue(pricingAudit, Map::class.java) as Map<String, Any>
                )
            } catch (e: Exception) {
                log.error("Pushing to DP ingestion failed for audit with refID ${pricingAudit.refId}", e)
            }
        }
    }

    fun createPriceAudit(pricingAudit: PricingAudit, orderReadyForCheckOut: Boolean): String? {
        if (orderReadyForCheckOut.not()) {
            return null
        }
        sendAuditEvent(pricingAudit)
        return pricingAudit.id
    }
}
