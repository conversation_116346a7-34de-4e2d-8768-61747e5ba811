package com.udaan.pricing.core.helpers

import com.google.inject.Inject
import com.udaan.resources.CloudBlobClientProxy
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.apache.commons.io.FileUtils
import java.io.File
import java.io.InputStream

class BlobStorageHelper @Inject constructor(
    private val blobClientProxy: CloudBlobClientProxy
) {

    /**
     * Uploads a file to blob storage
     */
    suspend fun uploadFileToBlobStorage(
        containerReference: String,
        directoryReference: String,
        file: File
    ) {
        // Implementation to get file from blob storage
        val container = blobClientProxy.getContainerReference(
            containerReference
        )
        container.createIfNotExists()
        container.getDirectoryReference(
            directoryReference
        ).getAppendBlobReference(file.name).run {
            createOrReplace()
            withContext(Dispatchers.IO) { upload(file.inputStream(), file.length()) }
            file.delete()
        }
    }

    /**
     * Uploads an input stream to blob storage
     */
    suspend fun uploadInputStreamToBlobStorage(
        containerReference: String,
        directoryReference: String,
        inputStream: InputStream,
        fileName: String,
        fileLength: Long
    ) {
        // Implementation to upload input stream from blob storage
        val container = blobClientProxy.getContainerReference(
            containerReference
        )
        container.createIfNotExists()
        container.getDirectoryReference(
            directoryReference
        ).getAppendBlobReference(fileName).run {
            createOrReplace()
            withContext(Dispatchers.IO) { upload(inputStream, fileLength) }
        }
    }

    /**
     * Downloads a file from blob storage
     */
    suspend fun getFileFromBlobStorage(
        containerReference: String,
        directoryReference: String,
        fileName: String
    ): File {
        // Implementation to get file from blob storage
        val blobReference = blobClientProxy.getContainerReference(
            containerReference
        ).getDirectoryReference(
            directoryReference
        ).getAppendBlobReference(fileName)

        withContext(Dispatchers.IO) {
            FileUtils.copyInputStreamToFile(blobReference.openInputStream(), File(fileName))
        }
        return File(fileName)
    }


    fun getInputStreamFromBlobStorage(
        containerReference: String,
        directoryReference: String,
        fileName: String
    ): InputStream {
        // Implementation to get file from blob storage
        val blobReference = blobClientProxy.getContainerReference(
            containerReference
        ).getDirectoryReference(
            directoryReference
        ).getAppendBlobReference(fileName)
        return blobReference.openInputStream()
    }
}
