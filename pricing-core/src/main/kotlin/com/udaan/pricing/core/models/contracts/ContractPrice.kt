package com.udaan.pricing.core.models.contracts

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo


@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes(
    JsonSubTypes.Type(value = ContractLadderPrice::class, name = "CONTRACT_LADDER_PRICE"),
    JsonSubTypes.Type(value = ContractLadderMrpMarkdownBps::class, name = "CONTRACT_LADDER_MRP_MARKDOWN_BPS")
)
sealed class ContractPrice


data class ContractLadderPrice(
    val value: List<ContractLadder>
): ContractPrice() {
    init {
        value.validate()
    }
}

data class ContractLadderMrpMarkdownBps(
    val value: List<ContractLadder>
): ContractPrice() {
    init {
        value.validate()
    }
}

fun List<ContractLadder>.validate() {
    val sortedLadders = this.sortedBy(ContractLadder::minQuantity)
    var prevMaxQuantity = 0
    val areLaddersQtyValid = sortedLadders.map { ladder ->
        val isValid = (ladder.minQuantity <= ladder.maxQuantity) && (ladder.minQuantity - prevMaxQuantity == 1)
        prevMaxQuantity = ladder.maxQuantity
        isValid
    }.all { it }

    require(areLaddersQtyValid) { "Contract ladder Quantity limits are not valid." }
}


data class ContractLadder(
    val minQuantity: Int,
    val maxQuantity: Int,
    val ladderValue: Long
) {
    init {
        require(minQuantity > 0) { "minQuantity must be non-negative" }
        require(maxQuantity >= minQuantity) { "maxQuantity must be greater than or equal to minQuantity" }
    }
}