package com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg

import com.udaan.catalog.model.VerticalCategory
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.CompMatchUtils
import com.udaan.pricing.core.utils.LadderUtils
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.variable.VariableId

/**
 * Evaluator for hyperpure complete match.
 * Logic:
 * If hp and cogs is available, take max(hp-x, cogs) or else pass previous output.
 */
internal object FmcgHyperpureCompDirectMatchEvaluator : Evaluator {

    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        ValidationUtils.validateInputs(data.strategy, data.inputs)
        require(data.previousOutput != null) {
            "Previous output is mandatory for hyperpure complete match."
        }
        val outputMetadata = mutableMapOf<String, String>()

        // fetching inputs required
        // conversion rate
        val conversionRate = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.CONVERSION_RATE
        ) as BigDecimalValue

        // previous input in ladder format (if not already)
        val ladderInputWithoutTaxPaisaAssortment = LadderUtils.convertToLadderValue(
            inputValue = data.previousOutput.output,
            outputMetadata = outputMetadata
        )

        val hyperPureCompMarkDownBps = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.HYPERPURE_COMP_MARKDOWN_BPS
        ) as? BigDecimalValue

        // comp price without tax at assortment level, if no comp price we return previous output
        val compPriceWithoutTaxPaisaAssortment = CompMatchUtils.getCompLadderPriceWithoutTaxAtAssortment(
            compVariableId = VariableId.HYPERPURE_COMP_LADDER_PRICE_WT_PAISA_UNIT,
            conversionRate = conversionRate.value,
            inputs = data.inputs,
            verticalCategory = VerticalCategory.FMCG,
            outputMetadata = outputMetadata
        ) ?: return EvaluatorOutput(ladderInputWithoutTaxPaisaAssortment, outputMetadata)

        val compPriceWithoutTaxPaisaAssortmentWithMarkDown = CompMatchUtils.applyCompMarkdownBps(
            compMarkDownBps = hyperPureCompMarkDownBps?.value,
            compPriceWithoutTaxPaisaAssortment = compPriceWithoutTaxPaisaAssortment
        )

        // comp floor guardrail price
        val floorGuardrailWithoutTaxPaiseAtAssortment = CompMatchUtils.getFloorGuardrailForCompInPaiseAtAssortment(
            inputs = data.inputs,
            variableId = VariableId.FMCG_COGS_WOT_PAISA_SET,
            conversionRate = conversionRate.value
        )

        outputMetadata["HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT"] = compPriceWithoutTaxPaisaAssortment.toString()
        outputMetadata["HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT_WITH_MARKDOWN"] = compPriceWithoutTaxPaisaAssortmentWithMarkDown.toString()
        outputMetadata["COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT"] =
            floorGuardrailWithoutTaxPaiseAtAssortment.toString()

        /**
         * Note: Pick max(hp-x, cogs) for final ladders. if cogs not available return previous input.
         */
        val finalLadders = LadderUtils.deriveFinalLaddersWithDirectCompAndMerge(
            compFloorGuardrailPrice = floorGuardrailWithoutTaxPaiseAtAssortment,
            ladderInputWithoutTaxPaisaAssortment = ladderInputWithoutTaxPaisaAssortment,
            compPriceWithoutTaxPaisaAssortment = compPriceWithoutTaxPaisaAssortmentWithMarkDown,
            outputMetadata = outputMetadata,
            applyLadderCountCap = false
        )
        return EvaluatorOutput(finalLadders, outputMetadata)
    }
}