package com.udaan.pricing.core.constants

import com.udaan.pricing.UserCohort
import com.udaan.pricing.UserCohortType

internal object UserCohortConstants {
    val USER_COHORTS = listOf(
        UserCohort(
            id = "UCZC7MZ9E3JF818FTN8R8SX5TYTE",
            sellerOrgId = "ORG1ZDLV1EKVPQC3ZJCRWNFLP95L4",
            cohortType = UserCohortType.PRIVATE,
            orgId = "ORGEV6YLVQ13XQNWF7FGCCTJTRQCK",
            cohortName = "mdp_billing",
            createdAt = 1632475005927,
            updatedAt = 1632475005927
        ),
        UserCohort(
            id = "UCGTVWW8E2RXQY0F8MD69LBXNP68",
            sellerOrgId = "ORG1ZDLV1EKVPQC3ZJCRWNFLP95L4",
            cohortType = UserCohortType.PRIVATE,
            orgId = "ORG6NCTN726KS8HWZBDNYVGYS1G84",
            cohortName = "mdp_billing",
            createdAt = 1632475006115,
            updatedAt = 1632475006115
        )
    )
}