package com.udaan.pricing.core.dao.automation

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.core.constants.CosmosDbConfig
import com.udaan.pricing.portfolio.Portfolio
import com.udaan.pricing.portfolio.PortfolioState
import kotlinx.coroutines.flow.toList

@Singleton
class PortfolioRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {

    private val portfolioCosmosDao by lazy {
        CosmosDbDao(
            configKey = "pricing",
            databaseName = CosmosDbConfig.PRICING_AUTOMATION_DB,
            containerName = CosmosDbConfig.PORTFOLIO_CONTAINER
        ) { builder ->
            builder.connectionSharingAcrossClientsEnabled(true)
        }
    }

    /**
     * Dummy function solely used for client initialisation while starting the service
     */
    suspend fun initialise() {
        portfolioCosmosDao.findItem("ID1")
    }

    suspend fun createOrUpdatePortfolio(portfolio: Portfolio): Portfolio {
        return portfolioCosmosDao.createOrUpdateItem(portfolio.toDocument()).toPortfolio()
    }

    suspend fun getActivePortfolioByName(portfolioName: String): Portfolio? {
        return portfolioCosmosDao.queryItems(
            queryName = "get-portfolio-by-name",
            querySpec = makeSqlQuerySpec(
                """
                    select * from c where c.name = @name and
                    c.state = "ACTIVE"
                """.trimIndent(),
                "@name" to portfolioName,
                "@state" to PortfolioState.ACTIVE
            )
        ).toList().map { it.toPortfolio() }.firstOrNull()
    }

    suspend fun getPortfolioById(portfolioId: String): Portfolio? {
        return portfolioCosmosDao.getItem(portfolioId, portfolioId)?.toPortfolio()
    }

    suspend fun getAllActivePortfolios(): List<Portfolio> {
        return portfolioCosmosDao.queryItems(
            queryName = "get-portfolios",
            querySpec = makeSqlQuerySpec(
                """
                    select * from c where c.state = @state
                """.trimIndent(),
                "@state" to PortfolioState.ACTIVE
            )
        ).toList().map { it.toPortfolio() }
    }

    private fun ObjectNode.toPortfolio() = objectMapper.convertValue(this, Portfolio::class.java)
    private fun Portfolio.toDocument(): ObjectNode = objectMapper.convertValue(this)
}