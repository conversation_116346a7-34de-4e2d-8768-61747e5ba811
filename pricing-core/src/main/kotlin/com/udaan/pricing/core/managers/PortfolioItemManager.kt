package com.udaan.pricing.core.managers

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.inject.Inject
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.core.dao.automation.PortfolioItemRepository
import com.udaan.pricing.core.helpers.CatalogEntityValidator
import com.udaan.pricing.core.helpers.ConfigHelper
import com.udaan.pricing.core.helpers.DataPlatformHelper
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.portfolioitem.CatalogEntityLevel
import com.udaan.pricing.portfolioitem.CreatePortfolioItemRequest
import com.udaan.pricing.portfolioitem.DeletePortfolioItemRequest
import com.udaan.pricing.portfolioitem.PortfolioItem
import com.udaan.pricing.portfolioitem.PortfolioItemState
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await
import javax.ws.rs.NotFoundException

@Singleton
class PortfolioItemManager @Inject constructor(
    private val catalogEntityValidator: CatalogEntityValidator,
    private val portfolioItemRepository: PortfolioItemRepository,
    private val dataPlatformHelper: DataPlatformHelper,
    private val portfolioManager: PortfolioManager,
    private val objectMapper: ObjectMapper,
    @Named(NamedConstants.Caches.PORTFOLIO_ITEM_CACHE) private val portfolioItemCache: RedisCache2<PortfolioItem?>,
    private val configSvcHelper: ConfigHelper
) {
    companion object {
        private val logger by logger()
    }

    suspend fun getPortfolioItemToPortfolioTaggingCount() =
        portfolioItemRepository.getCountOfPortfolioItemsForPortfolios()

    suspend fun createPortfolioItemTagging(
        createPortfolioItemRequest: CreatePortfolioItemRequest
    ): PortfolioItem {
        val portfolioItemToCreate = createPortfolioItemRequest.convert()
        // Validate portfolio
        val portfolio = portfolioManager.getPortfolioById(portfolioItemToCreate.portfolioId)

        // Validate catalog entity
        catalogEntityValidator.validateCatalogEntity(
            catalogEntity = portfolioItemToCreate.catalogEntity,
            catalogEntityLevel = portfolioItemToCreate.entityLevel,
            portfolioCategory = portfolio.category
        )

        logger.info("Creating portfolio item {}", portfolioItemToCreate)

        // save the item
        return createOrUpdatePortfolioItem(portfolioItemToCreate).also {
            portfolioItemCache.invalidate(
                k = getPortfolioItemCacheKey(
                    catalogEntity = it.catalogEntity,
                    locationValue = it.location.locationValue
                )
            ).await()
        }
    }

    suspend fun deletePortfolioItemTagging(
        deletePortfolioItemRequest: DeletePortfolioItemRequest
    ): PortfolioItem? {
        val portfolioItemId = getPortfolioItemId(
            catalogEntity = deletePortfolioItemRequest.catalogEntity,
            locationValue = deletePortfolioItemRequest.location.locationValue
        )

        val existingPortfolioItem = portfolioItemRepository.getByIdAndPartitionKey(
            id = portfolioItemId,
            partitionKey = deletePortfolioItemRequest.catalogEntity
        )

        return existingPortfolioItem?.let { portfolioItem ->
            val updatedPortfolioItem = portfolioItem.copy(
                state = PortfolioItemState.DELETED,
                updatedBy = deletePortfolioItemRequest.deletedBy,
                updatedAt = deletePortfolioItemRequest.deletedAt,
                metadata = portfolioItem.metadata.plus("DELETION_REASON" to deletePortfolioItemRequest.deletionReason)
            )
            portfolioItemRepository.update(updatedPortfolioItem).also {
                portfolioItemCache.invalidate(
                    k = getPortfolioItemCacheKey(
                        catalogEntity = it.catalogEntity,
                        locationValue = it.location.locationValue
                    )
                ).await()

                dataPlatformHelper.trackEvent(
                    eventData = portfolioItem.toMap(),
                    eventName = DataPlatformHelper.TrackEventName.PORTFOLIO_ITEM_AUDIT,
                    referenceId1 = portfolioItem.referenceId,
                    referenceId2 = portfolioItem.id
                )
            }
        }
    }

    private suspend fun createOrUpdatePortfolioItem(portfolioItem: PortfolioItem): PortfolioItem {
        return portfolioItemRepository.createOrUpdate(portfolioItem).also {
            dataPlatformHelper.trackEvent(
                eventData = portfolioItem.toMap(),
                eventName = DataPlatformHelper.TrackEventName.PORTFOLIO_ITEM_AUDIT,
                referenceId1 = portfolioItem.referenceId,
                referenceId2 = portfolioItem.id
            )
        }
    }

    suspend fun getPortfolioTaggingForEntityAndLocation(
        catalogEntity: String,
        locationValue: String
    ): PortfolioItem? {
        return portfolioItemCache.get(
            k = getPortfolioItemCacheKey(catalogEntity, locationValue)
        ) {
            TelemetryScope.future {
                val portfolioItemId = getPortfolioItemId(catalogEntity, locationValue)
                val portfolioItem = portfolioItemRepository.getByIdAndPartitionKey(
                    id = portfolioItemId,
                    partitionKey = catalogEntity
                )

                portfolioItem?.let {
                    if (it.state == PortfolioItemState.ACTIVE) {
                        it
                    } else {
                        null
                    }
                }
            }
        }.await()
    }

    /**
     * This fun checks for any active ssc portfolio entity for GID/WHID.
     * If portfolio entity is not present or not tagged to any ssc portfolio,
     * it would be tagged to default ssc portfolio.
     */
    suspend fun assignDefaultSscPortfolioIfNoSscTagging(
        groupId: String,
        warehouseId: String,
        createdBy: String
    ) {
        val sscPortfolioItem = try {
            getPortfolioTaggingForEntityAndLocation(
                catalogEntity = groupId,
                locationValue = warehouseId
            )
        } catch (e: NotFoundException) {
            logger.error("No portfolio item for $groupId, $warehouseId")
            null
        }
        val isCurrentPortfolioSsc = try {
            portfolioManager.isSscPortfolio(sscPortfolioItem?.portfolioId)
        } catch (ex: Exception) {
            logger.error(
                "Exception while checking portfolio is SSC or not for portfolioId ${sscPortfolioItem?.portfolioId}, " +
                        "proceeding with default tagging. Ex: ", ex
            )
            false
        }

        if (isCurrentPortfolioSsc.not()) {
            val defaultSscPortfolioId = configSvcHelper.getSscDefaultPortfolioId()
                ?: throw IllegalStateException("Default SSC portfolioId found null, cannot do default tagging")
            createPortfolioItemTagging(
                CreatePortfolioItemRequest(
                    catalogEntity = groupId,
                    createdBy = createdBy,
                    entityLevel = CatalogEntityLevel.PRODUCT_GROUP_ID,
                    jobIdReference = "",
                    location = Location(LocationType.WAREHOUSE, warehouseId),
                    portfolioId = defaultSscPortfolioId
                )
            )
            logger.info("Updated SSC tagging for $groupId, $warehouseId to $defaultSscPortfolioId")
        } else {
            logger.info("SSC tagging is present $groupId, $warehouseId to ${sscPortfolioItem!!.portfolioId}")
        }
    }

    private fun getPortfolioItemCacheKey(
        catalogEntity: String,
        locationValue: String
    ): String {
        return "$catalogEntity:$locationValue".uppercase()
    }

    private fun getPortfolioItemId(
        catalogEntity: String,
        locationValue: String
    ): String {
        return ("$catalogEntity:$locationValue").uppercase()
    }

    private fun <T: Any>T.toMap(): Map<String, Any> {
        return objectMapper.convertValue(this)
    }
}
