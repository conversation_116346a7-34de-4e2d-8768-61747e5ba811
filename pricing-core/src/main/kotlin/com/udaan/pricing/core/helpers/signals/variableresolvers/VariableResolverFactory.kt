package com.udaan.pricing.core.helpers.signals.variableresolvers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.pricing.variable.VariableId

@Singleton
class VariableResolverFactory @Inject constructor(
    private val defaultResolver: DefaultResolver,
    private val metadataResolver: MetadataResolver,
    private val fmcgLppResolver: FmcgLppResolver,
    private val compMetadataResolver: CompMetadataResolver,
    private val staplesCogsProxiesResolver: StaplesCogsProxiesResolver,
    private val tradingPriceResolver: TradingPriceResolver,
    private val territoryEnabledInputsResolver: TerritoryEnabledInputsResolver,
    private val territoryEnabledCompMetadataResolver: TerritoryEnabledCompMetadataResolver,
    private val vslChannelMetadataResolver: VslChannelMetadataResolver
) {
    fun getVariableResolver(variableId: VariableId): VariableResolver {
        return when(variableId) {
            VariableId.MRP_WT_PAISA_SET,
            VariableId.GST_BPS,
            VariableId.CESS_BPS,
            VariableId.CONVERSION_RATE,
            VariableId.PACKAGING_UNIT_TYPE,
            VariableId.QUANTITY_PER_UNIT,
            VariableId.QUANTITY_TYPE -> metadataResolver
            VariableId.FMCG_LPP_WOT_RUPEES_UNIT -> fmcgLppResolver
            VariableId.STAPLES_LIP_WOT_PAISA_UNIT,
            VariableId.STAPLES_LPP_WOT_RUPEES_UNIT -> staplesCogsProxiesResolver
            VariableId.TRADING_PRICE_WOT_PAISA_UNIT -> tradingPriceResolver
            VariableId.METRO_COMP_MRP_WT_PAISA_UNIT,
            VariableId.NINJACART_QUANTITY_PER_UNIT,
            VariableId.NINJACART_QUANTITY_TYPE,
            VariableId.NINJACART_WEIGHT_PER_PIECE_GRAMS,
            VariableId.HYPERPURE_QUANTITY_TYPE,
            VariableId.HYPERPURE_QUANTITY_PER_UNIT,
            VariableId.HYPERPURE_WEIGHT_PER_PIECE_GRAMS -> compMetadataResolver
            VariableId.JUMBOTAIL_COMP_MRP_WT_PAISA_UNIT -> territoryEnabledCompMetadataResolver
            VariableId.JUMBOTAIL_COMP_LADDER_PRICE_WT_PAISA_UNIT -> territoryEnabledInputsResolver
            VariableId.VSL_SELECTED_CHANNEL -> vslChannelMetadataResolver
            else -> defaultResolver
        }
    }
}
