package com.udaan.pricing.core.helpers.signals.variableresolvers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.pricing.core.managers.signals.SignalReadManager
import com.udaan.pricing.core.models.signals.CatalogEntityContext
import com.udaan.pricing.core.models.signals.LocationContext
import com.udaan.pricing.core.utils.signals.SignalUtil
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.variable.Variable
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.variable.requestreponse.ResolvedValue
import com.udaan.pricing.variable.requestreponse.ResolverLogic

@Singleton
class TradingPriceResolver @Inject constructor(
    private val signalReadManager: SignalReadManager
): VariableResolver {

    // @todo - add right resolver logic value (signal vs fallback)
    // right now the logic is written in a way that MFC fallback and anchor actual
    // signal are picked in same manner, hence distinction is not possible
    override suspend fun resolve(
        catalogEntityContext: CatalogEntityContext,
        locationContext: LocationContext,
        variable: Variable
    ): Pair<VariableId, ResolvedValue> {
        var selectedSignal: Signal? = null
        var resolverLogic: ResolverLogic? = null
        var exceptionMessage: String? = null

        try{
            val productGroupId = catalogEntityContext.productGroupId
                ?: throw IllegalStateException("No mapped GID found")

            // fetch most recent MFC signal
            val mfcLatestSignal = signalReadManager.getAllSignalsForLocations(
                catalogEntity = productGroupId,
                variableId = variable.id,
                locationValues = locationContext.mfcWarehouses
            ).maxByOrNull { it.createdAt }

            // if MFC signal is null, fetch most recent Anchor signal
            selectedSignal = mfcLatestSignal
                ?: signalReadManager.getAllSignalsForLocations(
                    catalogEntity = productGroupId,
                    variableId = variable.id,
                    locationValues = locationContext.anchorWarehouses
                ).maxByOrNull { it.createdAt }

        } catch (ex: Exception){
            exceptionMessage = ex.message
        }

        val finalValue = if (selectedSignal != null) {
            selectedSignal.signalData
        } else {
            resolverLogic = if (exceptionMessage != null) {
                ResolverLogic.DEFAULT_VALUE_AS_EXCEPTION_OCCURRED
            } else {
                ResolverLogic.DEFAULT_VALUE_AS_SIGNAL_ABSENT
            }
            variable.defaultValue
        }

        // adding whId to metadata - this is important to know which WH signal is finally served post fallback logic
        val finalMetadata = selectedSignal?.let {
            SignalUtil.addWarehouseIdToGenericMetadata(it.metadata, it.location.locationValue)
        }

        val resolvedValue = ResolvedValue(
            success = (exceptionMessage == null && finalValue != null),
            value = finalValue,
            referenceSignalId = selectedSignal?.referenceId,
            resolverLogic = resolverLogic,
            exception = exceptionMessage,
            metadata = finalMetadata,
            lastRefreshedAt = selectedSignal?.updatedAt
        )

        return Pair(variable.id, resolvedValue)
    }
}
