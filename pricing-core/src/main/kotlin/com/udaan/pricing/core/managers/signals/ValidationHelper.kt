package com.udaan.pricing.core.managers.signals

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.commons.StringValue
import com.udaan.pricing.signalanomaly.Guardrail
import com.udaan.pricing.signals.Signal
import java.math.BigDecimal
import java.math.RoundingMode

@Singleton
// todo: re-look at the class name, if it should be ValidationManager or something else
class ValidationHelper @Inject constructor() {

    companion object {
        private val LOG by logger()
    }

    fun getValidationFlag(newSignal: Signal, existingSignal: Signal?, guardrail: Guardrail): ValidationFlag {
        val existingValue = existingSignal?.signalData ?: return ValidationFlag.Green
        val newValue = newSignal.signalData

        // Validate types match
        if (existingValue::class != newValue::class) {
            throw SignalValidationException("Type mismatch: Expected ${existingValue::class.java.simpleName} but got ${newValue::class.java.simpleName}")
        }

        return when (newValue) {
            is BigDecimalValue -> {
                getValidationFlag(
                    productGroupId = newSignal.catalogEntity,
                    locationValue = newSignal.location.locationValue,
                    newValue = newValue,
                    existingValue = existingValue as? BigDecimalValue,
                    guardrail = guardrail
                )
            }
            is LadderValue, is StringValue -> {
                LOG.info("Ignoring validation for ${newValue::class.java.simpleName}")
                ValidationFlag.Green
            }
        }
    }

    private fun getRelativePercentDiff(existingValue: BigDecimalValue, newValue: BigDecimalValue): BigDecimal {
        val relativeDiff = if (existingValue.value != BigDecimal(0.0)) {
            (newValue.value - existingValue.value)
                .divide(existingValue.value, 2, RoundingMode.HALF_EVEN)
        } else {
            BigDecimal(1.0)
        }
        return relativeDiff.abs() * BigDecimal(100.0)
    }

    private fun getAbsoluteDiff(existingValue: BigDecimalValue, newValue: BigDecimalValue): BigDecimal {
        return  (newValue.value - existingValue.value).abs()
    }

    fun getValidationFlag(
        productGroupId: String,
        locationValue: String,
        newValue: BigDecimalValue,
        existingValue: BigDecimalValue? = null,
        guardrail: Guardrail
    ): ValidationFlag {
        if (existingValue == null) {
            return ValidationFlag.Green
        }

        val diff = if (guardrail.doAbsoluteDiff)
            getAbsoluteDiff(existingValue, newValue)
        else
            getRelativePercentDiff(existingValue, newValue)

        return when {
            diff < guardrail.low -> {
                LOG.info("${productGroupId}-${locationValue} Below yellow-guardrail $diff from ${existingValue.value}")
                ValidationFlag.Green
            }
            diff >= guardrail.high -> ValidationFlag.Red
            else -> ValidationFlag.Yellow
        }
    }
}

class SignalValidationException(message: String) : IllegalArgumentException(message)

enum class ValidationFlag { Green, Yellow, Red }
