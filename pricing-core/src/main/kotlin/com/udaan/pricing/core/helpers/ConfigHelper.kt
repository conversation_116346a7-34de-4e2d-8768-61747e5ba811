package com.udaan.pricing.core.helpers

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.core.svcinterfaces.ConfigServiceInterface
import com.udaan.pricing.core.utils.signals.BigDecimalUtil.toBigDecimalWithScale
import com.udaan.pricing.signalanomaly.Guardrail
import java.math.BigDecimal
import java.util.concurrent.TimeUnit

/**
 * ConfigServiceInterface fetches configuration values from the pricing-service related keys.
 * Make sure create a new ConfigServiceInterface class if we need keys from a different service.
 */
@Singleton
class ConfigHelper @Inject constructor(
    private val configSvcInterface: ConfigServiceInterface,
    private val objectMapper: ObjectMapper
) {

    private val log by logger()

    companion object {
        // buyer cohort related
        private const val BUYER_TAGS_PRIORITY = "BUYER_TAGS_PRIORITY"
        private const val HORECA_AB_BUYERS = "HORECA_AB_BUYERS"

        // contract related
        private const val SKIP_EXPIRY_LIMITS_CHECK = "SKIP_EXPIRY_LIMITS_CHECK"
        private const val CONTRACT_QUOTE_PRICE_VALIDITY_IN_DAYS = "CONTRACT_QUOTE_PRICE_VALIDITY_IN_DAYS"
        private const val CONTRACT_QUOTE_PRICE_VALIDITY_DEFAULT_KEY = "ALL"
        private const val CONTRACT_EXPIRY_LIMITS = "CONTRACT_EXPIRY_LIMITS"
        private const val CONTRACT_LOCK_IN_LIMITS = "CONTRACT_LOCK_IN_LIMITS"
        private const val CONTRACT_LIMITS_DEFAULT_VERTICAL_KEY = "ALL"
        private const val CONTRACT_PRICE_UPPER_THRESHOLD_IN_BPS = "CONTRACT_PRICE_UPPER_THRESHOLD_IN_BPS"

        // territory enabled inputs related
        private const val TERRITORY_ENABLED_INPUTS = "TERRITORY_ENABLED_INPUTS"

        // sugar GIDs related
        private const val PROBE_EXCLUDED_SUGAR_GIDS_MAP_NAME = "PROBE_EXCLUDED_GIDS_TO_MIGRATE_VSL"
        private const val SUGAR_KEY = "SUGAR"

        // default portfolio ID related
        private const val DEFAULT_PORTFOLIO_ID_MAP_NAME = "DEFAULT_PORTFOLIO_ID"
        private const val STAPLES_TRADING_KEY = "STAPLES_TRADING"
        private const val STAPLES_SSC_KEY = "STAPLES_SSC"
        private const val TRADING_PRICE_EXPIRY_DAYS = "TRADING_PRICE_EXPIRY_DAYS"
        private const val DEFAULT_TRADING_PRICE_EXPIRY_DAYS = 0

        // guardrail related
        private const val BENCHMARK_GID_FOR_GUARDRAIL = "BENCHMARK_GID_FOR_GUARDRAIL"

        // signals guardrails related
        private const val INPUT_GUARDRAILS_KEY = "INPUT_GUARDRAILS_YELLOW"
        private const val ABSOLUTE_DIFF_GUARDRAILS = "ABSOLUTE_DIFF_GUARDRAILS"
    }

    suspend fun getAllBuyerTagsPriority(): Map<String, String>? {
        return configSvcInterface.hGetAllAsync(BUYER_TAGS_PRIORITY)
    }

    suspend fun isBuyerHorecaAb(buyerOrgId: String): Boolean {
        return configSvcInterface.sIsMemberAsync(HORECA_AB_BUYERS, buyerOrgId.uppercase())
    }

    suspend fun isSkipVerticalExpiryLimitsEnabled(catalogId: String): Boolean {
        return configSvcInterface.sIsMemberAsync(SKIP_EXPIRY_LIMITS_CHECK, catalogId.uppercase())
    }

    suspend fun getContractQuotePriceValidityInDays(verticalId: String?): Int {
        val quotePriceValidityMap = configSvcInterface.hGetAllAsync<String>(CONTRACT_QUOTE_PRICE_VALIDITY_IN_DAYS)
            ?: emptyMap()

        return (verticalId?.let { quotePriceValidityMap[verticalId.lowercase()] }
            ?: quotePriceValidityMap[CONTRACT_QUOTE_PRICE_VALIDITY_DEFAULT_KEY.lowercase()])?.toInt()
            ?: error("No contract quote price limits are mentioned.")
    }

    suspend fun isContractExpiryLimitValid(
        startTime: Long,
        endTime: Long,
        verticalId: String?
    ): Boolean {
        val expiryLimitsMap = configSvcInterface.hGetAllAsync<String>(CONTRACT_EXPIRY_LIMITS)
            ?: emptyMap()
        val maxAllowedContractDays = (verticalId?.let { expiryLimitsMap[verticalId.lowercase()] }
            ?: expiryLimitsMap[CONTRACT_LIMITS_DEFAULT_VERTICAL_KEY.lowercase()])?.toInt()
            ?: error("No contract expiry limits mentioned is defined in config service")
        val contractDurationInDays = (endTime - startTime) / TimeUnit.DAYS.toMillis(1)
        return contractDurationInDays in 0..maxAllowedContractDays
    }

    suspend fun isContractLockInLimitValid(
        startTime: Long,
        lockInTime: Long,
        verticalId: String?
    ): Boolean {
        val lockInLimitsMap = configSvcInterface.hGetAllAsync<String>(CONTRACT_LOCK_IN_LIMITS)
            ?: emptyMap()
        val maxAllowedContractLockInDays = (verticalId?.let { lockInLimitsMap[verticalId.lowercase()] }
            ?: lockInLimitsMap[CONTRACT_LIMITS_DEFAULT_VERTICAL_KEY.lowercase()])?.toInt()
            ?: error("No contract expiry limits mentioned is defined in config service")
        val contractLockInDurationDays = (lockInTime - startTime) / TimeUnit.DAYS.toMillis(1)
        return contractLockInDurationDays in 0..maxAllowedContractLockInDays
    }

    /**
     * This would help in validating an uploaded contract price against the upper threshold.
     */
    suspend fun getContractPriceUpperThresholdInBps(): Int? {
        return configSvcInterface.getIntAsync(CONTRACT_PRICE_UPPER_THRESHOLD_IN_BPS)
    }

    suspend fun getAllTerritoryEnabledInputs(): List<String> {
        return configSvcInterface.sMembersAsync<String>(TERRITORY_ENABLED_INPUTS)?.toList()
            ?: emptyList()
    }

    suspend fun getSugarGidsExcludedInAutoTaggingProbe(): List<String> {
        return configSvcInterface.hGetAllAsync<String>(
            hashMapName = PROBE_EXCLUDED_SUGAR_GIDS_MAP_NAME
        )?.entries?.associate {
            it.key to objectMapper.readValue<List<String>>(it.value)
        }?.get(SUGAR_KEY) ?: emptyList()
    }

    suspend fun getTradingDefaultPortfolioId(): String? {
        return configSvcInterface.hGetValueAsync<String>(DEFAULT_PORTFOLIO_ID_MAP_NAME, STAPLES_TRADING_KEY)
    }

    suspend fun getSscDefaultPortfolioId(): String? {
        return configSvcInterface.hGetValueAsync<String>(DEFAULT_PORTFOLIO_ID_MAP_NAME, STAPLES_SSC_KEY)
    }

    /**
     * Returns the number of days for which the trading price is valid.
     * Example:
     * if its set 1, on tuesday run we consider price is valid if it is updated after monday 12 AM.
     * if its set 0, on monday run we consider price is valid if it is updated after monday 12 AM.
     */
    suspend fun getTradingPriceExpiryInDays(): Int {
        return configSvcInterface.hGetValueAsync<String?>(DEFAULT_PORTFOLIO_ID_MAP_NAME,
            TRADING_PRICE_EXPIRY_DAYS)?.toInt() ?: DEFAULT_TRADING_PRICE_EXPIRY_DAYS
    }

    suspend fun getMappedBenchmarkGidForGuardrail(productGroupId: String, sellerOrgCity: String): String? {
        return configSvcInterface.hGetValueAsync<String>(
            hashMapName = BENCHMARK_GID_FOR_GUARDRAIL,
            key = "$productGroupId:$sellerOrgCity".uppercase()
        )
    }

    suspend fun getGuardrailForVariable(inputType: String): Guardrail? {
        val guardrailString = configSvcInterface.hGetValueAsync<String>(
            hashMapName = INPUT_GUARDRAILS_KEY,
            key = inputType
        )
        if (guardrailString == null) {
            log.info("Guardrail for input $inputType was not found")
            return null
        }
        return guardrailString.toGuardrailPair()?.let {
            Guardrail(
                low = it.first,
                high = it.second,
                doAbsoluteDiff = isAbsoluteDiffEnabled(inputType)
            )
        }
    }

    suspend fun getAllYellowGuardrails(): Map<String, Guardrail> {
        val variable2GuardRail = configSvcInterface.hGetAllAsync<String>(
            hashMapName = INPUT_GUARDRAILS_KEY
        ).orEmpty()
        return variable2GuardRail.map {
            val (yellowLow, yellowHigh) = it.value.toGuardrailPair() ?: (null to null)
            if (yellowLow == null || yellowHigh == null) {
                return@map null
            } else {
                it.key to Guardrail(
                    low = yellowLow,
                    high = yellowHigh,
                    doAbsoluteDiff = isAbsoluteDiffEnabled(it.key)
                )
            }
        }.filterNotNull().toMap()
    }

    // @todo - should be moved to some Util
    private fun String.toGuardrailPair(): Pair<BigDecimal, BigDecimal>? {
        return try {
            val (low, high) = this.split(",")
            Pair(low.toBigDecimalWithScale(), high.toBigDecimalWithScale())
        } catch (e: Exception) {
            log.error("${e::class.java.simpleName} while trying to read guardrail from $this")
            null
        }
    }

    suspend fun isAbsoluteDiffEnabled(inputType: String): Boolean {
        return configSvcInterface.sIsMemberAsync(
            hashSetName = ABSOLUTE_DIFF_GUARDRAILS,
            value = inputType
        )
    }
}