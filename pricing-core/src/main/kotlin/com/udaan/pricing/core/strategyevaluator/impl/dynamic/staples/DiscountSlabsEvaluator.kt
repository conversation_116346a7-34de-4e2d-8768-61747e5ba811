package com.udaan.pricing.core.strategyevaluator.impl.dynamic.staples

import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import com.udaan.pricing.core.utils.LadderUtils
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.variable.VariableId
import java.math.BigDecimal

internal object DiscountSlabsEvaluator : Evaluator {

    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        require(data.previousOutput != null) {
            "Previous output is mandatory for discount slabs evaluator"
        }

        require(data.previousOutput.output is BigDecimalValue) {
            "Previous evaluator output for discount slabs should be of BigDecimal type"
        }

        ValidationUtils.validateInputs(data.strategy, data.inputs)

        val outputMetadata = mutableMapOf<String, String>()

        val previousOutput = data.previousOutput.output

        val adjustmentLadder = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.DISCOUNT_SLABS
        ) as? LadderValue
        val conversionRate = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.CONVERSION_RATE
        ) as BigDecimalValue

        val ladderOutput = if (adjustmentLadder == null) {
            LadderUtils.applyDefaultLadder(previousOutput.value, outputMetadata)
        } else {
            LadderUtils.applyDiscountLadders(previousOutput.value, adjustmentLadder.value, outputMetadata)
        }

        val finalLadderOutput = applyConversionRate(conversionRate.value, ladderOutput.value)
        return EvaluatorOutput(LadderValue(finalLadderOutput), outputMetadata)
    }

    private fun applyConversionRate(
        conversionRate: BigDecimal,
        ladders: List<Ladder>
    ): List<Ladder> {
        return ladders.map {
            Ladder(
                minQuantity = it.minQuantity,
                maxQuantity = it.maxQuantity,
                ladderValue = it.ladderValue.multiplyWithScale(conversionRate)
            )
        }
    }
}
