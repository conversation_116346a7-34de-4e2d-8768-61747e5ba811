package com.udaan.pricing.core.controller.automation

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.firstparty.trading.CreateTradingPriceRequestV3
import com.udaan.firstparty.trading.TradingPriceClient
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.managers.PortfolioPlanManager
import com.udaan.pricing.core.managers.StrategyManager
import com.udaan.pricing.core.helpers.PricingSignalsHelper
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorConfigInput
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.strategyevaluator.impl.EvaluatorFactory
import com.udaan.pricing.portfolioplan.PortfolioPlan
import com.udaan.pricing.strategy.Strategy
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.variable.requestreponse.ResolvedValue

@Singleton
class TradingRepriceController @Inject constructor(
    private val pricingSignalsHelper: PricingSignalsHelper,
    private val portfolioPlanManager: PortfolioPlanManager,
    private val strategyManager: StrategyManager,
    private val tradingPriceClient: TradingPriceClient,
    private val tradingItemCommonController: TradingItemCommonController
) {

    private val log by logger()

    suspend fun repriceItems(
        locationValue: String,
        locationType: LocationType,
        catalogEntity: String, referenceId: String
    ) {
        try {
            log.info("Reprice portfolio items for {}, {}, {}", locationValue, catalogEntity, referenceId)
            val tradingPortfolioItem = tradingItemCommonController.getTradingPortfolioItemOrNull(
                catalogEntity = catalogEntity,
                locationValue = locationValue
            )
            if (tradingPortfolioItem == null) {
                log.info("No portfolio item to reprice for $catalogEntity, $locationValue")
                return
            }
            val portfolioPlans = portfolioPlanManager.getAllPortfolioPlans(tradingPortfolioItem.portfolioId)
            val basePortfolioPlan = portfolioPlans.firstOrNull { it.buyerCohort == null }
            if (basePortfolioPlan == null) {
                log.info("No portfolio plan to reprice for $catalogEntity, $locationValue")
                return
            }
            val variableContexts = getEvaluatorRequestContext(
                productGroupId = catalogEntity,
                location = Location(
                    locationValue = locationValue,
                    locationType = locationType
                ),
                portfolioPlan = basePortfolioPlan
            )
            val evaluatorOutput = EvaluatorFactory.evaluate(variableContexts)
            log.info(
                "Evaluator output for {}, {}, {} is {}",
                catalogEntity, locationType, locationValue, evaluatorOutput
            )
            val unitPrice = (evaluatorOutput?.output as? BigDecimalValue)?.value?.toLong()
            unitPrice?.let {
                tradingPriceClient.addPriceV3(
                    CreateTradingPriceRequestV3(
                        groupId = catalogEntity,
                        warehouseId = locationValue,
                        unitPriceInPaisa = unitPrice,
                        updatedBy = referenceId,
                        strategyInputs = evaluatorOutput.metadata
                    )
                ).executeAwait()
            } ?: log.info("Not able to calculate unit price $catalogEntity, $locationValue")
            log.info(
                "Updated trading price for {}, {}, {} is {}",
                catalogEntity, locationType, locationValue, unitPrice
            )
        } catch (e: Exception) {
            log.error("Error while repricing for $catalogEntity, $locationValue with error ${e.cause}, ${e.message}")
            throw e
        }
    }


    private suspend fun getEvaluatorRequestContext(
        productGroupId: String,
        location: Location,
        portfolioPlan: PortfolioPlan
    ): List<EvaluatorRequestContext> {
        val strategyToConfigInputs = fetchEvaluatorConfigInputs(
            productGroupId = productGroupId,
            location = location,
            applicableCohortPlan = portfolioPlan
        )
        return strategyToConfigInputs.keys.map { strategy ->
            EvaluatorRequestContext(
                strategy = strategy,
                inputs = strategyToConfigInputs[strategy] ?: emptyList(),
                previousOutput = null
            )
        }
    }

    private suspend fun fetchEvaluatorConfigInputs(
        productGroupId: String,
        location: Location,
        applicableCohortPlan: PortfolioPlan
    ): Map<Strategy, List<EvaluatorConfigInput>> {
        val usedStrategies = applicableCohortPlan.strategies.map { strategyId ->
            strategyManager.getStrategyById(strategyId)
        }

        val variablesToFetch = usedStrategies.map { it.usedVariables }.flatten().distinct()
        val allSignalInputs = fetchSignals(
            productGroupId = productGroupId,
            location = location,
            variables = variablesToFetch
        )
        return usedStrategies.associateWith { strategy ->
            strategy.usedVariables.map { variableId ->
                val signalValue = allSignalInputs.filter { it.key == variableId }.entries.firstOrNull()?.value
                EvaluatorConfigInput(
                    variableId = variableId,
                    value = signalValue?.value,
                    referenceId = signalValue?.referenceSignalId
                )
            }
        }
    }

    private suspend fun fetchSignals(
        productGroupId: String,
        location: Location,
        variables: List<VariableId>
    ): Map<VariableId, ResolvedValue> {
        return pricingSignalsHelper.resolveValuesForProductGroup(
            locationType = location.locationType,
            locationValue = location.locationValue,
            variableIds = variables.toSet(),
            groupId = productGroupId,
            inputTerritoryMap = null    // trading price inputs, no territory concept here
        ).resolvedValuesMap
    }
}