package com.udaan.pricing.core.helpers.rider.impl.bot

import com.google.common.util.concurrent.ThreadFactoryBuilder
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.Telemetry
import java.time.Duration
import java.time.ZonedDateTime
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicReference
import kotlin.properties.ReadOnlyProperty
import kotlin.reflect.KProperty

fun <T> periodically(refreshInterval: Duration, clazz: Class<T>, initializer: (prev: T?) -> T
): ReadOnlyProperty<Any?, CompletableFuture<T>> {
    return PeriodicallyUpdatingValueDelegate(refreshInterval, clazz, initializer)
}

inline fun <reified T> periodically(refreshInterval: Duration, noinline initializer: (prev: T?) -> T
): ReadOnlyProperty<Any?, CompletableFuture<T>> {
    return periodically(refreshInterval, T::class.java, initializer)
}

/**
 * Delegator that helps refresh a value periodically
 * In case of an exception thrown from the initializer, the value is retained to the last know value
 *
 * NOTE:
 * a) while I am using CompletableFuture, the <code>initializers</code> are expected to execute in-sync
 * because the values will be loaded in a separate thread
 * b) the first value is always loaded in-sync; if we do not do this, then whenever the delegated value is invoked, it will cause load errors
 * Moreover, allowing initiazers to be async will complicate the design and hence avoided for the current case
 *
 * Having said that, we can review the design of initializers to return async values later
 */
private class PeriodicallyUpdatingValueDelegate<T>(
    private val refreshInterval: Duration,
    private val clazz: Class<T>,
    private val valueLoader: (prev: T?) -> T
) : ReadOnlyProperty<Any?, CompletableFuture<T>> {

    private val executor = Executors.newSingleThreadScheduledExecutor(
        ThreadFactoryBuilder()
            .setNameFormat("${PeriodicallyUpdatingValueDelegate::class.java.simpleName}-%d")
            .setDaemon(true)
            .build()
    )
    private val valueRef = AtomicReference<PeriodicallyUpdatingValueState<T>>()

    init {
        loadOrUpdateValue()

        val refreshIntervalMillis: Long = refreshInterval.toMillis()
        // Schedule the value to be refreshed at the given interval
        executor.scheduleAtFixedRate(
            ::loadOrUpdateValue,
            refreshIntervalMillis,
            refreshIntervalMillis,
            TimeUnit.MILLISECONDS
        )
    }

    override fun getValue(thisRef: Any?, property: KProperty<*>): CompletableFuture<T> {
        return valueRef.get()
    }

    private fun loadOrUpdateValue() {
        valueRef.updateAndGet { currValue ->
            if (currValue == null || currValue.isCompletedExceptionally) {
                loadValue(null)
            } else {
                loadValue(currValue.get())
            }
        }
    }

    private fun loadValue(currValue: T?): PeriodicallyUpdatingValueState<T> {
        val future = try {
            val value = valueLoader.invoke(currValue)

            log.debug("Loaded new value for '${clazz.simpleName}', next attempt at: ${ZonedDateTime.now().plus(refreshInterval)}, after $refreshInterval")

            completedFuture(value)
        } catch (e: Exception) {
            log.warn("Exception while loading the value for '${clazz.simpleName}', retaining current value, next attempt at: ${ZonedDateTime.now().plus(refreshInterval)} after $refreshInterval", e)
            completedExceptionally(e, currValue)
        }

        Telemetry.trackEvent(
            EVENT_NAME,
            properties = mapOf(
                "entity" to clazz.simpleName,
                "exception" to (future.exception?.message ?: "")
            ),
            measures = mapOf(
                "attempt" to future.attempt.toDouble(),
                "successes" to future.successCounter.toDouble(),
                "continuousFailures" to future.continuousFailureCount.toDouble()
            )
        )

        return future
    }

    /**
     * Build a new state on every successful completion
     */
    private fun completedFuture(value: T): PeriodicallyUpdatingValueState<T> {
        val current = valueRef.get() ?: PeriodicallyUpdatingValueState.initial()
        val attemptedAt = System.currentTimeMillis()
        return PeriodicallyUpdatingValueState<T>(
            attempt = current.attempt + 1,
            successCounter = current.successCounter + 1,
            continuousFailureCount = 0,
            attemptedAt = attemptedAt,
            lastSuccessAt = attemptedAt,
            exception = null
        ).apply {
            complete(value)
        }
    }

    /**
     * If completed exceptionally, we need to keep the last loaded value
     */
    private fun completedExceptionally(exception: Exception, currValue: T?): PeriodicallyUpdatingValueState<T> {
        val current = valueRef.get() ?: PeriodicallyUpdatingValueState.initial()
        val attemptedAt = System.currentTimeMillis()
        return PeriodicallyUpdatingValueState<T>(
            attempt = current.attempt + 1,
            successCounter = current.successCounter,
            continuousFailureCount = current.continuousFailureCount + 1,
            attemptedAt = attemptedAt,
            lastSuccessAt = current.lastSuccessAt,
            exception = exception
        ).apply {
            if (currValue != null) {
                complete(currValue)
            } else {
                completeExceptionally(exception)
            }
        }
    }

    companion object {
        private val log by logger()
        private const val EVENT_NAME = "PERIODICALLY_UPDATE_EVENT"
        private const val EXCEPTION_NAME = "PERIODICALLY_UPDATE_EXCEPTION"
    }
}

private data class PeriodicallyUpdatingValueState<T>(
    val attempt: Int,
    val successCounter: Int,
    val continuousFailureCount: Int,
    val attemptedAt: Long,
    val lastSuccessAt: Long,
    val exception: Exception?
): CompletableFuture<T>()  {
    companion object {
        fun <T> initial() =  PeriodicallyUpdatingValueState<T>(0, 0, 0, 0, 0, null)
    }
}
