package com.udaan.pricing.core.helpers.signals.variableresolvers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.client.UdaanServiceException
import com.udaan.pricing.commons.GenericValue
import com.udaan.pricing.commons.StringValue
import com.udaan.pricing.core.managers.signals.SignalReadManager
import com.udaan.pricing.core.models.signals.CatalogEntityContext
import com.udaan.pricing.core.models.signals.LocationContext
import com.udaan.pricing.core.utils.signals.CatalogEntityContextUtil.getCatalogEntityBasisTypeEnum
import com.udaan.pricing.core.utils.signals.LocationContextUtil.getLocationValueBasisTypeEnum
import com.udaan.pricing.core.utils.signals.SignalUtil
import com.udaan.pricing.signals.GenericMetadata
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalMetadata
import com.udaan.pricing.variable.Variable
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.variable.requestreponse.ResolvedValue
import com.udaan.pricing.variable.requestreponse.ResolverLogic

@Singleton
class VslChannelMetadataResolver @Inject constructor(
    private val signalReadManager: SignalReadManager
): VariableResolver {

    private val vslChannelKey = "SELECTED_CHANNEL"
    private val unknownVslChannelValue = "UNKNOWN"
    override suspend fun resolve(
        catalogEntityContext: CatalogEntityContext,
        locationContext: LocationContext,
        variable: Variable
    ): Pair<VariableId, ResolvedValue> {
        var selectedSignal: Signal? = null
        var resolverLogic: ResolverLogic? = null
        var exceptionMessage: String? = null

        try {
            run hierarchiesEvaluator@{
                variable.hierarchies.forEach {

                    // resolve location and catalog entity basis hierarchy of variable, else go to next hierarchy
                    val resolvedLocationAsPerHierarchy = locationContext.getLocationValueBasisTypeEnum(it.second)
                    val catalogEntityAsPerHierarchy = catalogEntityContext.getCatalogEntityBasisTypeEnum(it.first)
                    if(resolvedLocationAsPerHierarchy != null && catalogEntityAsPerHierarchy != null) {
                        val signal = signalReadManager.getSignalForEntityLocationAndVariableId(
                            catalogEntity = catalogEntityAsPerHierarchy,
                            locationValue = resolvedLocationAsPerHierarchy,
                            variableId = VariableId.VSL_PRICE_WOT_PAISA_UNIT
                        )

                        if (signal != null) {
                            // validate signal state and expiry
                            val isSignalFresh = SignalUtil.validateSignalStateAndFreshness(
                                signal = signal,
                                freshnessDurationInMillis = variable.freshnessDurationInMillis
                            )

                            if (isSignalFresh) {
                                selectedSignal = signal
                                return@hierarchiesEvaluator
                            }
                        }
                    }
                }
            }
        } catch (ex: UdaanServiceException) {
            exceptionMessage = ex.message
            resolverLogic = ResolverLogic.NO_VALUE_AS_DOWNSTREAM_SVC_ERROR
        } catch (ex: Exception) {
            exceptionMessage = ex.message
        }

        val finalValue = if (selectedSignal != null) {
            getChannelFromInputMetadata(selectedSignal!!.metadata)
        } else {
            resolverLogic = if (exceptionMessage != null) {
                ResolverLogic.DEFAULT_VALUE_AS_EXCEPTION_OCCURRED
            } else {
                ResolverLogic.DEFAULT_VALUE_AS_SIGNAL_ABSENT_OR_EXPIRED
            }
            variable.defaultValue
        }

        val resolvedValue = ResolvedValue(
            success = (exceptionMessage == null && finalValue != null),
            value = finalValue,
            referenceSignalId = selectedSignal?.referenceId,
            resolverLogic = resolverLogic,
            exception = exceptionMessage,
            metadata = selectedSignal?.metadata,
            lastRefreshedAt = selectedSignal?.updatedAt
        )

        return Pair(variable.id, resolvedValue)
    }

    private fun getChannelFromInputMetadata(metadata: SignalMetadata): GenericValue? {
        val parsedMetadata = metadata as GenericMetadata
        val selectedChannel = parsedMetadata.metadataMap[vslChannelKey] ?: unknownVslChannelValue
        return StringValue(selectedChannel)
    }
}
