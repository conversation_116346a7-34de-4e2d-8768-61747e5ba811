package com.udaan.pricing.core.helpers.signals.variableresolvers

import com.google.inject.Singleton
import com.udaan.pricing.core.models.signals.CatalogEntityContext
import com.udaan.pricing.core.models.signals.LocationContext
import com.udaan.pricing.core.utils.signals.MetadataUtil
import com.udaan.pricing.variable.Variable
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.variable.requestreponse.ResolvedValue

@Singleton
class MetadataResolver : VariableResolver {
    override suspend fun resolve(
        catalogEntityContext: CatalogEntityContext,
        locationContext: LocationContext,
        variable: Variable
    ): Pair<VariableId, ResolvedValue> {
        var exceptionMessage: String? = null
        val value = try {
            MetadataUtil.getMetadataValue(catalogEntityContext, variable.id)
        } catch (ex: Exception) {
            exceptionMessage = ex.message
            null
        }

        val resolvedValue = ResolvedValue(
            success = (exceptionMessage == null && value != null),
            value = value,
            referenceSignalId = null,
            resolverLogic = null,
            exception = exceptionMessage
        )

        return Pair(variable.id, resolvedValue)
    }
}
