package com.udaan.pricing.core.controller

import com.google.inject.Inject
import com.udaan.common.utils.getCurrentMillis
import com.udaan.pricing.UserCohort
import com.udaan.pricing.core.constants.UserCohortConstants.USER_COHORTS
import com.udaan.pricing.UserCohortCreateReq
import com.udaan.pricing.UserCohortType
import com.udaan.pricing.core.dao.UserCohortRepository
import com.udaan.pricing.core.utils.generateId
import javax.ws.rs.BadRequestException

/**
 * User Cohort Controller is not used at this moment. There are 2 cohorts in total which are hardcoded.
 * This cosmos container was using 1 lakhs RU's which was wasted, so we have used this brutal hardcode hack.
 * In order to use it back, please fix the architecture and move ahead
 */
@Deprecated("UserCohortController is deprecated.")
class UserCohortController @Inject constructor(
    private val userCohortRepository: UserCohortRepository
) {

    fun getByOrg(orgId: String): List<UserCohort> {
        return USER_COHORTS.filter { it.orgId == orgId }
    }

    fun getAllCohort(sellerOrgId: String): List<UserCohort> {
        return USER_COHORTS.filter { it.sellerOrgId == sellerOrgId }
    }

    fun createCohort(userCohortCreateReq: UserCohortCreateReq): UserCohort {
        val userCohortName = getByOrg(userCohortCreateReq.orgId).map { it.cohortName }

        if (userCohortName.contains(userCohortCreateReq.cohortName)) {
            throw BadRequestException("User Exists in cohort ${userCohortCreateReq.cohortName}")
        } else {
            throw BadRequestException("Unable to create user cohort")
//            return UserCohortRepository.createUserCohort(userCohortCreateReq.convert())
        }
    }

    fun deleteCohort(orgId: String, cohortName: String, sellerOrgId: String?, userCohortType: UserCohortType){
        val userCohort = USER_COHORTS.firstOrNull { it.orgId == orgId && it.cohortName == cohortName }
            ?: throw BadRequestException("User doesnt Exists in cohort  $cohortName")
        throw BadRequestException("Unable to delete User cohort")
//        UserCohortRepository.deleteCohort(userCohort.id, orgId)
    }

    private fun UserCohortCreateReq.convert(): UserCohort {
        return UserCohort(generateId("UC"),this.sellerOrgId,this.userCohortType,
                this.orgId,this.cohortName, getCurrentMillis(), getCurrentMillis())
    }
}
