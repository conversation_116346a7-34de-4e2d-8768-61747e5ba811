package com.udaan.pricing.core.models.signals

import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.variable.VariableId


data class LocationContext(
    val locationType: LocationType,
    val locationValue: String,
    val city: String,
    val sellerOrgCity: String,
    val anchorWarehouses: List<String>,
    val mfcWarehouses: List<String>,
    val territoryMap: Map<VariableId, String>?
)
