package com.udaan.pricing.core.strategyevaluator.impl.dynamic.common

import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.LadderUtils
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.variable.VariableId
import java.math.BigDecimal

internal object VolumetricDiscountLadderEvaluator : Evaluator {

    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        require(data.previousOutput != null) {
            "Previous output is mandatory for discount slabs evaluator"
        }

        val outputMetadata = mutableMapOf<String, String>()

        val previousOutput = when (data.previousOutput.output) {
            is LadderValue -> {
                data.previousOutput.output
            }
            is BigDecimalValue -> {
                LadderUtils.applyDefaultLadder(data.previousOutput.output.value, outputMetadata)
            }
            else -> {
                error(
                    "Previous evaluator output for volumetric discount " +
                            "ladder be of LadderValue or BigDecimalValue type"
                )
            }
        }

        ValidationUtils.validateInputs(data.strategy, data.inputs)

        val firstLadderPrice = previousOutput.value.first().ladderValue

        val volumetricDiscountLadders = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.VOLUMETRIC_LADDER_DISCOUNT_BPS
        ) as? LadderValue

        val ladderOutput = if (volumetricDiscountLadders == null) {
            outputMetadata["NO_VOLUME_DISCOUNT_LADDER"] = "true"
            data.previousOutput.output
        } else {
            applyLadders(
                firstLadderPrice,
                previousOutput.value,
                volumetricDiscountLadders.value,
                outputMetadata
            )
        }
        return EvaluatorOutput(ladderOutput, outputMetadata)
    }

    private fun applyLadders(
        absolutePrice: BigDecimal,
        previousLadders: List<Ladder>,
        volumetricLaddersDiscountBps: List<Ladder>,
        outputMetadata: MutableMap<String, String>
    ): LadderValue {
        val volumetricLadderPriceValues = LadderUtils.applyDiscountLadders(
            absolutePrice,
            volumetricLaddersDiscountBps,
            outputMetadata
        )
        val postMergingVolumeDiscountLadders = LadderUtils.compareAndCreateLadders(
            previousLadders, volumetricLadderPriceValues.value,
            floorGuardRailPrice = null,
            outputMetadata = outputMetadata
        )
        outputMetadata["POST_MERGE_VOLUME_DISCOUNT_VALUES"] = postMergingVolumeDiscountLadders.toString()
        return postMergingVolumeDiscountLadders
    }
}
