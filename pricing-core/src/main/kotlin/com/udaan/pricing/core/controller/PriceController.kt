package com.udaan.pricing.core.controller

import com.google.inject.Inject
import com.udaan.catalog.lot.client.CatalogLotClient
import com.udaan.catalog.models.ModelV2
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope.Companion.async
import com.udaan.pricing.*
import com.udaan.pricing.core.dao.BasePriceRepository
import com.udaan.pricing.core.helpers.CatalogHelper
import com.udaan.pricing.core.helpers.ConfigHelper
import com.udaan.pricing.core.helpers.PreferredWarehouseHelper
import com.udaan.pricing.core.helpers.SchemeHelper
import com.udaan.pricing.core.helpers.SellerOrgCityHelper
import com.udaan.pricing.core.helpers.ServingWarehouseHelper
import com.udaan.pricing.core.helpers.UserHelper
import com.udaan.pricing.core.helpers.UdaanServiceRequestExtension.executeAwaitWithRetry
import com.udaan.pricing.core.helpers.pricingstrategy.PricingStrategy
import com.udaan.pricing.core.helpers.pricingstrategy.StrategyFactory
import com.udaan.pricing.core.helpers.rider.PriceRider
import com.udaan.pricing.core.helpers.rider.PricingRequestContext
import com.udaan.pricing.core.helpers.rider.RiderFactory
import com.udaan.pricing.core.helpers.rider.RootRequestContext
import com.udaan.pricing.core.helpers.rider.impl.RiderHelper
import com.udaan.pricing.core.models.CatalogEntityContext
import com.udaan.pricing.core.models.common.SelectedListingSalesUnitPrice
import com.udaan.pricing.core.svcinterfaces.CatalogSvcInterface
import com.udaan.pricing.core.utils.generateId
import com.udaan.proto.models.ModelV1
import com.udaan.resources.RedisLettuce6Client
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.future.await
import javax.ws.rs.NotFoundException
import kotlin.time.measureTimedValue

class PriceController @Inject constructor(
    private val basePriceRepository: BasePriceRepository,
    private val pricingRedisClient: RedisLettuce6Client,
    private val lotClient: CatalogLotClient,
    private val schemeHelper: SchemeHelper,
    private val strategyFactory: StrategyFactory,
    private val priceAuditController: PriceAuditController,
    private val riderFactory: RiderFactory,
    private val geoLocationBasePriceController: GeoLocationBasePriceController,
    private val basePriceController: BasePriceController,
    private val servingWarehouseHelper: ServingWarehouseHelper,
    private val catalogHelper: CatalogHelper,
    private val preferredWarehouseHelper: PreferredWarehouseHelper,
    private val catalogSvcInterface: CatalogSvcInterface,
    private val userHelper: UserHelper,
    private val riderHelper: RiderHelper,
    private val configHelper: ConfigHelper,
    private val sellerOrgCityHelper: SellerOrgCityHelper
) {

    companion object {
        private val logger by logger()
    }

    suspend fun getPriceListing(
        listingId: String,
        saleUnitId: String?,
        contextualPriceRequest: ContextualPriceRequest?,
        rootRequestContext: RootRequestContext?,
        fetchInactive: Boolean,
        fetchRiderDetails: Boolean,
        fetchRiderAdditionalDetails: Boolean = false,
        orderReadyForCheckout: Boolean = false,
        filterSSCMetaData: Boolean = true
    ): List<PriceForListing> {
        val catalogEntityContext = catalogHelper.getCatalogEntityContextFromLidSuid(
            listingId = listingId,
            salesUnitId = saleUnitId
        )

        logger.info(
            "ListingId: {}, salesUnitId: {}, vertical: {} category: {}, ContextualPriceRequestDTO: {}",
            listingId,
            saleUnitId,
            catalogEntityContext.vertical.name,
            catalogEntityContext.verticalCategory,
            contextualPriceRequest
        )

        val (buyerOrgUnit, pincode) = getBuyerOrgUnitAndPincodeFromContext(contextualPriceRequest)

        val clusterList = if (contextualPriceRequest != null && !contextualPriceRequest.cluster.isNullOrBlank()) {
            listOf(contextualPriceRequest.cluster ?: "")
        } else if (pincode != null) {
            val staticCluster = pricingRedisClient.asyncCommands.get("pricing_cluster_$pincode")
                .toCompletableFuture()
                .await()
                ?.split(",")

            staticCluster
        } else null

        val salesUnitIdsToConsider = if (saleUnitId != null) {
            listOf(saleUnitId)
        } else {
            catalogEntityContext.listingDetail.salesUnitList.filter {
                it.status in listOf(ModelV2.EleStatus.ENABLED, ModelV2.EleStatus.INACTIVE)
            }.map { it.salesUnitId }
        }

        val preferredWarehouse =
            if (contextualPriceRequest?.buyerContext != null && salesUnitIdsToConsider.isNotEmpty()) {
                preferredWarehouseHelper.getPreferredWarehouseId(
                    listingId = listingId,
                    salesUnitIds = salesUnitIdsToConsider,
                    verticalCategory = catalogEntityContext.verticalCategory,
                    buyerContext = contextualPriceRequest.buyerContext!!
                )
            } else null

        // todo: this needs to be moved to promotions. Not required in pricing anymore
        val servingWarehouse = if (contextualPriceRequest != null && salesUnitIdsToConsider.isNotEmpty()) {
            servingWarehouseHelper.getServingWarehouseId(
                listingId = listingId,
                salesUnitIds = salesUnitIdsToConsider,
                listingDetails = catalogEntityContext.listingDetail,
                verticalCategory = catalogEntityContext.verticalCategory,
                contextualPriceRequest = contextualPriceRequest
            )
        } else null

        val requestContext = PricingRequestContext(
            contextualPriceRequest?.buyerContext,
            rootRequestContext,
            contextualPriceRequest?.refId
        )

        val mappedBenchmarkListingGuardrailPriceDeferred = getGuardrailPriceBasisBenchmarkListing(
            catalogEntityContext = catalogEntityContext,
            contextualPriceRequest = contextualPriceRequest,
            requestContext = requestContext,
            clusterList = clusterList,
            buyerOrgUnit = buyerOrgUnit
        )

        val ridersWithAdjustmentsAndDurationDeferred = PriceRider.entries.map {
            async {
                val priceRidersWithTimeDuration = measureTimedValue {
                    riderFactory.getRiderImpl(it).getPriceRiderWithAdditionalDetails(
                        catalogEntityContext = catalogEntityContext,
                        requestContext = requestContext,
                        fetchRiderDetails = fetchRiderDetails,
                        cluster = clusterList,
                        fetchRiderAdditionalDetails = fetchRiderAdditionalDetails,
                        preferredWarehouseId = preferredWarehouse,
                        servingWarehouseId = servingWarehouse
                    )
                }
                it to Pair(priceRidersWithTimeDuration.value, priceRidersWithTimeDuration.duration.inWholeMilliseconds)
            }
        }

        val priceFromAllStrategyDeferred = PricingStrategy.entries.map {
            async {
                val strategyWithPrice = it to strategyFactory.getAppropriateStrategy(it).getPrice(
                    catalogEntityContext = catalogEntityContext,
                    contextualPriceRequest = contextualPriceRequest,
                    fetchInactive = fetchInactive,
                    cluster = clusterList,
                    preferredWarehouseId = preferredWarehouse,
                    servingWarehouseId = servingWarehouse,
                    buyerOrgUnit = buyerOrgUnit,
                    mappedBenchmarkListingGuardrailPriceDeferred = mappedBenchmarkListingGuardrailPriceDeferred
                )
                strategyWithPrice
            }
        }

        val finalPricesWithRiders = getFinalPricesPostRidersApplication(
            contextualPriceRequest = contextualPriceRequest,
            catalogEntityContext = catalogEntityContext,
            priceFromAllStrategyDeferred = priceFromAllStrategyDeferred,
            ridersWithAdjustmentsAndDurationDeferred = ridersWithAdjustmentsAndDurationDeferred,
            orderReadyForCheckout = orderReadyForCheckout
        )

        /**
         * Ssc Metadata is not required for outside world,
         * its just a placeholder for SSC execution and for audit purpose.
         */
        return if (filterSSCMetaData) {
            finalPricesWithRiders.map {
                it.copy(sscMetadata = emptyMap())
            }
        } else {
            finalPricesWithRiders
        }
    }

    suspend fun getPriceListingV2(
        listingId: String,
        saleUnitId: String?,
        contextualPriceRequest: ContextualPriceRequest?,
        rootRequestContext: RootRequestContext,
        fetchInactive: Boolean,
        fetchRiderDetails: Boolean,
        fetchRiderAdditionalDetails: Boolean = false,
        packagingType: String? = "",
        orderReadyForCheckout: Boolean = false
    ): List<PriceForListing> {
        val priceForListing = getPriceListing(
            listingId,
            saleUnitId,
            contextualPriceRequest,
            rootRequestContext,
            fetchInactive,
            fetchRiderDetails,
            fetchRiderAdditionalDetails,
            orderReadyForCheckout = orderReadyForCheckout
        )
        if (packagingType != null) {
            val newPrices = priceForListing.map {
                val puPrices = it.prices.filter { price -> price.packagingUnit?.packaging?.name == packagingType }
                if (puPrices.isNotEmpty()) {
                    /**
                     *  For cart-service, the pricing ladders might differ from actual ladder in case of PU priced listings
                     *  We will have to price the buyer at a different price since the intended PU by buyer is different.
                     *  Eg: a buyer may add more eaches than the ladder quantity for each packaging type and though the buyer has moved to next ladder
                     *  1-71 EACH   72-215 CASE   216+ CASE
                     *  Buyer can still add 75 each from UI and would have to be priced at EACH price
                     *  Hence we alter our ladders in such a way that cart-service is agnostic of this logic and can find the correct unit price
                     *  So the new ladder for above case would be: 1+ EACH (when packagingUnit received is EACH)
                     */
                    val sortedPrices = puPrices.sortedBy { it.minQty }
                    val puSortedPrice = sortedPrices.mapIndexed { index, ladder ->
                        // change maxQty for last ladder as a precaution
                        if (index + 1 == sortedPrices.size) {
                            ladder.copy(maxQty = Int.MAX_VALUE)
                        } else {
                            ladder
                        }
                    }
                    it.copy(prices = puSortedPrice)
                } else {
                    it
                }
            }
            return newPrices
        } else {
            return priceForListing
        }

    }

    suspend fun getPriceMulti(
        priceMultiReq: PriceMultiReq,
        fetchInactive: Boolean,
        rootRequestContext: RootRequestContext?,
        fetchRiderDetails: Boolean
    ): List<PriceForListing> {
        return priceMultiReq.listingInfo.map {
            try {
                async {
                    val res = getPriceListing(
                        listingId = it.listingId,
                        saleUnitId = it.saleUnitId,
                        contextualPriceRequest = ContextualPriceRequest(
                            transactionContext = priceMultiReq.contextualPriceRequest.transactionContext,
                            buyerContext = priceMultiReq.contextualPriceRequest.buyerContext,
                            refId = it.refId
                        ),
                        rootRequestContext = rootRequestContext,
                        fetchInactive = fetchInactive,
                        fetchRiderDetails = fetchRiderDetails
                    ).firstOrNull()

                    Pair(it, res)
                }
            } catch (e: Exception) {
                logger.error("getPriceListing() failed with {} for listing {}", e, it.listingId)
                throw e
            }

        }.awaitAll().map {
            it.second ?: PriceForListing(it.first.listingId, it.first.saleUnitId, listOf(), null, null)
        }
    }

    suspend fun getPriceMultiV2(
        priceMultiReq: PriceMultiReq,
        fetchInactive: Boolean,
        rootRequestContext: RootRequestContext,
        fetchRiderDetails: Boolean,
        orderReadyForCheckout: Boolean
    ): List<PriceForListing> {
        return priceMultiReq.listingInfo.map {
            try {
                async {
                    val res = getPriceListingV2(
                        listingId = it.listingId,
                        saleUnitId = it.saleUnitId,
                        contextualPriceRequest = ContextualPriceRequest(
                            transactionContext = priceMultiReq.contextualPriceRequest.transactionContext,
                            buyerContext = priceMultiReq.contextualPriceRequest.buyerContext,
                            refId = it.refId
                        ),
                        rootRequestContext = rootRequestContext,
                        fetchInactive = fetchInactive,
                        fetchRiderDetails = fetchRiderDetails,
                        fetchRiderAdditionalDetails = false,
                        packagingType = it.packagingType?.name,
                        orderReadyForCheckout = orderReadyForCheckout
                    ).firstOrNull()

                    Pair(it, res)
                }
            } catch (e: Exception) {
                logger.error("getPriceListingV2() failed with {} for listing {}", e, it.listingId)
                throw e
            }

        }.awaitAll().map {
            it.second ?: PriceForListing(it.first.listingId, it.first.saleUnitId, listOf(), null, null)
        }
    }

    suspend fun getLotPricingBulk(
        getLotPriceMulti: List<GetLotPriceRequest>,
        orderReadyForCheckout: Boolean
    ): List<PriceForListing?> {
        logger.info("Received {} lots in getLotPricing request", getLotPriceMulti.size)
        val multipleLotsPriceResponse = getLotPriceMulti.map {
            async {
                try {
                    val listingData = catalogSvcInterface.getTradeListingMinimal(it.listingId)
                    val lotDetails = lotClient.lookupLotV2(it.lotId).executeAwaitWithRetry(3)
                    val scheme = schemeHelper.getApplicableSchemeForLot(
                        listingId = it.listingId,
                        orgId = listingData.orgId,
                        vertical = listingData.vertical,
                        lotDetails = lotDetails.lotAttributes
                    )
                    val offers = if (!scheme.isNullOrBlank()) schemeHelper.parseOffers(scheme) else emptyList()

                    val ptr = lotDetails.lotAttributes["ptr_paise"]?.toLong() ?: 0
                    val mrp = lotDetails.lotAttributes["mrp_paise"]?.toLong()
                    val expiryDate = lotDetails.lotAttributes["expiry_date"]
                    val batchId = lotDetails.lotAttributes["batch_id"]

                    val priceListing = PriceForListing(
                        listingId = it.listingId,
                        saleUnitId = it.saleUnitId,
                        prices = schemeHelper.constructPriceConditions(ptr, offers),
                        strategyRef = null,
                        metaData = MetaData(mrp, it.lotId, scheme, ptr, batchId, expiryDate)
                    )

                    val pricingAudit = PricingAudit(
                        id = generateId("PA"),
                        refId = it.refId ?: it.listingId,
                        listingId = it.listingId,
                        saleUnitId = it.saleUnitId,
                        strategyName = "LOT-Based",
                        strategyRef = priceListing.strategyRef,
                        prices = priceListing.prices,
                        metaData = priceListing.metaData
                    )
                    val pricingAuditId = priceAuditController.createPriceAudit(pricingAudit, orderReadyForCheckout)

                    priceListing.copy(
                        pricingAuditId = pricingAuditId
                    )
                } catch (e: Exception) {
                    logger.error(
                        "Got exception while fetching lot price for listingId: {}, lotId: {}, Exception: {}",
                        it.listingId, it.lotId, e.message
                    )
                    null
                }
            }
        }.awaitAll()
        logger.info("Lot Price Response has {} lots", multipleLotsPriceResponse.filterNotNull().size)
        return multipleLotsPriceResponse
    }

    suspend fun metaNull(): List<RawInfoListing> {
        return basePriceRepository.metaNull()
    }

    suspend fun getLotWithDesiredScheme(
        getLotPriceWithSchemeRequest: List<GetLotPriceWithSchemeRequest>,
        orderReadyForCheckout: Boolean
    ): List<PriceForListing?> {
        return getLotPriceWithSchemeRequest.map {
            async {
                try {
                    val lotDetails = lotClient.lookupLotV2(it.lotId).executeAwaitWithRetry(3)
                    val scheme = it.scheme

                    val offers = if (!scheme.isNullOrBlank()) schemeHelper.parseOffers(scheme) else emptyList()

                    val ptr = lotDetails.lotAttributes["ptr_paise"]?.toLong() ?: 0
                    val mrp = lotDetails.lotAttributes["mrp_paise"]?.toLong()
                    val expiryDate = lotDetails.lotAttributes["expiry_date"]
                    val batchId = lotDetails.lotAttributes["batch_id"]

                    val priceListing = PriceForListing(
                        listingId = it.listingId,
                        saleUnitId = it.saleUnitId,
                        prices = schemeHelper.constructPriceConditions(ptr, offers),
                        strategyRef = null,
                        metaData = MetaData(mrp, it.lotId, scheme, ptr, batchId, expiryDate)
                    )

                    val pricingAudit = PricingAudit(
                        id = generateId("PA"),
                        refId = it.refId ?: it.listingId,
                        listingId = it.listingId,
                        saleUnitId = it.saleUnitId,
                        strategyName = "LOT-Based-with-scheme",
                        strategyRef = priceListing.strategyRef,
                        prices = priceListing.prices,
                        metaData = priceListing.metaData
                    )
                    val pricingAuditId = priceAuditController.createPriceAudit(pricingAudit, orderReadyForCheckout)

                    priceListing.copy(pricingAuditId = pricingAuditId)
                } catch (e: Exception) {
                    logger.error(
                        "Got exception while fetching lot price for listingId: {}, Exception: {}",
                        it.listingId,
                        e.message
                    )
                    null
                }
            }
        }.awaitAll()
    }

    suspend fun getIndexingPriceForListingId(
        listingId: String,
        fetchInactive: Boolean
    ): List<PriceForListing> {
        val allGeoBasePricesForListingGroupedBySalesUnit = geoLocationBasePriceController.getAllActiveGeoBasePrices(
            listingId = listingId,
            salesUnitId = null
        ).groupBy { it.saleUnitId }

        val basePrices = if (fetchInactive) {
            basePriceController.getBasePricesForListing(listingId)
        } else {
            val listingDetails = catalogSvcInterface.getTradeListing(listingId)
            val activeSU =
                listingDetails.salesUnitList.filter { it.status == ModelV2.EleStatus.ENABLED }.map { it.salesUnitId }
                    .toSet()
            basePriceController.getBasePricesForListing(listingId).filter { activeSU.contains(it.saleUnitId) }
        }

        val priceForListing = basePrices.groupBy { it.saleUnitId }
        val decidedPrice = priceForListing.map { (salesUnitId, basePriceForSalesUnit) ->
            val latestBasePrice = basePriceForSalesUnit.maxByOrNull { it.updatedAt } ?: error("No latest base price found")

            val geoBasePricesForSalesUnitId = allGeoBasePricesForListingGroupedBySalesUnit[salesUnitId]

            if (geoBasePricesForSalesUnitId == null) {
                PriceForListing(
                    listingId = latestBasePrice.listingId,
                    saleUnitId = latestBasePrice.saleUnitId,
                    prices = latestBasePrice.qtyBasedPrice,
                    strategyRef = null,
                    metaData = latestBasePrice.metaData
                )
            } else {
                val maxGeoBasePriceForSalesUnit = geoBasePricesForSalesUnitId.maxByOrNull { geoBasePrice ->
                    val qtyBasedPrices = geoBasePrice.qtyBasedPrice
                    val maxLadderPrice = qtyBasedPrices.maxByOrNull { it.priceInPaisa.defaultPrice } ?: error("No max ladder price found")
                    maxLadderPrice.priceInPaisa.defaultPrice
                } ?: error("No max geo base price found")
                PriceForListing(
                    listingId = maxGeoBasePriceForSalesUnit.listingId,
                    saleUnitId = maxGeoBasePriceForSalesUnit.saleUnitId,
                    prices = maxGeoBasePriceForSalesUnit.qtyBasedPrice,
                    strategyRef = "${maxGeoBasePriceForSalesUnit.locationType.name}_${maxGeoBasePriceForSalesUnit.locationTypeId}",
                    metaData = null
                )
            }
        }

        val catalogEntityContext = catalogHelper.getCatalogEntityContextFromLidSuid(
            listingId = listingId,
            salesUnitId = null
        )

        // Pulling riderDiscounts up for one listing one call to respective Riders
        val riderWithAdjustments = PriceRider.entries.map {
            async {
                it to riderFactory.getRiderImpl(it).getPriceRiderWithAdditionalDetails(
                    catalogEntityContext = catalogEntityContext,
                    requestContext = PricingRequestContext(null, null, null),
                    fetchRiderDetails = false,
                    cluster = listOf(),
                    fetchRiderAdditionalDetails = false,
                    preferredWarehouseId = null,
                    servingWarehouseId = null
                )
            }
        }.awaitAll()

        val nonNullRidersWithAdjustments = riderWithAdjustments.mapNotNull { riderToPricePair ->
            riderToPricePair.second?.let {
                Pair(riderToPricePair.first, it)
            }
        }

        return decidedPrice.map { x ->
            var bpsVal = 0
            var flatVal = 0
            //@TODO:HACK for just trails of GEO
            var bpsValAdd = 0
            var flatValAdd = 0

            nonNullRidersWithAdjustments.map { y ->
                if (y.second.isAdditive == true) {
                    bpsValAdd += y.second.bpsInPercentage
                    flatValAdd += y.second.flatVal
                } else {
                    bpsVal -= y.second.bpsInPercentage
                    flatVal -= y.second.flatVal
                }
            }
            val hasWeightBasedPricing = catalogEntityContext.vertical.metadata.hasWeightBasedPricing
            PriceForListing(
                x.listingId,
                x.saleUnitId,
                x.prices.map { w ->
                    val disValue = (bpsVal.times(w.priceInPaisa.defaultPrice)).div(10000)
                    val addValue = (bpsValAdd.times(w.priceInPaisa.defaultPrice)).div(10000)
                    val pricePerKgDisValue = if (hasWeightBasedPricing) w.pricePerKgInPaisa?.let {
                        (bpsVal.times(it.defaultPrice)).div(10000)
                    } ?: 0 else 0
                    val pricePerKgAddValue = if (hasWeightBasedPricing) w.pricePerKgInPaisa?.let {
                        (bpsValAdd.times(it.defaultPrice)).div(10000)
                    } ?: 0 else 0
                    QtyBasedPrice(w.minQty, w.maxQty,
                        PriceInPaisa(
                            w.priceInPaisa.onCredit + disValue + flatVal +
                                    addValue + flatValAdd, w.priceInPaisa.onCOD + disValue + flatVal +
                                    addValue + flatValAdd, w.priceInPaisa.onPrepayment + disValue + flatVal +
                                    addValue + flatValAdd,
                            BasicPrice(
                                w.priceInPaisa.onCredit +
                                        addValue + flatValAdd, w.priceInPaisa.onCOD +
                                        addValue + flatValAdd, w.priceInPaisa.onPrepayment +
                                        addValue + flatValAdd
                            ), (riderWithAdjustments.map { z -> z.second })
                        ),
                        if (hasWeightBasedPricing) w.pricePerKgInPaisa?.let {
                            PriceInPaisa(
                                it.onCredit + pricePerKgDisValue + flatVal + pricePerKgAddValue + flatValAdd,
                                it.onCOD + pricePerKgDisValue + flatVal + pricePerKgAddValue + flatValAdd,
                                it.onPrepayment + pricePerKgDisValue + flatVal + pricePerKgAddValue + flatValAdd,
                                BasicPrice(
                                    it.onCredit + pricePerKgAddValue + flatValAdd,
                                    it.onCOD + pricePerKgAddValue + flatValAdd,
                                    it.onPrepayment + pricePerKgAddValue + flatValAdd
                                ), (riderWithAdjustments.map { z -> z.second })
                            )
                        } else PriceInPaisa(0, 0, 0, BasicPrice(0, 0, 0), (riderWithAdjustments.map { z -> z.second })),
                        w.taxableAmountPaise,
                        w.packagingUnit
                    )
                }, x.strategyRef, x.metaData
            )
        }
    }

    suspend fun deleteAllPricesForListingSalesUnitId(
        listingId: String,
        salesUnitId: String
    ): PriceDeleteResponseDto {
        val deletedBasePriceId = basePriceController.deleteBasePriceForListingSalesUnitId(listingId, salesUnitId)
        val deletedGeoBasePriceIds =
            geoLocationBasePriceController.deleteGeoBasePricesForListingSalesUnitId(listingId, salesUnitId)

        return PriceDeleteResponseDto(
            listingId = listingId,
            salesUnitId = salesUnitId,
            deletedBasePriceId = deletedBasePriceId,
            deactivatedGeoBasePriceIds = deletedGeoBasePriceIds,
            deactivatedContractPriceIds = emptyList()
        )
    }

    private suspend fun getBuyerOrgUnitAndPincodeFromContext(
        contextualPriceRequest: ContextualPriceRequest?
    ): Pair<ModelV1.OrgUnit?, String?> {
        val buyerContext = contextualPriceRequest?.buyerContext
        val buyerOrgDetails = buyerContext?.orgId?.let { userHelper.getBuyerOrgById(it) }
        val buyerOrgUnit = if (buyerOrgDetails != null && buyerContext.orgUnitId != null) {
            userHelper.getBuyerOrgUnit(buyerOrgDetails, buyerContext.orgUnitId!!)
        } else null
        val pincode = buyerOrgUnit?.let {
            val orgUnitIdPincode = userHelper.getBuyerOrgUnitPincode(buyerOrgUnit)
            logger.info("fetching pincode $orgUnitIdPincode from orgUnit details for orgUnitId ${buyerContext?.orgUnitId}")
            orgUnitIdPincode
        } ?: buyerContext?.pincode

        return Pair(buyerOrgUnit, pincode)
    }

    private suspend fun getFinalPricesPostRidersApplication(
        contextualPriceRequest: ContextualPriceRequest?,
        catalogEntityContext: CatalogEntityContext,
        priceFromAllStrategyDeferred:  List<Deferred<Pair<PricingStrategy, List<PriceForListing>>>>,
        ridersWithAdjustmentsAndDurationDeferred:  List<Deferred<Pair<PriceRider, Pair<PriceRiderForListing?, Long>>>>,
        orderReadyForCheckout: Boolean
    ): List<PriceForListing> {
        val ridersWithAdjustmentsAndDuration = ridersWithAdjustmentsAndDurationDeferred.awaitAll()
        val priceFromAllStrategy = priceFromAllStrategyDeferred.awaitAll()

        val ridersWithAdjustments = ridersWithAdjustmentsAndDuration.map {
            it.first to it.second.first
        }
        val ridersWithDuration = ridersWithAdjustmentsAndDuration.map {
            it.first to it.second.second
        }

        logger.info("All riders with values : {}", ridersWithAdjustments)
        logger.info("All riders with duration : {}", ridersWithDuration)
        val nonNullRidersWithAdjustments = ridersWithAdjustments.mapNotNull { ridersWithAdjustmentPair ->
            ridersWithAdjustmentPair.second?.let {
                Pair(ridersWithAdjustmentPair.first, it)
            }
        }

        val finalSelectedPrices = decidePrices(priceFromAllStrategy)
        logger.info("Selected prices with riders {}", finalSelectedPrices)

        val finalSelectedPricesWithRiders = riderHelper.getFinalPricesWithRiders(
            contextualPriceRequest = contextualPriceRequest,
            catalogEntityContext = catalogEntityContext,
            finalSelectedListingSalesUnitPrices = finalSelectedPrices,
            nonNullRidersWithAdjustments = nonNullRidersWithAdjustments,
            orderReadyForCheckout = orderReadyForCheckout
        )

        logger.info("Final price with riders {}", finalSelectedPricesWithRiders)
        return finalSelectedPricesWithRiders
    }

    private fun decidePrices(
        priceFromAllStrategy: List<Pair<PricingStrategy, List<PriceForListing>>>
    ): List<SelectedListingSalesUnitPrice> {
        val groupBy = priceFromAllStrategy.map { (strategy, data) ->
            data.map { price ->
                SelectedListingSalesUnitPrice(price.listingId, price.saleUnitId, strategy, price)
            }
        }.flatten().groupBy { Pair(it.listingId, it.salesUnitId) }

        return groupBy.mapNotNull { (_, multipleOption) ->
            val sortOnPriority = sortOnPriority(multipleOption)
            sortOnPriority.firstOrNull()
        }
    }

    private fun sortOnPriority(options: List<SelectedListingSalesUnitPrice>): List<SelectedListingSalesUnitPrice> {
        return options.sortedBy { it.strategy.getPriority() }
    }

    private fun getGuardrailPriceBasisBenchmarkListing(
        catalogEntityContext: CatalogEntityContext,
        contextualPriceRequest: ContextualPriceRequest?,
        requestContext: PricingRequestContext,
        clusterList: List<String>?,
        buyerOrgUnit: ModelV1.OrgUnit?
    ): Deferred<PriceForListing?>? {
        val priceRidersToEvaluate = listOf(
            PriceRider.GEO_PRICE_RIDER
        )

        val priceStrategyToEvaluate = listOf(
            PricingStrategy.MANUAL,
            PricingStrategy.SSC
        )

        return catalogEntityContext.productGroupId?.let { productGroupId ->
            async {
                val mappedBenchmarkListingPriceTimedValue = measureTimedValue {
                    val sellerOrgCity = sellerOrgCityHelper.getCityForSellerOrgId(
                        sellerOrgId = catalogEntityContext.listingDetail.orgId
                    ) ?: throw NotFoundException("Seller org city not found")

                    val mappedBenchmarkGid = configHelper.getMappedBenchmarkGidForGuardrail(
                        productGroupId = productGroupId,
                        sellerOrgCity = sellerOrgCity
                    )

                    val benchmarkListingToConsider = mappedBenchmarkGid?.let {
                        val activeListingForGidAndCity = catalogHelper.getAnActiveListingFromGidAndCityCached(
                            productGroupId = it,
                            sellerOrgCity = sellerOrgCity
                        )

                        logger.info(
                            "PL listing {} Benchmark GID {} and listing {} to consider.",
                            catalogEntityContext.listingId,
                            mappedBenchmarkGid,
                            activeListingForGidAndCity
                        )
                        activeListingForGidAndCity
                    }

                    benchmarkListingToConsider?.let {
                        val benchmarkCatalogEntityContext = catalogHelper.getCatalogEntityContextFromLidSuid(
                            listingId = it,
                            salesUnitId = null
                        )

                        val salesUnitIdsToConsider = benchmarkCatalogEntityContext.listingDetail.salesUnitList.filter {
                            it.status in listOf(ModelV2.EleStatus.ENABLED, ModelV2.EleStatus.INACTIVE)
                        }.map { it.salesUnitId }

                        val preferredWarehouseDeferred = async {
                            if (contextualPriceRequest?.buyerContext != null && salesUnitIdsToConsider.isNotEmpty()) {
                                preferredWarehouseHelper.getPreferredWarehouseId(
                                    listingId = it,
                                    salesUnitIds = salesUnitIdsToConsider,
                                    verticalCategory = benchmarkCatalogEntityContext.verticalCategory,
                                    buyerContext = contextualPriceRequest.buyerContext!!
                                )
                            } else null
                        }

                        val servingWarehouseDeferred = async {
                            if (contextualPriceRequest != null && salesUnitIdsToConsider.isNotEmpty()) {
                                servingWarehouseHelper.getServingWarehouseId(
                                    listingId = it,
                                    salesUnitIds = salesUnitIdsToConsider,
                                    listingDetails = benchmarkCatalogEntityContext.listingDetail,
                                    verticalCategory = benchmarkCatalogEntityContext.verticalCategory,
                                    contextualPriceRequest = contextualPriceRequest
                                )
                            } else null
                        }

                        val ridersWithAdjustmentsAndDurationDeferred = priceRidersToEvaluate.map {
                            async {
                                val priceRidersWithTimeDuration = measureTimedValue {
                                    riderFactory.getRiderImpl(it).getPriceRiderWithAdditionalDetails(
                                        catalogEntityContext = benchmarkCatalogEntityContext,
                                        requestContext = requestContext,
                                        fetchRiderDetails = true,
                                        cluster = clusterList,
                                        fetchRiderAdditionalDetails = false,
                                        preferredWarehouseId = preferredWarehouseDeferred.await(),
                                        servingWarehouseId = servingWarehouseDeferred.await()
                                    )
                                }
                                it to Pair(priceRidersWithTimeDuration.value, priceRidersWithTimeDuration.duration.inWholeMilliseconds)
                            }
                        }

                        val priceFromAllStrategyDeferred = priceStrategyToEvaluate.map {
                            async {
                                val strategyWithPrice = it to strategyFactory.getAppropriateStrategy(it).getPrice(
                                    catalogEntityContext = benchmarkCatalogEntityContext,
                                    contextualPriceRequest = contextualPriceRequest,
                                    fetchInactive = false,
                                    cluster = clusterList,
                                    preferredWarehouseId = preferredWarehouseDeferred.await(),
                                    servingWarehouseId = servingWarehouseDeferred.await(),
                                    buyerOrgUnit = buyerOrgUnit,
                                    mappedBenchmarkListingGuardrailPriceDeferred = null
                                )
                                strategyWithPrice
                            }
                        }

                        getFinalPricesPostRidersApplication(
                            contextualPriceRequest = contextualPriceRequest,
                            catalogEntityContext = benchmarkCatalogEntityContext,
                            priceFromAllStrategyDeferred = priceFromAllStrategyDeferred,
                            ridersWithAdjustmentsAndDurationDeferred = ridersWithAdjustmentsAndDurationDeferred,
                            orderReadyForCheckout = false
                        ).firstOrNull()
                    }
                }
                logger.info(
                    "Mapped benchmark listing price calculation took {}ms",
                    mappedBenchmarkListingPriceTimedValue.duration.inWholeMilliseconds
                )

                mappedBenchmarkListingPriceTimedValue.value
            }
        }
    }
}
