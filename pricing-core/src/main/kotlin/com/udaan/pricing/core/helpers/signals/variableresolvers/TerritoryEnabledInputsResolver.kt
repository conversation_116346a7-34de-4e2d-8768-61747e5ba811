package com.udaan.pricing.core.helpers.signals.variableresolvers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.client.UdaanServiceException
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.managers.signals.SignalReadManager
import com.udaan.pricing.core.models.signals.CatalogEntityContext
import com.udaan.pricing.core.models.signals.LocationContext
import com.udaan.pricing.core.utils.signals.CatalogEntityContextUtil.getCatalogEntityBasisTypeEnum
import com.udaan.pricing.core.utils.signals.LocationContextUtil.getLocationValueBasisTypeEnum
import com.udaan.pricing.core.utils.signals.SignalUtil
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.variable.Variable
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.variable.requestreponse.ResolvedValue
import com.udaan.pricing.variable.requestreponse.ResolverLogic

/**
 * This resolver is used to resolve variables that are territory enabled.
 * The logic is similar to DefaultResolver except for few key changes:
 * 1. the location resolution call - here we pass isTerritoryEnabled = true and
 * variableId in location resolution call.
 * 2. the value returned follows below logic:
 *   a) if territory (cluster) available, fresh signal at territory available, return signal
 *   b) if territory (cluster) available, signal not available, return default value (and NOT fallback at city level)
 *   c) if territory (cluster) not available, check at city level
 *
 * Keeping a separate resolver allows control over which inputs we want to be territory enabled and helps
 * avoid putting territory check logic in DefaultResolver which is used for other variables too.
 */
@Singleton
class TerritoryEnabledInputsResolver @Inject constructor(
    private val signalReadManager: SignalReadManager
): VariableResolver {
    override suspend fun resolve(
        catalogEntityContext: CatalogEntityContext,
        locationContext: LocationContext,
        variable: Variable
    ): Pair<VariableId, ResolvedValue> {
        var selectedSignal: Signal? = null
        var resolverLogic: ResolverLogic? = null
        var exceptionMessage: String? = null

        try {
            run hierarchiesEvaluator@{
                variable.hierarchies.forEach {

                    // resolve location and catalog entity basis hierarchy of variable, else go to next hierarchy
                    // location value for cluster type to be fetched from territory map
                    val resolvedLocationAsPerHierarchy = locationContext.getLocationValueBasisTypeEnum(
                        locationType = it.second, isTerritoryEnabled = true, variableId = variable.id
                    )
                    val catalogEntityAsPerHierarchy = catalogEntityContext.getCatalogEntityBasisTypeEnum(it.first)
                    if(resolvedLocationAsPerHierarchy != null && catalogEntityAsPerHierarchy != null) {
                        val signal = signalReadManager.getSignalForEntityLocationAndVariableId(
                            catalogEntity = catalogEntityAsPerHierarchy,
                            locationValue = resolvedLocationAsPerHierarchy,
                            variableId = variable.id
                        )

                        if (signal != null) {
                            // validate signal state and expiry
                            val isSignalFresh = SignalUtil.validateSignalStateAndFreshness(
                                signal = signal,
                                freshnessDurationInMillis = variable.freshnessDurationInMillis
                            )

                            if (isSignalFresh) {
                                selectedSignal = signal
                                return@hierarchiesEvaluator
                            }
                        }
                        // in case of territory (cluster) available, return regardless of signal found or not
                        if(it.second == LocationType.CLUSTER) return@hierarchiesEvaluator
                    }
                }
            }
        } catch (ex: UdaanServiceException) {
            exceptionMessage = ex.message
            resolverLogic = ResolverLogic.NO_VALUE_AS_DOWNSTREAM_SVC_ERROR
        } catch (ex: Exception) {
            exceptionMessage = ex.message
        }

        val finalValue = if (selectedSignal != null) {
            selectedSignal!!.signalData
        } else {
            resolverLogic = if (exceptionMessage != null) {
                ResolverLogic.DEFAULT_VALUE_AS_EXCEPTION_OCCURRED
            } else {
                ResolverLogic.DEFAULT_VALUE_AS_SIGNAL_ABSENT_OR_EXPIRED
            }
            variable.defaultValue
        }

        val resolvedValue = ResolvedValue(
            success = (exceptionMessage == null && finalValue != null),
            value = finalValue,
            referenceSignalId = selectedSignal?.referenceId,
            resolverLogic = resolverLogic,
            exception = exceptionMessage,
            metadata = selectedSignal?.metadata,
            lastRefreshedAt = selectedSignal?.updatedAt
        )

        return Pair(variable.id, resolvedValue)
    }
}
