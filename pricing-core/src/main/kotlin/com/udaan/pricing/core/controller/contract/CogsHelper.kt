package com.udaan.pricing.core.controller.contract

import com.google.inject.Inject
import com.udaan.catalog.model.VerticalCategory
import com.udaan.catalog.models.ModelV2
import com.udaan.catalog.models.ModelV2.TradeListing
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.core.models.common.CogsDetails
import com.udaan.pricing.core.helpers.CatalogHelper
import com.udaan.pricing.core.helpers.PreferredWarehouseHelper
import com.udaan.pricing.core.helpers.PricingSignalsHelper
import com.udaan.pricing.core.svcinterfaces.CatalogSvcInterface
import com.udaan.pricing.core.svcinterfaces.FpCatalogSvcInterface
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import java.math.BigDecimal

class CogsHelper @Inject constructor(
    private val catalogSvcInterface: CatalogSvcInterface,
    private val fpCatalogSvcInterface: FpCatalogSvcInterface,
    private val catalogHelper: CatalogHelper,
    private val pricingSignalsHelper: PricingSignalsHelper,
    private val preferredWarehouseHelper: PreferredWarehouseHelper
) {

    private val log by logger()

    /**
     * Fetches the COGS unit price for a category.
     */
    suspend fun fetchCogsUnitPriceForCategory(
        catalogEntityId: String,
        buyerOrgId: String,
        verticalCategory: VerticalCategory?,
        catalogEntity: CatalogEntityType,
        activeCityListings: List<TradeListing>
    ): CogsDetails? {
        return when (catalogEntity) {
            CatalogEntityType.PRODUCT_GROUP_ID -> {
                fetchCogsPriceForGroupId(catalogEntityId, buyerOrgId, verticalCategory, activeCityListings)
            }
            CatalogEntityType.LISTING_ID -> {
                fetchCogsPriceForListingId(catalogEntityId, buyerOrgId, verticalCategory)
            }
            else -> {
                log.info("Unsupported catalog entity type to fetch cogs : $catalogEntity")
                null
            }
        }
    }


    /**
     * Fetches the COGS price for a listing.
     */
    private suspend fun fetchCogsPriceForListingId(
        listingId: String,
        buyerOrgId: String,
        verticalCategory: VerticalCategory?
    ): CogsDetails? {
        val listingDetails = catalogSvcInterface.getTradeListingMinimal(listingId)
        return when (verticalCategory) {
            VerticalCategory.STAPLES -> {
                val groupId = fpCatalogSvcInterface.fetchGroupIdFromListingId(listingId)
                groupId?.let {
                    fetchCogsUnitPriceForStaples(groupId, listingDetails, verticalCategory, buyerOrgId)
                }?.let {
                    CogsDetails(
                        catalogEntity = CatalogEntityType.LISTING_ID,
                        catalogEntityId = listingId,
                        listingDetails = listingDetails,
                        salesUnitId = catalogHelper.fetchActiveSalesUnits(listingDetails).map { it.salesUnitId }
                            .first(),
                        cogsUnitPriceWithOutTax = it
                    )
                }
            }
            VerticalCategory.FMCG -> {
                fetchCogsUnitPriceForFmcg(listingDetails)?.let {
                    CogsDetails(
                        catalogEntity = CatalogEntityType.LISTING_ID,
                        catalogEntityId = listingId,
                        listingDetails = listingDetails,
                        salesUnitId = it.salesUnitId,
                        cogsUnitPriceWithOutTax = it.cogsUnitPriceWithOutTax
                    )
                }
            }
            else -> {
                log.info("Unsupported vertical category type to fetch cogs : $verticalCategory")
                null
            }
        }
    }

    /**
     * Fetches the COGS price for a product group.
     */
    private suspend fun fetchCogsPriceForGroupId(
        groupId: String,
        buyerOrgId: String,
        verticalCategory: VerticalCategory?,
        activeCityListings: List<TradeListing>
    ): CogsDetails? {
        return when (verticalCategory) {
            VerticalCategory.STAPLES -> {
                activeCityListings.forEach { listingDetails ->
                    val cogsUnitPrice =
                        fetchCogsUnitPriceForStaples(groupId, listingDetails, verticalCategory, buyerOrgId)
                    if (cogsUnitPrice != null && cogsUnitPrice > BigDecimal.ZERO) {
                        return CogsDetails(
                            catalogEntity = CatalogEntityType.PRODUCT_GROUP_ID,
                            catalogEntityId = groupId,
                            listingDetails = listingDetails,
                            salesUnitId = catalogHelper.fetchActiveSalesUnits(listingDetails).map { it.salesUnitId }
                                .first(),
                            cogsUnitPriceWithOutTax = cogsUnitPrice
                        )
                    }
                }
                null
            }
            VerticalCategory.FMCG -> {
                activeCityListings.forEach { listingDetails ->
                    val cogsDetailsForFmcg = fetchCogsUnitPriceForFmcg(listingDetails)
                    if (cogsDetailsForFmcg?.cogsUnitPriceWithOutTax != null) {
                        return CogsDetails(
                            catalogEntity = CatalogEntityType.PRODUCT_GROUP_ID,
                            catalogEntityId = groupId,
                            listingDetails = listingDetails,
                            salesUnitId = cogsDetailsForFmcg.salesUnitId,
                            cogsUnitPriceWithOutTax = cogsDetailsForFmcg.cogsUnitPriceWithOutTax
                        )
                    }
                }
                null
            }
            else -> {
                log.info("Unsupported vertical category type to fetch cogs : $verticalCategory")
                null
            }
        }
    }

    /**
     * Fetches the COGS unit price for Staples category.
     */
    private suspend fun fetchCogsUnitPriceForStaples(
        groupId: String,
        listingDetails: TradeListing,
        verticalCategory: VerticalCategory,
        buyerOrgId: String
    ): BigDecimal? {
        val salesUnits = catalogHelper.fetchActiveSalesUnits(listingDetails = listingDetails).map { it.salesUnitId }
        if (salesUnits.isEmpty()) {
            return null
        }
        val preferredWarehouseId = preferredWarehouseHelper.getPreferredWarehouseIdForBuyerOrg(
            listingId = listingDetails.listingId,
            salesUnitIds = salesUnits,
            verticalCategory = verticalCategory,
            buyerOrgId = buyerOrgId
        )
        if (preferredWarehouseId == null) {
            log.warn("No preferred warehouse found for listing: ${listingDetails.listingId} and buyer $buyerOrgId")
            return null
        }
        return pricingSignalsHelper.getStaplesCogsForProductGroup(groupId, preferredWarehouseId)
    }

    /**
     * Fetches the COGS unit price for FMCG category.
     */
    private suspend fun fetchCogsUnitPriceForFmcg(
        listingDetails: TradeListing
    ): CogsDetailsForFmcg? {
        catalogHelper.fetchActiveSalesUnits(listingDetails).map { salesUnit ->
            val assortment = salesUnit.numItemsAssortment.coerceAtLeast(1)
            val cogsPrice = pricingSignalsHelper.getFmcgCogsForListing(
                listingDetails = listingDetails,
                salesUnitId = salesUnit.salesUnitId
            )
            val cogsUnitPrice = cogsPrice?.divideWithScale(assortment.toBigDecimal())
            if (cogsUnitPrice != null && cogsUnitPrice > BigDecimal.ZERO) {
                return CogsDetailsForFmcg(
                    listingDetails = listingDetails,
                    salesUnitId = salesUnit.salesUnitId,
                    cogsUnitPriceWithOutTax = cogsUnitPrice
                )
            }
        }
        return null
    }

    data class CogsDetailsForFmcg(
        val listingDetails: ModelV2.TradeListing,
        val salesUnitId: String,
        val cogsUnitPriceWithOutTax: BigDecimal?
    )
}