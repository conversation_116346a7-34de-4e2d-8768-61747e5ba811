package com.udaan.pricing.core.managers.signals

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.dao.signals.SignalRepository
import com.udaan.pricing.core.utils.signals.SignalUtil
import com.udaan.pricing.core.utils.signals.getSuspended
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.variable.VariableId
import com.udaan.resources.cache.RedisCache2
import javax.inject.Named

@Singleton
class SignalReadManager @Inject constructor(
    private val signalRepository: SignalRepository,
    @Named(NamedConstants.Caches.SIGNALS) private val signalsCache: RedisCache2<Signal?>
) {
    companion object {
        private val logger by logger()
    }

    suspend fun getSignalForEntityLocationAndVariableId(
        catalogEntity: String,
        locationValue: String,
        variableId: VariableId
    ): Signal? {
        val id = SignalUtil.getSignalId(catalogEntity, locationValue, variableId.name)
        return signalsCache.getSuspended(id) {
            signalRepository.getSignalByIdAndPartitionKey(
                id = id,
                partitionKey = SignalUtil.getSignalPartitionKey(catalogEntity, locationValue)
            )
        }
    }

    suspend fun getSignalByReferenceId(
        referenceId: String
    ): Signal? {
        return signalRepository.getSignalByReferenceId(referenceId)
    }

    /**
     * Don't call this method in service api calls, these methods are used only for cron jobs
     */
    suspend fun getSignalsToMarkExpired(): Collection<Signal> {
        return signalRepository.getSignalsToMarkExpired()
    }

    /**
     * Don't call this method in service api calls, these methods are used only for cron jobs
     */
    suspend fun getSignalsForVariable(variableId: VariableId): Collection<Signal> {
        return signalRepository.getSignalsForVariable(variableId.name)
    }

    suspend fun getAllSignalsForLocations(
        catalogEntity: String,
        variableId: VariableId,
        locationValues: List<String>
    ): List<Signal> {
        return locationValues.parallelMap {
            getSignalForEntityLocationAndVariableId(
                catalogEntity = catalogEntity,
                locationValue = it,
                variableId = variableId
            )
        }.filterNotNull()
    }
}
