package com.udaan.pricing.core.utils.signals

import com.udaan.instrumentation.TelemetryScope
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.await

fun <T> T.launchOnIO(block: suspend () -> Unit): T {
    TelemetryScope.launch(Dispatchers.IO) {
        block.invoke()
    }
    return this
}

internal suspend fun <V> RedisCache2<V>.getSuspended(
    k: String,
    suspendableLoader: suspend (String) -> V
): V? {
    val loader = { k: String ->
        TelemetryScope.future {
            suspendableLoader(k)
        }
    }
    return get(k, loader).await()
}
