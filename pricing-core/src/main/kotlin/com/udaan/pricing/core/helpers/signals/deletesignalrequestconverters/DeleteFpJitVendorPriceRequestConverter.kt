package com.udaan.pricing.core.helpers.signals.deletesignalrequestconverters

import com.udaan.pricing.signaldeletion.DeleteFpJitVendorPriceRequest
import com.udaan.pricing.signaldeletion.DeletionReason
import com.udaan.pricing.signaldeletion.SignalDeletionInfo
import com.udaan.pricing.variable.VariableId


class DeleteFpJitVendorPriceRequestConverter: DeleteSignalRequestConverter<DeleteFpJitVendorPriceRequest>() {

    override suspend fun convert(deleteSignalRequest: DeleteFpJitVendorPriceRequest): List<SignalDeletionInfo> {

        return listOf(
            SignalDeletionInfo(
                catalogEntity = deleteSignalRequest.groupId.uppercase(),
                locationValue = deleteSignalRequest.warehouseId.uppercase(),
                signalType = VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT.name,
                deletionReason = DeletionReason.DEACTIVATION,
                deletedBy = "SYSTEM"
            )
        )
    }
}
