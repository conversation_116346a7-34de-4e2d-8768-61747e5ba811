package com.udaan.pricing.core.utils.signals

import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.GenericValue
import com.udaan.pricing.variable.AllowedValuesConstraint
import com.udaan.pricing.variable.ListValuesConstraint
import com.udaan.pricing.variable.RangeValuesConstraint
import com.udaan.pricing.variable.Variable

object VariableUtil {
    fun validateValuesForVariableAsPerConstraint(
        variableAllowedValuesConstraint: AllowedValuesConstraint,
        valueToValidate: GenericValue
    ) {
        variableAllowedValuesConstraint.let { allowedValuesConstraint ->
            when (allowedValuesConstraint) {
                is RangeValuesConstraint -> {
                    when (valueToValidate) {
                        is BigDecimalValue -> {
                            val value = valueToValidate.value
                            val min = allowedValuesConstraint.minValue
                            val max = allowedValuesConstraint.maxValue
                            require(value >= (min as BigDecimalValue).value && value <= (max as BigDecimalValue).value) {
                                "Signal value $value is out of allowed range " +
                                        "[${min.value}, ${(max as BigDecimalValue).value}]"
                            }
                        }

                        else -> throw IllegalArgumentException("Unsupported signal data type for range validation.")
                    }
                }

                is ListValuesConstraint -> {
                    when (valueToValidate) {
                        is BigDecimalValue -> {
                            val value =  valueToValidate.value
                            val allowedBigDecimalValues = allowedValuesConstraint.value.map {
                                (it as BigDecimalValue).value
                            }
                            /**
                             * BigDecimal 1.000 is not equal to BigDecimal 1, so you have to compare numerical value.
                             */
                            require(allowedBigDecimalValues.any { value.compareTo(it) == 0 }) {
                                "Signal value $valueToValidate is not in the allowed list: $allowedBigDecimalValues"
                            }
                        }

                        else -> {
                            require(allowedValuesConstraint.value.contains(valueToValidate)) {
                                "Signal value $valueToValidate is not in the allowed list: ${allowedValuesConstraint.value.joinToString()}"
                            }
                        }
                    }
                }
            }
        }
    }

    fun Variable.validateVariableConstraintsAndDefault() {
        val resolvedValueClassSubtype = this.resolvedValueType.typeClass
        allowedValuesConstraint?.let {
            when (it) {
                is RangeValuesConstraint -> {
                    val isRangeDataTypeValid = (it.minValue::class == BigDecimalValue::class &&
                            it.maxValue::class == BigDecimalValue::class)
                    require(isRangeDataTypeValid) {
                        "Only BigDecimalSignalData are supported for RangeData"
                    }
                }

                is ListValuesConstraint -> {
                    val isListDataTypeValid = it.value.all { value -> value::class == resolvedValueClassSubtype }

                    require(isListDataTypeValid) {
                        "Allowed values data type (${it::class}) must match the variable data type - $resolvedValueClassSubtype. "
                    }
                }
            }
        }

        defaultValue?.let { default ->
            require(resolvedValueClassSubtype == default::class) {
                "Default value dataType and variable dataType should be matching!"
            }

            allowedValuesConstraint?.let {
                validateValuesForVariableAsPerConstraint(
                    variableAllowedValuesConstraint = it,
                    valueToValidate = default
                )
            }
        }
    }
}
