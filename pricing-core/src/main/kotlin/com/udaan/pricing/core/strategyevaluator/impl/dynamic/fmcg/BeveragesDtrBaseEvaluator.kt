package com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg

import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.variable.VariableId
import java.math.BigDecimal

internal object BeveragesDtrBaseEvaluator : Evaluator {

    /**
     * FMCG_BEVS_MOP_WT_PAISA_UNIT * (1 + BEVS_MARGIN_BPS / 10000)
     */
    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        ValidationUtils.validateInputs(data.strategy, data.inputs)
        val bevsMop = (VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.BEVS_MOP_WT_PAISA_UNIT
        ) as? BigDecimalValue)?.value
            ?: throw IllegalArgumentException("BEVS_MOP_WT_PAISA_UNIT is mandatory for BEVS DTR evaluator")

        val bevsMarginBps = (VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.BEVS_MARGIN_BPS
        ) as? BigDecimalValue)?.value
            ?: throw IllegalArgumentException("BEVS_MARGIN_BPS is mandatory for BEVS DTR evaluator")

        val calculatedPrice = bevsMop.multiplyWithScale(
            (BigDecimal(1) + bevsMarginBps.divideWithScale(BigDecimal(10000)))
        )
        return EvaluatorOutput(BigDecimalValue(calculatedPrice), emptyMap())
    }
}
