package com.udaan.pricing.core.helpers.pricingstrategy.impl

import com.google.inject.Inject
import com.udaan.common.loc.PincodeCityDataset
import com.udaan.common.loc.PostalDB
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.ContextualPriceRequest
import com.udaan.pricing.GeoLocationType
import com.udaan.pricing.PriceForListing
import com.udaan.pricing.core.controller.GeoLocationBasePriceController
import com.udaan.pricing.core.helpers.BuyerTagHelper
import com.udaan.pricing.core.helpers.pricingstrategy.Strategy
import com.udaan.pricing.core.models.CatalogEntityContext
import com.udaan.proto.models.ModelV1
import kotlinx.coroutines.Deferred

class GeoLocationBasePriceStrategy @Inject constructor(
    private val geoLocationBasePriceController: GeoLocationBasePriceController,
    private val pincodeDataset: PincodeCityDataset,
    private val buyerTagHelper: BuyerTagHelper
): Strategy() {

    companion object {
        private val logger by logger()
    }

    override suspend fun getPrice(
        catalogEntityContext: CatalogEntityContext,
        contextualPriceRequest: ContextualPriceRequest?,
        fetchInactive: Boolean,
        cluster: List<String>?,
        preferredWarehouseId: String?,
        servingWarehouseId: String?,
        buyerOrgUnit: ModelV1.OrgUnit?,
        mappedBenchmarkListingGuardrailPriceDeferred: Deferred<PriceForListing?>?
    ): List<PriceForListing> {
        val pincode = contextualPriceRequest?.buyerContext?.pincode
        val pincodeDetails = pincode?.let {
            pincodeDataset.lookup(it)
        }

        // derive buyer based cluster if applicable
        val buyerCohort = if (contextualPriceRequest?.buyerContext?.orgId != null) {
            val buyerOrgId = contextualPriceRequest.buyerContext!!.orgId
            // we are picking static buyer cohort (which has least priority) here as base price is derived basis that for now
            // @todo- this will lead to different price in legacy vs new system if custom cohorts with higher priority will start getting separate plans with different pricing strategy in new system. We need to migrate out of legacy before that.
            val derivedBuyerCohort = buyerTagHelper.getBuyerTagsApplicable(buyerOrgId).minByOrNull { it.cohortPriority }?.buyerCohort
            if (derivedBuyerCohort != null && !preferredWarehouseId.isNullOrEmpty()) {
                (preferredWarehouseId + "_" + derivedBuyerCohort).lowercase()
            } else null
        } else null

        val buyerCohortLocation = buyerCohort?.let { listOf(it) }

        return if (pincode == null || pincodeDetails == null) {
            emptyList()
        } else {
            getListingGeoPricesGroupedBySalesUnit(
                catalogEntityContext = catalogEntityContext,
                pincodeDetails = pincodeDetails,
                cluster = (cluster.orEmpty() + buyerCohortLocation.orEmpty() + preferredWarehouseId.orEmpty()).filter { it.isNotEmpty() },
                buyerCohortLocation = buyerCohortLocation
            )
        }
    }

    private suspend fun getListingGeoPricesGroupedBySalesUnit(
        catalogEntityContext: CatalogEntityContext,
        pincodeDetails: PostalDB.Tuple,
        cluster: List<String>?,
        buyerCohortLocation: List<String>? = null
    ): List<PriceForListing> {
        val locationDataFromPincode = listOf(
            Pair(GeoLocationType.CITY, pincodeDetails.city.lowercase()),
            Pair(GeoLocationType.STATE, pincodeDetails.state.lowercase())
        )

        val clusterData = cluster?.map {
            listOf(
                Pair(GeoLocationType.CLUSTER, it.lowercase()),
                Pair(GeoLocationType.WAREHOUSE, it.lowercase())
            )
        }?.flatten() ?: emptyList()

        return geoLocationBasePriceController.getApplicableGeoBasePrices(
            listingId = catalogEntityContext.listingId,
            salesUnitId = catalogEntityContext.salesUnitId,
            locationData = clusterData + locationDataFromPincode,
            buyerCohortLocation = buyerCohortLocation
        )
    }
}
