package com.udaan.pricing.core.models.contracts

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.common.utils.getCurrentMillis

@JsonIgnoreProperties(ignoreUnknown = true)
data class Contract(
    val buyerOrgId: String,
    val catalogEntityId: String,
    val contractCatalogEntity: ContractCatalogEntity,
    val price: ContractPrice,
    val type: ContractType = ContractType.EXPIRY,
    val duration: ContractDuration,
    val state: ContractState = ContractState.ACTIVE,
    val referenceId: String, // a new id would be generated on every contract update.
    val lastRefreshedAt: Long,
    val lastRefreshedBy: String,
    val metadata: ContractMetadata,
    val quoteInfo: ContractQuoteInfo? = null
) {
    val id = getContractId(buyerOrgId, catalogEntityId)

    companion object {
        fun getContractId(buyerOrgId: String, catalogEntityId: String): String {
            return ("$buyerOrgId:$catalogEntityId").uppercase()
        }
    }
}


enum class ContractState {
    ACTIVE,
    DELETED,
    EXPIRED
}

enum class ContractType {
    LOCK_IN,
    EXPIRY
}


enum class ContractCatalogEntity {
    LISTING_ID,
    PRODUCT_GROUP_ID;

    fun getPriority(): Int {
        return when (this) {
            LISTING_ID -> 0
            PRODUCT_GROUP_ID -> 1
        }
    }
}

data class ContractDuration(
    val startTime: Long,
    val endTime: Long,
    val lockInTime: Long = 0L // Will be 0 in case of contract expiry.
)


@JsonIgnoreProperties(ignoreUnknown = true)
data class ContractMetadata @JvmOverloads constructor(
    val buyerNumber: String,
    val reason: ContractReason,
    val volumeCommitted: Long = 0L, // will be 0, in case of LIQUIDATION and other contracts
    val city: String? = null,
    val volumeOrdered: Long = 0L,
    val volumeOrderedRefreshedTill: Long? = null
)

data class ContractQuoteInfo(
    val quotePriceInPaisa: Long? = null,
    val cogsUnitPriceInPaisa: Long? = null,
    val quoteMrpInMarkdownBps: Long? = null,
    val quoteRefreshedAt: Long = getCurrentMillis(),
    val listingIdUsedForQuote: String,
    val bestCompPriceInPaisa: Long? = null,
    val bestCompName: String? = null
)


enum class ContractReason {
    VOLUME,
    LIQUIDATION,
    UNKNOWN
}