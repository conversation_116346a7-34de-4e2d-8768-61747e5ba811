package com.udaan.pricing.core.helpers.signals.rawsignalinputconverters

import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.signalcreation.CompOfflineSignalInput
import com.udaan.pricing.signals.GenericMetadata
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState
import com.udaan.pricing.variable.VariableId

class CompOfflineInputConverter: RawSignalInputConverter<CompOfflineSignalInput>() {
    companion object {
        private val logger by logger()
    }

    override suspend fun convert(rawSignalInput: CompOfflineSignalInput): List<Signal> {
        val signalEntity = rawSignalInput.productGroupId

        val convertedSignal = Signal(
            catalogEntity = signalEntity.uppercase(),
            catalogEntityType = CatalogEntityType.PRODUCT_GROUP_ID,
            variableId = VariableId.OFFLINE_COMPETITION_WT_PAISA_UNIT.name,
            signalData = BigDecimalValue(
                value = rawSignalInput.value.toBigDecimal(),
            ),
            metadata = GenericMetadata(emptyMap()),
            location = Location(
                locationType = rawSignalInput.location.locationType,
                locationValue = rawSignalInput.location.locationValue.uppercase()
            ),
            state = SignalState.ACTIVE,
            createdBy = "OFFLINE_CRAWL_SYSTEM",
            updatedBy = "OFFLINE_CRAWL_SYSTEM"
        )

        logger.info("converted signal {}", convertedSignal)
        return listOf(convertedSignal)
    }

}
