package com.udaan.pricing.core.models.contracts

import com.udaan.common.utils.getCurrentMillis
import com.udaan.pricing.BasicPrice
import com.udaan.pricing.PUInfo
import com.udaan.pricing.PriceInPaisa
import com.udaan.pricing.QtyBasedPrice
import com.udaan.pricing.contracts.ContractQuoteResponse
import java.util.concurrent.*

fun Contract.isValid(): Boolean {
    val currentEpoch = getCurrentMillis()
    return this.state == ContractState.ACTIVE &&
            currentEpoch >= this.duration.startTime && currentEpoch <= this.duration.endTime
}

fun Contract.expiryDurationInDays(): Int {
    return ((this.duration.endTime - this.duration.startTime) / TimeUnit.DAYS.toMillis(1)).toInt()
}

fun Contract.lockInDurationInDays(): Int {
    return ((this.duration.lockInTime - this.duration.startTime) / TimeUnit.DAYS.toMillis(1)).toInt()
}

fun ContractLadder.toQtyBasedPrice(price: Long, puInfo: PUInfo?): QtyBasedPrice {
    return QtyBasedPrice(
        minQty = this.minQuantity,
        maxQty = this.maxQuantity,
        priceInPaisa = PriceInPaisa(price, price, price, BasicPrice(price, price, price), null),
        pricePerKgInPaisa = PriceInPaisa(price, price, price, BasicPrice(price, price, price), null),
        taxableAmountPaise = price,
        packagingUnit = puInfo
    )
}

fun ContractQuoteResponse.toContractQuoteInfo(): ContractQuoteInfo {
    return ContractQuoteInfo(
        quotePriceInPaisa = this.quotePriceInPaisa,
        cogsUnitPriceInPaisa = this.cogsUnitPriceInPaisa,
        quoteMrpInMarkdownBps = this.mrpMarkDownBps,
        quoteRefreshedAt = getCurrentMillis(),
        listingIdUsedForQuote = this.referenceListingId,
        bestCompPriceInPaisa = this.bestCompetitorQuote.priceInPaisa,
        bestCompName = this.bestCompetitorQuote.competitor
    )
}
