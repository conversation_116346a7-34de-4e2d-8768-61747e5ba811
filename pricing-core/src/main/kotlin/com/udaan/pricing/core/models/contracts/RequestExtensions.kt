package com.udaan.pricing.core.models.contracts

import com.udaan.common.utils.getCurrentMillis
import com.udaan.pricing.contracts.ContractRequest
import com.udaan.pricing.contracts.ContractPriceRequestType
import com.udaan.pricing.contracts.ContractQuoteRequest
import com.udaan.pricing.core.utils.generateId

fun ContractRequest.toContract(
    buyerOrgId: String,
    buyerNumber: String,
    duration: ContractDuration
): Contract {
    return Contract(
        buyerOrgId = buyerOrgId,
        catalogEntityId = this.contractCatalogEntityId,
        contractCatalogEntity = ContractCatalogEntity.valueOf(this.contractCatalogEntity),
        duration = duration,
        type = ContractType.valueOf(this.type),
        lastRefreshedAt = getCurrentMillis(),
        lastRefreshedBy = this.requestedBy,
        metadata = ContractMetadata(
            buyerNumber = buyerNumber,
            reason = ContractReason.valueOf(this.reason),
            volumeCommitted = this.volumeCommitted,
            city = this.city
        ),
        price = when (this.priceType) {
            ContractPriceRequestType.MRP_MARKDOWN_BPS -> {
                ContractLadderMrpMarkdownBps(
                    value = this.price.value.map {
                        ContractLadder(it.minQuantity, it.maxQuantity, it.ladderValue)
                    }
                )
            }
            ContractPriceRequestType.ABSOLUTE_LADDER_PRICE -> {
                ContractLadderPrice(
                    value = this.price.value.map {
                        ContractLadder(it.minQuantity, it.maxQuantity, it.ladderValue)
                    }
                )
            }
        },
        referenceId = generateId("CON")
    )
}


fun Contract.toContractQuoteRequest(defaultCity: String): ContractQuoteRequest {
    return ContractQuoteRequest(
        buyerOrgId = buyerOrgId,
        city = metadata.city ?: defaultCity,
        contractCatalogEntityId = catalogEntityId,
        contractCatalogEntity = contractCatalogEntity.name,
        volumeCommitted = metadata.volumeCommitted,
        targetUnitPrice = null,
        requestedBy = lastRefreshedBy
    )
}