package com.udaan.pricing.core.strategyevaluator.impl

import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.DynamicEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.formula.FormulaEvaluator
import com.udaan.pricing.strategy.StrategyType

object EvaluatorFactory {

    private val log by logger()
    private const val SIGNAL_INPUT_SUFFIX = "_INPUT"
    private const val INPUT_REFERENCE_SUFFIX = "_REFERENCE"

    private fun evaluate(evaluatorRequestContext: EvaluatorRequestContext): EvaluatorOutput? {
        val evaluator = when (evaluatorRequestContext.strategy.type) {
            StrategyType.FORMULA -> FormulaEvaluator
            StrategyType.DYNAMIC -> DynamicEvaluator
        }
        return evaluator.evaluate(evaluatorRequestContext)
    }

    fun evaluate(evaluatorRequestContexts: List<EvaluatorRequestContext>): EvaluatorOutput? {
        var output: EvaluatorOutput? = evaluatorRequestContexts.firstOrNull()?.previousOutput
        val outputMetadata = mutableMapOf<String, String>()
        outputMetadata.putAll(output?.metadata ?: emptyMap())

        evaluatorRequestContexts.map { evaluatorRequestConfig ->
            log.info(
                "Evaluating {}, with inputs {}",
                evaluatorRequestConfig.strategy.name, evaluatorRequestConfig.inputs
            )
            val strategyInputs = evaluatorRequestConfig.inputs.associate {
                it.variableId.toString() to it.value.toString()
            }
            val strategyInputReference = evaluatorRequestConfig.inputs.mapNotNull { input ->
                input.referenceId?.let {
                    input.variableId.toString() + INPUT_REFERENCE_SUFFIX to input.referenceId.toString()
                }
            }.toMap()
            outputMetadata[evaluatorRequestConfig.strategy.name + SIGNAL_INPUT_SUFFIX] =
                output?.output?.toString() ?: ""
            outputMetadata.putAll(strategyInputs)
            outputMetadata.putAll(strategyInputReference)
            // evaluating current strategy while measuring evaluation time
            val currentStrategyOutput = evaluate(
                evaluatorRequestConfig.copy(previousOutput = output)
            )
            log.info(
                "Evaluated {}, with output {}",
                evaluatorRequestConfig.strategy.name, currentStrategyOutput?.output
            )
            // allocating current strategy evaluation metadata to fun scope metadata (to aggregate metadata at each step)
            currentStrategyOutput?.metadata?.let { metadata ->
                outputMetadata.putAll(metadata)
            }
            outputMetadata[evaluatorRequestConfig.strategy.name + "_OUTPUT"] =
                currentStrategyOutput?.output?.toString() ?: ""
            // allocating current strategy output to fun scope output (to be fed as previousOutput input to next strategy)
            output = currentStrategyOutput
        }

        return output?.copy(metadata = outputMetadata)
    }

}
