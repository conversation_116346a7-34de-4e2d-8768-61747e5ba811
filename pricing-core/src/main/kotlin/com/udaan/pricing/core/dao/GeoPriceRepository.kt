package com.udaan.pricing.core.dao

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.google.inject.Singleton
import com.microsoft.azure.documentdb.ConnectionPolicy
import com.microsoft.azure.documentdb.ConsistencyLevel
import com.microsoft.azure.documentdb.DocumentClient
import com.microsoft.azure.documentdb.bulkexecutor.DocumentBulkExecutor
import com.udaan.common.utils.kotlin.logger
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.FetchGeoPricingRequest
import com.udaan.pricing.GeoPricing
import com.udaan.pricing.GeoPricingReq
import com.udaan.pricing.QueryGeoPricingReq
import com.udaan.pricing.core.constants.CosmosDbConfig.COSMOS_DB_NAME
import com.udaan.pricing.core.constants.CosmosDbConfig.GEO_ADMIN_TABLE
import com.udaan.resources.ResourceBuilder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.withContext
import org.apache.commons.lang3.tuple.MutablePair

@Singleton
class GeoPriceRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {
    private val documentDbDao by lazy {
        CosmosDbDao(
            configKey = "pricing",
            databaseName = COSMOS_DB_NAME,
            containerName = GEO_ADMIN_TABLE
        ) { builder ->
            builder.connectionSharingAcrossClientsEnabled(true)
        }
    }

    private val documentBulkExecutor by lazy {
        val documentDbBuilder = ResourceBuilder.documentClient("pricing")

        val documentClient = DocumentClient(
            documentDbBuilder.documentDBHost,
            documentDbBuilder.accessKey,
            ConnectionPolicy.GetDefault(),
            ConsistencyLevel.Session
        )

        val collectionLink = "/dbs/$COSMOS_DB_NAME/colls/$GEO_ADMIN_TABLE"
        val collection = documentClient.readCollection(collectionLink, null).resource

        DocumentBulkExecutor.builder().from(
            documentClient,
            COSMOS_DB_NAME,
            GEO_ADMIN_TABLE,
            collection.partitionKey,
            10 * 1000
        ).build()
    }

    private val log by logger()

    /**
     * Dummy function solely used for client initialisation while starting the service
     */
    suspend fun initialise() {
        documentDbDao.findItem("ID1")
    }

    suspend fun createGeoPricing(geoPricing: GeoPricing): GeoPricing? {
        return documentDbDao.createOrUpdateItem(geoPricing.toDocument()).toGeoPricing()
    }

    suspend fun deleteItems(items: Collection<GeoPricing>) {
        withContext(Dispatchers.IO) {
            documentBulkExecutor.deleteAll(items.map {
                MutablePair(it.orgId, it.id)
            })
        }
    }

    suspend fun getGeoPricingById(geoPriceId: String, orgId: String): GeoPricing? {
        return documentDbDao.getItem(geoPriceId, orgId)?.toGeoPricing()
    }

    suspend fun fetchDataByTimeRange(fromTimeStamp: Long, toTimeStamp: Long): List<GeoPricing> {
        return documentDbDao.queryItems(
            queryName = "get-geo-prices-by-time-range",
            querySpec = makeSqlQuerySpec(
                """
                    select * from c where
                    c._ts >= @fromTimeStamp and c._ts <= @toTimeStamp
                """.trimIndent(),
                "@fromTimeStamp" to fromTimeStamp,
                "@toTimeStamp" to toTimeStamp
            )
        ).toList().map { it.toGeoPricing() }
    }

    suspend fun fetchDataCountByTimeRange(fromTimeStamp: Long, toTimeStamp: Long): Long {
        return documentDbDao.queryItems(
            queryName = "get-geo-prices-count-between-time-range",
            querySpec = makeSqlQuerySpec(
                """
                    select count(1) from c where
                    c._ts >= @fromTimeStamp and c._ts <= @toTimeStamp
                """.trimIndent(),
                "@fromTimeStamp" to fromTimeStamp,
                "@toTimeStamp" to toTimeStamp
            )
        ).toList().first().get("$1").asLong()
    }

    suspend fun getFilteredPricingAdmin(geoPricingReq: GeoPricingReq): List<GeoPricing> {
        val query = "select * from c where c.orgId = '${geoPricingReq.orgId}'" +
                " AND c.geoTypeId = '${geoPricingReq.geoTypeId}' ${getFilterClause(geoPricingReq)}"
        return documentDbDao.queryItems(
            queryName = "getFilteredPricingAdmin",
            querySpec = makeSqlQuerySpec(query.trimIndent())
        ).toList().map { it.toGeoPricing() }
    }

    suspend fun getGeoPricing(fetchGeoPricingRequest: FetchGeoPricingRequest): List<GeoPricing> {
        return documentDbDao.queryItems(
            queryName = "getGeoPricing",
            querySpec = makeSqlQuerySpec(
                """
                    SELECT * FROM c 
                    where c.orgId= '${fetchGeoPricingRequest.orgId}'
                    AND (${getPricingClause(fetchGeoPricingRequest)})
                    AND c.state IN (${fetchGeoPricingRequest.state.joinToString(",") { "'$it'" }})
                """.trimIndent()
            )
        ).toList().map { it.toGeoPricing() }
    }

    suspend fun queryGeoPricing(queryGeoPricingReq: QueryGeoPricingReq):List<GeoPricing>{
        return documentDbDao.queryItems(
            queryName = "getGeoPricingConsole",
            querySpec = makeSqlQuerySpec(
                """
                    select * from c
                    where ${queryPricingClause(queryGeoPricingReq)}
                """.trimIndent()
            )
        ).toList().map { it.toGeoPricing() }
    }

    private fun getFilterClause(geoPricingReq: GeoPricingReq):String{
        var clause = ""

        val AND_CLAUSE = " AND "

        clause+= AND_CLAUSE + "c.geoType= '${geoPricingReq.geoType}'"

        clause+= AND_CLAUSE + (geoPricingReq.listingId?.let { "c.listingId= '${geoPricingReq.listingId}'" } ?: "IS_NULL(c.listingId)")
        clause+= AND_CLAUSE + (geoPricingReq.vertical?.let { "c.vertical= '${geoPricingReq.vertical}'" } ?: "IS_NULL(c.vertical)")
        clause+= AND_CLAUSE + (geoPricingReq.salesUnitId?.let { "c.salesUnitId= '${geoPricingReq.salesUnitId}'" } ?: "IS_NULL(c.salesUnitId)")

        return clause
    }

    private fun getPricingClause(fetchGeoPricingRequest: FetchGeoPricingRequest): String{
        val clause = mutableListOf<String>()

        clause.add("c.listingId = '${fetchGeoPricingRequest.listingId}'")

        if (fetchGeoPricingRequest.vertical != null) {
            clause.add("c.vertical = '${fetchGeoPricingRequest.vertical}'")
        }

        clause.add("(c.listingId = null and c.vertical = null)")

        return if (clause.isEmpty()) return " " else {
            clause.joinToString(" OR ")
        }

    }

    /**
     * if (cascade == true) then returns all results else returns only for that level
     * Eg. If (cascade == true) and queries at Org Level records then it returns record for that listing as well
     */
    private fun queryPricingClause(getGeoPricingReq: QueryGeoPricingReq):String{
        val clause = mutableListOf<String>()

        getClause(getGeoPricingReq.listingId, getGeoPricingReq.cascadeDown).takeIf {
            it.isNotEmpty()
        }?.let {
            if (it !== "null" && getGeoPricingReq.cascadeUp)
                clause.add("c.listingId= $it OR c.listingId= null")
            else clause.add("c.listingId= $it")
        }

        getClause(getGeoPricingReq.vertical, getGeoPricingReq.cascadeDown).takeIf {
            it.isNotEmpty()
        }?.let {
            if (it !== "null" && getGeoPricingReq.cascadeUp)
                clause.add("c.vertical= $it OR c.vertical= null")
            else clause.add("c.vertical= $it")
        }

        getClause(getGeoPricingReq.orgId, getGeoPricingReq.cascadeDown).takeIf {
            it.isNotEmpty()
        }?.let {
            if (it !== "null" && getGeoPricingReq.cascadeUp)
                clause.add("c.orgId= $it OR c.orgId= null")
            else clause.add("c.orgId= $it")
        }

        return clause.joinToString(" AND ")
    }

    private fun getClause(clause: String?, cascade: Boolean): String {
        return when {
            clause != null -> "'$clause'"
            cascade.not() -> "null"
            else -> ""
        }
    }

    private fun GeoPricing.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toGeoPricing() = objectMapper.convertValue(this, GeoPricing::class.java)
}
