package com.udaan.pricing.core.dao.network

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.pricing.network.TerritoryHex
import com.udaan.pricing.network.TerritoryType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.jdbi.v3.core.Jdbi
import java.sql.Timestamp

/**
 * This class provides fun related to cosmos actions for demand_cluster container
 */
@Singleton
class TerritoryHexDao @Inject constructor(
    private val jdbi: Jdbi
) {

    companion object {
        private const val TABLE_NAME = "territory_hexes"
    }

    suspend fun createOrUpdate(territoryHex: TerritoryHex): TerritoryHex {
        return withContext(Dispatchers.IO) {
            jdbi.withHandle<Any, Exception> { handle ->
                handle.createUpdate("""
                    INSERT INTO $TABLE_NAME (
                        id, hex_id, territory_type, territory_id, status, resolution, created_at, updated_at
                    ) VALUES (
                        :id, :hex_id, :territory_type, :territory_id, :status, :resolution, :created_at, :updated_at
                    )
                """.trimIndent()
                )
                    .bind("id", territoryHex.id)
                    .bind("hex_id", territoryHex.hexId)
                    .bind("territory_type", territoryHex.territoryType.name)
                    .bind("territory_id", territoryHex.territoryId)
                    .bind("status", territoryHex.status.name)
                    .bind("resolution", territoryHex.resolution)
                    .bind("created_at", territoryHex.createdAt)
                    .bind("updated_at", Timestamp(System.currentTimeMillis()))
                    .execute()
            }
            return@withContext territoryHex
        }
    }

    suspend fun getActiveTerritory(territoryType: TerritoryType, hexId: String): TerritoryHex? {
        return withContext(Dispatchers.IO) {
            jdbi.withHandle<TerritoryHex, Exception> { handle ->
                handle.createQuery("""
                    SELECT * FROM $TABLE_NAME 
                    WHERE territory_type = :territory_type AND hex_id = :hex_id AND status = 'ACTIVE'
                """.trimIndent()
                )
                    .bind("territory_type", territoryType.name)
                    .bind("hex_id", hexId)
                    .mapTo(TerritoryHex::class.java)
                    .findFirst()
                    .orElse(null)
            }
        }
    }
}