package com.udaan.pricing.core.utils

import com.udaan.common.slack.SlackIncomingHook
import com.udaan.common.slack.SlackMessage
import kotlinx.coroutines.future.await
import org.asynchttpclient.Response

class SlackNotifier(private val slackUrl: String) {

    private val slackClient by lazy {
        SlackIncomingHook(
            slackUrl
        )
    }

    suspend fun sendMessage(notifyTo: String, message: String): Response? {
        return slackClient.sendText(SlackMessage("$notifyTo $message")).await()
    }
}
