package com.udaan.pricing.core.helpers.signals

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.Telemetry
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.core.cache.RedisHelper
import com.udaan.pricing.core.utils.signals.TelemetryUtil.timedRedis
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.variable.requestreponse.ResolvedValue
import io.lettuce.core.KeyValue
import java.time.Duration

@Singleton
class ResolvedValuesRedisHelper @Inject constructor(
    private val redisHelper: RedisHelper,
    private val objectMapper: ObjectMapper
) {

    companion object {
        private val LOG by logger()
    }

    private fun getTerritoriesCacheKeyComponent(
        territoryRefIds: List<String>
    ): String {
        return territoryRefIds.sorted().joinToString(separator = "::") { it.uppercase() }
    }

    private fun getListingLocationCacheKey(
        listingId: String,
        salesUnitId: String,
        location: Location,
        territoryRefIds: List<String>?
    ): String {
        return territoryRefIds?.let {
            val territoryComponent = getTerritoriesCacheKeyComponent(it)
            "signals::" + (listingId + "::" + salesUnitId + "::" + location.locationValue + "::" + territoryComponent).uppercase()
        } ?: ("signals::" + (listingId + "::" + salesUnitId + "::" + location.locationValue).uppercase())
    }

    private fun getProductGroupLocationCacheKey(
        productGroupId: String,
        location: Location,
        territoryRefIds: List<String>?
    ): String {
        return territoryRefIds?.let {
            val territoryComponent = getTerritoriesCacheKeyComponent(it)
            "signals::" + (productGroupId + "::" + location.locationValue + "::" + territoryComponent).uppercase()
        } ?: ("signals::" + (productGroupId + "::" + location.locationValue).uppercase())
    }

    suspend fun getListingSalesUnitLocationValues(
        listingId: String,
        salesUnitId: String,
        location: Location,
        variables: List<String>,
        territoryRefIds: List<String>?
    ): Map<VariableId, ResolvedValue> {
        val key = getListingLocationCacheKey(listingId, salesUnitId, location, territoryRefIds)
        return getFieldValuesForKey(key, variables)
    }

    suspend fun getProductGroupLocationValues(
        productGroupId: String,
        location: Location,
        variables: List<String>,
        territoryRefIds: List<String>?
    ): Map<VariableId, ResolvedValue> {
        val key = getProductGroupLocationCacheKey(productGroupId, location, territoryRefIds)
        return getFieldValuesForKey(key, variables)
    }

    suspend fun getFieldValuesForKey(
        key: String,
        fields: List<String>
    ): Map<VariableId, ResolvedValue> {
        return timedRedis(key, "HMGET") {
            val variables2Signals = redisHelper.getRedisHashValues(
                hashKey = key,
                fieldNames = fields
            )
                ?.mapNotNull { it.toKeySignalPair() }
                ?.toMap()
            // treat as cache-hit only if all variables are found in cache
            return@timedRedis if (variables2Signals?.size != fields.size)
                null
            else
                variables2Signals
        }.orEmpty()
    }

    /**
     * To key signal pair
     *
     * @return
     * <variable_id, signal?> if there's signal/null for a variable in cache
     * else null - cache miss/exception
     */
    private fun KeyValue<String, String>.toKeySignalPair(): Pair<VariableId, ResolvedValue>? {
        return try {
            Pair(VariableId.valueOf(this.key), objectMapper.readValue(this.value, ResolvedValue::class.java))
        } catch (nse: NoSuchElementException) {
            LOG.info("NoSuchElementException while trying to read ${this.key} from cache, considering cache miss")
            null
        } catch (e: Exception) {
            Telemetry.trackException(e)
            null
        }
    }

    suspend fun cacheListingSalesUnitLocationValues(
        listingId: String,
        salesUnitId: String,
        location: Location,
        resolvedVariableMap: Map<VariableId, ResolvedValue>,
        territoryRefIds: List<String>?
    ) {
        val cacheKey = getListingLocationCacheKey(listingId, salesUnitId, location, territoryRefIds)
        redisHelper.setHashValuesAndExpiryForKey(
            cacheKey = cacheKey,
            valueMap = convertResolvedVariableMap(resolvedVariableMap),
            expiryInSeconds = Duration.ofMinutes(5).seconds
        )
    }

    suspend fun cacheProductGroupLocationValues(
        productGroupId: String,
        location: Location,
        resolvedVariableMap: Map<VariableId, ResolvedValue>,
        territoryRefIds: List<String>?
    ) {
        val cacheKey = getProductGroupLocationCacheKey(productGroupId, location, territoryRefIds)
        redisHelper.setHashValuesAndExpiryForKey(
            cacheKey = cacheKey,
            valueMap = convertResolvedVariableMap(resolvedVariableMap),
            expiryInSeconds = Duration.ofMinutes(5).seconds
        )
    }

    /*
     * Addition of territoryRefIds in this func renders it moot for cache invalidation
     * of any territory enabled input, as this is called from invalidateSignals which is
     * further called when we create a new signal, or mark signal expired or delete signal.
     * In all these cases, we do not have any buyer context and hence no territoryRefIds.
     *
     * This is still fine as this was primarily implemented to invalidate cache for trading
     * inputs (manual input) and not SSC related inputs, which will work still as no territory
     * enabled inputs in trading strategies.
     *
     * @todo - if we want this to work for territory enabled inputs, we may need to fetch all
     *   territoryRefIds for the input in consideration and then invalidate cache for all of them. Needs thinking.
     */
    suspend fun invalidateProductGroupLocationValue(
        productGroupId: String,
        location: Location,
        variableId: String,
        territoryRefIds: List<String>?
    ) {
        val cacheKey = getProductGroupLocationCacheKey(productGroupId, location, territoryRefIds)
        redisHelper.delHashValuesInRedis(
            cacheKey = cacheKey,
            field = variableId
        )
    }

    private fun convertResolvedVariableMap(
        resolvedVariableMap: Map<VariableId, ResolvedValue>
    ): Map<String, String> {
        return resolvedVariableMap.map {
            it.key.name to objectMapper.writeValueAsString(it.value)
        }.toMap()
    }
}
