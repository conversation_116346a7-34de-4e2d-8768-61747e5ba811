package com.udaan.pricing.core.dao

import com.udaan.pricing.GeoLocationBasePrice
import com.udaan.pricing.GeoLocationBasePriceResponse
import com.udaan.pricing.GeoPricing
import com.udaan.pricing.QueryGeoPricingReq
import com.udaan.pricing.masterPolicyMap
import javax.ws.rs.BadRequestException

fun QueryGeoPricingReq.validate() {
    this.listingId
        ?: this.orgId
        ?: this.vertical
        ?: throw BadRequestException()
}

fun List<GeoPricing>.sortByPolicy(): List<GeoPricing> {
    return this.sortedByDescending {
        masterPolicyMap.getValue(it.geoType)
    }
}

fun List<GeoPricing>.filterRecords(vertical:String?): List<GeoPricing> {
    return when {
        this.any { it.listingId?.isNotEmpty() ?: false } -> this.filter { it.listingId?.isNotEmpty() ?: false }
        this.any { it.vertical?.isNotEmpty() ?: false } -> this.filter { it.vertical?.isNotEmpty()?: false && it.vertical == vertical  }
        else -> this
    }
}

fun GeoLocationBasePrice.toUpsertResponse(): GeoLocationBasePriceResponse =
        GeoLocationBasePriceResponse(
                id = this.id,
                listingId = this.listingId,
                qtyBasedPrice = this.qtyBasedPrice,
                locationTypeId = this.locationTypeId,
                locationType = this.locationType,
                saleUnitId = this.saleUnitId,
                state = this.state
        )
