package com.udaan.pricing.core.utils.signals

import com.fasterxml.jackson.databind.ObjectMapper
import com.udaan.common.utils.kotlin.logger
import org.apache.http.client.methods.HttpPost
import org.apache.http.entity.ContentType
import org.apache.http.entity.StringEntity
import org.apache.http.impl.client.HttpClients
import com.fasterxml.jackson.annotation.JsonProperty

object MSTeamsNotifier {

    private val httpClient = HttpClients.createDefault()
    private val LOG by logger()
    private val objectMapper = ObjectMapper()

    fun sendCard(channel: TeamChannel, message: TeamsMessage): Boolean {
        return try {
            val request = HttpPost(channel.webhookUrl).apply {
                entity = StringEntity(
                    objectMapper.writeValueAsString(message),
                    ContentType.APPLICATION_JSON
                )
            }

            val response = httpClient.execute(request)
            response.statusLine.statusCode == 200
        } catch (e: Exception) {
            LOG.error("Failed to send Teams webhook", e)
            false
        }
    }

}


data class TeamsMessage(
    @JsonProperty("@type")
    val type: String = "MessageCard",
    @JsonProperty("@context")
    val context: String = "http://schema.org/extensions",
    val themeColor: String = "FF0000",
    val summary: String?,
    val title: String,
    val sections: List<TeamsSection>,
    val potentialAction: List<TeamsAction> = emptyList()
)

data class TeamsSection(
    val activityTitle: String?,
    val activitySubtitle: String?,
    val facts: List<TeamsFact>,
    val text: String?,
    val markdown: Boolean = true
)

data class TeamsFact(
    val name: String,
    val value: String
)

data class TeamsAction(
    @JsonProperty("@type")
    val type: String = "OpenUri",
    val name: String,
    val targets: List<TeamsUriActionTarget>
)

data class TeamsUriActionTarget(
    val uri: String,
    val os: String = "default"
)

enum class TeamChannel(val webhookUrl: String) {
    Guardrail(
        "https://prod-17.centralindia.logic.azure.com:443/workflows/71647cc75a334cc48cb5e9657aac8c20/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=wjCj-MvJUnUHkH4cJT4AOQ9WUwkVwKjiMGZnESSFQ6s"
    ),
    Test(
        "https://udaandotcom.webhook.office.com/webhookb2/c998c370-1b65-4f1a-90b8-7b47eab2132e@d855c253-b234-4880-b9e6-d490d030a35d/IncomingWebhook/a03bc7fb237a438187051be943d2bb2e/9a06494a-86d6-45bd-8cf5-57833a5a13e1/V2HYsWh_smGFyG3lqye6lTX-8OXtIsDJkmUCLLzCzCREM1"
    )
}