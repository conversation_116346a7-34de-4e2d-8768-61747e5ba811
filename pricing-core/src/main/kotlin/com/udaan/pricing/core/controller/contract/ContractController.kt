package com.udaan.pricing.core.controller.contract

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.udaan.common.utils.getCurrentMillis
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.contracts.ContractRequest
import com.udaan.pricing.core.common.ContractPriceValidationException
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.dao.ContractRepository
import com.udaan.pricing.core.dao.ExceptionContractsRepository
import com.udaan.pricing.core.helpers.DataPlatformHelper
import com.udaan.pricing.core.models.CatalogEntityContext
import com.udaan.pricing.core.models.contracts.Contract
import com.udaan.pricing.core.models.contracts.ContractCatalogEntity
import com.udaan.pricing.core.models.contracts.ContractQuoteInfo
import com.udaan.pricing.core.models.contracts.ContractState
import com.udaan.pricing.core.models.contracts.ExceptionContract
import com.udaan.pricing.core.models.contracts.isValid
import com.udaan.pricing.core.models.contracts.toContract
import com.udaan.pricing.core.svcinterfaces.UserSvcInterface
import com.udaan.pricing.core.utils.getSuspended
import com.udaan.proto.models.ModelV1
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await
import javax.inject.Named

@Suppress("LongParameterList")
class ContractController @Inject constructor(
    private val contractRepository: ContractRepository,
    private val userSvcInterface: UserSvcInterface,
    private val objectMapper: ObjectMapper,
    private val dataPlatformHelper: DataPlatformHelper,
    private val contractValidator: ContractValidator,
    @Named(NamedConstants.Caches.CONTRACT_CACHE) private val contractCache: RedisCache2<Contract?>,
    private val exceptionContractsRepository: ExceptionContractsRepository
) {

    private val log by logger()

    /**
     * Creates or updates the contract.
     *  1. Default end time(based on config), in case end time is not mentioned in request.
     *  2. Validates the request.
     *  3. generates a new referenceId for every contract update.
     *  4. Pushes audit to dp for every update.
     *
     *  This method doesn't check the overlapped contracts ie. two contracts can co-exist at GID and LID (of same GID).
     *  at run time we take LID as preference and then followed by GID.
     */
    suspend fun createOrUpdateContract(
        contractRequest: ContractRequest,
        skipPriceValidation: Boolean = false
    ) {
        val buyerOrg = getBuyerOrg(contractRequest.buyerOrgId)
        val contract = contractRequest.toContract(
            buyerOrgId = buyerOrg.orgId,
            buyerNumber = buyerOrg.userIdOwner,
            duration = contractValidator.getContractDuration(contractRequest)
        )
        log.info("Converted request {} to contract {}", contractRequest, contract)
        try {
            contractValidator.validateContract(contract, skipPriceValidation)
        } catch (e: ContractPriceValidationException) {
            pushToExceptionWorkflow(
                contract = contract,
                cogsUnitPrice = e.cogsUnitPrice,
                mrpUnitPriceWithPuTax = e.mrpUnitPriceWithOutTax,
                message = e.message ?: "Validation failed",
                requestedAt = getCurrentMillis()
            )
            log.error("Validation failed for contract: $contractRequest", e)
            throw e
        }
        contractRepository.createOrUpdateContract(contract).also {
            dataPlatformHelper.trackEvent(
                eventData = objectMapper.convertValue(contract, Map::class.java) as Map<String, Any>,
                eventName = DataPlatformHelper.TrackEventName.CONTRACT_PRICE_AUDIT,
                referenceId1 = contract.id,
                referenceId2 = contract.id
            )
            contractCache.invalidate(contract.id).await()
        }
        exceptionContractsRepository.deleteExceptionContractById(contract.id, contract.buyerOrgId)
    }

    suspend fun updateVolumeOrderedForBuyer(
        buyerOrgId: String,
        catalogEntityId: String,
        volumeOrdered: Long,
        volumeOrderedRefreshedTill: Long?
    ) {
        val contractId = Contract.getContractId(buyerOrgId = buyerOrgId, catalogEntityId = catalogEntityId)
        val contract = contractRepository.getContract(id = contractId, buyerOrgId = buyerOrgId)
        if (contract != null && contract.isValid()) {
            contractRepository.createOrUpdateContract(
                contract.copy(
                    metadata = contract.metadata.copy(
                        buyerNumber = contract.metadata.buyerNumber,
                        reason = contract.metadata.reason,
                        volumeCommitted = contract.metadata.volumeCommitted,
                        city = contract.metadata.city,
                        volumeOrdered = volumeOrdered,
                        volumeOrderedRefreshedTill = volumeOrderedRefreshedTill
                    )
                )
            )
        } else {
            log.info(
                "No valid contract found for buyerOrgId: $buyerOrgId " +
                        "and catalogEntityId: $catalogEntityId to update volume ordered."
            )
        }
    }

    suspend fun updateQuoteInfoForContract(
        buyerOrgId: String,
        catalogEntityId: String,
        quoteInfo: ContractQuoteInfo?
    ) {
        val contractId = Contract.getContractId(buyerOrgId = buyerOrgId, catalogEntityId = catalogEntityId)
        val contract = contractRepository.getContract(id = contractId, buyerOrgId = buyerOrgId)
        if (contract != null) {
            contractRepository.createOrUpdateContract(
                contract.copy(quoteInfo = quoteInfo)
            )
        } else {
            log.info(
                "No valid contract found for buyerOrgId: $buyerOrgId " +
                        "and catalogEntityId: $catalogEntityId to update quote-info."
            )
        }
    }

    private suspend fun pushToExceptionWorkflow(
        contract: Contract,
        cogsUnitPrice: Long?,
        mrpUnitPriceWithPuTax: Long?,
        message: String,
        requestedAt: Long
    ) {
        val exceptionContract = ExceptionContract(
            buyerOrgId = contract.buyerOrgId,
            contract = contract,
            cogsUnitPrice = cogsUnitPrice,
            mrpUnitPriceWithNoTax = mrpUnitPriceWithPuTax,
            message = message,
            requestedAt = requestedAt
        )
        exceptionContractsRepository.createOrUpdateExceptionContract(exceptionContract)
    }

    suspend fun deleteContract(
        buyerNumber: String,
        catalogEntityId: String,
        lastRefreshedBy: String
    ) {
        val buyerOrgId = userSvcInterface.getBuyerOrg(buyerNumber)?.orgId
            ?: error("Invalid buyerNumber provided.")
        val contractId = Contract.getContractId(buyerOrgId, catalogEntityId)
        val contract = contractRepository.getContract(id = contractId, buyerOrgId = buyerOrgId)
            ?: error("Contract not found for buyerOrgId: $buyerOrgId and catalogEntityId: $catalogEntityId")
        contractRepository.createOrUpdateContract(
            contract.copy(
                state = ContractState.DELETED,
                lastRefreshedAt = getCurrentMillis(),
                lastRefreshedBy = lastRefreshedBy
            )
        )
        contractCache.invalidate(contractId).await()
    }

    private suspend fun getBuyerOrg(buyerOrgId: String): ModelV1.OrgAccount {
        val buyerOrg = userSvcInterface.getBuyer(buyerOrgId)
        require(buyerOrg != null) {
            "Given $buyerOrgId has no valid buyer account"
        }
        return buyerOrg
    }


    /**
     * This method would be used in get-path.
     * Based on buyer context, and listingId -
     * we fetch available contracts and check the valid ones with state ACTIVE and falls under current time.
     */
    suspend fun getContract(
        buyerOrgId: String,
        catalogEntityContext: CatalogEntityContext
    ): Contract? {
        val contracts = getContractsForBuyerOrg(buyerOrgId, catalogEntityContext)
        return contracts.firstOrNull { contract -> contract.isValid() }
    }


    private suspend fun getContractsForBuyerOrg(
        buyerOrgId: String,
        catalogEntityContext: CatalogEntityContext
    ): List<Contract> {
        return ContractCatalogEntity.values().sortedBy { it.getPriority() }.mapNotNull {
            val catalogEntityId = fetchCatalogEntityId(it, catalogEntityContext)
            catalogEntityId?.let {
                getContractByBuyerOrgAndCatalogEntity(
                    catalogEntityId = catalogEntityId,
                    buyerOrgId = buyerOrgId
                )
            }
        }
    }

    suspend fun getContractsForBuyer(
        buyerOrgId: String,
        filterOnlyActive: Boolean = false
    ): List<Contract> {
        if (filterOnlyActive) {
            return contractRepository.getContractsForBuyer(buyerOrgId).filter { it.isValid() }
        }
        return contractRepository.getContractsForBuyer(buyerOrgId)
    }

    suspend fun getExceptionContractsForBuyer(buyerOrgId: String): List<ExceptionContract> {
        return exceptionContractsRepository.getExceptionContractsForBuyer(buyerOrgId)
    }

    suspend fun approveExceptionContract(
        buyerOrgId: String, catalogEntityId: String,
        lastRefreshedBy: String
    ) {
        val exceptionContract = exceptionContractsRepository.getExceptionContract(
            ExceptionContract.getContractId(buyerOrgId, catalogEntityId), buyerOrgId
        ) ?: error("No exception contract found for buyerOrgId: $buyerOrgId and catalogEntityId: $catalogEntityId")
        val contract = exceptionContract.contract.copy(
            state = ContractState.ACTIVE,
            lastRefreshedAt = getCurrentMillis(),
            lastRefreshedBy = lastRefreshedBy
        )
        contractRepository.createOrUpdateContract(contract).also {
            dataPlatformHelper.trackEvent(
                eventData = objectMapper.convertValue(contract, Map::class.java) as Map<String, Any>,
                eventName = DataPlatformHelper.TrackEventName.CONTRACT_PRICE_AUDIT,
                referenceId1 = contract.id,
                referenceId2 = contract.id
            )
            contractCache.invalidate(contract.id).await()
        }
        exceptionContractsRepository.deleteExceptionContract(exceptionContract)
    }

    suspend fun rejectExceptionContract(buyerOrgId: String, catalogEntityId: String) {
        val exceptionContract = exceptionContractsRepository.getExceptionContract(
            ExceptionContract.getContractId(buyerOrgId, catalogEntityId), buyerOrgId
        ) ?: error("No exception contract found for buyerOrgId: $buyerOrgId and catalogEntityId: $catalogEntityId")
        exceptionContractsRepository.deleteExceptionContract(exceptionContract)
    }

    private fun fetchCatalogEntityId(
        contractCatalogEntity: ContractCatalogEntity,
        catalogEntityContext: CatalogEntityContext
    ): String? {
        return when (contractCatalogEntity) {
            ContractCatalogEntity.PRODUCT_GROUP_ID -> catalogEntityContext.productGroupId
            ContractCatalogEntity.LISTING_ID -> catalogEntityContext.listingId
        }
    }

    suspend fun getContractByBuyerOrgAndCatalogEntity(buyerOrgId: String, catalogEntityId: String): Contract? {
        val id = Contract.getContractId(buyerOrgId, catalogEntityId)
        return contractCache.getSuspended(id) {
            contractRepository.getContract(
                id = id,
                buyerOrgId = buyerOrgId
            )
        }
    }
}
