package com.udaan.pricing.core.dao.signals

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.core.constants.CosmosDbConfig
import com.udaan.pricing.job.AsyncJob
import com.udaan.pricing.job.AsyncJobType
import kotlinx.coroutines.flow.toList

@Singleton
class AsyncJobRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {
    companion object {
        private val cosmosDbDao by lazy {
            CosmosDbDao(
                configKey = CosmosDbConfig.DB_ACCOUNT_CONFIG,
                databaseName = CosmosDbConfig.PRICING_AUTOMATION_DB,
                containerName = CosmosDbConfig.ASYNC_JOB_COLLECTION
            ) { builder ->
                builder.connectionSharingAcrossClientsEnabled(true)
            }
        }
    }

    /**
     * This method is used to reduce the connection time to cosmos, used at the time of warmup
     */
    suspend fun initialise() {
        getAsyncJob("")
    }

    suspend fun createOrUpdateAsyncJob(asyncJob: AsyncJob): AsyncJob {
        return cosmosDbDao.createOrUpdateItem(asyncJob.toDocument()).toAsyncJob()
    }

    suspend fun getAsyncJob(
        jobId: String
    ): AsyncJob? {
        return cosmosDbDao.findItem(jobId)?.toAsyncJob()
    }

    /**
     * offset and limit are hardcoded to 0 and 10 respectively.
     * this might change based on console requirements.
     */
    suspend fun getAsyncJobs(
        jobType: AsyncJobType,
        offSet: Int,
        limit: Int
    ): List<AsyncJob> {
        return cosmosDbDao.queryItems(
            queryName = "get-jobs-by-type",
            querySpec = makeSqlQuerySpec(
                """
                select * from c where c.type = @type 
                order by c._ts desc
                offset @offSet limit @limit
            """.trimIndent(),
                "@type" to jobType.name,
                "@offSet" to offSet,
                "@limit" to limit
            )
        ).toList().map { it.toAsyncJob() }
    }

    private fun AsyncJob.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toAsyncJob() = objectMapper.convertValue(this, AsyncJob::class.java)
}
