package com.udaan.pricing.core.controller.network

import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.pricing.core.dao.network.TerritoryDao
import com.udaan.pricing.core.dao.network.TerritoryHexDao
import com.udaan.pricing.core.utils.network.HexHelper
import com.udaan.pricing.core.utils.network.getHexResolution
import com.udaan.pricing.network.LatLong
import com.udaan.pricing.network.Territory
import com.udaan.pricing.network.TerritoryHex
import com.udaan.pricing.network.TerritoryStatus
import com.udaan.pricing.network.TerritoryType
import com.udaan.tradequality.models.generateId
import kotlinx.coroutines.Dispatchers
import java.sql.Timestamp
import javax.ws.rs.BadRequestException

class TerritoryController @Inject constructor(
    private val territoryHexDao: TerritoryHexDao,
    private val hexHelper: He<PERSON><PERSON><PERSON><PERSON>,
    private val territoryDao: TerritoryDao
) {
    companion object {
        private val ALL_TYPES = TerritoryType.values().toSet()
        private val LOGGER by logger()
    }

    suspend fun getTerritoriesForLatLong(
        latitude: Double,
        longitude: Double,
        territoryTypes: Set<TerritoryType> = ALL_TYPES
    ): Map<TerritoryType, String?> {
        val hexId = hexHelper.getHexIdForLatLong(latitude, longitude)
        return territoryTypes.parallelMap(5, Dispatchers.IO) {
            it to territoryHexDao.getActiveTerritory(it, hexId)
        }.associate { (territoryType, territory) ->
            territoryType to territory?.territoryId
        }
    }

    suspend fun createTerritory(
        territoryType: TerritoryType,
        territoryName: String,
        points: List<LatLong>,
        createdBy: String
    ) {
        val existingTerritory = territoryDao.getTerritoryByName(territoryType, territoryName)
        if (existingTerritory != null) {
            throw BadRequestException("Territory with name $territoryName for $territoryType already exists")
        }
        val newTerritory = territoryDao.createOrUpdate(
            Territory(
                id = generateId("TERR"),
                name = territoryName,
                type = territoryType,
                status = TerritoryStatus.ACTIVE,
                createdAt = Timestamp(System.currentTimeMillis()),
                updatedAt = Timestamp(System.currentTimeMillis()),
                createdBy = createdBy,
                updatedBy = createdBy
            )
        )
        val hexIds = hexHelper.getH3ForPolygon(points)
        LOGGER.info("Creating ${hexIds.size} hexes for $territoryType $territoryName")
        hexIds.parallelMap(5, Dispatchers.IO) { hexId ->
            val currentHex = territoryHexDao.getActiveTerritory(territoryType, hexId)
            val newOrUpdatedHex = currentHex?.copy(
                territoryId = newTerritory.id,
                status = TerritoryStatus.ACTIVE,
                updatedAt = Timestamp(System.currentTimeMillis())
            ) ?: TerritoryHex(
                id = generateId("THEX"),
                territoryType = territoryType,
                territoryId = newTerritory.id,
                hexId = hexId,
                status = TerritoryStatus.ACTIVE,
                resolution = getHexResolution(territoryType),
                createdAt = Timestamp(System.currentTimeMillis()),
                updatedAt = Timestamp(System.currentTimeMillis())
            )
            LOGGER.info("Mapping $hexId to territory $territoryName for $territoryType")
            territoryHexDao.createOrUpdate(newOrUpdatedHex)
        }
    }
}