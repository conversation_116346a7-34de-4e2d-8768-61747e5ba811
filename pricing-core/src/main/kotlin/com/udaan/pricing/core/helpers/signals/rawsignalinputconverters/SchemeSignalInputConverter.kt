package com.udaan.pricing.core.helpers.signals.rawsignalinputconverters

import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.core.utils.signals.BigDecimalUtil.roundValues
import com.udaan.pricing.signalcreation.SchemeSignalInput
import com.udaan.pricing.signals.GenericMetadata
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState
import com.udaan.pricing.variable.VariableId

class SchemeSignalInputConverter: RawSignalInputConverter<SchemeSignalInput>() {
    companion object {
        private val logger by logger()
    }

    override suspend fun convert(rawSignalInput: SchemeSignalInput): List<Signal> {
        val variableId = getVariableIdFromSchemeAndSource(
            rawSignalInput.schemeChannel,
            rawSignalInput.source
        )

        val convertedSignal = Signal(
            catalogEntity = rawSignalInput.productGroupId.uppercase(),
            catalogEntityType = CatalogEntityType.PRODUCT_GROUP_ID,
            variableId = variableId.name,
            signalData = LadderValue(
                value = rawSignalInput.ladder.roundValues()
            ),
            metadata = GenericMetadata(
                metadataMap = rawSignalInput.additionalMetadata
            ),
            location = Location(
                locationType = rawSignalInput.location.locationType,
                locationValue = rawSignalInput.location.locationValue.uppercase()
            ),
            state = SignalState.ACTIVE,
            createdBy = rawSignalInput.createdBy,
            updatedBy = rawSignalInput.createdBy
        )

        logger.info("converted signal {}", convertedSignal)
        return listOf(convertedSignal)
    }

    private fun getVariableIdFromSchemeAndSource(
        schemeChannel: String,
        source: String
    ): VariableId {
        return when {
            source == "DTR" && schemeChannel == "RETAIL" -> VariableId.DTR_RETAIL_SCHEME
            source == "GT" && schemeChannel == "RETAIL" -> VariableId.GT_RETAIL_SCHEME
            source == "GT" && schemeChannel == "WS" -> VariableId.GT_WS_SCHEME
            else -> throw IllegalArgumentException("Invalid source $source and schemeChannel $schemeChannel found")
        }
    }

}
