package com.udaan.pricing.core.dao.signals

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.getCurrentMillis
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.core.constants.CosmosDbConfig
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState
import kotlinx.coroutines.flow.toList

@Singleton
class SignalRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {
    companion object {
        private val cosmosDbDao by lazy {
            CosmosDbDao(
                configKey = CosmosDbConfig.DB_ACCOUNT_CONFIG,
                databaseName = CosmosDbConfig.PRICING_AUTOMATION_DB,
                containerName = CosmosDbConfig.SIGNALS_COLLECTION
            ) { builder ->
                builder.connectionSharingAcrossClientsEnabled(true)
            }
        }
    }

    /**
     * This method is used to reduce the connection time to cosmos, used at the time of warmup
     */
    suspend fun initialise() {
        getSignalByReferenceId("")
    }

    suspend fun createOrUpdateSignal(signal: Signal): Signal {
        return cosmosDbDao.createOrUpdateItem(signal.toDocument()).toSignal()
    }

    suspend fun updateSignal(signal: Signal): Signal {
        return cosmosDbDao.updateItem(signal.toDocument()).toSignal()
    }

    suspend fun getSignalByIdAndPartitionKey(
        id: String,
        partitionKey: String
    ): Signal? {
        return cosmosDbDao.getItem(id, partitionKey)?.toSignal()
    }

    suspend fun getSignalByReferenceId(
        referenceId: String
    ): Signal? {
        return cosmosDbDao.queryItems(
            queryName = "get-signal-by-reference-id",
            querySpec = makeSqlQuerySpec(
                """
                select * from c where c.referenceId = @referenceId
            """.trimIndent(),
                "@referenceId" to referenceId
            )
        ).toList().map { it.toSignal() }.firstOrNull()
    }

    /**
     * Don't call this method in service api calls, these methods are used only for cron jobs
     */
    suspend fun getSignalsToMarkExpired(): List<Signal> {
        return cosmosDbDao.queryItems(
            queryName = "get-expired-signals",
            querySpec = makeSqlQuerySpec(
                """
                select * from c where c.state = @state and c.validTill < @currentTime
            """.trimIndent(),
                "@currentTime" to getCurrentMillis(),
                "@state" to SignalState.ACTIVE
            )
        ).toList().map { it.toSignal() }
    }

    /**
     * Don't call this method in service api calls, these methods are used only for cron jobs
     */
    suspend fun getSignalsForVariable(variableId: String): List<Signal> {
        return cosmosDbDao.queryItems(
            queryName = "get-signals-for-variable",
            querySpec = makeSqlQuerySpec(
                """
                select * from c where c.variableId = @variableId and c.state = @state
            """.trimIndent(),
                "@variableId" to variableId,
                "@state" to SignalState.ACTIVE
            )
        ).toList().map { it.toSignal() }
    }

    private fun Signal.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toSignal() = objectMapper.convertValue(this, Signal::class.java)
}
