package com.udaan.pricing.core.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.dao.ManualPriceRepository
import com.udaan.pricing.core.helpers.BuyerCohortDetails
import com.udaan.pricing.core.helpers.DataPlatformHelper
import com.udaan.pricing.core.svcinterfaces.CatalogSvcInterface
import com.udaan.pricing.core.utils.PriceInfoValidatorUtil
import com.udaan.pricing.core.utils.getSuspended
import com.udaan.pricing.manualprice.ManualPrice
import com.udaan.pricing.manualprice.ManualPriceDeleteRequest
import com.udaan.pricing.manualprice.ManualPriceState
import com.udaan.pricing.manualprice.ManualPriceUpdateRequest
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await
import javax.inject.Named

class ManualPriceController @Inject constructor(
    private val manualPriceRepository: ManualPriceRepository,
    private val objectMapper: ObjectMapper,
    private val dataPlatformHelper: DataPlatformHelper,
    private val catalogSvcInterface: CatalogSvcInterface,
    @Named(NamedConstants.Caches.MANUAL_PRICES_CACHE) private val manualPricesCache: RedisCache2<List<ManualPrice>>
) {
    companion object {
        private val logger by logger()
        private const val MANUAL_PRICE_EXPIRY_THRESHOLD = 2592000000L
        private const val DELETED_MANUAL_PRICE_COSMOS_TTL = 1296000L // 30 days of persistence
    }

    suspend fun createOrUpdateManualPrice(
        manualPriceUpdateRequest: ManualPriceUpdateRequest
    ): ManualPrice {
        val newManualPrice = manualPriceUpdateRequest.convert()
        validateManualPriceUpdateRequest(manualPriceUpdateRequest)

        return saveNewManualPrice(newManualPrice)
    }

    suspend fun deleteManualPrice(
        manualPriceDeleteRequest: ManualPriceDeleteRequest
    ): ManualPrice? {
        val existingManualPrice = manualPriceRepository.getManualPriceByIdAndPartitionKey(
            id = getManualPriceId(
                catalogEntity = manualPriceDeleteRequest.catalogEntity,
                locationValue = manualPriceDeleteRequest.locationValue,
                buyerCohort = manualPriceDeleteRequest.buyerCohort
            ),
            partitionKey = manualPriceDeleteRequest.catalogEntity
        )

        return existingManualPrice?.let { manualPrice ->
            val deletionMetadataMap = mapOf(
                "DELETION_REASON" to manualPriceDeleteRequest.deletionReason
            ).plus(
                manualPriceDeleteRequest.jobIdReference?.let {
                    mapOf("DELETION_JOB_ID_REFERENCE" to it)
                } ?: emptyMap()
            )

            val updatedManualPrice = manualPrice.copy(
                state = ManualPriceState.DELETED,
                metadata = manualPrice.metadata.plus(deletionMetadataMap),
                updatedBy = manualPriceDeleteRequest.deletedBy,
                updatedAt = System.currentTimeMillis()
            )

            logger.info("Deleting manual price {}", updatedManualPrice)

            manualPriceRepository.updateManualPrice(
                manualPrice = updatedManualPrice,
                ttlInSeconds = DELETED_MANUAL_PRICE_COSMOS_TTL
            ).also {
                dataPlatformHelper.trackEvent(
                    eventData = updatedManualPrice.toMap().plus("DELETION_REASON" to manualPriceDeleteRequest.deletionReason),
                    eventName = DataPlatformHelper.TrackEventName.MANUAL_PRICE_AUDIT,
                    referenceId1 = updatedManualPrice.referenceId,
                    referenceId2 = updatedManualPrice.id
                )
            }.also {
                manualPricesCache.invalidate(manualPriceDeleteRequest.catalogEntity).await()
            }
        }
    }

    suspend fun getValidManualPrice(
        catalogEntitiesSortedByPriority: List<String>,
        locationsSortedByPriority: List<Pair<LocationType, String>>,
        allBuyerCohorts: List<BuyerCohortDetails>
    ): ManualPrice? {
        var validManualPrice: ManualPrice? = null
        val allCatalogEntityLevelPrices = catalogEntitiesSortedByPriority.parallelMap { catalogEntity ->
            getAllActiveManualPricesForEntity(catalogEntity)
        }.flatten()

        run manualPriceFetcher@ {
            catalogEntitiesSortedByPriority.forEach { catalogEntity ->
                locationsSortedByPriority.forEach { (locationType, locationValue) ->
                    val locationAndCatalogFilteredManualPrices = allCatalogEntityLevelPrices.filter {
                        it.catalogEntity.equals(catalogEntity, ignoreCase = true) &&
                                it.locationValue.equals(locationValue, ignoreCase = true) &&
                                it.locationType == locationType
                    }

                    val cohortFilteredManualPrice = allBuyerCohorts.mapNotNull { buyerCohortWithPriority ->
                        locationAndCatalogFilteredManualPrices.firstOrNull {
                            it.buyerCohort.equals(buyerCohortWithPriority.buyerCohort, ignoreCase = true)
                        }
                    }.firstOrNull() ?: locationAndCatalogFilteredManualPrices.firstOrNull {
                        it.buyerCohort == null
                    }

                    if (cohortFilteredManualPrice != null && isManualPriceValid(cohortFilteredManualPrice)) {
                        validManualPrice = cohortFilteredManualPrice
                        return@manualPriceFetcher
                    }
                }

            }
        }

        return validManualPrice
    }

    private suspend fun getAllActiveManualPricesForEntity(
        catalogEntity: String
    ): List<ManualPrice> {
        return manualPricesCache.getSuspended(catalogEntity) {
            manualPriceRepository.getManualPricesForCatalogEntityAndState(
                catalogEntity = catalogEntity,
                manualPriceState = ManualPriceState.ACTIVE
            )
        } ?: emptyList()
    }

    private suspend fun saveNewManualPrice(newManualPrice: ManualPrice): ManualPrice {
        return manualPriceRepository.createOrUpdateManualPrice(newManualPrice).also {
            manualPricesCache.invalidate(newManualPrice.catalogEntity).await()

            dataPlatformHelper.trackEvent(
                eventData = newManualPrice.toMap(),
                eventName = DataPlatformHelper.TrackEventName.MANUAL_PRICE_AUDIT,
                referenceId1 = newManualPrice.referenceId,
                referenceId2 = newManualPrice.id
            )
        }
    }

    private suspend fun validateManualPriceUpdateRequest(
        manualPriceUpdateRequest: ManualPriceUpdateRequest
    ) {
        // todo: add validation for category level valid catalogEntityType (LID for fresh, LID/GID for FMCG)
        validateCatalogEntity(
            catalogEntity = manualPriceUpdateRequest.catalogEntity,
            catalogEntityType = manualPriceUpdateRequest.catalogEntityType
        )

        PriceInfoValidatorUtil.validatePriceInfo(manualPriceUpdateRequest.priceInfo)

        validateExpiry(manualPriceUpdateRequest.expiryTimeInMillis)

        // todo: add locationValue validation
    }

    private suspend fun validateCatalogEntity(
        catalogEntity: String,
        catalogEntityType: CatalogEntityType
    ) {
        when (catalogEntityType) {
            CatalogEntityType.PRODUCT_GROUP_ID -> {
                // todo: Add validation for product group id
            }
            CatalogEntityType.LISTING_ID -> {
                catalogSvcInterface.getTradeListingMinimal(catalogEntity)
            }

            CatalogEntityType.PRODUCT_ID -> {
                // todo: add productId validation
            }
            else -> {
                // todo: add veritcal validation
            }
        }
    }

    private fun validateExpiry(expiryTimeInMillis: Long) {
        // todo: evaluate whether we need category level expiry validation
        if (expiryTimeInMillis < System.currentTimeMillis()) {
            throw IllegalArgumentException("Expiry time cannot be in the past")
        }

        if (expiryTimeInMillis - System.currentTimeMillis() > MANUAL_PRICE_EXPIRY_THRESHOLD) {
            throw IllegalArgumentException("Expiry time cannot exceed 30 days")
        }
    }

    private fun getManualPriceId(
        catalogEntity: String,
        locationValue: String,
        buyerCohort: String?
    ): String {
        return "$catalogEntity:$locationValue:$buyerCohort".uppercase()
    }

    private fun isManualPriceValid(manualPrice: ManualPrice): Boolean {
        return manualPrice.state == ManualPriceState.ACTIVE &&
                manualPrice.expiryTimeInMillis >= System.currentTimeMillis()
    }

    private fun <T : Any> T.toMap(): Map<String, Any> {
        return objectMapper.convertValue(this)
    }
}
