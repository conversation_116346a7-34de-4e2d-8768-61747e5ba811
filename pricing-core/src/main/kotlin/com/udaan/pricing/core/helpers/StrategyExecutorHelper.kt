package com.udaan.pricing.core.helpers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.catalog.model.VerticalCategory
import com.udaan.common.client.exceptions.UdaanTimeoutException
import com.udaan.common.utils.kotlin.logger
import com.udaan.firstpartycatalog.models.Product
import com.udaan.pricing.PUInfo
import com.udaan.pricing.PriceBaseStrategy
import com.udaan.pricing.PriceForListing
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.core.helpers.ConfigHelper
import com.udaan.pricing.core.helpers.pricingstrategy.PricingStrategy
import com.udaan.pricing.core.managers.PortfolioItemManager
import com.udaan.pricing.core.managers.PortfolioPlanManager
import com.udaan.pricing.core.managers.StrategyManager
import com.udaan.pricing.core.models.SlabPrice
import com.udaan.pricing.core.models.common.StrategyOutput
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorConfigInput
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.models.transformToPriceForListingDTO
import com.udaan.pricing.core.strategyevaluator.impl.EvaluatorFactory
import com.udaan.pricing.portfolioitem.PortfolioItem
import com.udaan.pricing.portfolioplan.CohortInput
import com.udaan.pricing.portfolioplan.DEFAULT_COHORT
import com.udaan.pricing.portfolioplan.PortfolioPlan
import com.udaan.pricing.strategy.Strategy
import com.udaan.pricing.variable.VariableId
import com.udaan.proto.models.ModelV1
import com.udaan.resources.cache.RedisCacheException
import kotlinx.coroutines.Deferred
import kotlin.time.measureTimedValue

@Singleton
@Suppress("TooGenericExceptionCaught", "LongParameterList")
class StrategyExecutorHelper @Inject constructor(
    private val catalogHelper: CatalogHelper,
    private val configHelper: ConfigHelper,
    private val pricingNetworkHelper: PricingNetworkHelper,
    private val pricingSignalsHelper: PricingSignalsHelper,
    private val userHelper: UserHelper,
    private val portfolioPlanManager: PortfolioPlanManager,
    private val strategyManager: StrategyManager,
    private val portfolioItemManager: PortfolioItemManager
) {

    private val log by logger()

    suspend fun getPrice(
        listingId: String,
        salesUnitId: String,
        catalogEntity: String,
        locationsSortedByPriority: List<Location>,
        allBuyerCohorts: List<BuyerCohortDetails>,
        verticalCategory: VerticalCategory,
        mappedFpProductDetails: Product?,
        buyerOrgUnit: ModelV1.OrgUnit?,
        mappedBenchmarkListingGuardrailPriceDeferred: Deferred<PriceForListing?>?
    ): PriceForListing? {
        val applicableLocationAndPortfolioItem = getApplicableLocationAndPortfolioItemTagging(
            catalogEntity = catalogEntity,
            locationsSortedByPriority = locationsSortedByPriority
        )

        val applicableCohortPlan = applicableLocationAndPortfolioItem?.let {
            fetchApplicablePortfolioPlan(it.second, allBuyerCohorts)
        } ?: return null

        val locationToConsider = applicableLocationAndPortfolioItem.first
        val portfolioItem = applicableLocationAndPortfolioItem.second

        val mappedBenchmarkListingGuardrailPriceTimedValue = measureTimedValue {
            mappedBenchmarkListingGuardrailPriceDeferred?.await()
        }

        log.info(
            "Guardrail price computation blocked {}ms in SSC execution",
            mappedBenchmarkListingGuardrailPriceTimedValue.duration.inWholeMilliseconds
        )

        val extraInputs = listOfNotNull(
            mappedBenchmarkListingGuardrailPriceTimedValue.value?.let {
                EvaluatorConfigInput(
                    variableId = VariableId.BENCHMARK_LISTING_PRICE_WOT_PAISA_SET,
                    value = LadderValue(
                        value = it.prices.map { slabPrice ->
                            Ladder(
                                minQuantity = slabPrice.minQty.toLong(),
                                maxQuantity = slabPrice.maxQty.toLong(),
                                ladderValue = slabPrice.priceInPaisa.onCOD.toBigDecimal()
                            )
                        }
                    )
                )
            }
        )
        val priceForListing = try {
            val evaluatorRequestContexts = getEvaluatorRequestContext(
                listingId = listingId,
                salesUnitId = salesUnitId,
                location = locationToConsider,
                strategies = applicableCohortPlan.strategies,
                cohortInputs = applicableCohortPlan.cohortInputs,
                extraInputs = extraInputs,
                buyerOrgUnit = buyerOrgUnit,
                verticalCategory = verticalCategory
            )
            val evaluatorOutput = EvaluatorFactory.evaluate(evaluatorRequestContexts)
            log.info(
                "Evaluator output for {}, {}, cohorts: {} is {}",
                listingId, salesUnitId, allBuyerCohorts, evaluatorOutput
            )
            val puInfo = if (verticalCategory == VerticalCategory.FMCG) {
                catalogHelper.fetchPuInfoFromFpProduct(mappedFpProductDetails)
            } else {
                null
            }
            val convertedPrice = convertEvaluatorOutput(listingId, salesUnitId, puInfo, evaluatorOutput)

            convertedPrice.copy(
                sscMetadata = evaluatorOutput?.metadata?.plus(
                    listOf(
                        Pair("PORTFOLIO_ITEM_ID", portfolioItem.referenceId),
                        Pair("PORTFOLIO_PLAN_ID", applicableCohortPlan.id),
                        Pair("BUYER_COHORT", (applicableCohortPlan.buyerCohort ?: DEFAULT_COHORT).uppercase())
                    )
                ) ?: emptyMap(),
                priceBaseStrategy = PriceBaseStrategy.SSC
            )
        } catch (ex: UdaanTimeoutException) {
            log.error(
                "Timeout exception - {} in ssc evaluation for {}, {}, {}, {}, {}",
                ex.message,
                listingId,
                catalogEntity,
                verticalCategory,
                locationToConsider.locationValue,
                allBuyerCohorts
            )
            throw ex
        } catch (ex: RedisCacheException) {
            log.error(
                "Redis Cache Timeout exception - {} in ssc evaluation for {}, {}, {}, {}, {}",
                ex.message,
                listingId,
                catalogEntity,
                verticalCategory,
                locationToConsider.locationValue,
                allBuyerCohorts
            )
            throw ex
        } catch (ex: Exception) {
            log.error(
                "Exception - {} in ssc evaluation for {}, {}, {}, {}, {}",
                ex.message,
                listingId,
                catalogEntity,
                verticalCategory,
                locationToConsider.locationValue,
                allBuyerCohorts
            )
            null
        }

        return priceForListing
    }

    suspend fun getPrice(
        listingId: String,
        salesUnitId: String,
        location: Location,
        strategies: List<String>,
        cohortInputs: List<CohortInput>,
        extraInputs: List<EvaluatorConfigInput> = emptyList(),
        buyerOrgUnit: ModelV1.OrgUnit?,
        verticalCategory: VerticalCategory?
    ): StrategyOutput {
        val requestContext = getEvaluatorRequestContext(
            listingId = listingId,
            salesUnitId = salesUnitId,
            location = location,
            strategies = strategies,
            cohortInputs = cohortInputs,
            extraInputs = extraInputs,
            buyerOrgUnit = buyerOrgUnit,
            verticalCategory = verticalCategory
        )
        val requestContextWithExtraInputs = requestContext.map {
            it.copy(
                inputs = it.inputs + extraInputs
            )
        }
        return StrategyOutput(
            strategies = strategies,
            requestContext = requestContextWithExtraInputs,
            evaluatorOutput = EvaluatorFactory.evaluate(requestContextWithExtraInputs)
        )
    }

    private suspend fun fetchApplicablePortfolioPlan(
        portfolioItem: PortfolioItem,
        buyerCohorts: Collection<BuyerCohortDetails>
    ): PortfolioPlan? {
        val portfolioPlans = portfolioPlanManager.getAllPortfolioPlans(
            portfolioId = portfolioItem.portfolioId
        )

        val highestPriorityCohortPlan = buyerCohorts
            .sortedByDescending { it.cohortPriority }
            .mapNotNull { cohortDetail ->
                portfolioPlans.firstOrNull { it.buyerCohort.equals(cohortDetail.buyerCohort, ignoreCase = true) }
            }.firstOrNull()

        val portfolioPlan = highestPriorityCohortPlan ?: portfolioPlans.firstOrNull {
            it.buyerCohort.equals(DEFAULT_COHORT, ignoreCase = true)
        }
        log.info(
            "Executing plan {} with cohorts {} for {} and {}",
            portfolioPlan?.id, buyerCohorts, portfolioItem.catalogEntity, portfolioItem.location.locationValue
        )
        return portfolioPlan
    }

    private suspend fun getEvaluatorRequestContext(
        listingId: String,
        salesUnitId: String,
        location: Location,
        strategies: List<String>,
        cohortInputs: List<CohortInput>,
        extraInputs: List<EvaluatorConfigInput>,
        buyerOrgUnit: ModelV1.OrgUnit?,
        verticalCategory: VerticalCategory?
    ): List<EvaluatorRequestContext> {

        // applying territory concept only for Staples
        // @todo - remove this check when we expand territory concept to other categories
        val applicableBuyerOrgUnit = if(verticalCategory == VerticalCategory.STAPLES) {
            buyerOrgUnit
        } else {
            log.info("Passing null buyerOrgUnit (excluding territory) as " +
                    "verticalCategory $verticalCategory and not Staples")
            null
        }

        val strategyToConfigInputs = fetchEvaluatorConfigInputs(
            listingId = listingId,
            salesUnitId = salesUnitId,
            location = location,
            strategies = strategies,
            cohortInputs = cohortInputs,
            extraInputs = extraInputs,
            buyerOrgUnit = applicableBuyerOrgUnit
        )
        return strategyToConfigInputs.keys.map { strategy ->
            EvaluatorRequestContext(
                strategy = strategy,
                inputs = strategyToConfigInputs[strategy] ?: emptyList(),
                previousOutput = null
            )
        }
    }

    private suspend fun fetchEvaluatorConfigInputs(
        listingId: String,
        salesUnitId: String,
        location: Location,
        strategies: List<String>,
        cohortInputs: List<CohortInput>,
        extraInputs: List<EvaluatorConfigInput>,
        buyerOrgUnit: ModelV1.OrgUnit?
    ): Map<Strategy, List<EvaluatorConfigInput>> {
        val usedStrategies = strategies.map { strategyId ->
            strategyManager.getStrategyById(strategyId)
        }

        val variablesToFetch = usedStrategies.map { it.usedVariables }.flatten().distinct()
        val cohortVariables = cohortInputs.map { it.variableId }
        val applicableTerritoryInputMap =
            buyerOrgUnit?.let { fetchTerritoryInputMap(variablesToFetch, buyerOrgUnit) }
        val allSignalInputs = pricingSignalsHelper.resolveValuesForListing(
            listingId = listingId,
            salesUnitId = salesUnitId,
            locationType = location.locationType,
            locationValue = location.locationValue,
            variableIds = variablesToFetch.filter { variableId ->
                cohortVariables.contains(variableId).not()
            }.distinct().toSet(),
            inputTerritoryMap = applicableTerritoryInputMap
        ).resolvedValuesMap
        return usedStrategies.associateWith { strategy ->
            strategy.usedVariables.map { variableId ->
                if (cohortVariables.contains(variableId)) {
                    EvaluatorConfigInput(
                        variableId = variableId,
                        value = cohortInputs.firstOrNull {
                            it.variableId == variableId
                        }?.value
                    )
                } else {
                    extraInputs.firstOrNull { it.variableId == variableId } ?: run {
                        val signalValue = allSignalInputs.filter { it.key == variableId }.entries.firstOrNull()?.value
                        EvaluatorConfigInput(
                            variableId = variableId,
                            value = signalValue?.value,
                            referenceId = signalValue?.referenceSignalId
                        )
                    }
                }
            }
        }
    }

    /**
     * This function is used to get the territoryRefIds for the given variable ids.
     * This is required for territory enabled inputs.
     */
    private suspend fun fetchTerritoryInputMap(
        variablesToFetch: List<VariableId>,
        buyerOrgUnit: ModelV1.OrgUnit
    ): Map<String, String>? {
        // fetch geolocation for buyer org unit
        val buyerOrgUnitGeoLoc = userHelper.getBuyerOrgUnitGeoLocation(buyerOrgUnit)
            ?: return null

        // fetch territory-enabled inputs from config and check if any of them are present in the input list
        val allTerritoryEnabledInputs = configHelper.getAllTerritoryEnabledInputs()
        val territoryEnabledVariables = variablesToFetch.filter { variable ->
            allTerritoryEnabledInputs.contains(variable.toString())
        }.takeIf { it.isNotEmpty() } ?: return null

        // fetch territoryRefIds for the applicable variable ids
        val territoryInputMap = pricingNetworkHelper.getCachedInputTerritoryMapForBuyer(
            latitude = buyerOrgUnitGeoLoc.latitude,
            longitude = buyerOrgUnitGeoLoc.longitude,
            inputs = territoryEnabledVariables,
            orgUnitId = buyerOrgUnit.orgUnitId
        )
        log.info("Territory input map for buyerOrgUnitId {} is {}", buyerOrgUnit.orgUnitId, territoryInputMap)
        return territoryInputMap
    }

    private fun convertEvaluatorOutput(
        listingId: String,
        salesUnitId: String,
        puInfo: PUInfo?,
        evaluatorOutput: EvaluatorOutput?
    ): PriceForListing {
        val slabPrices = when (evaluatorOutput?.output) {
            is LadderValue -> {
                (evaluatorOutput.output as LadderValue).value.map {
                    SlabPrice(
                        minQty = it.minQuantity.toInt(),
                        maxQty = it.maxQuantity.toInt(),
                        priceInPaise = it.ladderValue
                    )
                }
            }
            is BigDecimalValue -> {
                listOf(
                    SlabPrice(
                        minQty = 1,
                        maxQty = Int.MAX_VALUE,
                        priceInPaise = (evaluatorOutput.output as BigDecimalValue).value
                    )
                )
            }
            else -> {
                error("Evaluator output should be of ladder value or unit price.")
            }
        }
        return slabPrices.transformToPriceForListingDTO(
            listingId = listingId,
            salesUnitId = salesUnitId,
            puInfo = puInfo,
            pricingStrategy = PricingStrategy.SSC
        )
    }

    private suspend fun getApplicableLocationAndPortfolioItemTagging(
        catalogEntity: String,
        locationsSortedByPriority: List<Location>
    ): Pair<Location, PortfolioItem>? {
        var applicableLocationWithPortfolioItem: Pair<Location, PortfolioItem>? = null

        run portfolioItemFetcher@{
            locationsSortedByPriority.forEach { location ->
                val portfolioItemForLocation = portfolioItemManager.getPortfolioTaggingForEntityAndLocation(
                    catalogEntity = catalogEntity,
                    locationValue = location.locationValue
                )

                if (portfolioItemForLocation != null) {
                    applicableLocationWithPortfolioItem = Pair(location, portfolioItemForLocation)
                    return@portfolioItemFetcher
                }
            }
        }
        return applicableLocationWithPortfolioItem
    }
}
