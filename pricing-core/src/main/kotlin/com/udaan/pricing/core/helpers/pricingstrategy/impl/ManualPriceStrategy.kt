package com.udaan.pricing.core.helpers.pricingstrategy.impl

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.catalog.model.VerticalCategory
import com.udaan.catalog.models.ModelV2
import com.udaan.common.utils.kotlin.logger
import com.udaan.firstpartycatalog.models.Product
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.BasicPrice
import com.udaan.pricing.ContextualPriceRequest
import com.udaan.pricing.PUInfo
import com.udaan.pricing.PriceBaseStrategy
import com.udaan.pricing.PriceForListing
import com.udaan.pricing.PriceInPaisa
import com.udaan.pricing.QtyBasedPrice
import com.udaan.pricing.commons.AbsolutePriceInfo
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.MrpMarkdownInfo
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.models.common.getMrpWithOutTax
import com.udaan.pricing.core.controller.ManualPriceController
import com.udaan.pricing.core.helpers.BuyerCohortDetails
import com.udaan.pricing.core.helpers.BuyerTagHelper
import com.udaan.pricing.core.helpers.CatalogHelper
import com.udaan.pricing.core.helpers.LocationHelper
import com.udaan.pricing.core.helpers.pricingstrategy.PricingStrategy
import com.udaan.pricing.core.helpers.pricingstrategy.Strategy
import com.udaan.pricing.core.models.CatalogEntityContext
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.roundToLong
import com.udaan.pricing.core.utils.LadderUtils
import com.udaan.pricing.manualprice.ManualPrice
import com.udaan.proto.models.ModelV1
import kotlinx.coroutines.Deferred
import java.math.BigDecimal

@Singleton
class ManualPriceStrategy @Inject constructor(
    private val manualPriceController: ManualPriceController,
    private val buyerTagHelper: BuyerTagHelper,
    private val locationHelper: LocationHelper,
    private val catalogHelper: CatalogHelper
) : Strategy() {

    companion object {
        private val logger by logger()
        /*
            allowing manual price to be uploaded at below levels:
            - FMCG: listing x city
            - Staples: listing x WH, listing x city, GID x WH, GID x city
            - Fresh: listing x city
            - Meat: listing x city
            - other/unknown: listing x WH, listing x city, GID x WH, GID x city
         */
        private val categoryToManualPriceEntityTypes = mapOf(
            VerticalCategory.FMCG to listOf(CatalogEntityType.LISTING_ID),
            VerticalCategory.STAPLES to listOf(CatalogEntityType.LISTING_ID, CatalogEntityType.PRODUCT_GROUP_ID),
            VerticalCategory.FRESH to listOf(CatalogEntityType.LISTING_ID),
            VerticalCategory.MEAT to listOf(CatalogEntityType.LISTING_ID),
            VerticalCategory.OTHER to listOf(CatalogEntityType.LISTING_ID, CatalogEntityType.PRODUCT_GROUP_ID)
        )

        private val categoryToLocationTypes = mapOf(
            VerticalCategory.FMCG to listOf(LocationType.WAREHOUSE, LocationType.CITY),
            VerticalCategory.STAPLES to listOf(LocationType.WAREHOUSE, LocationType.CITY),
            VerticalCategory.FRESH to listOf(LocationType.CITY),
            VerticalCategory.MEAT to listOf(LocationType.CITY),
            VerticalCategory.OTHER to listOf(LocationType.WAREHOUSE, LocationType.CITY)
        )
    }

    override suspend fun getPrice(
        catalogEntityContext: CatalogEntityContext,
        contextualPriceRequest: ContextualPriceRequest?,
        fetchInactive: Boolean,
        cluster: List<String>?,
        preferredWarehouseId: String?,
        servingWarehouseId: String?,
        buyerOrgUnit: ModelV1.OrgUnit?,
        mappedBenchmarkListingGuardrailPriceDeferred: Deferred<PriceForListing?>?
    ): List<PriceForListing> {
        val salesUnits = catalogEntityContext.fetchSalesUnits()
        val buyerCohorts = TelemetryScope.async {
            fetchBuyerCohorts(contextualPriceRequest?.buyerContext?.orgId).sortedByDescending { it.cohortPriority }
        }
        val catalogEntitiesSortedByPriority = fetchApplicableCatalogEntities(catalogEntityContext)

        val locationsSortedByPriority = locationHelper.fetchApplicableLocations(
            listingOrgId = catalogEntityContext.listingDetail.orgId,
            verticalCategory = catalogEntityContext.verticalCategory,
            preferredWarehouseId = preferredWarehouseId,
            categoryToLocationTypes = categoryToLocationTypes
        )

        return if (salesUnits.isEmpty() || catalogEntitiesSortedByPriority.isEmpty() || locationsSortedByPriority.isEmpty()) {
            logger.info(
                "SalesUnits {} or CatalogEntities {}  or locations {} are missing for ssc execution",
                salesUnits, catalogEntitiesSortedByPriority, locationsSortedByPriority
            )
            emptyList()
        } else {
            val applicableManualPrices = getApplicableManualPrices(
                listingId = catalogEntityContext.listingId,
                salesUnits = salesUnits.toList(),
                listingDetails = catalogEntityContext.listingDetail,
                catalogEntitiesSortedByPriority = catalogEntitiesSortedByPriority,
                locationsSortedByPriority = locationsSortedByPriority,
                allBuyerCohorts = buyerCohorts.await(),
                verticalCategory = catalogEntityContext.verticalCategory,
                mappedFpProductDetails = catalogEntityContext.fpProductDetails
            )

            logger.info(
                "Final manual prices for {}, with riders is {}",
                catalogEntityContext.listingId,
                applicableManualPrices
            )
            applicableManualPrices
        }
    }

    private suspend fun getApplicableManualPrices(
        listingId: String,
        salesUnits: List<String>,
        listingDetails: ModelV2.TradeListing,
        catalogEntitiesSortedByPriority: List<String>,
        locationsSortedByPriority: List<Pair<LocationType, String>>,
        allBuyerCohorts: List<BuyerCohortDetails>,
        verticalCategory: VerticalCategory,
        mappedFpProductDetails: Product?
    ): List<PriceForListing> {
        val validManualPrice = manualPriceController.getValidManualPrice(
            catalogEntitiesSortedByPriority = catalogEntitiesSortedByPriority,
            locationsSortedByPriority = locationsSortedByPriority,
            allBuyerCohorts = allBuyerCohorts
        )

        return if (validManualPrice == null) {
            logger.info(
                "No valid manual price found for {} {} {}",
                catalogEntitiesSortedByPriority,
                locationsSortedByPriority,
                allBuyerCohorts
            )
            emptyList()
        } else {
            logger.info(
                "Valid manual price found for {} {} {} {}",
                catalogEntitiesSortedByPriority,
                locationsSortedByPriority,
                allBuyerCohorts,
                validManualPrice
            )
            val puInfo = if (verticalCategory == VerticalCategory.FMCG) {
                catalogHelper.fetchPuInfoFromFpProduct(mappedFpProductDetails)
            } else {
                null
            }
            salesUnits.map { salesUnitId ->
                val slabsPrices = getFinalPrice(
                    salesUnitId = salesUnitId,
                    listingDetails = listingDetails,
                    manualPrice = validManualPrice,
                    puInfo = puInfo
                )

                PriceForListing(
                    listingId = listingId,
                    saleUnitId = salesUnitId,
                    prices = slabsPrices,
                    strategyRef = PricingStrategy.MANUAL.name,
                    // todo: figure out a correct way to propagate referenceId, using hack for now
                    sscMetadata = mapOf("MANUAL_PRICE_REF_ID" to validManualPrice.referenceId),
                    priceBaseStrategy = PriceBaseStrategy.MANUAL
                )
            }
        }
    }

    private fun getFinalPrice(
        salesUnitId: String,
        listingDetails: ModelV2.TradeListing,
        manualPrice: ManualPrice,
        puInfo: PUInfo?
    ): List<QtyBasedPrice> {
        val conversionRate = getConversionRateForSalesUnit(
            salesUnitId = salesUnitId,
            listingDetails = listingDetails
        )

        return when (val priceInfo = manualPrice.priceInfo) {
            is AbsolutePriceInfo -> {
                val adjustedSlabsQuantityBasisConversionRate = LadderUtils.getAssortmentAdjustedLadder(
                    conversionRate = conversionRate,
                    inputLadder = priceInfo.priceLadderSlabs.map {
                        Ladder(
                            minQuantity = it.minQuantity,
                            maxQuantity = it.maxQuantity,
                            ladderValue = it.ladderValue
                        )
                    }
                )

                adjustedSlabsQuantityBasisConversionRate.map {
                    convertToQtyBasedPrice(
                        minQuantity = it.minQuantity,
                        maxQuantity = it.maxQuantity,
                        priceInPaise = it.ladderValue.multiplyWithScale(conversionRate.toBigDecimal()),
                        puInfo = puInfo
                    )
                }
            }
            is MrpMarkdownInfo -> {
                val adjustedSlabsQuantityBasisConversionRate = LadderUtils.getAssortmentAdjustedLadder(
                    conversionRate = conversionRate,
                    inputLadder = priceInfo.markdownLadderSlabs.map {
                        Ladder(
                            minQuantity = it.minQuantity,
                            maxQuantity = it.maxQuantity,
                            ladderValue = it.ladderValue
                        )
                    }
                )

                val listingTaxDetails = catalogHelper.getListingTaxDetails(
                    listingDetails = listingDetails,
                    salesUnitId = salesUnitId
                )

                val mrpWithoutTax = listingTaxDetails.getMrpWithOutTax()?.toBigDecimal()
                    ?: error("No MRP found for listingId ${listingDetails.listingId}")

                adjustedSlabsQuantityBasisConversionRate.map {
                    val priceInPaise = mrpWithoutTax.multiplyWithScale(
                        BigDecimal.ONE.minus(it.ladderValue.divideWithScale(BigDecimal(10000)))
                    )
                    convertToQtyBasedPrice(
                        minQuantity = it.minQuantity,
                        maxQuantity = it.maxQuantity,
                        priceInPaise = priceInPaise,
                        puInfo = puInfo
                    )
                }
            }
        }
    }

    // todo: @om.raj - remove this, once we refactor and inject the buyer cohorts in the beginning
    private suspend fun fetchBuyerCohorts(buyerOrgId: String?): List<BuyerCohortDetails> {
        return buyerOrgId?.let {
            buyerTagHelper.getBuyerTagsApplicable(
                buyerOrgId = it
            )
        } ?: emptyList()
    }

    private fun fetchApplicableCatalogEntities(
        catalogEntityContext: CatalogEntityContext
    ): List<String> {
        val catalogEntityTypes = categoryToManualPriceEntityTypes[catalogEntityContext.verticalCategory]

        val applicableCatalogEntities = catalogEntityTypes?.mapNotNull { catalogEntityType ->
            when (catalogEntityType) {
                CatalogEntityType.PRODUCT_GROUP_ID -> catalogEntityContext.productGroupId
                CatalogEntityType.LISTING_ID -> catalogEntityContext.listingId
                else -> null
            }
        }

        return applicableCatalogEntities ?: emptyList()
    }

    private fun convertToQtyBasedPrice(
        minQuantity: Long,
        maxQuantity: Long,
        priceInPaise: BigDecimal,
        puInfo: PUInfo?
    ): QtyBasedPrice {
        return QtyBasedPrice(
            minQty = minQuantity.toInt(),
            maxQty = maxQuantity.toInt(),
            priceInPaisa = PriceInPaisa(
                onCredit = priceInPaise.roundToLong(),
                onCOD = priceInPaise.roundToLong(),
                onPrepayment = priceInPaise.roundToLong(),
                basicPrice = BasicPrice(
                    onCreditBasePrice = priceInPaise.roundToLong(),
                    onCODBasePrice = priceInPaise.roundToLong(),
                    onPrepaymentBasePrice = priceInPaise.roundToLong()
                ),
                priceRiders = null
            ),
            pricePerKgInPaisa = null,
            packagingUnit = puInfo,
            taxableAmountPaise = priceInPaise.roundToLong(),
            bulkLadder = null
        )
    }

    private fun getConversionRateForSalesUnit(
        salesUnitId: String,
        listingDetails: ModelV2.TradeListing
    ): Long {
        return listingDetails.salesUnitList?.firstOrNull {
            it.salesUnitId == salesUnitId
        }
            ?.assortmentDetails
            ?.numItemsAssortment
            ?.coerceAtLeast(1)
            ?.toLong()
            ?: error("No assortment details found for $salesUnitId")
    }

}
