package com.udaan.pricing.core.helpers.signals

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope.Companion.async
import com.udaan.pricing.core.helpers.CatalogHelper
import com.udaan.pricing.core.models.signals.CatalogEntityContext
import com.udaan.pricing.core.svcinterfaces.CatalogSvcInterface
import com.udaan.pricing.core.svcinterfaces.FpCatalogSvcInterface
import com.udaan.pricing.core.svcinterfaces.VerticalSvcInterface

@Singleton
class CatalogEntityHelper @Inject constructor(
    private val catalogSvcInterface: CatalogSvcInterface,
    private val fpCatalogServiceInterface: FpCatalogSvcInterface,
    private val verticalServiceInterface: VerticalSvcInterface,
    private val catalogHelper: CatalogHelper
) {
    companion object {
        private val logger by logger()
    }

    suspend fun getCatalogEntityContextFromLidSuid(
        listingId: String,
        salesUnitId: String
    ): CatalogEntityContext {
        val listingDetailDeferred = async {
            catalogSvcInterface.getTradeListing(listingId)
        }
        val mappedProductDeferred = async {
            fpCatalogServiceInterface.getProductForListingId(listingId)
        }
        // GID may not be available in some cases (like Fresh, Meat, Mario),
        // in which case we need to handle exception returning null
        val mappedGroupDetailsDeferred = async {
            try{
                mappedProductDeferred.await()?.let {
                    fpCatalogServiceInterface.getProductGroupDetailsForProductId(it.productId)
                }
            } catch (ex: Exception) {
                logger.warn("Error while fetching product group details for listingId: $listingId", ex)
                null
            }
        }

        val vertical = verticalServiceInterface.getVerticalForLid(listingId)
        return CatalogEntityContext(
            listingId = listingId,
            salesUnitId = salesUnitId,
            listingDetail = listingDetailDeferred.await(),
            fpProductDetails = mappedProductDeferred.await(),
            productGroupId = mappedGroupDetailsDeferred.await(),
            vertical = vertical.name,
            verticalCategory = catalogHelper.getCategoryForVertical(vertical.name)
        )
    }

    suspend fun getCatalogEntityContextFromGid(productGroupId: String): CatalogEntityContext {
        val vertical = async {
            fpCatalogServiceInterface.getCentralVerticalForProductGroupId(productGroupId)
        }
        val mappedProductDeferred = async {
            fpCatalogServiceInterface.getAProductFromGroupId(productGroupId)
        }
        return CatalogEntityContext(
            listingId = null,
            salesUnitId = null,
            listingDetail = null,
            fpProductDetails = mappedProductDeferred.await(),
            productGroupId = productGroupId,
            vertical = vertical.await().name,
            verticalCategory = catalogHelper.getCategoryForVertical(vertical.await().name)
        )
    }
}
