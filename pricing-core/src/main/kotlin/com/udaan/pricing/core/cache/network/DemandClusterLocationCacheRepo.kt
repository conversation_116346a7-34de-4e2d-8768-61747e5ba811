package com.udaan.pricing.core.cache.network

import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.dao.network.DemandClusterLocationsRepository
import com.udaan.pricing.network.DemandClusterLocations
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await

class DemandClusterLocationCacheRepo @Inject constructor(
    @Named(NamedConstants.Caches.DEMAND_CLUSTER_LOCATIONS_CACHE) private val demandClusterLocationCache: RedisCache2<Collection<DemandClusterLocations>>
) {
    companion object {
        private const val DCL_LOCATION_NAME_KEY_PREFIX = "DCL:LocationName:"
        private const val DCL_CITY_KEY_PREFIX = "DCL:CITY:"
        private const val DCL_WH_KEY_PREFIX = "DCL:WH:"
        private const val DCL_CLUSTER_ID_KEY_PREFIX = "DCL:CLUSTER_ID:"
        private const val DCL_ALL_CLUSTERS_KEY_PREFIX = "DCL:ALL"
    }

    suspend fun getDemandClusterLocationsForLocationName(
        locationName: String
    ): Collection<DemandClusterLocations> {
        val demandClusterLocation = demandClusterLocationCache.get(
            DCL_LOCATION_NAME_KEY_PREFIX + locationName.lowercase()
        ) {
            TelemetryScope.future {
                val demandLocation = DemandClusterLocationsRepository.getLocationForName(locationName)
                if (demandLocation == null)
                    emptyList()
                else
                    listOf(demandLocation)
            }
        }.await()
        return demandClusterLocation ?: emptyList()
    }

    suspend fun getDemandClusterLocationsForCity(
        city: String
    ): Collection<DemandClusterLocations> {
        val demandClusterLocation = demandClusterLocationCache.get(DCL_CITY_KEY_PREFIX + city.lowercase()) {
            TelemetryScope.future {
                DemandClusterLocationsRepository.getAllLocationsForCity(city)
            }
        }.await()
        return demandClusterLocation ?: emptyList()
    }

    suspend fun getDemandClusterLocationsForWarehouse(
        warehouseId: String
    ): Collection<DemandClusterLocations> {
        val demandClusterLocation = demandClusterLocationCache.get(DCL_WH_KEY_PREFIX + warehouseId.lowercase()) {
            TelemetryScope.future {
                DemandClusterLocationsRepository.getLocationsForWarehouse(warehouseId)
            }
        }.await()
        return demandClusterLocation ?: emptyList()
    }

    suspend fun getDemandClusterLocationsForDemandClusterId(
        demandClusterId: String
    ): Collection<DemandClusterLocations> {
        val demandClusterLocation = demandClusterLocationCache.get(DCL_CLUSTER_ID_KEY_PREFIX + demandClusterId.lowercase()) {
            TelemetryScope.future {
                DemandClusterLocationsRepository.getAllLocationsForClusterId(demandClusterId)
            }
        }.await()
        return demandClusterLocation ?: emptyList()
    }


    suspend fun getAllDemandClusterLocations(): Collection<DemandClusterLocations> {
        val demandClusterLocation = demandClusterLocationCache.get(DCL_ALL_CLUSTERS_KEY_PREFIX.lowercase()) {
            TelemetryScope.future {
                DemandClusterLocationsRepository.getAllLocations()
            }
        }.await()
        return demandClusterLocation ?: emptyList()
    }

    suspend fun invalidateDemandClusterLocationCache(demandClusterLocation: DemandClusterLocations) {
        demandClusterLocationCache.invalidate(
            DCL_LOCATION_NAME_KEY_PREFIX + demandClusterLocation.name.lowercase()
        ).await()

        demandClusterLocationCache.invalidate(
            DCL_CLUSTER_ID_KEY_PREFIX + demandClusterLocation.demandClusterId.lowercase()
        ).await()

        demandClusterLocationCache.invalidate(DCL_ALL_CLUSTERS_KEY_PREFIX.lowercase()).await()

        demandClusterLocation.fulfilmentCenters.map {
            demandClusterLocationCache.invalidate(DCL_WH_KEY_PREFIX + it.whId.lowercase())
        }.map { it.await() }

        demandClusterLocationCache.invalidate(
            DCL_CITY_KEY_PREFIX + demandClusterLocation.city.lowercase()
        ).await()
    }
}
