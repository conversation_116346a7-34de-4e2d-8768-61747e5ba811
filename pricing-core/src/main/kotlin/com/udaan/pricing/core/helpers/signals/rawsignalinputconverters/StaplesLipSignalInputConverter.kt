package com.udaan.pricing.core.helpers.signals.rawsignalinputconverters

import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.core.utils.signals.BigDecimalUtil.roundToDefaultScale
import com.udaan.pricing.signalcreation.StaplesLipSignalInput
import com.udaan.pricing.signals.GenericMetadata
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState
import com.udaan.pricing.variable.VariableId

class StaplesLipSignalInputConverter: RawSignalInputConverter<StaplesLipSignalInput>() {
    companion object {
        private val log by logger()
    }

    override suspend fun convert(rawSignalInput: StaplesLipSignalInput): List<Signal> {
        log.info("Got conversion request for $rawSignalInput")
        val convertedSignal = Signal(
            catalogEntity = rawSignalInput.productGroupId.uppercase(),
            catalogEntityType = CatalogEntityType.PRODUCT_GROUP_ID,
            variableId = VariableId.STAPLES_LIP_WOT_PAISA_UNIT.name,
            signalData = BigDecimalValue(
                value = rawSignalInput.valueInPaise.toBigDecimal().roundToDefaultScale()
            ),
            metadata = GenericMetadata(
                mapOf(
                    "LINE_ITEM_CONSUMED_ID" to rawSignalInput.lineItemConsumedId,
                    "IRN_ID" to rawSignalInput.irnId,
                    "PURCHASE_ORDER_ID" to rawSignalInput.purchaseOrderId,
                    "PRODUCT_ID" to rawSignalInput.productId,
                    "SELLER_ORG_ID" to rawSignalInput.sellerOrgId
                )
            ),
            location = rawSignalInput.location.copy(
                locationValue = rawSignalInput.location.locationValue.uppercase()
            ),
            state = SignalState.ACTIVE,
            createdBy = rawSignalInput.updatedBy,
            updatedBy = rawSignalInput.updatedBy
        )

        log.info("Request {} was converted to signal {}", rawSignalInput, convertedSignal)
        return listOf(convertedSignal)
    }
}
