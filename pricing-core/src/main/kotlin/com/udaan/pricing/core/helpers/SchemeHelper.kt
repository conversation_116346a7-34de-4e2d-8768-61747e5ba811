package com.udaan.pricing.core.helpers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.pricing.QtyBasedPrice
import java.math.BigDecimal


sealed class Offer

data class PriceDiscount(val minQty: Int, val discount: BigDecimal) : Offer()
data class Free(val minQty: Int, val freeUnits: Int) : Offer()

@Singleton
class SchemeHelper @Inject constructor() {

    fun Offer.minQty() = when(this) { is PriceDiscount -> this.minQty
        is Free -> this.minQty}


    fun Offer.discountPercent() = when(this) {
        is PriceDiscount -> this.discount.multiply(BigDecimal("100"))
        is Free -> BigDecimal(this.freeUnits).div(BigDecimal(this.freeUnits + this.minQty)).multiply(BigDecimal("100"))
    }

    fun parseFreeOffers(scheme: String) : List<Offer> {
        val freeOffers = ArrayList<Offer>()
        var startIndex = 0

        while(true) {
            var result = "(\\d+)\\+(\\d+)[-| ]".toRegex().find(scheme, startIndex)
            result = result ?: "(\\d+)\\+(\\d+)$".toRegex().find(scheme, startIndex)
            if (result == null) {
                break
            }
            val (minQty, freeQty) = result!!.destructured
            freeOffers.add(Free(minQty.toInt(), freeQty.toInt()))
            startIndex = result.range.last + 1
        }
        return freeOffers
    }
    fun parseVolumeDiscounts(scheme: String): List<Offer> {

        val volumeDiscounts = ArrayList<Offer>()
        var startIndex = 0

        while(true) {
            var result = "([0-9]+)\\+([0-9]+(\\.[0-9][0-9]?)?)%".toRegex().find(scheme, startIndex)
            if (result == null) {
                break
            }
            val (minQty, discountPercent) = result!!.destructured
            volumeDiscounts.add(PriceDiscount(minQty.toInt(), BigDecimal(discountPercent).divide(BigDecimal(100))))
            startIndex = result.range.last + 1
        }
        return volumeDiscounts
    }

    fun parseOffers(scheme: String?) : List<Offer> {
        val offers = mutableListOf<Offer>()
        scheme?.let {
            offers.addAll(parseFreeOffers(scheme))
            offers.addAll(parseVolumeDiscounts(scheme))
        }
        offers.sortBy {
            when(it) {
                is PriceDiscount -> it.minQty
                is Free -> it.minQty
            }
        }
        return offers
    }
    fun Offer.discount(): BigDecimal {
        return when (this) {
            is PriceDiscount -> this.discount
            is Free -> BigDecimal(0)
        }
    }

    fun constructPriceConditions(ptrPaise: Long,offers: List<Offer>):List<QtyBasedPrice>{
        val priceConditions = mutableListOf<QtyBasedPrice>()
        if (offers.isEmpty()){
            priceConditions.add(LotHelper.getQtyBasedPrice(1, Int.MAX_VALUE, ptrPaise))
            return priceConditions
        }
        val firstOffer = offers[0]
        if (firstOffer.minQty() > 1) {
            priceConditions.add(LotHelper.getQtyBasedPrice(1, firstOffer.minQty() - 1, ptrPaise))
        }
        for (i in 0 until offers.size - 1) {
            val offer = offers[i]
            val minQty = offer.minQty()
            val maxQty = offers[i + 1].minQty() - 1
            val discount = offer.discount()
            priceConditions.add(
                LotHelper.getQtyBasedPrice(
                    minQty,
                    maxQty,
                    getDiscountedPrice(ptrPaise.toBigDecimal(), discount).toLong()
                )
            )
        }

        val lastOffer = offers.last()
        priceConditions.add(
            LotHelper.getQtyBasedPrice(
                lastOffer.minQty(), Int.MAX_VALUE,
                getDiscountedPrice(ptrPaise.toBigDecimal(), lastOffer.discount()).toLong()
            )
        )
        return priceConditions
    }


    suspend fun getApplicableSchemeForLot(listingId: String, orgId:String,vertical:String,lotDetails: Map<String,String>) : String? {

        val lotScheme = when(vertical) {
            "MedicineNew" -> {
                val schemeUnit = lotDetails["min_scheme_unit"]?.toLongOrNull() ?: 0
                val schemePercent = lotDetails["scheme_discount_bps"]?.let {
                    it.toLongOrNull()?.div(100.0)
                } ?: 0.0
                if (schemeUnit > 0 && schemePercent > 0.0) {
                    "$schemeUnit+$schemePercent%"
                } else {
                    null
                }
            }
            else ->lotDetails["scheme"]?.let { if (it == "0") null else it }
        }

        return lotScheme
    }

    private fun getDiscountedPrice(RATE: BigDecimal, schemeDiscount: BigDecimal) =
        RATE.multiply(BigDecimal("1.00").minus(schemeDiscount))

}
