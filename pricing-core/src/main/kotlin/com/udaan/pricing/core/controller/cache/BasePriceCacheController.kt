package com.udaan.pricing.core.controller.cache

import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.common.utils.parallelMap
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.BasePrice
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.dao.BasePriceRepository
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await

class BasePriceCacheController @Inject constructor(
    @Named(NamedConstants.Caches.LISTING_BASE_PRICE_CACHE) private val listingBasePriceCache: RedisCache2<List<BasePrice>>,
    private val basePriceRepository: BasePriceRepository
) {

    suspend fun getCachedBasePricesForListing(listingId: String): List<BasePrice>? {
        val basePrice = listingBasePriceCache.get(listingId) {
            TelemetryScope.future {
                basePriceRepository.getAllPricesForListing(listingId)
            }
        }
        return basePrice.await()
    }

    suspend fun invalidateCachedBasePriceForListing(listingId: String) {
        listingBasePriceCache.invalidate(listingId).await()
    }

    suspend fun invalidateCachedBasePriceForListings(listingIds: Collection<String>) {
        listingIds.parallelMap { listingId ->
            listingBasePriceCache.invalidate(listingId)
        }
    }

}
