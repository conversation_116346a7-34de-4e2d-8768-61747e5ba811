package com.udaan.pricing.core.models.common

import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import java.math.BigDecimal

data class ListingTaxDetails(
    val mrpInPaise: Long?,
    val cessBps: Long,
    val gstBps: Long
)


fun ListingTaxDetails.getMrpWithOutTax(): Long? {
    val taxFactor = BigDecimal.ONE.plus(BigDecimal(gstBps + cessBps).divideWithScale(BigDecimal(10000)))
    return this.mrpInPaise?.let { BigDecimal(it) }?.divideWithScale(taxFactor)?.toLong()
}