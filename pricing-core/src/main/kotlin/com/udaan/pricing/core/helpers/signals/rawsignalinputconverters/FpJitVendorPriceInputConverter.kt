package com.udaan.pricing.core.helpers.signals.rawsignalinputconverters

import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.utils.signals.BigDecimalUtil.roundToDefaultScale
import com.udaan.pricing.signalcreation.FpJitVendorPriceInput
import com.udaan.pricing.signals.GenericMetadata
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState
import com.udaan.pricing.variable.VariableId

class FpJitVendorPriceInputConverter: RawSignalInputConverter<FpJitVendorPriceInput>() {
    companion object {
        private val log by logger()
    }

    override suspend fun convert(rawSignalInput: FpJitVendorPriceInput): List<Signal> {
        log.info("Got conversion request for $rawSignalInput")
        val convertedSignal = Signal(
            catalogEntity = rawSignalInput.groupId.uppercase(),
            catalogEntityType = CatalogEntityType.PRODUCT_GROUP_ID,
            variableId = VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT.name,
            signalData = BigDecimalValue(
                value = rawSignalInput.unitPrice.toBigDecimal().roundToDefaultScale()
            ),
            metadata = GenericMetadata(
                metadataMap = mapOf(
                    "vendorId" to rawSignalInput.vendorId
                )
            ),
            location = Location(
                locationType = LocationType.WAREHOUSE,
                locationValue = rawSignalInput.warehouseId.uppercase()
            ),
            state = SignalState.ACTIVE,
            createdBy = rawSignalInput.vendorId,
            updatedBy = rawSignalInput.vendorId
        )

        log.info("Request {} was converted to signal {}", rawSignalInput, convertedSignal)
        return listOf(convertedSignal)
    }
}
