package com.udaan.pricing.core.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import java.math.BigDecimal


class SellingPriceLimitsController @Inject constructor(
    private val objectMapper: ObjectMapper
){

    private val log by logger()

    suspend fun checkIntentPriceForPriceLimits(productId: String, price: BigDecimal) {
        return
    }
}
