package com.udaan.pricing.core.utils

import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit

object TimeUtils {


    private val IST: ZoneOffset = ZoneOffset.of("+0530")
    private const val MIDNIGHT_HOUR_IN_24_HOUR_FORMAT = 0

    fun getCutOffTimeForManualPriceSignals(expireBeforeInDays: Int) : Long {
        /*
           For sunday - we don't run cron
           We have to expiry signals before expireBeforeInDays midnight (12AM).
           if sunday was part of the list of days, we need to add one more day before it to ensure that we are excluding sunday for expiry.
        */
        val today = LocalDate.now().dayOfWeek
        val listOfDays = (1..expireBeforeInDays).map { today.minus(it.toLong()) }.toMutableList()

        // If Sunday is in the list, add one more day before it
        if (DayOfWeek.SUNDAY in listOfDays) {
            listOfDays.add(today.minus((expireBeforeInDays + 1).toLong()))
        }
        return getUnixTimestampGivenHoursDaysBefore(MIDNIGHT_HOUR_IN_24_HOUR_FORMAT, listOfDays.size.toLong())
    }

    /**
     * return unix timestamp value in ms for provided time (in 24-hour format) and given days before today in IST
     * ex:
     * - passing noOfDaysBefore = 1 and hoursIn24HourFormat 15 will give unix timestamp of 3PM yesterday
     * - passing noOfDaysBefore = 0 and hoursIn24HourFormat 1 will give unix timestamp of 1AM today
     */
    private fun getUnixTimestampGivenHoursDaysBefore(hourIn24HourFormat: Int, noOfDaysBefore: Long): Long {
        val now = LocalDateTime.now()
        val previousDay = now.minus(noOfDaysBefore, ChronoUnit.DAYS)
            .withHour(hourIn24HourFormat)
            .withMinute(0)
            .withSecond(0)
            .withNano(0)

        return previousDay.toEpochSecond(IST)*1000
    }
}