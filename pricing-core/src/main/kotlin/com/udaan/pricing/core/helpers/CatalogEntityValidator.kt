package com.udaan.pricing.core.helpers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.core.svcinterfaces.CatalogSvcInterface
import com.udaan.pricing.core.svcinterfaces.FpCatalogSvcInterface
import com.udaan.pricing.portfolio.PortfolioCategory
import com.udaan.pricing.portfolioitem.CatalogEntityLevel

@Singleton
class CatalogEntityValidator @Inject constructor(
    private val catalogServiceInterface: CatalogSvcInterface,
    private val fpCatalogServiceInterface: FpCatalogSvcInterface
) {
    companion object {
        private val logger by logger()
    }

    private suspend fun validateListingId(listingId: String) {
        try {
            catalogServiceInterface.getTradeListingMinimal(listingId)
        } catch (ex: Exception) {
            logger.error("Listing: {}, validation failed, Exception: {}", listingId, ex.message)
            throw kotlin.IllegalArgumentException("Listing $listingId, validation failed, Exception: ${ex.message}")
        }
    }

    private suspend fun validateProductId(productId: String) {
        try {
            fpCatalogServiceInterface.getFpProductDetails(productId)
        } catch (ex: Exception) {
            logger.error("Product validation failed: {}, Exception: {}", productId, ex.message)
            throw kotlin.IllegalArgumentException("PID validation failed: $productId, Exception: ${ex.message}")
        }
    }

    private suspend fun validateProductGroupId(productGroupId: String) {
        val mappedProductIds = fpCatalogServiceInterface.getMappedFpProductIdsFromGroupId(productGroupId)

        if (mappedProductIds.isEmpty()) {
            throw kotlin.IllegalArgumentException("No mapped PID's found, validation failed for $productGroupId")
        }
    }

    suspend fun validateCatalogEntity(
        catalogEntity: String,
        catalogEntityLevel: CatalogEntityLevel,
        portfolioCategory: PortfolioCategory
    ) {
        val validCatalogEntityLevelForCategory = getCatalogEntityLevelForCategory(portfolioCategory)

        if (validCatalogEntityLevelForCategory != catalogEntityLevel) {
            throw kotlin.IllegalArgumentException("Invalid entity level $catalogEntityLevel for $portfolioCategory")
        }

        when (catalogEntityLevel) {
            CatalogEntityLevel.LISTING_ID -> validateListingId(catalogEntity)
            CatalogEntityLevel.PRODUCT_ID -> validateProductId(catalogEntity)
            CatalogEntityLevel.PRODUCT_GROUP_ID -> validateProductGroupId(catalogEntity)
        }
    }

    private fun getCatalogEntityLevelForCategory(
        portfolioCategory: PortfolioCategory
    ): CatalogEntityLevel {
        return when (portfolioCategory) {
            PortfolioCategory.FMCG, PortfolioCategory.STAPLES -> CatalogEntityLevel.PRODUCT_GROUP_ID
            PortfolioCategory.FRESH, PortfolioCategory.MEAT, PortfolioCategory.OTHER -> CatalogEntityLevel.LISTING_ID
        }
    }
}
