package com.udaan.pricing.core.helpers

import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.catalog.client.CategoryTreeV2
import com.udaan.catalog.model.VerticalCategory
import com.udaan.catalog.models.ModelV2
import com.udaan.catalog.models.ModelV2.TradeListing.Status
import com.udaan.common.utils.kotlin.logger
import com.udaan.firstpartycatalog.models.Product
import com.udaan.fulfilment.sku.PackagingType
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.PUInfo
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.models.common.ListingTaxDetails
import com.udaan.pricing.core.models.CatalogEntityContext
import com.udaan.pricing.core.svcinterfaces.CatalogSvcInterface
import com.udaan.pricing.core.svcinterfaces.FpCatalogSvcInterface
import com.udaan.pricing.core.svcinterfaces.VerticalSvcInterface
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await
import javax.ws.rs.NotFoundException

class CatalogHelper @Inject constructor(
    private val fpCatalogSvcInterface: FpCatalogSvcInterface,
    private val catalogSvcInterface: CatalogSvcInterface,
    private val verticalSvcInterface: VerticalSvcInterface,
    private val categoryTreeV2: CategoryTreeV2,
    private val sellerOrgCityHelper: SellerOrgCityHelper,
    @Named(NamedConstants.Caches.GID_CITY_TO_LISTING_CACHE)
    private val gidAndCityToListingCache: RedisCache2<String?>
) {

    private val log by logger()

    companion object {
        private val ACTIVE_LISTING_STATUS = listOf(Status.ACTIVE, Status.INACTIVE)
        private val VALID_SALES_UNIT_STATUS = listOf(ModelV2.EleStatus.ENABLED, ModelV2.EleStatus.INACTIVE)
        private const val PACKAGING_TYPE_KEY = "packagingType"
        private const val CONVERSION_RATE_KEY = "conversionRate"
    }

    suspend fun getActiveListingsForGroupId(groupId: String, city: String): List<ModelV2.TradeListing> {
        return fpCatalogSvcInterface.getListingMarketplaceMappingsForGid(groupId).mapNotNull {
            val listingDetails = getListingDetailsOrNull(it.listingId)
            val listingCity = listingDetails?.orgId?.let {
                sellerOrgCityHelper.getCityForSellerOrgId(listingDetails.orgId)
            }
            if (listingDetails != null && listingCity != null &&
                listingCity.equals(city, ignoreCase = true) &&
                ACTIVE_LISTING_STATUS.contains(listingDetails.status)
            ) {
                listingDetails
            } else {
                null
            }
        }
    }

    suspend fun getAnActiveListingFromGidAndCityCached(
        productGroupId: String,
        sellerOrgCity: String
    ): String? {
        return gidAndCityToListingCache.get("$productGroupId::$sellerOrgCity") {
            TelemetryScope.future {
                getActiveListingsForGroupId(
                    groupId = productGroupId,
                    city = sellerOrgCity
                ).firstOrNull()?.listingId
            }
        }.await()
    }

    private suspend fun getActiveListingsForGroupIdAndCity(groupId: String, city: String): List<ModelV2.TradeListing> {
        return fpCatalogSvcInterface.getListingMarketplaceMappingsForGid(groupId).mapNotNull {
            getActiveListingByCity(it.listingId, city)
        }
    }

    private suspend fun getActiveListingByCity(listingId: String, city: String): ModelV2.TradeListing? {
        val listingDetails = getListingDetailsOrNull(listingId = listingId)
        val listingCity = listingDetails?.let {
            sellerOrgCityHelper.getCityForSellerOrgId(listingDetails.orgId)
        }
        return if (listingDetails != null && listingCity.equals(city, ignoreCase = true) &&
            ACTIVE_LISTING_STATUS.contains(listingDetails.status)
        ) {
            listingDetails
        } else {
            null
        }
    }

    suspend fun getListingDetailsOrNull(listingId: String): ModelV2.TradeListing? {
        return try {
            catalogSvcInterface.getTradeListingMinimal(listingId)
        } catch (e: NotFoundException) {
            log.error("Listing not found for listingId: $listingId")
            null
        }
    }

    /**
     * Fetches the tax details based on sales-unit-id.
     * Just a wrapper to fetch only tax details from listing details.
     */
    fun getListingTaxDetails(
        listingDetails: ModelV2.TradeListing,
        salesUnitId: String
    ): ListingTaxDetails {

        val mrpPaise = listingDetails.salesUnitList?.firstOrNull {
            it.salesUnitId == salesUnitId
        }?.priceDetails?.mrpPaise

        return ListingTaxDetails(
            mrpInPaise = mrpPaise,
            gstBps = listingDetails.taxDetails.gstBps.toLong(),
            cessBps = listingDetails.taxDetails.cessBps.toLong()
        )
    }

    suspend fun fetchVerticalCategory(
        catalogEntity: CatalogEntityType,
        catalogEntityId: String
    ): VerticalCategory? {
        return when (catalogEntity) {
            CatalogEntityType.PRODUCT_GROUP_ID -> {
                getCategoryForProductGroupId(catalogEntityId)
            }
            CatalogEntityType.LISTING_ID -> {
                verticalSvcInterface.getVerticalCategoryForLid(catalogEntityId)
            }
            else -> {
                null
            }
        }
    }

    suspend fun fetchVerticalIdForCatalogId(
        catalogEntity: CatalogEntityType,
        catalogEntityId: String
    ): String? {
        return when (catalogEntity) {
            CatalogEntityType.PRODUCT_GROUP_ID -> {
                fpCatalogSvcInterface.getCentralVerticalForProductGroupId(catalogEntityId).name
            }
            CatalogEntityType.LISTING_ID -> {
                verticalSvcInterface.fetchVerticalIdFromListingId(catalogEntityId)
            }
            else -> {
                null
            }
        }
    }

    suspend fun getCatalogTitle(
        catalogEntity: CatalogEntityType,
        catalogEntityId: String
    ): String? {
        return when (catalogEntity) {
            CatalogEntityType.LISTING_ID -> {
                getListingDetailsOrNull(catalogEntityId)?.title
            }
            CatalogEntityType.PRODUCT_GROUP_ID -> {
                fpCatalogSvcInterface.getGroupIdTitle(catalogEntityId)
            }
            else -> {
                null
            }
        }
    }

    suspend fun fetchActiveListingsByCity(
        catalogEntity: CatalogEntityType,
        contractCatalogEntityId: String,
        city: String
    ): List<ModelV2.TradeListing> {
        return when (catalogEntity) {
            CatalogEntityType.LISTING_ID -> {
                listOfNotNull(
                    getActiveListingByCity(listingId = contractCatalogEntityId, city = city)
                )
            }
            CatalogEntityType.PRODUCT_GROUP_ID -> {
                getActiveListingsForGroupIdAndCity(
                    contractCatalogEntityId,
                    city
                )
            }
            else -> {
                emptyList()
            }
        }
    }

    fun fetchActiveSalesUnits(listingDetails: ModelV2.TradeListing): List<ModelV2.SalesUnit> {
        return listingDetails.salesUnitList.filter {
            it.status in VALID_SALES_UNIT_STATUS
        }
    }

    suspend fun getCatalogEntityContextFromLidSuid(
        listingId: String,
        salesUnitId: String?
    ): CatalogEntityContext {
        val listingDetailDeferred = TelemetryScope.async {
            catalogSvcInterface.getTradeListing(listingId)
        }
        val mappedProductDeferred = TelemetryScope.async {
            fpCatalogSvcInterface.getProductForListingId(listingId)
        }
        val mappedGroupDetailsDeferred = TelemetryScope.async {
            mappedProductDeferred.await()?.let {
                fpCatalogSvcInterface.getProductGroupDetailsForProductId(it.productId)
            }
        }

        val vertical = verticalSvcInterface.getVerticalForLid(listingId)
        return CatalogEntityContext(
            listingId = listingId,
            salesUnitId = salesUnitId,
            listingDetail = listingDetailDeferred.await(),
            fpProductDetails = mappedProductDeferred.await(),
            productGroupId = mappedGroupDetailsDeferred.await(),
            vertical = vertical,
            verticalCategory = getCategoryForVertical(vertical.name)
        )
    }

    fun fetchPuInfoFromFpProduct(fpProductDetails: Product?): PUInfo {
        return fpProductDetails?.let {
            val packagingUnitType = fpProductDetails.packagingAttributes[PACKAGING_TYPE_KEY]
                ?: throw IllegalStateException("Unable to get Packaging unit type from mapped product")

            val conversionRate = fpProductDetails.packagingAttributes[CONVERSION_RATE_KEY]
                ?: throw IllegalStateException("Unable to get Conversion rate from mapped product")

            PUInfo(
                packagingUnitType = packagingUnitType,
                assortment = conversionRate.toInt(),
                multiplier = 1,
                packaging = PackagingType.valueOf(packagingUnitType)
            )
        } ?: throw IllegalStateException("Unable to get mapped FP product details")
    }


    suspend fun getCategoryForProduct(productId: String): VerticalCategory {
        val productData = fpCatalogSvcInterface.getProductDetails(productId)
        val vertical = verticalSvcInterface.getFpVertical(productData.vertical)
        return getCategoryForVertical(vertical.name)
    }

    suspend fun getCategoryForProductGroupId(productGroupId: String): VerticalCategory {
        val centralVertical = fpCatalogSvcInterface.getCentralVerticalForProductGroupId(productGroupId)
        return getCategoryForVertical(centralVertical.name)
    }

    suspend fun getCategoryForVertical(verticalName: String): VerticalCategory {
        return categoryTreeV2.getVerticalCategory(verticalName)
    }
}
