package com.udaan.pricing.core.strategyevaluator.impl.dynamic.common

import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.variable.VariableId
import java.math.BigDecimal

internal object CohortAdjustmentEvaluator : Evaluator {

    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        ValidationUtils.validateInputs(data.strategy, data.inputs)
        require(data.previousOutput != null) {
            "Previous output is mandatory for Cohort adjustment evaluator"
        }
        require(data.previousOutput.output::class in listOf(BigDecimalValue::class, LadderValue::class)) {
            "Previous evaluator output for Cohort adjustment should be of BigDecimal or Ladder type"
        }

        val cohortAdjustmentBps = (VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.COHORT_ADJUSTMENT_BPS
        ) as? BigDecimalValue)?.value
            ?: throw IllegalArgumentException("COHORT_ADJUSTMENT_BPS is mandatory for adjustment markup evaluator")

        val calculatedSlabPricesPostAdjustment = when (val previousOutput = data.previousOutput.output) {
            is BigDecimalValue -> {
                val adjustedPrice = previousOutput.value.multiplyWithScale(
                    (BigDecimal(1) + cohortAdjustmentBps.divideWithScale(BigDecimal(10000)))
                )

                BigDecimalValue(adjustedPrice)
            }
            is LadderValue -> {
                val adjustedPriceSlabs = previousOutput.value.map { slabPriceBeforeMarkup ->
                    val slabPricePostCohortAdjustment = slabPriceBeforeMarkup.ladderValue.multiplyWithScale(
                        (BigDecimal(1) + cohortAdjustmentBps.divideWithScale(BigDecimal(10000)))
                    )

                    slabPriceBeforeMarkup.copy(
                        ladderValue = slabPricePostCohortAdjustment
                    )
                }

                LadderValue(adjustedPriceSlabs)
            }
            else -> {
                throw IllegalArgumentException(
                    "Expected previous output of type BigDecimalValue or " +
                            "LadderValue but got ${previousOutput::class.simpleName}"
                )
            }
        }

        return EvaluatorOutput(calculatedSlabPricesPostAdjustment, emptyMap())
    }
}
