package com.udaan.pricing.core.models.signals

import com.udaan.catalog.model.VerticalCategory
import com.udaan.catalog.models.ModelV2
import com.udaan.firstpartycatalog.models.Product

data class CatalogEntityContext(
    val listingId: String?,
    val salesUnitId: String?,
    val listingDetail: ModelV2.TradeListing?,
    val fpProductDetails: Product?,
    val productGroupId: String?,
    val vertical: String,
    val verticalCategory: VerticalCategory
)
