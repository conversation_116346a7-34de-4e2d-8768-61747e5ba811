package com.udaan.pricing.core.helpers

import com.google.inject.Inject
import com.udaan.catalog.model.VerticalCategory
import com.udaan.pricing.commons.location.LocationType

class LocationHelper @Inject constructor(
    private val sellerOrgCityHelper: SellerOrgCityHelper
) {

    suspend fun fetchApplicableLocations(
        listingOrgId: String,
        verticalCategory: VerticalCategory,
        preferredWarehouseId: String?,
        categoryToLocationTypes: Map<VerticalCategory, List<LocationType>>
    ): List<Pair<LocationType, String>> {
        /*
            Brutal Hack:
            - Meat and Fresh listings have orgs associated which return India as city.
            - Since that is not a valid city, for now we return hardcoded Bangalore city for them.
            @todo - hack to be removed when city values fixed for these categories or we want to expand beyond Bangalore.
         */
        if (verticalCategory in listOf(VerticalCategory.FRESH, VerticalCategory.MEAT)) {
            return listOf(Pair(LocationType.CITY, "bangalore"))
        }

        val locationTypePriorityList = categoryToLocationTypes[verticalCategory] ?: emptyList()

        val applicableLocations = locationTypePriorityList.mapNotNull { locationType ->
            val locationValue = when (locationType) {
                LocationType.CITY -> {
                    sellerOrgCityHelper.getCityForSellerOrgId(listingOrgId)
                }
                LocationType.WAREHOUSE -> {
                    preferredWarehouseId
                }
                else -> null
            }

            locationValue?.let {
                Pair(locationType, it)
            }
        }

        return applicableLocations
    }
}