package com.udaan.pricing.core.controller.automation

import com.google.inject.Inject
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.dao.automation.TradingPortfolioItemRepository
import com.udaan.pricing.portfolioitem.PortfolioItemState
import com.udaan.pricing.portfolioitem.TradingPortfolioItem
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await
import javax.ws.rs.NotFoundException

/**
 * To remove circular dependency between TradingRepriceController and PortfolioItemManager,
 * we have this common controller.
 */
@Singleton
class TradingItemCommonController @Inject constructor(
    @Named(NamedConstants.Caches.TRADING_PORTFOLIO_ITEM_CACHE) private val tradingPortfolioItemCache: RedisCache2<TradingPortfolioItem?>,
    private val tradingPortfolioItemRepository: TradingPortfolioItemRepository
) {

    private val log by logger()

    suspend fun getTradingPortfolioItemOrNull(
        catalogEntity: String,
        locationValue: String
    ): TradingPortfolioItem? {
        return try {
            val item = getPortfolioTaggingForEntityAndLocation(
                catalogEntity = catalogEntity,
                locationValue = locationValue
            )
            if (item.state == PortfolioItemState.ACTIVE) {
                item
            } else {
                null
            }
        } catch (e: NotFoundException) {
            log.error("No portfolio item for $catalogEntity, $locationValue")
            null
        }
    }

    suspend fun getPortfolioTaggingForEntityAndLocation(
        catalogEntity: String,
        locationValue: String
    ): TradingPortfolioItem {
        return tradingPortfolioItemCache.get(
            k = getPortfolioItemId(catalogEntity, locationValue)
        ) {
            TelemetryScope.Companion.future {
                val portfolioItemId = getPortfolioItemId(catalogEntity, locationValue)
                val portfolioItem = tradingPortfolioItemRepository.getByIdAndPartitionKey(
                    id = portfolioItemId,
                    partitionKey = catalogEntity
                )

                portfolioItem?.let {
                    if (it.state == PortfolioItemState.ACTIVE) {
                        it
                    } else {
                        null
                    }
                }
            }
        }.await() ?: throw NotFoundException("No portfolio item found for $catalogEntity and $locationValue")
    }


    private fun getPortfolioItemId(
        catalogEntity: String,
        locationValue: String
    ): String {
        return ("$catalogEntity:$locationValue").uppercase()
    }
}