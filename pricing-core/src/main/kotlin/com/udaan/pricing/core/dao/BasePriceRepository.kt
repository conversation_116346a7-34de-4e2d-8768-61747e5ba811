package com.udaan.pricing.core.dao

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.google.inject.Singleton
import com.microsoft.azure.documentdb.ConnectionPolicy
import com.microsoft.azure.documentdb.ConsistencyLevel
import com.microsoft.azure.documentdb.DocumentClient
import com.microsoft.azure.documentdb.bulkexecutor.DocumentBulkExecutor
import com.udaan.common.utils.kotlin.logger
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.BasePrice
import com.udaan.pricing.RawInfoListing
import com.udaan.pricing.core.constants.CosmosDbConfig.BASE_PRICE_TABLE_V2
import com.udaan.pricing.core.constants.CosmosDbConfig.COSMOS_DB_NAME
import com.udaan.pricing.uniqueKey
import com.udaan.resources.ResourceBuilder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.withContext
import org.apache.commons.lang3.tuple.MutablePair

@Singleton
class BasePriceRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {
    private val log by logger()
    private val documentDbDao by lazy {
        CosmosDbDao(
            configKey = "pricing",
            databaseName = COSMOS_DB_NAME,
            containerName = BASE_PRICE_TABLE_V2
        ) { builder ->
            builder.connectionSharingAcrossClientsEnabled(true)
        }
    }

    /**
     * Dummy function solely used for client initialisation while starting the service
     */
    suspend fun initialise() {
        documentDbDao.findItem("ID1")
    }

    private val documentBulkExecutor by lazy {
        val documentDbBuilder = ResourceBuilder.documentClient("pricing")

        val documentClient = DocumentClient(
            documentDbBuilder.documentDBHost,
            documentDbBuilder.accessKey,
            ConnectionPolicy.GetDefault(),
            ConsistencyLevel.Session
        )

        val collectionLink = "/dbs/$COSMOS_DB_NAME/colls/$BASE_PRICE_TABLE_V2"
        val collection = documentClient.readCollection(collectionLink, null).resource

        DocumentBulkExecutor.builder().from(
            documentClient,
            COSMOS_DB_NAME,
            BASE_PRICE_TABLE_V2,
            collection.partitionKey,
            5 * 1000
        ).build()
    }

    suspend fun createPrice(basePrice: BasePrice): BasePrice {
       return documentDbDao.createOrUpdateItem(basePrice.toDocument()).toBasePrice()
    }

    suspend fun deleteItems(basePrices: Collection<BasePrice>) {
        withContext(Dispatchers.IO) {
            documentBulkExecutor.deleteAll(basePrices.map {
                MutablePair(it.listingId, it.id)
            })
            log.info("Deleted ${basePrices.map { it.id }}")
        }
    }

    suspend fun getAllPricesForListing(listingId: String): List<BasePrice> {
        return documentDbDao.queryItems(
            queryName = "get-all-prices-for-listing",
            querySpec = makeSqlQuerySpec(
                """
                    select * from c 
                    where c.listingId = @listingId
                """.trimIndent(),
                "@listingId" to listingId
            )
        ).toList().map { it.toBasePrice() }
    }

    suspend fun fetchDataCountByTimeRange(fromTimeStamp: Long, toTimeStamp: Long): Long {
        return documentDbDao.queryItems(
            queryName = "get-prices-count-between-time-range",
            querySpec = makeSqlQuerySpec(
                """
                    select count(1) from c where
                    c._ts >= @fromTimeStamp and c._ts <= @toTimeStamp
                """.trimIndent(),
                "@fromTimeStamp" to fromTimeStamp,
                "@toTimeStamp" to toTimeStamp
            )
        ).toList().first().get("$1").asLong()
    }

    suspend fun fetchDataByTimeRange(fromTimeStamp: Long, toTimeStamp: Long): List<BasePrice> {
        return documentDbDao.queryItems(
            queryName = "get-prices-by-time-range",
            querySpec = makeSqlQuerySpec(
                """
                    select * from c where
                    c._ts >= @fromTimeStamp and c._ts <= @toTimeStamp
                """.trimIndent(),
                "@fromTimeStamp" to fromTimeStamp,
                "@toTimeStamp" to toTimeStamp
            )
        ).toList().map { it.toBasePrice() }
    }

    suspend fun findDocument(id: String): BasePrice? {
        return documentDbDao.findItem(id)?.toBasePrice()
    }

    suspend fun delete(basePrice: BasePrice) {
        log.info("deleting ${basePrice.uniqueKey()} -> ${basePrice.id}")
        documentDbDao.deleteItem(basePrice.id, basePrice.listingId)
    }

    suspend fun metaNull():List<RawInfoListing> {
        val query = """
                SELECT c.listingId,c.saleUnitId FROM c where (NOT IS_DEFINED(c.metaData) 
                or IS_NULL(c.metaData) ) and c.state = 'ACTIVE' and 
                c.orgId in ('ORG4CDH913CBTQR540G3B7D6JRBWN','ORG6MYQL12RHBDVE4CGP2HT8C42CE','ORG2M2J9NTMHZCPG4PLRZWBSY4TC6',
                'ORG14ST6K434CDXSZ6Q6M487ERKKQ','ORG1EPXTJJEWFQ60G9L50X13TFBPW','ORGFJZNPEFJCNDZCFSVSTVCYX5X2H',
                'ORG78Y9XMH6HXDNK4RTLX3SGFQP3P','ORGN3EZ6CCHEFDWJF5JFDVD9ST3C0','ORGK24DSHEVRBC03GRK5K8N4CX3PH',
                'ORGBXP2C7TW9TQ3GZWH671SWENSPK','ORGXDC1MM8WY0C2GGCHRC7T3935QJ','ORGK7H0WS3WG2C7TF2460DEEBTP5Q')
            """.trimIndent()
        return documentDbDao.queryItems("metaNull", makeSqlQuerySpec(query)).toList().map {
            objectMapper.convertValue(it, RawInfoListing::class.java)
        }
    }

    private fun BasePrice.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toBasePrice() = objectMapper.convertValue(this, BasePrice::class.java)
}
