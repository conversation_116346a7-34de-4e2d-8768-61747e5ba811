package com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg

import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.variable.VariableId
import java.math.BigDecimal

internal object MrpPtrEvaluator : Evaluator {

    /**
     * (MRP_WT_PAISA_SET/(1+ PTR_BPS /10000))/(1+(GST_BPS + CESS_BPS)/10000)
     */
    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        ValidationUtils.validateInputs(data.strategy, data.inputs)
        val mrpInPaisa = (VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.MRP_WT_PAISA_SET
        ) as? BigDecimalValue)?.value ?: throw IllegalArgumentException("MRP is mandatory for MRP - PTR evaluation")

        val ptrBps = (VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.PTR_BPS
        ) as? BigDecimalValue)?.value ?: BigDecimal(0.0)

        val gstBps = (VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.GST_BPS
        ) as? BigDecimalValue)?.value ?: throw IllegalArgumentException("GST_BPS is mandatory for MRP - PTR evaluation")

        val cessBps = (VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.CESS_BPS
        ) as? BigDecimalValue)?.value
            ?: throw IllegalArgumentException("CESS_BPS is mandatory for MRP - PTR evaluation")

        val calculatedPrice = mrpInPaisa
            .divideWithScale(BigDecimal(1) + ptrBps.divideWithScale(BigDecimal(10000)))
            .divideWithScale((BigDecimal(1) + (gstBps + cessBps).divideWithScale(BigDecimal(10000))))
        return EvaluatorOutput(BigDecimalValue(calculatedPrice), emptyMap())
    }

}
