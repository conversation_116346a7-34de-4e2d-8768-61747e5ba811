package com.udaan.pricing.core.dao

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.google.inject.Singleton
//import com.fasterxml.jackson.databind.node.ObjectNode
//import com.udaan.cosmosdb.utils.CosmosDbDao
//import com.udaan.cosmosdb.utils.makeSqlQuerySpec
//import com.udaan.pricing.UserCohort
//import com.udaan.pricing.core.utils.USER_COHORT
//import com.udaan.pricing.core.utils.dbname

//import kotlinx.coroutines.flow.toList

@Deprecated("UserCohortRepository is deprecated.")
@Singleton
class UserCohortRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {
//    private val userCohortDbDao by lazy {
//        CosmosDbDao(
//            configKey = "pricing",
//            databaseName = dbname,
//            containerName = USER_COHORT
//        )
//    } { builder ->
//        builder.connectionSharingAcrossClientsEnabled(true)
//    }
//
//    suspend fun createUserCohort(uc: UserCohort): UserCohort {
//        return userCohortDbDao.createItem(uc.toDocument()).toUserCohort()
//    }
//
//    suspend fun getCohortByOrgId(orgId: String): List<UserCohort> {
//        return userCohortDbDao.queryItems(
//            queryName = "get-cohort-by-orgId",
//            querySpec = makeSqlQuerySpec(
//                """
//                    select * from c where c.orgId= '$orgId'
//                """.trimIndent()
//            )
//        ).toList().map { it.toUserCohort() }
//    }
//
//    suspend fun getCohortByOrgIdAndCohort(orgId: String, cohortName: String): UserCohort? {
//        return userCohortDbDao.queryItems(
//            queryName = "get-cohort-by-orgId-and-cohort",
//            querySpec = makeSqlQuerySpec(
//                """
//                    select * from c where c.orgId = '$orgId'
//                    and c.cohortName = '$cohortName'
//                """.trimIndent()
//            )
//        ).toList().map { it.toUserCohort() }.firstOrNull()
//    }
//
//
//    suspend fun getAllCohort(sellerOrgId: String): List<UserCohort> {
//        return userCohortDbDao.queryItems(
//            queryName = "get-all-public-cohorts-for-org",
//            querySpec = makeSqlQuerySpec(
//                """
//                    SELECT * from c where c.sellerOrgId = '$sellerOrgId'
//                    or c.cohortType = 'PUBLIC'
//                """.trimIndent()
//            )
//        ).toList().map { it.toUserCohort() }
//    }
//
//    suspend fun deleteCohort(id: String, orgId: String) {
//        userCohortDbDao.deleteItem(id, orgId)
//    }
//
//    private fun UserCohort.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
//    private fun ObjectNode.toUserCohort() = objectMapper.convertValue(this, UserCohort::class.java)
}
