package com.udaan.pricing.core.dao

import com.udaan.pricing.Region

val regionsMasterList = listOf(
    Region(
        id = "RGNIFL1HE2QSMFQ754RT85WJX2CHY3",
        displayName = "North India",
        description = "Rajasthan, Chandigarh, Delhi, Haryana, Himachal Pradesh, Punjab, Uttar Pradesh, Uttarakhand",
        listOfStates = listOf("Rajasthan", "Chandigarh", "Delhi", "Haryana", "Himachal Pradesh", "Punjab", "Uttar Pradesh", "Uttarakhand")
    ),
    Region(
        id = "RGNIKCP1MJCR1BDFJFDR3B7RRXTQRZ",
        displayName = "East India",
        description = "Chattisgarh, Bihar, Jharkhand, Odisha, West Bengal",
        listOfStates = listOf("Chattisgarh","Bihar","Jharkhand","Odisha","West Bengal")
    ),
    Region(
        id = "RGNIXT0PL3EQZPD08GC0MFQZGTQWNZ",
        displayName = "North East India",
        description = "Arunachal Pradesh, Assam, Meghalaya, Mizoram, Nagaland, Sikkim, Tripura, Manipur, Jammu & Kashmir",
        listOfStates = listOf("Arunachal Pradesh", "Assam", "Meghalaya", "Mizoram", "Nagaland", "Sikkim", "Tripura", "Manipur", "Jammu & Kashmir")
    ),
    Region(
        id = "RGNIG0JFFPPEENQ84Z5PCGV298S4ZN",
        displayName = "South India",
        description = "Andhra Pradesh, Karnataka, Kerala, Pondicherry, Tamil Nadu, Telangana",
        listOfStates = listOf("Andhra Pradesh", "Karnataka", "Kerala", "Pondicherry", "Tamil Nadu", "Telangana")
    ),
    Region(
        id = "RGNI2R4N3GDFBQ82WGY1C8QMDBZCK0",
        displayName = "West India",
        description = "Daman & Diu, Goa, Gujarat, Maharashtra, Madhya Pradesh, Nagpur",
        listOfStates = listOf("Daman & Diu", "Goa", "Gujarat", "Maharashtra", "Madhya Pradesh", "Nagpur")
    )

)

