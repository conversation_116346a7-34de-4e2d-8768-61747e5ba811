package com.udaan.pricing.core.svcinterfaces

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.catalog.client.RedisListingRepository
import com.udaan.catalog.models.ModelV2
import kotlinx.coroutines.future.await

@Singleton
class CatalogSvcInterface @Inject constructor(
    private val redisListingRepository: RedisListingRepository
) {
    suspend fun getTradeListing(listingId: String): ModelV2.TradeListing {
        return redisListingRepository.getListing(listingId = listingId, retryCount = 3).await()
    }

    suspend fun getTradeListingMinimal(listingId: String): ModelV2.TradeListing {
        return redisListingRepository.getListingMinimal(listingId, retryCount = 3).await()
    }
}
