package com.udaan.pricing.core.helpers.rider.impl

import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.*
import com.udaan.pricing.core.helpers.rider.PricingRequestContext
import com.udaan.pricing.core.helpers.rider.Rider
import com.udaan.pricing.core.models.CatalogEntityContext

class SuperClubDiscountRider @Inject constructor(): Rider() {
    private val log by logger()

    override val isAdditive: Boolean
        get() = false

    override suspend fun getPriceRider(
        catalogEntityContext: CatalogEntityContext,
        requestContext: PricingRequestContext,
        fetchRiderDetails: Boolean,
        cluster: List<String>?,
        preferredWarehouseId: String?,
        servingWarehouseId: String?
    ): PriceRiderForListing? {
        return null
    }
}
