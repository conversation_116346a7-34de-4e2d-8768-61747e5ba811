package com.udaan.pricing.core.strategyevaluator.impl.dynamic

import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.common.CohortAdjustmentEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.common.VolumetricDiscountLadderEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg.BeveragesDtrBaseEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg.BnsBaseEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg.CogsFloorGuardrailEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg.FmcgHyperpureCompMatchEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg.FmcgHyperpureCompDirectMatchEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg.FmcgJumbotailCompMatchEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg.FmcgMetroCompMatchEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg.MrpCeilGuardrailEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg.MrpPtrEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg.SchemeMultiplierWithRetentionCapEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fresh.FreshCogsFloorGuardrailEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fresh.FreshHyperpureCompMatchEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.staples.DiscountSlabsEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.staples.HyperpureLadderMatchEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.staples.JtLadderMatchEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.staples.LipGuardrailEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.staples.MetroLadderMatchEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.staples.VslMarkupPriceEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fresh.FreshManualPriceEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fresh.FreshNinjacartCompMatchEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.meat.MeatCogsFloorGuardrailEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.meat.MeatHyperpureCompMatchEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.meat.MeatManualPriceEvaluator
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.staples.PLBenchmarkListingCeilGuardrailEvaluator

internal object DynamicEvaluatorFactory {

    fun getDynamicEvaluator(evaluatorRequestContext: EvaluatorRequestContext): Evaluator {
        return when (evaluatorRequestContext.strategy.name) {
            "FMCG_JUMBOTAIL_COMP" -> FmcgJumbotailCompMatchEvaluator
            "FMCG_HYPERPURE_COMP" -> FmcgHyperpureCompMatchEvaluator
            "FMCG_HYPERPURE_COMP_DIRECT" -> FmcgHyperpureCompDirectMatchEvaluator
            "FMCG_METRO_COMP" -> FmcgMetroCompMatchEvaluator
            "MRP_PTR_SCHEME" -> MrpPtrEvaluator
            "FMCG_SCHEME_MULTIPLIER_WITH_RETENTION_CAP" -> SchemeMultiplierWithRetentionCapEvaluator
            "COGS_FLOOR_GUARDRAIL" -> CogsFloorGuardrailEvaluator
            "MRP_CEIL_GUARDRAIL" -> MrpCeilGuardrailEvaluator
            "BEVS_DTR_BASE" -> BeveragesDtrBaseEvaluator
            "BNS_BASE" -> BnsBaseEvaluator
            "COHORT_ADJUSTMENT" -> CohortAdjustmentEvaluator
            "VSL_MARKUP_PRICE" -> VslMarkupPriceEvaluator
            "JT_LADDER_MATCH" -> JtLadderMatchEvaluator
            "HYPERPURE_LADDER_MATCH" -> HyperpureLadderMatchEvaluator
            "METRO_LADDER_MATCH" -> MetroLadderMatchEvaluator
            "DISCOUNT_SLABS" -> DiscountSlabsEvaluator
            "VOLUMETRIC_LADDER_DISCOUNT" -> VolumetricDiscountLadderEvaluator
            "LIP_GUARDRAIL" -> LipGuardrailEvaluator
            "FRESH_MANUAL_PRICE" -> FreshManualPriceEvaluator
            "MEAT_MANUAL_PRICE" -> MeatManualPriceEvaluator
            "FRESH_COGS_FLOOR_GUARDRAIL" -> FreshCogsFloorGuardrailEvaluator
            "MEAT_COGS_FLOOR_GUARDRAIL" -> MeatCogsFloorGuardrailEvaluator
            "MEAT_HYPERPURE_COMP_MATCH" -> MeatHyperpureCompMatchEvaluator
            "FRESH_HYPERPURE_COMP_MATCH" -> FreshHyperpureCompMatchEvaluator
            "FRESH_NINJACART_COMP_MATCH" -> FreshNinjacartCompMatchEvaluator
            "PL_BENCHMARK_LISTING_CEIL_GUARDRAIL" -> PLBenchmarkListingCeilGuardrailEvaluator
            else -> error("Unknown evaluator identifier provided: ${evaluatorRequestContext.strategy.name}")
        }
    }
}
