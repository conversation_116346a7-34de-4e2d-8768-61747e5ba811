package com.udaan.pricing.core.dao.network

import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.network.DemandCluster
import kotlinx.coroutines.flow.toList

/**
 * This class provides fun related to cosmos actions for demand_cluster container
 */
object DemandClusterRepository {
    private val documentDbDao by lazy {
        CosmosDbDao(
            configKey = "pricing",
            databaseName = DbConstants.pricing_network_db,
            containerName = DbConstants.demand_cluster_container
        )
    }

    private fun DemandCluster.toDocument() = jacksonObjectMapper().convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toDemandCluster() = jacksonObjectMapper().convertValue(this, DemandCluster::class.java)

    suspend fun createOrUpdate(demandCluster: DemandCluster): DemandCluster {
        return documentDbDao.createOrUpdateItem(demandCluster.toDocument()).toDemandCluster()
    }

    /**
     * This method is used to reduce the connection time to cosmos, used at the time of warmup
     */
    suspend fun initialise() {
        getClusterForId("")
    }

    suspend fun getClusterForId(demandClusterId: String): DemandCluster? {
        return documentDbDao.queryItems(
            "getClusterForId",
            makeSqlQuerySpec("""
                select * from c
                where c.id = "$demandClusterId"
                and c.state="ACTIVE"
            """.trimIndent())
        ).toList().map { it.toDemandCluster() }.firstOrNull()
    }

    suspend fun getClusterForAnchorCity(anchorCity: String): DemandCluster? {
        return documentDbDao.queryItems(
            "getClusterForAnchorCity",
            makeSqlQuerySpec("""
                select * from c
                where lower(c.anchorCityName) = lower("$anchorCity")
                and c.state="ACTIVE"
            """.trimIndent())
        ).toList().map { it.toDemandCluster() }.firstOrNull()
    }


}