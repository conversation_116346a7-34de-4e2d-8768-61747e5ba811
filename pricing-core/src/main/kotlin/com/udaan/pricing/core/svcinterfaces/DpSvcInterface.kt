package com.udaan.pricing.core.svcinterfaces

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.dataplatform.client.DataPlatformDownloadClient
import com.udaan.dataplatform.client.DataPlatformUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.InputStream

@Singleton
class DpServiceInterface @Inject constructor(
    private val dataPlatformDownloadClient: DataPlatformDownloadClient,
    val dataPlatformUtils: DataPlatformUtils
) {

    suspend fun getProbe(probeId: String): InputStream {
        return dataPlatformDownloadClient.downloadDataItem(probeId).executeAwait(3)
    }

    suspend inline fun <reified T> getProbeData(probeId: String): List<T> {
        return withContext(Dispatchers.IO) {
            dataPlatformUtils.getProbeReader<T>(
                probeId
            ).asSequence().toList()
        }
    }
}
