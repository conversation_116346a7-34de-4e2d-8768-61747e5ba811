package com.udaan.pricing.core.helpers.signals.variableresolvers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.pricing.core.helpers.PricingNetworkHelper
import com.udaan.pricing.core.managers.signals.SignalReadManager
import com.udaan.pricing.core.models.signals.CatalogEntityContext
import com.udaan.pricing.core.models.signals.LocationContext
import com.udaan.pricing.core.utils.signals.SignalUtil
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.variable.Variable
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.variable.requestreponse.ResolvedValue
import com.udaan.pricing.variable.requestreponse.ResolverLogic

/**
 * Currently we fetch LPP/LIP basis below logic:
 * - fetch the latest signal for all Whs in geo city
 * - apply hard expiry
 *      - for LIP, apply hard expiry (variable level expiry) + state check for the fallback signal
 *      - this part is insignificant for LPP as no hard expiry at variable level
 * - serve final signal as derived if valid
 *
 */
@Singleton
class StaplesCogsProxiesResolver @Inject constructor(
    private val signalReadManager: SignalReadManager,
    private val pricingNetworkHelper: PricingNetworkHelper
): VariableResolver {

    override suspend fun resolve(
        catalogEntityContext: CatalogEntityContext,
        locationContext: LocationContext,
        variable: Variable
    ): Pair<VariableId, ResolvedValue> {
        var selectedSignal: Signal? = null
        var resolverLogic: ResolverLogic? = null
        var exceptionMessage: String? = null

        try {
            val productGroupId = catalogEntityContext.productGroupId
                ?: throw IllegalStateException("No mapped GID found")

            // get all Whs for geo city and the applicable LIP/LPP signal
            // (the most recent signal among the whole network applied with hard expiry check)
            val finalSignalValue = getFinalSignal(
                productGroupId = productGroupId,
                geoCity = locationContext.city,
                variableId = variable.id,
                freshnessDurationInMillis = variable.freshnessDurationInMillis
            )

            // setting resolver logic
            resolverLogic = ResolverLogic.VALUE_FROM_SIGNAL

            selectedSignal = finalSignalValue
        } catch (ex: Exception) {
            exceptionMessage = ex.message
        }

        val finalValue = if (selectedSignal != null) {
            selectedSignal.signalData
        } else {
            resolverLogic = if (exceptionMessage != null) {
                ResolverLogic.DEFAULT_VALUE_AS_EXCEPTION_OCCURRED
            } else {
                ResolverLogic.DEFAULT_VALUE_AS_SIGNAL_ABSENT_OR_EXPIRED
            }
            variable.defaultValue
        }

        // adding whId to metadata - this is important to know which WH signal is finally served post fallback logic
        val finalMetadata = selectedSignal?.let {
            SignalUtil.addWarehouseIdToGenericMetadata(it.metadata, it.location.locationValue)
        }

        val resolvedValue = ResolvedValue(
            success = (exceptionMessage == null && finalValue != null),
            value = finalValue,
            referenceSignalId = selectedSignal?.referenceId,
            resolverLogic = resolverLogic,
            exception = exceptionMessage,
            metadata = finalMetadata,
            lastRefreshedAt = selectedSignal?.updatedAt
        )
        return Pair(variable.id, resolvedValue)
    }

    /**
     * Provides signal for LIP/LPP case.
     * Logic used is:
     * 1. fetch all WHs which are mapped to city (geoCity)
     * 2. fetch signals for WHs
     * 3. pick most recent signal amongst all signals
     * 4. apply variable level expiry check if applicable to derive final value
     *
     * @see PricingNetworkHelper.fetchStaplesCogsWhsForGeoCity
     */
    suspend fun getFinalSignal(
        productGroupId: String,
        geoCity: String,
        variableId: VariableId,
        freshnessDurationInMillis: Long?
    ): Signal? {
        // fetch signals for WHs for geo city
        val geoCityWhs = try {
            pricingNetworkHelper.fetchStaplesCogsWhsForGeoCity(geoCity = geoCity)
        } catch (ex: Exception) {
            throw IllegalArgumentException(
                "Error in locating all WHs for geo city ${geoCity}, ", ex
            )
        }
        val geoCityWhsSignals = signalReadManager.getAllSignalsForLocations(
            catalogEntity = productGroupId,
            variableId = variableId,
            locationValues = geoCityWhs
        )

        // get the latest signal from all WHs for the geo city
        val latestLipSignalInCluster = geoCityWhsSignals.maxByOrNull { it.createdAt }
        // apply hard (variable level) expiry check to get final signal
        return SignalUtil.signalValuePostExpiryCheck(
            applicableSignal = latestLipSignalInCluster,
            freshnessDurationInMillis = freshnessDurationInMillis
        )
    }
}
