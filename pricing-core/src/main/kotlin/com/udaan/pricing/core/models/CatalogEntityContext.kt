package com.udaan.pricing.core.models

import com.udaan.catalog.model.VerticalCategory
import com.udaan.catalog.models.ModelV2
import com.udaan.firstpartycatalog.models.Product
import com.udaan.vertical.model.CentralVertical

data class CatalogEntityContext(
    val listingId: String,
    val salesUnitId: String?,
    val listingDetail: ModelV2.TradeListing,
    val fpProductDetails: Product?,
    val productGroupId: String?,
    val vertical: CentralVertical,
    val verticalCategory: VerticalCategory
) {
    fun fetchSalesUnits(): List<String> {
        return if (salesUnitId != null) {
            listOf(salesUnitId)
        } else {
            listingDetail.salesUnitList.filter {
                it.status in listOf(ModelV2.EleStatus.ENABLED, ModelV2.EleStatus.INACTIVE)
            }.map { it.salesUnitId }.toList()
        }
    }
}
