package com.udaan.pricing.core.dao.network

import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.network.DemandClusterLocations
import kotlinx.coroutines.flow.toList

/**
 * This class provides fun related to cosmos actions for demand_cluster_locations container
 */
object DemandClusterLocationsRepository {
    private val documentDbDao by lazy {
        CosmosDbDao(
            configKey = "pricing",
            databaseName = DbConstants.pricing_network_db,
            containerName = DbConstants.demand_cluster_locations_container
        )
    }

    private fun DemandClusterLocations.toDocument() = jacksonObjectMapper().convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toDemandClusterLocations() = jacksonObjectMapper().convertValue(this, DemandClusterLocations::class.java)

    /**
     * This method is used to reduce the connection time to cosmos, used at the time of warmup
     */
    suspend fun initialise() {
        getLocationForName("")
    }

    suspend fun createOrUpdate(demandClusterLocations: DemandClusterLocations): DemandClusterLocations {
        return documentDbDao.createOrUpdateItem(demandClusterLocations.toDocument()).toDemandClusterLocations()
    }

    suspend fun getLocationForName(locationName: String): DemandClusterLocations? {
        return documentDbDao.queryItems(
            "getLocationForName",
            makeSqlQuerySpec("""
                select * from c
                where lower(c.name)=lower("$locationName")
                and c.state="ACTIVE"
            """.trimIndent())
        ).toList().map { it.toDemandClusterLocations() }.firstOrNull()
    }

    suspend fun getAllLocationsForClusterId(id: String): List<DemandClusterLocations> {
        return documentDbDao.queryItems(
            "getAllLocationsForClusterId",
            makeSqlQuerySpec("""
                select * from c
                where c.demandClusterId="$id"
                and c.state="ACTIVE"
            """.trimIndent())
        ).toList().map { it.toDemandClusterLocations() }
    }

    suspend fun getAllLocationsForCity(city: String): List<DemandClusterLocations> {
        return documentDbDao.queryItems(
            "getAllLocationsForCity",
            makeSqlQuerySpec("""
                select * from c
                where lower(c.city)=lower("$city")
                and c.state="ACTIVE"
            """.trimIndent())
        ).toList().map { it.toDemandClusterLocations() }
    }

    suspend fun getAllLocationsForNames(locationNamesWithCityInLocChain: List<String>): List<DemandClusterLocations> {
        return documentDbDao.queryItems(
            "getAllLocationsForNames",
            makeSqlQuerySpec("""
                select * from c
                where lower(c.name) in ${
                locationNamesWithCityInLocChain.joinToString(
                    prefix = "('",
                    postfix = "')",
                    separator = "','"
                )}
                and c.state="ACTIVE"
            """.trimIndent())
        ).toList().map { it.toDemandClusterLocations() }
    }

    suspend fun getLocationsForWarehouse(warehouseId: String): List<DemandClusterLocations> {
        return documentDbDao.queryItems(
            "getLocationsForWarehouse",
            makeSqlQuerySpec("""
                SELECT distinct value c
                FROM c
                join ffCenters in c.fulfilmentCenters
                where lower(ffCenters.whId)=lower("$warehouseId")
                and c.state="ACTIVE"
            """.trimIndent())
        ).toList().map { it.toDemandClusterLocations() }
    }
    
    suspend fun getAllLocations(): List<DemandClusterLocations> {
        return documentDbDao.queryItems(
            "getAllLocations",
            makeSqlQuerySpec("select * from c where c.state = 'ACTIVE' ")
        ).toList().map { it.toDemandClusterLocations() }
    }
}
