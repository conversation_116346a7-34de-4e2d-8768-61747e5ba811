package com.udaan.pricing.core.helpers.rider.impl.bot

import com.google.inject.Inject
import kotlinx.coroutines.future.await
import java.time.Duration

class BotIdentifier @Inject constructor(private val builder: BotDetectionDbBuilderBase) {
    private val botDB by periodically<BotDetectionDB>(Duration.ofHours(12)) {
        builder.loadData() ?: BotDetectionDB.EMPTY
    }

    suspend fun isBot(orgId: String, userId: String?, clientIp: String?): Boolean {
        return botDB.await().let {
            clientIp?.let { ip -> it.matchesIp(ip) } ?: false
                    || userId?.let { uId -> it.matchesUserId(uId) } ?: false
                    || it.matchesOrgId(orgId)
        }
    }

    suspend fun isBotIdentifiedByIp(clientIp: String): Boolean {
        return botDB.await().matchesIp(clientIp)
    }
}
