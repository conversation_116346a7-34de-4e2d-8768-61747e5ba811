package com.udaan.pricing.core.utils

import com.udaan.catalog.model.VerticalCategory
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorConfigInput
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.commons.StringValue
import com.udaan.pricing.variable.VariableId
import java.math.BigDecimal

object CompMatchUtils {

    private val RUPEES_TO_PAISE_FACTOR = BigDecimal(100)
    private const val VSL_CHANNEL_JIT = "FP_JIT"

    /**
     * Fetches the comp price without tax at assortment level.
     */
    fun getCompLadderPriceWithoutTaxAtAssortment(
        compVariableId: VariableId,
        conversionRate: BigDecimal,
        inputs: List<EvaluatorConfigInput>,
        verticalCategory: VerticalCategory,
        outputMetadata: MutableMap<String, String>
    ): LadderValue? {
        val gstBps = (VariableUtils.getApplicableVariableValueForVariable(
            inputs,
            VariableId.GST_BPS
        ) as? BigDecimalValue)?.value ?: throw IllegalArgumentException("GST_BPS is mandatory for JT comp match")
        val cessBps = (VariableUtils.getApplicableVariableValueForVariable(
            inputs,
            VariableId.CESS_BPS
        ) as? BigDecimalValue)?.value ?: throw IllegalArgumentException("CESS_BPS is mandatory for JT comp match")
        val taxFactor = (gstBps + cessBps).divideWithScale(BigDecimal(10000)).add(BigDecimal(1))

        val compPriceWithTaxPaisaUnit = (VariableUtils.getApplicableVariableValueForVariable(
            inputs,
            compVariableId
        ) as? LadderValue)?.value

        // removing tax from comp value
        val compPriceWithoutTaxPaisaUnit = compPriceWithTaxPaisaUnit?.map {
            it.copy(
                ladderValue = it.ladderValue.divideWithScale(taxFactor)
            )
        }
        // returning with conversion rate applied
        return compPriceWithoutTaxPaisaUnit?.let {
            LadderValue(
                value = LadderUtils.applyConversionRateOnLadders(
                    conversionRate = conversionRate,
                    ladders = it,
                    verticalCategory = verticalCategory,
                    outputMetadata = outputMetadata
                )
            )
        }
    }

    /**
     * Applies markdown bps to the comp price.
     */
    fun applyCompMarkdownBps(
        compMarkDownBps: BigDecimal?,
        compPriceWithoutTaxPaisaAssortment: LadderValue
    ): LadderValue {
        val compPriceWithMarkupInTaxPaisaUnit = compMarkDownBps?.let {
            compPriceWithoutTaxPaisaAssortment.value.map {
                it.copy(
                    ladderValue = it.ladderValue.multiplyWithScale(
                        (BigDecimal(1) - compMarkDownBps.divideWithScale(BigDecimal(10000)))
                    )
                )
            }
        } ?: compPriceWithoutTaxPaisaAssortment.value
        return LadderValue(compPriceWithMarkupInTaxPaisaUnit)
    }

    /**
     * Fetches the comp MRP and listing MRP, and returns true if MRP matches else false.
     * Updates metadata with MRP values and match status.
     */
    fun isCompMrpMatching(
        compMrpVariableId: VariableId,
        conversionRate: BigDecimal,
        inputs: List<EvaluatorConfigInput>,
        outputMetadata: MutableMap<String, String>
    ): Boolean {
        // comp MRP
        val compMrpWithTaxPaiseUnit = (VariableUtils.getApplicableVariableValueForVariable(
            inputs,
            compMrpVariableId
        ) as? BigDecimalValue)?.value

        // udaan's MRP
        val udaanMrpWithTaxPaiseUnit = (VariableUtils.getApplicableVariableValueForVariable(
            inputs,
            VariableId.MRP_WT_PAISA_SET
        ) as? BigDecimalValue)?.value?.divideWithScale(conversionRate)

        outputMetadata["LISTING_MRP_WITH_TAX_PAISE_UNIT"] = udaanMrpWithTaxPaiseUnit.toString()
        outputMetadata["COMP_MRP_WITH_TAX_PAISE_UNIT"] = compMrpWithTaxPaiseUnit.toString()

        return (compMrpWithTaxPaiseUnit != null && udaanMrpWithTaxPaiseUnit != null
                && compMrpWithTaxPaiseUnit.compareTo(udaanMrpWithTaxPaiseUnit) == 0).also {
            if (it) {
                outputMetadata["MRP_MATCHED"] = "true"
            } else outputMetadata["MRP_MATCHED"] = "false"
        }
    }

    /**
     * Fetches the floor guardrail value for comp by converting
     * guardrail input passed to without tax, in paise, at assortment level (if not already).
     *
     * For Staples, if VSL selected channel is JIT, guardrail is returned as JIT best vendor price.
     * Else, it fetches the guardrail value based on the variableId passed.
     *
     * Supports below inputs as valid guardrails currently:
     * - STAPLES_LIP_WOT_PAISA_UNIT
     * - STAPLES_LPP_WOT_RUPEES_UNIT
     * - FMCG_COGS_WOT_PAISA_SET
     * - FMCG_LPP_WOT_RUPEES_UNIT
     * - MEAT_COGS_WOT_PAISA_UNIT
     * - FRESH_COGS_WOT_PAISA_UNIT
     *
     * If guardrail variable is anything other than above, it will throw IllegalArgumentException.
     */
    fun getFloorGuardrailForCompInPaiseAtAssortment(
        inputs: List<EvaluatorConfigInput>,
        variableId: VariableId,
        conversionRate: BigDecimal,
        verticalCategory: VerticalCategory? = null,
        outputMetadata: MutableMap<String, String> = mutableMapOf()
    ): BigDecimal? {
        // if staples, check for VSL selected channel
        if (verticalCategory == VerticalCategory.STAPLES) {
            val vslChannel = (VariableUtils.getApplicableVariableValueForVariable(
                inputs,
                VariableId.VSL_SELECTED_CHANNEL
            ) as? StringValue)?.value

            // for JIT VSL channel, use JIT value as guardrail
            if (vslChannel.equals(VSL_CHANNEL_JIT, true)) {
                outputMetadata["JIT_GUARDRAIL_AS_JIT_CHANNEL_SELECTED"] = "true"

                val jitPriceWithoutTaxPaisaAtAssortment = (VariableUtils.getApplicableVariableValueForVariable(
                    inputs,
                    VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT
                ) as? BigDecimalValue)?.value?.multiplyWithScale(conversionRate)
                outputMetadata["JIT_PRICE_WITHOUT_TAX_PAISE_ASSORTMENT"] =
                    jitPriceWithoutTaxPaisaAtAssortment.toString()

                return jitPriceWithoutTaxPaisaAtAssortment
            }
        }
        // if not JIT channel for Staples, use variableId passed to fetch guardrail
        return when (variableId) {
            VariableId.STAPLES_LIP_WOT_PAISA_UNIT -> {
                (VariableUtils.getApplicableVariableValueForVariable(
                    inputs,
                    variableId
                ) as? BigDecimalValue)?.value?.multiplyWithScale(conversionRate)
            }

            VariableId.STAPLES_LPP_WOT_RUPEES_UNIT -> {
                (VariableUtils.getApplicableVariableValueForVariable(
                    inputs,
                    variableId
                ) as? BigDecimalValue)?.value?.multiplyWithScale(conversionRate)
                    ?.multiplyWithScale(RUPEES_TO_PAISE_FACTOR)
            }

            VariableId.FMCG_COGS_WOT_PAISA_SET -> {
                (VariableUtils.getApplicableVariableValueForVariable(
                    inputs,
                    variableId
                ) as? BigDecimalValue)?.value
            }

            VariableId.FMCG_LPP_WOT_RUPEES_UNIT -> {
                (VariableUtils.getApplicableVariableValueForVariable(
                    inputs,
                    variableId
                ) as? BigDecimalValue)?.value?.multiplyWithScale(conversionRate)
                    ?.multiplyWithScale(RUPEES_TO_PAISE_FACTOR)
            }

            VariableId.MEAT_COGS_WOT_PAISA_UNIT -> {
                (VariableUtils.getApplicableVariableValueForVariable(
                    inputs,
                    variableId
                ) as? BigDecimalValue)?.value?.multiplyWithScale(conversionRate)
            }

            VariableId.FRESH_COGS_WOT_PAISA_UNIT -> {
                (VariableUtils.getApplicableVariableValueForVariable(
                    inputs,
                    variableId
                ) as? BigDecimalValue)?.value?.multiplyWithScale(conversionRate)
            }

            else -> throw IllegalArgumentException("Unsupported variableId: $variableId")
        }
    }

    /**
     * Fetches the quantity types for both comp and listing, and returns true if they match else false.
     * Updates metadata with quantity type values and match status.
     *
     * @param compQtyTypeVariableId Variable ID for competitor's quantity type
     * @param inputs List of evaluator config inputs containing variable values
     * @param outputMetadata Mutable map to store metadata about the comparison
     * @return Boolean indicating if both quantity types match
     */
    fun isQuantityTypeMatching(
        compQtyTypeVariableId: VariableId,
        inputs: List<EvaluatorConfigInput>,
        outputMetadata: MutableMap<String, String>
    ): Boolean {
        // fetch comp quantity type
        val compQtyType = (VariableUtils.getApplicableVariableValueForVariable(
            inputs,
            compQtyTypeVariableId
        ) as? StringValue)?.value

        // fetch listing quantity type
        val listingQtyType = (VariableUtils.getApplicableVariableValueForVariable(
            inputs,
            VariableId.QUANTITY_TYPE
        ) as? StringValue)?.value

        outputMetadata["LISTING_QUANTITY_TYPE"] = listingQtyType.toString()
        outputMetadata["COMP_QUANTITY_TYPE"] = compQtyType.toString()

        return (compQtyType != null && listingQtyType != null
                && compQtyType.equals(listingQtyType, true)).also {
            if (it) {
                outputMetadata["QUANTITY_TYPE_MATCHED"] = "true"
            } else outputMetadata["QUANTITY_TYPE_MATCHED"] = "false"
        }
    }

    /**
     * Translates competitor's ladder to listing's pack size based on quantity per unit of both.
     *
     * @param compLadderValue The competitor's ladder value to be translated
     * @param compQuantityPerUnitVariable Variable ID for competitor's quantity per unit
     * @param inputs List of evaluator config inputs containing variable values
     * @param outputMetadata Mutable map to store metadata about the translation status
     * @return Translated LadderValue adjusted to listing's pack size, or null if quantity per unit values are missing
     *
     * @see [LadderUtils.translateCompLaddersToListingPackSize]
     */
    fun translateCompToListingPackSize(
        compLadderValue: LadderValue,
        compQuantityPerUnitVariable: VariableId,
        inputs: List<EvaluatorConfigInput>,
        outputMetadata: MutableMap<String, String>
    ): LadderValue? {
        // fetch comp quantity per unit
        val compQuantityPerUnit = (VariableUtils.getApplicableVariableValueForVariable(
            inputs,
            compQuantityPerUnitVariable
        ) as? BigDecimalValue)?.value

        // fetch quantity per unit
        val listingQuantityPerUnit = (VariableUtils.getApplicableVariableValueForVariable(
            inputs,
            VariableId.QUANTITY_PER_UNIT
        ) as? BigDecimalValue)?.value

        // return null if any of the qty per unit values are null
        if (compQuantityPerUnit == null || listingQuantityPerUnit == null) {
            outputMetadata["COMP_CONVERTED_TO_LISTING_PACK_SIZE"] = "false"
            return null
        }

        // apply translation and return LadderValue
        return LadderValue(
            value = LadderUtils.translateCompLaddersToListingPackSize(
                compLadders = compLadderValue.value,
                compQtyPerUnit = compQuantityPerUnit,
                listingQtyPerUnit = listingQuantityPerUnit
            )
        ).also {
            outputMetadata["COMP_CONVERTED_TO_LISTING_PACK_SIZE"] = "true"
        }
    }
}
