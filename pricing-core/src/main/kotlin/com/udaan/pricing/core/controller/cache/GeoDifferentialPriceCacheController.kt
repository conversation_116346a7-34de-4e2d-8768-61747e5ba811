package com.udaan.pricing.core.controller.cache

import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.common.utils.parallelMap
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.FetchGeoPricingRequest
import com.udaan.pricing.GeoPricing
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.dao.GeoPriceRepository
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await

class GeoDifferentialPriceCacheController @Inject constructor(
    @Named(NamedConstants.Caches.LISTING_GEO_DIFF_PRICE_CACHE) private val geoDifferentialPriceCache: RedisCache2<List<GeoPricing>>,
    private val geoPriceRepository: GeoPriceRepository
) {
    suspend fun getActiveGeoDiffPriceForListing(fetchGeoPricingRequest: FetchGeoPricingRequest): List<GeoPricing> {
        return geoDifferentialPriceCache.get(fetchGeoPricingRequest.listingId) {
            TelemetryScope.future {
                geoPriceRepository.getGeoPricing(fetchGeoPricingRequest)
            }
        }.await() ?: throw IllegalArgumentException("Geo diff price not found for ${fetchGeoPricingRequest.listingId}")
    }

    suspend fun invalidateGeoDiffForListing(listingId: String?) {
        if (listingId != null) {
            geoDifferentialPriceCache.invalidate(listingId).await()
        }
    }

    suspend fun invalidateGeoDiffForListings(listingIds: Collection<String>) {
        listingIds.parallelMap { listingId ->
            geoDifferentialPriceCache.invalidate(listingId).await()
        }
    }
}
