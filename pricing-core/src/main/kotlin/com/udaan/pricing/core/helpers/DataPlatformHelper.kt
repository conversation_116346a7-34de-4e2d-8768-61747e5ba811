package com.udaan.pricing.core.helpers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.dataplatform.client.DPEventsIngestionClient
import com.udaan.dataplatform.client.DataPlatformUtils
import com.udaan.pricing.core.svcinterfaces.DpServiceInterface
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.InputStream
import kotlin.sequences.asSequence
import kotlin.sequences.toList
import kotlin.text.lowercase

@Singleton
class DataPlatformHelper @Inject constructor(
    val dataPlatformUtils: DataPlatformUtils,
    private val dpSvcInterface: DpServiceInterface
) {
    companion object {
        private const val DP_EVENTS_TEAM_NAME = "pricing"
    }

    fun trackEvent(
        eventData: Map<String, Any>,
        eventName: TrackEventName,
        referenceId1: String,
        referenceId2: String
    ) {
        DPEventsIngestionClient.logEvent(
            teamName = DP_EVENTS_TEAM_NAME,
            eventData = eventData,
            eventName = eventName.name.lowercase(),
            refId1 = referenceId1,
            refId2 = referenceId2
        )
    }

    suspend fun getProbe(probeId: String): InputStream {
        return dpSvcInterface.getProbe(probeId)
    }

    suspend inline fun <reified T> getDataFromDataPlatform(
        probeId: String
    ): List<T> {
        return withContext(Dispatchers.IO) {
            dataPlatformUtils.getProbeReader<T>(probeId).asSequence().toList()
        }
    }

    enum class TrackEventName {
        PORTFOLIO_ITEM_AUDIT,
        TRADING_PORTFOLIO_ITEM_AUDIT,
        LISTING_SSC_PRICE_METADATA_AUDIT,
        CONTRACT_PRICE_AUDIT,
        MANUAL_PRICE_AUDIT,
        SIGNAL_AUDIT
    }
}
