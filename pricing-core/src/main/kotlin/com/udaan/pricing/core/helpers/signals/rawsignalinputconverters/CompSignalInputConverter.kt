package com.udaan.pricing.core.helpers.signals.rawsignalinputconverters

import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.core.utils.signals.BigDecimalUtil.roundToDefaultScale
import com.udaan.pricing.core.utils.signals.OnlineCompetitionInputsUtil
import com.udaan.pricing.signalcreation.CompSignalInput
import com.udaan.pricing.signals.CompSignalMetadata
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState

class CompSignalInputConverter: RawSignalInputConverter<CompSignalInput>() {
    companion object {
        private val logger by logger()
    }

    override suspend fun convert(rawSignalInput: CompSignalInput): List<Signal> {
        val signalEntity = rawSignalInput.catalogEntityId

        val compPriceSlabs = when {
            rawSignalInput.sellingLadder.isNotEmpty() -> rawSignalInput.sellingLadder
            rawSignalInput.unitPriceInPaisa != null -> listOf(
                Ladder(1, Int.MAX_VALUE.toLong(), rawSignalInput.unitPriceInPaisa!!.toBigDecimal())
            )
            else -> null
        }

        require (compPriceSlabs != null) {
            "Comp data has no ladder value to consider."
        }

        return OnlineCompetitionInputsUtil.getVariableIdForLadderCompPrice(rawSignalInput.competitionName)
            ?.let { variableId ->
                val convertedSignal = Signal(
                    catalogEntity = signalEntity.uppercase(),
                    catalogEntityType = rawSignalInput.catalogEntityType,
                    variableId = variableId.name,
                    signalData = LadderValue(
                        value = compPriceSlabs.map {
                            it.copy(ladderValue = it.ladderValue.roundToDefaultScale())
                        }
                    ),
                    metadata = CompSignalMetadata(
                        mrpInPaisa = rawSignalInput.mrpInPaisa,
                        sellingPriceLadder = rawSignalInput.sellingLadder,
                        benchmarkRefId = rawSignalInput.benchmarkRefId,
                        quantityPerUnit = rawSignalInput.quantityPerUnit,
                        quantityType = rawSignalInput.quantityType,
                        weightPerPcGrams = rawSignalInput.weightPerPcGrams
                    ),
                    location = Location(
                        locationType = rawSignalInput.location.locationType,
                        locationValue = rawSignalInput.location.locationValue.uppercase()
                    ),
                    state = SignalState.ACTIVE,
                    createdBy = rawSignalInput.benchmarkRefId,
                    updatedBy = rawSignalInput.benchmarkRefId
                )
                logger.info("converted signal {}", convertedSignal)
                listOf(convertedSignal)
            } ?: run {
            logger.info("No competition variable mapping exists for ${rawSignalInput.competitionName}")
            emptyList()
        }
    }

}
