package com.udaan.pricing.core.managers.signals

import com.google.inject.Inject
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.instrumentation.Telemetry
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.dao.signals.VariableRepository
import com.udaan.pricing.core.helpers.PricingNetworkHelper
import com.udaan.pricing.core.helpers.signals.CatalogEntityHelper
import com.udaan.pricing.core.helpers.signals.ResolvedValuesRedisHelper
import com.udaan.pricing.core.helpers.signals.variableresolvers.VariableResolverFactory
import com.udaan.pricing.core.utils.signals.TerritoryUtil
import com.udaan.pricing.core.utils.signals.VariableUtil.validateVariableConstraintsAndDefault
import com.udaan.pricing.core.utils.signals.launchOnIO
import com.udaan.pricing.signals.GenericMetadata
import com.udaan.pricing.variable.Variable
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.variable.VariableRedisUtils
import com.udaan.pricing.variable.VariableRequest
import com.udaan.pricing.variable.convert
import com.udaan.pricing.variable.requestreponse.BmtDataResponse
import com.udaan.pricing.variable.requestreponse.GetResolvedValuesResponse
import com.udaan.pricing.variable.requestreponse.JitVendorBestPriceCityGidResponse
import com.udaan.pricing.variable.requestreponse.JitVendorBestPriceWhGid
import com.udaan.pricing.variable.requestreponse.SourcingModel
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await
import javax.ws.rs.BadRequestException
import javax.ws.rs.NotFoundException
import kotlin.String

@Singleton
class VariableManager @Inject constructor(
    private val variableRepository: VariableRepository,
    @Named(NamedConstants.Caches.VARIABLE_CACHE) private val variableCache: RedisCache2<List<Variable>>,
    private val resolvedValuesRedisHelper: ResolvedValuesRedisHelper,
    private val variableResolverFactory: VariableResolverFactory,
    private val catalogEntityHelper: CatalogEntityHelper,
    private val pricingNetworkHelper: PricingNetworkHelper,
    private val signalReadManager: SignalReadManager
) {
    companion object {
        private val logger by logger()

        // vendorId key in metadata for JIT signal
        private const val JIT_SIGNAL_VENDOR_ID_KEY = "vendorId"
    }

    suspend fun createVariable(variableRequest: VariableRequest): Variable {
        val variableToCreate = variableRequest.convert()
        val existingVariable = variableRepository.getVariableById(variableToCreate.id.name)
        if (existingVariable == null) {
            variableToCreate.validateVariableConstraintsAndDefault()

            logger.info("Creating new variable {}", variableToCreate)
            return variableRepository.createVariable(variableToCreate).also {
                variableCache.invalidate(VariableRedisUtils.VARIABLE_ALL_KEY)
            }
        } else {
            throw BadRequestException("Variable with id ${variableRequest.id} already exists")
        }
    }

    suspend fun getVariable(id: String): Variable {
        return variableCache.get(id) {
            TelemetryScope.future {
                listOf(
                    variableRepository.getVariableById(id)
                        ?: throw NotFoundException("Variable not found with id $id")
                )
            }
        }.await()?.firstOrNull() ?: throw NotFoundException("Variable not found with id $id")
    }

    suspend fun getVariableOrNull(id: String): Variable? {
        return try {
            getVariable(id)
        } catch (e: NotFoundException) {
            null
        }
    }

    suspend fun getAllVariables(): List<Variable> {
        return variableCache.get(VariableRedisUtils.VARIABLE_ALL_KEY) {
            TelemetryScope.future {
                variableRepository.getAllVariables()
            }
        }.await() ?: emptyList()
    }

    /**
     * Resolves values for a listing-salesUnit combination with caching.
     * First attempts to fetch values from cache, if not found or incomplete, resolves values from source.
     *
     * @param listingId The ID of the listing
     * @param salesUnitId The ID of the sales unit
     * @param location The location context for value resolution
     * @param variableIds Set of variable IDs to resolve values for
     * @param inputTerritoryMap Optional territory mapping for competitive data
     * @return [GetResolvedValuesResponse] containing resolved values for requested variables
     *
     * @see TerritoryUtil.mapTerritoryKeysToVariableIds
     * @see resolveValuesForListingSalesUnit
     */
    suspend fun resolveValuesForListingSalesUnitCached(
        listingId: String,
        salesUnitId: String,
        location: Location,
        variableIds: Set<VariableId>,
        inputTerritoryMap: Map<String, String>?
    ): GetResolvedValuesResponse {
        val modifiedLocation = location.copy(
            locationValue = location.locationValue.uppercase()
        )
        val modifiedTerritoryMap =
            inputTerritoryMap?.let { TerritoryUtil.mapTerritoryKeysToVariableIds(inputTerritoryMap) }

        val cachedValues = resolvedValuesRedisHelper.getListingSalesUnitLocationValues(
            listingId = listingId,
            salesUnitId = salesUnitId,
            location = modifiedLocation,
            variables = variableIds.map { it.name },
            territoryRefIds = modifiedTerritoryMap?.values?.toList()
        )

        val telemetryMap = mapOf(
            "listingId" to listingId,
            "location" to modifiedLocation.locationValue,
            "variables" to cachedValues.keys.joinToString(),
            "null-variables" to cachedValues.filterValues { it.success }.keys.joinToString()
        )
        Telemetry.trackEvent(
            name = "resolved-values-from-lid-cache",
            properties = inputTerritoryMap?.let {
                telemetryMap.plus(it)
            } ?: telemetryMap
        )

        return if (cachedValues.isNotEmpty() && cachedValues.keys == variableIds.toSet()) {
            GetResolvedValuesResponse(cachedValues)
        } else {
            resolveValuesForListingSalesUnit(
                listingId = listingId,
                salesUnitId = salesUnitId,
                location = modifiedLocation,
                variableIds = variableIds,
                territoryMap = modifiedTerritoryMap
            )
        }
    }

    /**
     * Resolves values for a product group with caching support.
     * First attempts to fetch values from cache, if not found or incomplete, resolves values from source.
     *
     * @param productGroupId The ID of the product group
     * @param location The location context for value resolution
     * @param variableIds Set of variable IDs to resolve values for
     * @param inputTerritoryMap Optional territory mapping for competitive data
     * @return [GetResolvedValuesResponse] containing resolved values for requested variables
     *
     * @see [TerritoryUtil.mapTerritoryKeysToVariableIds]
     * @see resolveValuesForProductGroup
     */
    suspend fun resolveValuesForProductGroupCached(
        productGroupId: String,
        location: Location,
        variableIds: Set<VariableId>,
        inputTerritoryMap: Map<String, String>?
    ): GetResolvedValuesResponse {
        val modifiedLocation = location.copy(
            locationValue = location.locationValue.uppercase()
        )
        val modifiedTerritoryMap =
            inputTerritoryMap?.let { TerritoryUtil.mapTerritoryKeysToVariableIds(inputTerritoryMap) }

        val cachedValues = resolvedValuesRedisHelper.getProductGroupLocationValues(
            productGroupId = productGroupId,
            location = modifiedLocation,
            variables = variableIds.map { it.name },
            territoryRefIds = modifiedTerritoryMap?.values?.toList()
        )

        val telemetryMap = mapOf(
            "productGroupId" to productGroupId,
            "location" to modifiedLocation.locationValue,
            "variables" to cachedValues.keys.joinToString(),
            "null-variables" to cachedValues.filterValues { it.success }.keys.joinToString()
        )
        Telemetry.trackEvent(
            name = "resolved-values-from-gid-cache",
            properties = inputTerritoryMap?.let {
                telemetryMap.plus(it)
            } ?: telemetryMap
        )

        return if (cachedValues.isNotEmpty() && cachedValues.keys == variableIds.toSet()) {
            GetResolvedValuesResponse(cachedValues)
        } else {
            resolveValuesForProductGroup(
                productGroupId = productGroupId,
                location = modifiedLocation,
                variableIds = variableIds,
                territoryMap = modifiedTerritoryMap
            )
        }
    }

    private suspend fun resolveValuesForListingSalesUnit(
        listingId: String,
        salesUnitId: String,
        location: Location,
        variableIds: Set<VariableId>,
        territoryMap: Map<VariableId, String>?
    ): GetResolvedValuesResponse {
        val catalogEntityContext = catalogEntityHelper.getCatalogEntityContextFromLidSuid(listingId, salesUnitId)
        val locationContext = pricingNetworkHelper.getLocationContext(location, territoryMap)
        val resolvedValuesMap = variableIds.parallelMap {
            val variable = getVariable(it.name)

            variableResolverFactory.getVariableResolver(variable.id).resolve(
                catalogEntityContext = catalogEntityContext,
                locationContext = locationContext,
                variable = variable
            )
        }.toMap()

        logger.info("Resolved values map: {}", resolvedValuesMap)

        return GetResolvedValuesResponse(resolvedValuesMap).also {
            launchOnIO {
                resolvedValuesRedisHelper.cacheListingSalesUnitLocationValues(
                    listingId, salesUnitId, location, resolvedValuesMap, territoryMap?.values?.toList()
                )
            }
        }
    }

    private suspend fun resolveValuesForProductGroup(
        productGroupId: String,
        location: Location,
        variableIds: Set<VariableId>,
        territoryMap: Map<VariableId, String>?
    ): GetResolvedValuesResponse {
        val catalogEntityContext = catalogEntityHelper.getCatalogEntityContextFromGid(productGroupId)
        val locationContext = pricingNetworkHelper.getLocationContext(location, territoryMap)
        val resolvedValuesMap = variableIds.parallelMap {
            val variable = getVariable(it.name)

            variableResolverFactory.getVariableResolver(variable.id).resolve(
                catalogEntityContext = catalogEntityContext,
                locationContext = locationContext,
                variable = variable
            )
        }.toMap()

        logger.info("Resolved values map: {}", resolvedValuesMap)

        return GetResolvedValuesResponse(resolvedValuesMap).also {
            launchOnIO {
                resolvedValuesRedisHelper.cacheProductGroupLocationValues(
                    productGroupId, location, resolvedValuesMap, territoryMap?.values?.toList()
                )
            }
        }
    }

    suspend fun resolveBmtValuesForProductGroup(
        productGroupId: String,
        location: Location,
        sourcingModel: SourcingModel
    ): BmtDataResponse? {
        val catalogEntityContext = catalogEntityHelper.getCatalogEntityContextFromGid(productGroupId)
        val locationContext = pricingNetworkHelper.getLocationContext(
            location = location,
            territoryMap = null
        )

        val bmtVariable = getVariable(VariableId.BMT_GID_BPS.name)

        val resolvedBmt = variableResolverFactory.getVariableResolver(bmtVariable.id).resolve(
            catalogEntityContext = catalogEntityContext,
            locationContext = locationContext,
            variable = bmtVariable
        ).second

        val bmtDataResponse = if (resolvedBmt.success) {
            val resolvedBmtMultiplier = if (sourcingModel == SourcingModel.WAREHOUSE) {
                val bmtMultiplier = getVariable(VariableId.BMT_MULTIPLIER.name)

                variableResolverFactory.getVariableResolver(bmtMultiplier.id).resolve(
                    catalogEntityContext = catalogEntityContext,
                    locationContext = locationContext,
                    variable = bmtMultiplier
                ).second
            } else {
                null
            }

            val finalBmtBps = if (resolvedBmtMultiplier?.success == true) {
                ((resolvedBmt.value) as BigDecimalValue).value * ((resolvedBmtMultiplier.value) as BigDecimalValue).value
            } else {
                ((resolvedBmt.value) as BigDecimalValue).value
            }

            BmtDataResponse(
                sourcingModel = sourcingModel,
                baseBmt = (resolvedBmt.value as BigDecimalValue).value.toDouble(),
                bmtMultiplier = (resolvedBmtMultiplier?.value as? BigDecimalValue)?.value?.toDouble(),
                baseBmtStaticDataIdForReference = resolvedBmt.referenceSignalId,
                bmtMultiplierStaticDataIdForReference = resolvedBmtMultiplier?.referenceSignalId,
                finalBmtBps = finalBmtBps.toDouble()
            )
        } else {
            logger.error("BMT value not found for group {} and {}", productGroupId, location.locationValue)
            null
        }

        logger.info("Bmt Margin Data V2 for {} and {} is {}", productGroupId, location.locationValue, bmtDataResponse)
        return bmtDataResponse
    }

    /**
     * Resolves the best JIT vendor price for a product group in a specific city.
     * This method fetches all applicable warehouses for the given city, retrieves JIT signals,
     * and determines the minimum vendor price across all warehouses.
     *
     * @param productGroupId The ID of the product group to get prices for
     * @param city The city name to search warehouses in
     * @return [JitVendorBestPriceCityGidResponse] containing the minimum JIT vendor price and reference data,
     *         or null if no JIT signals are found
     * @throws IllegalArgumentException if productGroupId or city is blank, or if no warehouses found for the city
     */
    suspend fun resolveJitVendorBestPriceForCityGid(
        productGroupId: String,
        city: String
    ): JitVendorBestPriceCityGidResponse? {
        // validate params
        if (productGroupId.isBlank() || city.isBlank()) {
            throw IllegalArgumentException("Product group ID and city cannot be empty")
        }

        // fetch applicable WHs for the geoCity passed
        val whsForGeoCity = pricingNetworkHelper.fetchAllWhsForGeoCity(city.lowercase())
        if(whsForGeoCity.isEmpty()) { throw IllegalArgumentException("No Warehouses found for ${city.lowercase()}") }

        // fetch applicable signals for WHs
        val jitSignalsForWhs = signalReadManager.getAllSignalsForLocations(
            catalogEntity = productGroupId,
            variableId = VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT,
            locationValues = whsForGeoCity
        )
        if (jitSignalsForWhs.isEmpty()) return null

        // get min value among all WHs + keeping all reference signals in metadata
        val jitMinValueAcrossCity =
            jitSignalsForWhs.minOfOrNull { signal -> (signal.signalData as BigDecimalValue).value.toDouble() }
                ?: return null
        val refJitValues = jitSignalsForWhs.map {
            JitVendorBestPriceWhGid(
                jitVendorBestPrice = (it.signalData as BigDecimalValue).value.toDouble(),
                warehouseId = it.location.locationValue,
                vendorId = (it.metadata as GenericMetadata).metadataMap[JIT_SIGNAL_VENDOR_ID_KEY],
                signalRefId = it.referenceId
            )
        }

        // returning response
        return JitVendorBestPriceCityGidResponse(
            city = city.uppercase(),
            productGroupId = productGroupId,
            jitVendorBestPrice = jitMinValueAcrossCity,
            refInputs = refJitValues
        ).also {
            logger.info("JIT vendor price response for {} and {} is {}", productGroupId, city, it)
        }
    }
}