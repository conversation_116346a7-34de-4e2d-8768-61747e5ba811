package com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg

import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.commons.StringValue
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorConfigInput
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import com.udaan.pricing.core.utils.LadderUtils
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.variable.VariableId
import java.math.BigDecimal

internal object SchemeMultiplierWithRetentionCapEvaluator : Evaluator {
    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        require(data.previousOutput != null) {
            "Previous output is mandatory for scheme multiplier with Retention Cap evaluator"
        }

        ValidationUtils.validateInputs(data.strategy, data.inputs)
        val outputMetadata = mutableMapOf<String, String>()
        val (retailScheme, retailSchemeMetadata) = curateFinalRetailScheme(data.inputs)
        outputMetadata.putAll(retailSchemeMetadata)

        val (finalRetailSchemeWithMultiplier, schemeMetadata) = applySchemeMultiplierWithRetentionCapping(
            data.inputs,
            retailScheme
        )
        outputMetadata.putAll(schemeMetadata)

        val puConversionRate = getPackagingUnitConversionRate(data.inputs).toLong()
        val convertedSchemesBasisPackagingUnit = finalRetailSchemeWithMultiplier?.let {
            LadderUtils.getAssortmentAdjustedLadder(
                conversionRate = puConversionRate,
                inputLadder = it
            )
        }

        val basePrice = data.previousOutput.output as BigDecimalValue
        val finalPriceWithSchemes = getAdjustedPricesWithRetailSchemes(
            basePrice.value,
            convertedSchemesBasisPackagingUnit,
            outputMetadata
        )
        return EvaluatorOutput(LadderValue(finalPriceWithSchemes), metadata = outputMetadata)

    }

    /**
     * This returns which retail scheme is applicable along with metadata around it.
     * Scheme can be DTR-Retail or GT-Retail.
     * For GT-retail, guardrail of GT-Wholesale scheme is applied.
     * The channel selection (DTR vs Bns) is done using channel signal data.
     */
    private fun curateFinalRetailScheme(
        inputs: List<EvaluatorConfigInput>
    ): Pair<List<Ladder>?, Map<String, String>> {
        val metadata = mutableMapOf<String, String>()
        val dtrRetailSchemeLadder = VariableUtils.getApplicableVariableValueForVariable(
            inputs = inputs,
            variableId = VariableId.DTR_RETAIL_SCHEME
        ) as? LadderValue

        val gtRetailSchemeLadder = VariableUtils.getApplicableVariableValueForVariable(
            inputs = inputs,
            variableId = VariableId.GT_RETAIL_SCHEME
        ) as? LadderValue

        val gtWsSchemeLadder = VariableUtils.getApplicableVariableValueForVariable(
            inputs = inputs,
            variableId = VariableId.GT_WS_SCHEME
        ) as? LadderValue

        val maxWsSchemeLadderValue = gtWsSchemeLadder?.value
            ?.maxByOrNull { it.ladderValue.abs() }
            ?.ladderValue
            ?: BigDecimal(0.0)

        /* Gaurdrailed to Max of WS SCHEME */
        val bestGTScheme = gtRetailSchemeLadder?.value?.map {
            if (it.ladderValue.abs() > maxWsSchemeLadderValue.abs()) {
                metadata["GT_WS_GUARDRAIL_HIT"] = "true"
                it.copy(ladderValue = maxWsSchemeLadderValue)
            } else {
                metadata["GT_WS_GUARDRAIL_HIT"] = "false"
                it
            }
        }

        val sourcingChannel = VariableUtils.getApplicableVariableValueForVariable(
            inputs = inputs,
            variableId = VariableId.SOURCING_CHANNEL
        )

        return when ((sourcingChannel as? StringValue)?.value) {
            "DTR" -> {
                Pair(dtrRetailSchemeLadder?.value, metadata)
            }
            "BNS" -> {
                Pair(bestGTScheme, metadata)
            }
            else -> {
                Pair(null, metadata)
            }
        }
    }

    private fun applySchemeMultiplierWithRetentionCapping(
        inputs: List<EvaluatorConfigInput>,
        retailScheme: List<Ladder>?
    ): Pair<List<Ladder>?, Map<String, String>> {
        val schemeMetadata = mutableMapOf<String, String>()
        val schemeMultiplierBps = (VariableUtils.getApplicableVariableValueForVariable(
            inputs = inputs,
            variableId = VariableId.SCHEME_MULTIPLIER_BPS
        ) as? BigDecimalValue)
            ?.value
            ?: throw IllegalArgumentException("Scheme multiplier mandatory for scheme multiplier evaluator")

        val schemeRetentionCapBps = (VariableUtils.getApplicableVariableValueForVariable(
            inputs = inputs,
            variableId = VariableId.SCHEME_RETENTION_CAP_BPS
        ) as? BigDecimalValue)
            ?.value
            ?: throw IllegalArgumentException("Scheme retention cap is mandatory for scheme retention cap evaluator")

        retailScheme?.let { ladders ->
            schemeMetadata["SELECTED_SCHEME_WITHOUT_MULTIPLIER"] = LadderValue(ladders).toString()
        }

        val updatedSchemeWithMultiplier = retailScheme?.map {
            it.copy(
                ladderValue = it.ladderValue.multiplyWithScale(schemeMultiplierBps.divideWithScale(BigDecimal(10000)))
            )
        }

        updatedSchemeWithMultiplier?.let { ladders ->
            schemeMetadata["SELECTED_SCHEME_WITH_MULTIPLIER"] = LadderValue(ladders).toString()
        }

        val updatedSchemeWithMultiplierAndCapping = if (retailScheme != null && updatedSchemeWithMultiplier != null) {
            // todo: @om.raj - Adjust all calculations to account for both positive and negative values.
            retailScheme.zip(updatedSchemeWithMultiplier).map { (first, second) ->
                val passedSchemeBps = second.ladderValue.abs()

                val retainedSchemeBps = first.ladderValue.abs() - passedSchemeBps.abs()

                val finalRetainedSchemeBps = if (retainedSchemeBps > schemeRetentionCapBps) {
                    schemeMetadata["RETAINED_SCHEME_CAP_HIT"] = "true"
                    schemeRetentionCapBps
                } else {
                    schemeMetadata["RETAINED_SCHEME_CAP_HIT"] = "false"
                    retainedSchemeBps
                }

                val finalPassedScheme = first.ladderValue.abs() - finalRetainedSchemeBps.abs()

                first.copy(
                    ladderValue = finalPassedScheme
                )
            }
        } else null

        return Pair(updatedSchemeWithMultiplierAndCapping, schemeMetadata)
    }

    private fun getPackagingUnitConversionRate(inputs: List<EvaluatorConfigInput>): BigDecimal {
        return (VariableUtils.getApplicableVariableValueForVariable(
            inputs,
            VariableId.CONVERSION_RATE
        ) as BigDecimalValue).value
    }

    private fun getAdjustedPricesWithRetailSchemes(
        basePrice: BigDecimal,
        finalRetailSchemeWithMultiplier: List<Ladder>?,
        metadata: MutableMap<String, String>
    ): List<Ladder> {
        val calculatedPriceLadders = if (!finalRetailSchemeWithMultiplier.isNullOrEmpty()) {
            metadata["SCHEME_POST_MULTIPLIER_AND_RETENTION_CAP"] =
                LadderValue(finalRetailSchemeWithMultiplier).toString()

            val finalSchemeAdjustedPrice = finalRetailSchemeWithMultiplier.map { ladder ->
                val laddersPriceInPaise = basePrice.multiplyWithScale(
                    BigDecimal(10000) - ladder.ladderValue
                ).divideWithScale(BigDecimal(10000))

                Ladder(
                    minQuantity = ladder.minQuantity,
                    maxQuantity = ladder.maxQuantity,
                    ladderValue = laddersPriceInPaise
                )
            }

            finalSchemeAdjustedPrice
        } else {
            metadata["SCHEME_POST_MULTIPLIER_AND_RETENTION_CAP"] = "null"
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = basePrice
                )
            )
        }

        // slabs where ladder value is same are merged while returning
        return LadderUtils.mergeLaddersWithSimilarValue(LadderValue(calculatedPriceLadders)).value
    }
}
