package com.udaan.pricing.core.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.google.inject.name.Named
import com.microsoft.azure.eventhubs.EventData
import com.microsoft.azure.eventhubs.EventHubClient
import com.udaan.catalog.models.ModelV2
import com.udaan.common.utils.getCurrentMillis
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.BasePrice
import com.udaan.pricing.EventChangeType
import com.udaan.pricing.PriceChangeEvent
import com.udaan.pricing.PriceForListing
import com.udaan.pricing.PriceRequest
import com.udaan.pricing.PriceState
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.controller.cache.BasePriceCacheController
import com.udaan.pricing.core.dao.BasePriceRepository
import com.udaan.pricing.core.svcinterfaces.CatalogSvcInterface
import com.udaan.pricing.core.utils.generateId
import kotlinx.coroutines.future.await
import java.util.concurrent.CompletableFuture
import javax.ws.rs.NotFoundException

class BasePriceController @Inject constructor(
    private val basePriceCacheController: BasePriceCacheController,
    private val basePriceRepository: BasePriceRepository,
    private val objectMapper: ObjectMapper,
    private val catalogSvcInterface: CatalogSvcInterface,
    @Named(NamedConstants.Events.PRICE_CHANGE_EVENTS) private val priceChangeEventhubClient: EventHubClient
) {

    companion object {
        private val logger by logger()
    }

    suspend fun createOrUpdatePrice(
        priceRequest: PriceRequest
    ) {
        val existingBasePrice = getBasePricesForListingSalesUnit(
            listingId = priceRequest.listingId,
            salesUnitId = priceRequest.saleUnitId
        ).firstOrNull()

        createOrUpdatePriceAsPerExistingPrice(
            priceRequest = priceRequest,
            existingBasePrice = existingBasePrice
        )
    }

    suspend fun createOrUpdatePriceAsPerExistingPrice(
        priceRequest: PriceRequest,
        existingBasePrice: BasePrice?
    ) {
        var createOrUpdate = EventChangeType.CREATE

        val updated = if (existingBasePrice != null) {
            if (priceRequest.similar(existingBasePrice)) {
                logger.info("similar prices ${priceRequest.listingId}")
                if (priceRequest.metaData != null) {
                    existingBasePrice.copy(
                        metaData = priceRequest.metaData,
                        state = priceRequest.state,
                        updatedAt = System.currentTimeMillis()

                    )
                } else if (priceRequest.state != existingBasePrice.state) {
                    existingBasePrice.copy(
                        state = priceRequest.state,
                        updatedAt = System.currentTimeMillis()
                    )
                } else {
                    return
                }

            } else {
                logger.info("No similar prices ${priceRequest.listingId}")
                createOrUpdate = EventChangeType.UPDATE
                existingBasePrice.copy(
                    qtyBasedPrice = priceRequest.price,
                    state = priceRequest.state,
                    orgId = priceRequest.orgId,
                    updatedAt = System.currentTimeMillis()
                )
            }
        } else {
            logger.info("Creating new prices ${priceRequest.listingId}")

            createOrUpdate = EventChangeType.CREATE
            BasePrice(
                id = generateId("BP"),
                listingId = priceRequest.listingId,
                saleUnitId = priceRequest.saleUnitId,
                qtyBasedPrice = priceRequest.price,
                state = priceRequest.state,
                orgId = priceRequest.orgId,
                metaData = priceRequest.metaData
            )
        }

        basePriceRepository.createPrice(updated).also {
            basePriceCacheController.invalidateCachedBasePriceForListing(priceRequest.listingId)
            sendNotification(updated, createOrUpdate)
        }
        logger.info("Request Completed for createPrice with ${priceRequest.listingId} ${priceRequest.saleUnitId} price: ${updated.qtyBasedPrice}")
    }

    private suspend fun sendNotification(basePrice: BasePrice, createOrUpdate: EventChangeType) {
        try {
            val makeEventData = basePrice.makeEventData(createOrUpdate)
            priceChangeEventhubClient.send(EventData.create(makeEventData)).await()
        } catch (e: Exception) {
            logger.error("Failed sending event", e)
        }
    }

    private suspend fun BasePrice.makeEventData(createOrUpdate: EventChangeType): ByteArray? {
        return CompletableFuture.supplyAsync {
            objectMapper.writeValueAsBytes(
                PriceChangeEvent(
                    this.listingId,
                    this.saleUnitId,
                    createOrUpdate
                )
            )
        }.await()
    }

    private fun PriceRequest.similar(basePrice: BasePrice): Boolean {
        // The price in PriceRequest when created using catalog is not PU aware
        val qtyBasedPrice = basePrice.qtyBasedPrice.map {
            it.copy(packagingUnit = null)
        }
        return qtyBasedPrice == this.price
    }

    suspend fun getBasePricesForListingSalesUnit(
        listingId: String,
        salesUnitId: String
    ): List<BasePrice> {
        val basePricesForListing = basePriceCacheController.getCachedBasePricesForListing(listingId)
            ?: throw NotFoundException("Base Price not found for listing $listingId")

        val basePricesForListingSalesUnit = basePricesForListing.filter {
            it.saleUnitId == salesUnitId
        }

        return basePricesForListingSalesUnit
    }

    suspend fun getBasePricesForListing(
        listingId: String
    ): List<BasePrice> {
        return basePriceCacheController.getCachedBasePricesForListing(listingId)
            ?: throw NotFoundException("Base Price not found for listing $listingId")
    }

    suspend fun getBasePriceForListingSalesUnitWithFilter(
        listingId: String,
        salesUnitId: String,
        fetchInactive: Boolean
    ): BasePrice {
        val basePrice = getBasePricesForListingSalesUnit(
            listingId,
            salesUnitId
        ).firstOrNull() ?: throw NotFoundException("Base Price not found for listing $listingId salesUnit $salesUnitId")

        val listingDetails = catalogSvcInterface.getTradeListing(listingId)
        val activeSU = listingDetails.salesUnitList.filter { it.status == ModelV2.EleStatus.ENABLED }.map { it.salesUnitId }.toSet()

        // If the found base price is Active, return it
        return if (activeSU.contains(basePrice.saleUnitId)) {
            basePrice
        } else {
            // If the found base price is Inactive, return response based on fetchInactive flag
            if (fetchInactive) {
                basePrice
            } else {
                throw NotFoundException("Active Base Price not found for listing $listingId salesUnit $salesUnitId")
            }
        }
    }

    suspend fun getPriceOnId(id: String): PriceForListing? {
        val startTime = getCurrentMillis()
        val price = basePriceRepository.findDocument(id)
        logger.info("total time to fetch {} of size {} is {} ms", id, 1, getCurrentMillis() - startTime)

        return price?.let {
            PriceForListing(
                listingId = price.listingId,
                saleUnitId = price.saleUnitId,
                prices = price.qtyBasedPrice,
                strategyRef = null,
                metaData = price.metaData
            )
        }
    }

    suspend fun deleteBasePriceForListingSalesUnitId(
        listingId: String,
        salesUnitId: String
    ): String? {
        val existingBasePrice = getBasePricesForListingSalesUnit(listingId, salesUnitId).firstOrNull()

        if (existingBasePrice != null) {
            logger.info("Setting existing base price to empty {} and state to DELETED", existingBasePrice)

            val updatedPrice = existingBasePrice.copy(
                qtyBasedPrice = emptyList(),
                updatedAt = System.currentTimeMillis(),
                state = PriceState.INACTIVE
            )
            basePriceRepository.createPrice(updatedPrice).also {
                basePriceCacheController.invalidateCachedBasePriceForListing(listingId)
                sendNotification(existingBasePrice, EventChangeType.UPDATE)
            }
        } else {
            logger.info("No existing base price found for {} and {}", listingId, salesUnitId)
        }

        return existingBasePrice?.id
    }
}
