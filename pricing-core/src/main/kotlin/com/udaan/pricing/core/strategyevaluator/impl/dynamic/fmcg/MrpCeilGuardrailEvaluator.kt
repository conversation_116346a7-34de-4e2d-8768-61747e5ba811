package com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg

import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import com.udaan.pricing.core.utils.LadderUtils
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.variable.VariableId
import java.math.BigDecimal

internal object MrpCeilGuardrailEvaluator : Evaluator {
    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        require(data.previousOutput != null) {
            "Previous output is mandatory for mrp ceil guardrail evaluator"
        }

        val outputMetadata = mutableMapOf<String, String>()
        ValidationUtils.validateInputs(data.strategy, data.inputs)

        val mrpInPaisa = (VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.MRP_WT_PAISA_SET
        ) as? BigDecimalValue)?.value ?: throw IllegalArgumentException("MRP is mandatory for MRP ceil guardrail")

        val gstBps = (VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.GST_BPS
        ) as? BigDecimalValue)?.value ?: throw IllegalArgumentException("GST_BPS is mandatory for MRP ceil guardrail")

        val cessBps = (VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.CESS_BPS
        ) as? BigDecimalValue)?.value ?: throw IllegalArgumentException("CESS_BPS is mandatory for MRP ceil guardrail")

        val mrpCeilGuardrailBps = (VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.MRP_CEIL_GUARDRAIL_MD_BPS
        ) as? BigDecimalValue)?.value ?: BigDecimal(0.0)

        val calculatedCeilGuardrailPrice = mrpInPaisa
            .divideWithScale((BigDecimal(1) + (gstBps + cessBps).divideWithScale(BigDecimal(10000))))
            .multiplyWithScale((BigDecimal(1) - (mrpCeilGuardrailBps).divideWithScale(BigDecimal(10000))))

        outputMetadata["CEIL_GUARDRAIL_PRICE"] = calculatedCeilGuardrailPrice.toString()
        outputMetadata["CEIL_GUARDRAIL_HIT"] = "false"

        val previousOutput = data.previousOutput.output
        outputMetadata["PRICE_WITHOUT_CEIL_GUARDRAIL"] = previousOutput.toString()

        val guardrailedPrice = when (previousOutput) {
            is BigDecimalValue -> {
                if (previousOutput.value > calculatedCeilGuardrailPrice) {
                    outputMetadata["CEIL_GUARDRAIL_HIT"] = "true"
                    outputMetadata["PRICE_WITHOUT_CEIL_GUARDRAIL"] = previousOutput.value.toString()
                    BigDecimalValue(calculatedCeilGuardrailPrice)
                } else {
                    previousOutput
                }
            }
            is LadderValue -> {
                val updatedLadders = previousOutput.value.map { ladder ->
                    val overridedLadderValue = if (ladder.ladderValue > calculatedCeilGuardrailPrice) {
                        outputMetadata["CEIL_GUARDRAIL_HIT"] = "true"
                        outputMetadata["PRICE_WITHOUT_CEIL_GUARDRAIL"] = LadderValue(previousOutput.value).toString()
                        calculatedCeilGuardrailPrice
                    } else {
                        ladder.ladderValue
                    }

                    ladder.copy(
                        ladderValue = overridedLadderValue
                    )
                }
                LadderUtils.mergeLaddersWithSimilarValue(LadderValue(updatedLadders))
            }
            else -> {
                throw IllegalArgumentException("Expected previous output of type BigDecimalValue or " +
                        "LadderValue but got ${previousOutput::class.simpleName}")
            }
        }

        return EvaluatorOutput(guardrailedPrice, outputMetadata)
    }

}
