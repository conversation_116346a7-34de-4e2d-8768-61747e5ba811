package com.udaan.pricing.core.helpers.rider.impl

import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.PriceRiderForListing
import com.udaan.pricing.RiderCode
import com.udaan.pricing.core.helpers.rider.PricingRequestContext
import com.udaan.pricing.core.helpers.rider.Rider
import com.udaan.pricing.core.models.CatalogEntityContext
import com.udaan.resources.RedisLettuce6Client
import kotlinx.coroutines.future.await

class CreditPriceRider @Inject constructor(
    private val pricingRedisClient: RedisLettuce6Client
): Rider() {
    private val log by logger()
    
    companion object {
        private const val HORECA_VERTICALS_REDIS_KEY = "horeca_verticals"
        private const val HORECA_LISTINGS_REDIS_KEY = "horeca_listings"
        private const val HORECA_ADDITIONAL_ORGS = "horeca_extra_orgs"
        private const val PLACEHOLDER_ORG = "TEST_ORG"
    }
    
    override val isAdditive: Boolean = false

    override suspend fun getPriceRider(
        catalogEntityContext: CatalogEntityContext,
        requestContext: PricingRequestContext,
        fetchRiderDetails: Boolean,
        cluster: List<String>?,
        preferredWarehouseId: String?,
        servingWarehouseId: String?
    ): PriceRiderForListing? {
        val buyerContext = requestContext.buyerContext
        val buyerOrgId = buyerContext?.orgId ?: PLACEHOLDER_ORG
        val verticalName = catalogEntityContext.vertical.name.lowercase()
        // horeca check

        isHorecaAdditionalBuyer(buyerOrgId).also { buyerMarkup ->
            if (buyerMarkup >= 0) {
                isListingRateDifferential(catalogEntityContext.listingId).apply {
                    if (this > 0) {
                        // log.info("For listingId " + listingId + " and buyerId: " + buyerContext?.orgId + " creditMarkup: " + this)
                        return PriceRiderForListing(catalogEntityContext.listingId, "", (buyerMarkup + this), 0, true, RiderCode.CREDIT.name + ": HoReCa-Listing", "markupBps: ${buyerMarkup + this}")
                    }
                }
                isVerticalRateDifferential(verticalName).apply {
                    if (this > 0) {
                        // log.info("For listingId " + listingId + " and buyerId: " + buyerContext?.orgId + " creditMarkup: " + verticalMarkup)
                        return PriceRiderForListing(catalogEntityContext.listingId, "", (buyerMarkup + this), 0, true, RiderCode.CREDIT.name + ": HoReCa-Vertical", "markupBps: ${buyerMarkup + this}")
                    }
                }
            }
        }

        return null

    }

    private suspend fun isHorecaAdditionalBuyer(buyerOrgId: String): Int {
        return pricingRedisClient.asyncCommands.hget(HORECA_ADDITIONAL_ORGS, buyerOrgId).toCompletableFuture().await()?.toInt() ?: -1
    }


    private suspend fun isListingRateDifferential(listingId: String): Int {
        return pricingRedisClient.asyncCommands.hget(HORECA_LISTINGS_REDIS_KEY, listingId).toCompletableFuture().await()?.toInt() ?: 0
    }

    private suspend fun isVerticalRateDifferential(vertical: String): Int {
        return pricingRedisClient.asyncCommands.hget(HORECA_VERTICALS_REDIS_KEY, vertical).toCompletableFuture().await()?.toInt() ?: 0
    }

}
