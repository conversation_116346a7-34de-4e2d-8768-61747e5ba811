package com.udaan.pricing.core.helpers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.catalog.model.VerticalCategory
import com.udaan.common.utils.kotlin.logger
import com.udaan.orchestrator.client.servingTenants.ServingTenantsCacheClient
import com.udaan.pricing.BuyerContext
import com.udaan.pricing.core.svcinterfaces.UserSvcInterface
import com.udaan.proto.models.ModelV1
import com.udaan.user.client.headOfficeUnit
import kotlinx.coroutines.future.await

@Singleton
class PreferredWarehouseHelper @Inject constructor(
    private val servingTenantsCacheClient: ServingTenantsCacheClient,
    private val userSvcInterface: UserSvcInterface
) {
    companion object {
        private val logger by logger()
    }

    suspend fun getPreferredWarehouseIdForBuyerOrg(
        listingId: String,
        salesUnitIds: List<String>,
        verticalCategory: VerticalCategory,
        buyerOrgId: String
    ): String? {
        val headOfficeOrgUnit = userSvcInterface.getBuyer(buyerOrgId)?.headOfficeUnit
        val pinCode = headOfficeOrgUnit?.unitAddress?.pincode
        if (headOfficeOrgUnit == null || pinCode == null) {
            logger.error("No head office org unit or pin-code found for buyerOrgId: {}", buyerOrgId)
            return null
        }
        return getPreferredWarehouseId(
            listingId = listingId,
            salesUnitIds = salesUnitIds,
            verticalCategory = verticalCategory,
            buyerContext = BuyerContext(
                orgId = buyerOrgId,
                orgUnitId = headOfficeOrgUnit.orgUnitId,
                pincode = headOfficeOrgUnit.unitAddress?.pincode
            )
        )
    }

    suspend fun getPreferredWarehouseId(
        listingId: String,
        salesUnitIds: List<String>,
        verticalCategory: VerticalCategory,
        buyerContext: BuyerContext
    ): String? {
        if (verticalCategory !in listOf(VerticalCategory.FMCG, VerticalCategory.STAPLES)) {
            return null
        }

        // salesUnitIds -> either passed SU in request or all active SU's for listing
        val salesUnitId = salesUnitIds.first()

        // if no selling platform, default to Udaan_MP
        val sellingPlatform = buyerContext.platform ?: ModelV1.SellingPlatform.UDAAN_MARKETPLACE.name

        val preferredWarehousesForListingResponse = try {
            servingTenantsCacheClient.getPreferredWarehousesForListing(
                listingId = listingId,
                salesUnitId = salesUnitId,
                pincode = buyerContext.pincode!!,
                buyerOrgUnitId = buyerContext.orgUnitId,
                sellingPlatform = ModelV1.SellingPlatform.valueOf(sellingPlatform)
            ).await()
        } catch (e: Exception) {
            logger.error(
                "Error getting preferred warehouseId for buyerContext: {}, exception: {}, stacktrace: {}",
                buyerContext, e.message, e.stackTrace
            )
            throw e
        }

        logger.info("Listing {} salesUnit {} buyerContext: {} preferred warehouses: {}",
            listingId, salesUnitId, buyerContext, preferredWarehousesForListingResponse)

        val mostPreferredWarehouse = preferredWarehousesForListingResponse?.preferredWarehouses?.minByOrNull { it.rank }
        return mostPreferredWarehouse?.warehouseId
    }
}
