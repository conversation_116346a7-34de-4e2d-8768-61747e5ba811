package com.udaan.pricing.core.helpers.rider.impl.bot

import com.fasterxml.jackson.databind.MappingIterator
import com.fasterxml.jackson.dataformat.csv.CsvSchema
import com.google.common.io.Resources
import com.udaan.common.client.UdaanServiceException
import com.udaan.common.utils.kotlin.logger
import com.udaan.firstparty.dataplatform.DataPlatformUtil
import java.net.URL
import kotlin.reflect.KClass

interface GenericDataLoader<T> {
    fun loadData(): T?
}

interface DataTransformer<In, Out> {
    fun transform(data: MappingIterator<In>): Out
}

class DataPlatformLoader<D : Any, T>(
    private val probeId: String,
    private val clazz: KClass<D>,
    private val transformer: DataTransformer<D, T>
) : GenericDataLoader<T> {
    override fun loadData(): T? {
        val stream = try {
            DataPlatformUtil.dpClient.downloadDataItem(probeId)
                .executeSyncOrNull()
        } catch (e: UdaanServiceException) {
            log.warn("Unable to load data for probe: $probeId", e)
            null
        } ?: return null

        val data: MappingIterator<D> = DataPlatformUtil.csvMapper
            .readerFor(clazz.java)
            .with(csvScheme)
            .readValues<D>(stream)

        return transformer.transform(data)
    }

    companion object {
        private val log by logger()
    }
}

class CSVLoader<D : Any, T>(
    private val resourceName: String,
    private val clazz: KClass<D>,
    private val transformer: DataTransformer<D, T>
) : GenericDataLoader<T> {
    override fun loadData(): T {
        val resourceURL: URL = Resources.getResource(resourceName)
        return resourceURL.openStream().use {
            val data: MappingIterator<D> = DataPlatformUtil.csvMapper
                .readerFor(clazz.java)
                .with(csvScheme)
                .readValues<D>(it)

            transformer.transform(data)
        }
    }
}

private val csvScheme = CsvSchema.emptySchema().withHeader()
