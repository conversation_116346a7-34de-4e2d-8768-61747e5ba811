package com.udaan.pricing.core.helpers.signals.rawsignalinputconverters

import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.signalcreation.GenericVerticalLevelInput
import com.udaan.pricing.signals.GenericMetadata
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState

class GenericVerticalLevelInputConverter: RawSignalInputConverter<GenericVerticalLevelInput>() {
    companion object {
        private val logger by logger()
    }

    override suspend fun convert(rawSignalInput: GenericVerticalLevelInput): List<Signal> {
        val convertedSignal = Signal(
            catalogEntity = rawSignalInput.vertical.uppercase(),
            catalogEntityType = CatalogEntityType.VERTICAL,
            variableId = rawSignalInput.variableId.name,
            signalData = rawSignalInput.data,
            metadata = GenericMetadata(rawSignalInput.metadata),
            location = rawSignalInput.location.copy(
                locationValue = rawSignalInput.location.locationValue.uppercase()
            ),
            state = SignalState.ACTIVE,
            createdBy = rawSignalInput.updatedBy,
            updatedBy = rawSignalInput.updatedBy
        )

        logger.info("converted signal {}", convertedSignal)
        return listOf(convertedSignal)
    }

}
