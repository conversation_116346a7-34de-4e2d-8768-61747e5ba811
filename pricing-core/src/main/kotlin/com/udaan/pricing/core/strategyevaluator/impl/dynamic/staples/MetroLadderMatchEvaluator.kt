package com.udaan.pricing.core.strategyevaluator.impl.dynamic.staples

import com.udaan.catalog.model.VerticalCategory
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.CompMatchUtils
import com.udaan.pricing.core.utils.LadderUtils
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.variable.VariableId

internal object MetroLadderMatchEvaluator : Evaluator {

    // @todo - Should we abstract out online comp logic even further?
    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        ValidationUtils.validateInputs(data.strategy, data.inputs)
        val outputMetadata = mutableMapOf<String, String>()

        // fetching inputs required
        // conversion rate
        val conversionRate = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.CONVERSION_RATE
        ) as BigDecimalValue

        // previous input in ladder format (if not already)
        val ladderInputWithoutTaxPaisaAssortment = LadderUtils.convertToLadderValue(
            inputValue = data.previousOutput?.output,
            outputMetadata = outputMetadata
        )

        // comp price without tax at assortment level
        val compPriceWithoutTaxPaisaAssortment = CompMatchUtils.getCompLadderPriceWithoutTaxAtAssortment(
            compVariableId = VariableId.METRO_COMP_LADDER_PRICE_WT_PAISA_UNIT,
            conversionRate = conversionRate.value,
            inputs = data.inputs,
            verticalCategory = VerticalCategory.STAPLES,
            outputMetadata = outputMetadata
        ) ?: return EvaluatorOutput(ladderInputWithoutTaxPaisaAssortment, outputMetadata)

        // comp floor guardrail price
        val floorGuardrailWithoutTaxPaiseAtAssortment = CompMatchUtils.getFloorGuardrailForCompInPaiseAtAssortment(
            inputs = data.inputs,
            variableId = VariableId.STAPLES_LIP_WOT_PAISA_UNIT,
            conversionRate = conversionRate.value,
            verticalCategory = VerticalCategory.STAPLES,
            outputMetadata = outputMetadata
        )

        outputMetadata["METRO_LADDER_PRICE_WOT_PAISE_ASSORTMENT"] = compPriceWithoutTaxPaisaAssortment.toString()
        outputMetadata["COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT"] =
            floorGuardrailWithoutTaxPaiseAtAssortment.toString()

        // deriving final ladders post comparison and merge
        val finalLadders = LadderUtils.deriveFinalLaddersPostComparisonAndMerge(
            compFloorGuardrailPrice = floorGuardrailWithoutTaxPaiseAtAssortment,
            ladderInputWithoutTaxPaisaAssortment = ladderInputWithoutTaxPaisaAssortment,
            compPriceWithoutTaxPaisaAssortment = compPriceWithoutTaxPaisaAssortment,
            outputMetadata = outputMetadata,
            applyLadderCountCap = VariableUtils.getApplyMaxLadderCountThresholdVariable(data.inputs)
        )
        return EvaluatorOutput(finalLadders, outputMetadata)
    }
}
