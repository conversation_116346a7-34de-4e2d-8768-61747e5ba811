package com.udaan.pricing.core.managers

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.inject.Inject
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.core.controller.automation.TradingRepriceController
import com.udaan.pricing.core.dao.automation.TradingPortfolioItemRepository
import com.udaan.pricing.core.helpers.CatalogEntityValidator
import com.udaan.pricing.core.helpers.ConfigHelper
import com.udaan.pricing.core.helpers.DataPlatformHelper
import com.udaan.pricing.core.helpers.PricingSignalsHelper
import com.udaan.pricing.core.utils.TimeUtils
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.controller.automation.TradingItemCommonController
import com.udaan.pricing.portfolioitem.CatalogEntityLevel
import com.udaan.pricing.portfolioitem.CreateTradingPortfolioItemRequest
import com.udaan.pricing.portfolioitem.DeletePortfolioItemRequest
import com.udaan.pricing.portfolioitem.PortfolioItemState
import com.udaan.pricing.portfolioitem.TradingPortfolioItem
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await
import javax.ws.rs.NotFoundException

@Singleton
class TradingPortfolioItemManager @Inject constructor(
    private val catalogEntityValidator: CatalogEntityValidator,
    private val tradingPortfolioItemRepository: TradingPortfolioItemRepository,
    private val dataPlatformHelper: DataPlatformHelper,
    private val portfolioManager: PortfolioManager,
    private val objectMapper: ObjectMapper,
    @Named(NamedConstants.Caches.TRADING_PORTFOLIO_ITEM_CACHE) private val tradingPortfolioItemCache: RedisCache2<TradingPortfolioItem?>,
    private val configSvcHelper: ConfigHelper,
    private val pricingSignalHelper: PricingSignalsHelper,
    private val tradingRepriceController: TradingRepriceController,
    private val tradingItemCommonController: TradingItemCommonController
) {
    companion object {
        private val logger by logger()
        private val TRADING_MANUAL_PORTFOLIO_ID = "PFY14VVA2SE251NTG4UEFH"
    }

    suspend fun createPortfolioItemTagging(
        createTradingPortfolioItemRequest: CreateTradingPortfolioItemRequest
    ): TradingPortfolioItem {
        val portfolioItemToCreate = createTradingPortfolioItemRequest.convert()
        // Validate portfolio
        val portfolio = portfolioManager.getPortfolioById(portfolioItemToCreate.portfolioId)

        // Validate catalog entity
        catalogEntityValidator.validateCatalogEntity(
            catalogEntity = portfolioItemToCreate.catalogEntity,
            catalogEntityLevel = portfolioItemToCreate.entityLevel,
            portfolioCategory = portfolio.category
        )

        logger.info("Creating portfolio item {}", portfolioItemToCreate)

        // save the item
        return createOrUpdatePortfolioItem(portfolioItemToCreate).also {
            tradingPortfolioItemCache.invalidate(
                k = getPortfolioItemCacheKey(
                    catalogEntity = it.catalogEntity,
                    locationValue = it.location.locationValue
                )
            ).await()
        }.also {
            tradingRepriceController.repriceItems(
                catalogEntity = it.catalogEntity,
                locationType = LocationType.valueOf(it.location.locationType.name),
                locationValue = it.location.locationValue,
                referenceId = "${it.catalogEntity}:${it.location.locationValue}"
            )
            logger.info("Repriced portfolio item {}", it.id)
        }
    }

    suspend fun deletePortfolioItemTagging(
        deletePortfolioItemRequest: DeletePortfolioItemRequest
    ): TradingPortfolioItem? {
        val portfolioItemId = getPortfolioItemId(
            catalogEntity = deletePortfolioItemRequest.catalogEntity,
            locationValue = deletePortfolioItemRequest.location.locationValue
        )

        val existingPortfolioItem = tradingPortfolioItemRepository.getByIdAndPartitionKey(
            id = portfolioItemId,
            partitionKey = deletePortfolioItemRequest.catalogEntity
        )

        return existingPortfolioItem?.let { tradingPortfolioItem ->
            val updatedPortfolioItem = tradingPortfolioItem.copy(
                state = PortfolioItemState.DELETED,
                updatedBy = deletePortfolioItemRequest.deletedBy,
                updatedAt = deletePortfolioItemRequest.deletedAt,
                metadata = tradingPortfolioItem.metadata.plus("DELETION_REASON" to deletePortfolioItemRequest.deletionReason)
            )
            tradingPortfolioItemRepository.update(updatedPortfolioItem).also {
                tradingPortfolioItemCache.invalidate(
                    k = getPortfolioItemCacheKey(
                        catalogEntity = it.catalogEntity,
                        locationValue = it.location.locationValue
                    )
                ).await()
                dataPlatformHelper.trackEvent(
                    eventData = tradingPortfolioItem.toMap(),
                    eventName = DataPlatformHelper.TrackEventName.TRADING_PORTFOLIO_ITEM_AUDIT,
                    referenceId1 = tradingPortfolioItem.referenceId,
                    referenceId2 = tradingPortfolioItem.id
                )
            }
        }
    }

    private suspend fun createOrUpdatePortfolioItem(portfolioItem: TradingPortfolioItem): TradingPortfolioItem {
        return tradingPortfolioItemRepository.createOrUpdate(portfolioItem).also {
            dataPlatformHelper.trackEvent(
                eventData = portfolioItem.toMap(),
                eventName = DataPlatformHelper.TrackEventName.TRADING_PORTFOLIO_ITEM_AUDIT,
                referenceId1 = portfolioItem.referenceId,
                referenceId2 = portfolioItem.id
            )
        }
    }

    suspend fun getPortfolioTaggingForEntityAndLocation(
        catalogEntity: String,
        locationValue: String
    ): TradingPortfolioItem {
        return tradingPortfolioItemCache.get(
            k = getPortfolioItemCacheKey(catalogEntity, locationValue)
        ) {
            TelemetryScope.future {
                val portfolioItemId = getPortfolioItemId(catalogEntity, locationValue)
                val portfolioItem = tradingPortfolioItemRepository.getByIdAndPartitionKey(
                    id = portfolioItemId,
                    partitionKey = catalogEntity
                )

                portfolioItem?.let {
                    if (it.state == PortfolioItemState.ACTIVE) {
                        it
                    } else {
                        null
                    }
                }
            }
        }.await() ?: throw NotFoundException("No portfolio item found for $catalogEntity and $locationValue")
    }

    /**
     * For a given GID/WH, we check existing portfolio tagging.
     * if existing tagging is not default (config based trading portfolio),
     * or it is tagged to manual portfolio, and manual price is not valid
     * then we move the trading portfolio item to new default (config based trading portfolio).
     */
    suspend fun assignDefaultTradingPortfolioIfExistingTaggingIsNotDefault(
        groupId: String,
        warehouseId: String,
        createdBy: String
    ) {
        val tradingPortfolioItem = tradingItemCommonController.getTradingPortfolioItemOrNull(
            catalogEntity = groupId,
            locationValue = warehouseId
        )
        val todayMidNightInEpoch = TimeUtils.getCutOffTimeForManualPriceSignals(configSvcHelper.getTradingPriceExpiryInDays())
        val isManualPriceValid = pricingSignalHelper.fetchAndValidateTradingManualPrice(
            groupId = groupId,
            warehouseId = warehouseId,
            priceValidTimeInMillis = todayMidNightInEpoch
        )
        val defaultTradingPortfolioId = configSvcHelper.getTradingDefaultPortfolioId()
            ?: error("Default trading portfolioId found null, cannot do default tagging")
        if (defaultTradingPortfolioId == tradingPortfolioItem?.portfolioId) {
            logger.info("Default tagging is present for $groupId, $warehouseId, $defaultTradingPortfolioId")
            return
        }
        if (tradingPortfolioItem?.portfolioId == TRADING_MANUAL_PORTFOLIO_ID && isManualPriceValid) {
            logger.info(
                "Fresh manual price, with trading manual portfolio tagging " +
                        "is present for $groupId, $warehouseId to $TRADING_MANUAL_PORTFOLIO_ID"
            )
            return
        }
        createPortfolioItemTagging(
            CreateTradingPortfolioItemRequest(
                catalogEntity = groupId,
                createdBy = createdBy,
                entityLevel = CatalogEntityLevel.PRODUCT_GROUP_ID,
                jobIdReference = createdBy,
                location = Location(
                    com.udaan.pricing.commons.location.LocationType.WAREHOUSE,
                    warehouseId
                ),
                portfolioId = defaultTradingPortfolioId
            )
        )
        logger.info("Updated trading tagging for $groupId, $warehouseId to $defaultTradingPortfolioId")
    }

    /**
     * This fun checks for any active trading portfolio entity for GID/WHID.
     * If portfolio entity is not present or tagged to other non-trading portfolios,
     * it would be tagged to default trading portfolio.
     */
    suspend fun assignDefaultTradingPortfolioIfNoTradingTagging(
        groupId: String,
        warehouseId: String,
        createdBy: String
    ) {
        val tradingPortfolioItem = try {
            getPortfolioTaggingForEntityAndLocation(
                catalogEntity = groupId,
                locationValue = warehouseId
            )
        } catch (e: NotFoundException) {
            logger.error("No portfolio item for $groupId, $warehouseId")
            null
        }
        val isCurrentPortfolioTrading = try {
            portfolioManager.isTradingPortfolio(tradingPortfolioItem?.portfolioId)
        } catch (ex: Exception) {
            logger.error(
                "Exception while checking portfolio is Trading or not for portfolioId ${tradingPortfolioItem?.portfolioId}, " +
                        "proceeding with default tagging. Ex: ", ex
            )
            false
        }

        if (isCurrentPortfolioTrading.not()) {
            val defaultTradingPortfolioId = configSvcHelper.getTradingDefaultPortfolioId()
                ?: throw IllegalStateException("Default trading portfolioId found null, cannot do default tagging")
            createPortfolioItemTagging(
                CreateTradingPortfolioItemRequest(
                    catalogEntity = groupId,
                    createdBy = createdBy,
                    entityLevel = CatalogEntityLevel.PRODUCT_GROUP_ID,
                    jobIdReference = "",
                    location = Location(
                        com.udaan.pricing.commons.location.LocationType.WAREHOUSE,
                        warehouseId
                    ),
                    portfolioId = defaultTradingPortfolioId
                )
            )
            logger.info("Updated trading tagging for $groupId, $warehouseId to $defaultTradingPortfolioId")
        } else {
            logger.info("Trading tagging is present $groupId, $warehouseId to ${tradingPortfolioItem!!.portfolioId}")
        }
    }

    private fun getPortfolioItemCacheKey(
        catalogEntity: String,
        locationValue: String
    ): String {
        return "$catalogEntity:$locationValue".uppercase()
    }

    private fun getPortfolioItemId(
        catalogEntity: String,
        locationValue: String
    ): String {
        return ("$catalogEntity:$locationValue").uppercase()
    }

    private fun <T : Any> T.toMap(): Map<String, Any> {
        return objectMapper.convertValue(this)
    }
}
