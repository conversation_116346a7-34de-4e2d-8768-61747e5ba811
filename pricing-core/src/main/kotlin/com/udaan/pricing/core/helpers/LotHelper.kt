package com.udaan.pricing.core.helpers

import com.udaan.pricing.BasicPrice
import com.udaan.pricing.PriceInPaisa
import com.udaan.pricing.QtyBasedPrice

object LotHelper {
    fun calculateMD(price:Double,bps:Double):Double{
        return price*(1.0 -(bps/10000.0))
    }

    fun getQtyBasedPriceByScheme(ptr: Long, minQty: Int, discount: Int): List<QtyBasedPrice> {
        var qtyBasedPrice = mutableListOf<QtyBasedPrice>()

        qtyBasedPrice.add(getQtyBasedPrice(1,Int.MAX_VALUE,ptr))

        if (discount > 0) {
            val discountedVal = calculateMD(ptr.toDouble(),discount.toDouble()).toLong()
            if ( minQty == 1){
                qtyBasedPrice[0] = getQtyBasedPrice(1,Int.MAX_VALUE,discountedVal)
            } else{
                qtyBasedPrice[0] =  qtyBasedPrice[0].copy(maxQty = minQty-1)
                qtyBasedPrice.add(
                    getQtyBasedPrice(minQty, Int.MAX_VALUE,discountedVal)
                )
            }

        }
        return qtyBasedPrice
    }

    fun getQtyBasedPrice(minQty: Int, maxQty:Int,price:Long): QtyBasedPrice {
        return QtyBasedPrice(
            minQty, maxQty, PriceInPaisa(
                price, price, price,
                BasicPrice(
                    price, price, price
                ), null
            ), null, null
        )
    }

}
