package com.udaan.pricing.core

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.AbstractModule
import com.google.inject.Provides
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.udaan.pricing.BasePrice
import com.udaan.pricing.GeoLocationBasePrice
import com.udaan.pricing.GeoPricing
import com.udaan.pricing.MaxPromotion
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.helpers.rider.impl.BotMarkupData
import com.udaan.pricing.core.models.contracts.Contract
import com.udaan.pricing.manualprice.ManualPrice
import com.udaan.pricing.network.DemandCluster
import com.udaan.pricing.network.DemandClusterLocations
import com.udaan.pricing.portfolio.Portfolio
import com.udaan.pricing.portfolioitem.PortfolioItem
import com.udaan.pricing.portfolioitem.TradingPortfolioItem
import com.udaan.pricing.portfolioplan.PortfolioPlan
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.strategy.Strategy
import com.udaan.pricing.variable.Variable
import com.udaan.resources.RedisLettuce6Client
import com.udaan.resources.ResourceBuilder
import com.udaan.resources.cache.LocalCacheConfig
import com.udaan.resources.cache.RedisCache2
import com.udaan.resources.cache.RedisCacheConfig
import io.lettuce.core.AbstractRedisAsyncCommands
import java.util.concurrent.TimeUnit

class PricingCacheModule: AbstractModule() {

    @Provides
    @Singleton
    private fun getPricingRedisLettuce6Client(): RedisLettuce6Client {
        return ResourceBuilder.redisClient("pricing-cache-onprem").buildLettuce6Client()
    }

    @Provides
    @Singleton
    private fun getPricingRedisCacheCommands(
        redisLettuce6Client: RedisLettuce6Client
    ): AbstractRedisAsyncCommands<String, String> {
        return redisLettuce6Client.asyncCommands
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.LISTING_BASE_PRICE_CACHE)
    private fun getListingBasePriceCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<List<BasePrice>> {
        return RedisCache2.create(
            name = "listing-base-price-cache",
            lettuceClient = null,
            localCacheConfig = null,
            redisCacheConfig = RedisCacheConfig(
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(30),
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(15),
                refreshLockTimeoutMs = TimeUnit.SECONDS.toMillis(3)
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructCollectionType(List::class.java, BasePrice::class.java),
            lettuce6Client = redisLettuce6Client
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.LISTING_GEO_BASE_PRICE_CACHE)
    private fun getListingGeoBasePriceCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<List<GeoLocationBasePrice>> {
        return RedisCache2.create(
            name = "listing-geo-base-price",
            lettuceClient = null,
            localCacheConfig = null,
            redisCacheConfig = RedisCacheConfig(
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(60),
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(30),
                refreshLockTimeoutMs = TimeUnit.SECONDS.toMillis(3)
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructCollectionType(List::class.java, GeoLocationBasePrice::class.java),
            lettuce6Client = redisLettuce6Client
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.LISTING_GEO_DIFF_PRICE_CACHE)
    private fun getListingGeoDiffPriceCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<List<GeoPricing>> {
        return RedisCache2.create(
            name = "listing-geo-diff-price",
            lettuceClient = null,
            localCacheConfig = null,
            redisCacheConfig = RedisCacheConfig(
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(30),
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(15),
                refreshLockTimeoutMs = TimeUnit.SECONDS.toMillis(3)
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructCollectionType(List::class.java, GeoPricing::class.java),
            lettuce6Client = redisLettuce6Client
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.CONTRACT_CACHE)
    fun getContractsCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<Contract?> {
        return RedisCache2.createRemoteOnly(
            name = "contract-cache",
            lettuceClient = null,
            redisCacheConfig = RedisCacheConfig(
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(15),
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(30),
                storeNull = true
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructType(Contract::class.java),
            lettuce6Client = redisLettuce6Client
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.STRATEGY_CACHE)
    fun getStrategyRedisCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<Strategy?> {
        return RedisCache2.create(
            name = "pricing-strategy",
            lettuceClient = null,
            localCacheConfig = null,
            redisCacheConfig = RedisCacheConfig(
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(30),
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(15),
                storeNull = true
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructType(Strategy::class.java),
            lettuce6Client = redisLettuce6Client
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.PORTFOLIO_CACHE)
    fun getPortfolioRedisCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<Portfolio?> {
        return RedisCache2.create(
            name = "pricing-automation-portfolio",
            lettuceClient = null,
            localCacheConfig = null,
            redisCacheConfig = RedisCacheConfig(
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(30),
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(15),
                storeNull = true
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructType(Portfolio::class.java),
            lettuce6Client = redisLettuce6Client
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.PORTFOLIO_PLAN_CACHE)
    fun getPortfolioPlanRedisCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<List<PortfolioPlan>> {
        return RedisCache2.create(
            name = "pricing-automation-portfolio-plan",
            lettuceClient = null,
            localCacheConfig = null,
            redisCacheConfig = RedisCacheConfig(
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(30),
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(15)
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructCollectionType(List::class.java, PortfolioPlan::class.java),
            lettuce6Client = redisLettuce6Client
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.ORG_INPUT_TERRITORY_CACHE)
    fun getTerritoryCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<Map<String, String>?> {
        return RedisCache2.createRemoteOnly(
            name = "org-input-territory-cache",
            lettuceClient = null,
            redisCacheConfig = RedisCacheConfig(
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(15),
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(30),
                storeNull = true
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructMapType(
                Map::class.java,
                String::class.java,
                String::class.java
            ),
            lettuce6Client = redisLettuce6Client
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.PORTFOLIO_ITEM_CACHE)
    private fun getPortfolioItemRedisCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<PortfolioItem?> {
        return RedisCache2.create(
            name = "pricing-automation-portfolio-item",
            lettuceClient = null,
            localCacheConfig = null,
            redisCacheConfig = RedisCacheConfig(
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(30),
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(15),
                storeNull = true
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructType(PortfolioItem::class.java),
            lettuce6Client = redisLettuce6Client
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.TRADING_PORTFOLIO_ITEM_CACHE)
    private fun getTradingPortfolioItemRedisCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<TradingPortfolioItem?> {
        return RedisCache2.create(
            name = "pricing-automation-trading-portfolio-item",
            lettuceClient = null,
            localCacheConfig = null,
            redisCacheConfig = RedisCacheConfig(
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(15),
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(10),
                storeNull = true
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructType(TradingPortfolioItem::class.java),
            lettuce6Client = redisLettuce6Client
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.BOT_MARKUP_CACHE)
    private fun getBotMarkupCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<BotMarkupData> {
        return RedisCache2.create(
            name = "bot-rider-markup",
            lettuceClient = null,
            localCacheConfig = LocalCacheConfig(size = 10000, expireAfterWriteMs = TimeUnit.HOURS.toMillis(1)),
            redisCacheConfig = RedisCacheConfig(
                expireAfterWriteMs = TimeUnit.HOURS.toMillis(7),
                refreshAfterWriteMs = TimeUnit.HOURS.toMillis(6),
                refreshLockTimeoutMs = TimeUnit.SECONDS.toMillis(3)
            ),
            objectMapper = objectMapper,
            lettuce6Client = redisLettuce6Client
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.LISTING_MAX_PROMOTION_CACHE)
    private fun getListingMaxPromotionCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<List<MaxPromotion>> {
        return RedisCache2.create(
            name = "listing-max-promotion",
            lettuceClient = null,
            localCacheConfig = null,
            redisCacheConfig = RedisCacheConfig(
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(30),
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(15),
                refreshLockTimeoutMs = TimeUnit.SECONDS.toMillis(3)
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructCollectionType(List::class.java, MaxPromotion::class.java),
            lettuce6Client = redisLettuce6Client
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.MANUAL_PRICES_CACHE)
    private fun getManualPriceCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<List<ManualPrice>> {
        return RedisCache2.createRemoteOnly(
            name = "manual-prices",
            lettuceClient = null,
            redisCacheConfig = RedisCacheConfig(
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(15),
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(30)
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructCollectionType(List::class.java, ManualPrice::class.java),
            lettuce6Client = redisLettuce6Client
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.VARIABLE_CACHE)
    private fun getVariableRedisCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<List<Variable>> {
        return RedisCache2.create(
            name = "pricing-variable",
            lettuceClient = null,
            localCacheConfig = LocalCacheConfig(
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(10),
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(5)
            ),
            redisCacheConfig = RedisCacheConfig(
                expireAfterWriteMs = TimeUnit.HOURS.toMillis(6),
                refreshAfterWriteMs = TimeUnit.HOURS.toMillis(1)
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructCollectionType(List::class.java,
                Variable::class.java),
            lettuce6Client = redisLettuce6Client
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.SIGNALS)
    private fun getSignalsCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<Signal?> {
        return RedisCache2.createRemoteOnly(
            name = "signals-cache",
            lettuceClient = null,
            redisCacheConfig = RedisCacheConfig(
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(15),
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(16),
                storeNull = true
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructType(Signal::class.java),
            lettuce6Client = redisLettuce6Client
        )
    }


    @Provides
    @Singleton
    @Named(NamedConstants.Caches.DEMAND_CLUSTER_LOCATIONS_CACHE)
    private fun provideDemandClusterLocationsCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<Collection<DemandClusterLocations>> {
        return RedisCache2.create(
            name = "demand-cluster-locations",
            lettuceClient = null,
            localCacheConfig = LocalCacheConfig(
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(10),
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(10)
            ),
            redisCacheConfig = RedisCacheConfig(
                refreshAfterWriteMs = TimeUnit.HOURS.toMillis(3),
                expireAfterWriteMs = TimeUnit.HOURS.toMillis(12)
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructCollectionType(Collection::class.java,
                DemandClusterLocations::class.java),
            lettuce6Client = redisLettuce6Client
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.DEMAND_CLUSTER_CACHE)
    private fun provideDemandClusterCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<Collection<DemandCluster>> {
        return RedisCache2.create(
            name = "demand-cluster",
            lettuceClient = null,
            localCacheConfig = LocalCacheConfig(
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(10),
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(10)
            ),
            redisCacheConfig = RedisCacheConfig(
                refreshAfterWriteMs = TimeUnit.HOURS.toMillis(3),
                expireAfterWriteMs = TimeUnit.HOURS.toMillis(12)
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructCollectionType(Collection::class.java,
                DemandCluster::class.java),
            lettuce6Client = redisLettuce6Client
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Caches.GID_CITY_TO_LISTING_CACHE)
    private fun provideGidAndCityToListingCache(
        objectMapper: ObjectMapper,
        redisLettuce6Client: RedisLettuce6Client
    ): RedisCache2<String?> {
        return RedisCache2.create(
            name = "gid-city-to-listing",
            lettuceClient = null,
            redisCacheConfig = RedisCacheConfig(
                expireAfterWriteMs = TimeUnit.HOURS.toMillis(6),
                refreshAfterWriteMs = TimeUnit.HOURS.toMillis(2),
                storeNull = false
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructType(String::class.java),
            lettuce6Client = redisLettuce6Client
        )
    }
}
