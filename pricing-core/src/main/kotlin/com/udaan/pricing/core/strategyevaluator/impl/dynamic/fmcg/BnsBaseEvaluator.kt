package com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg

import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorConfigInput
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.variable.VariableId
import java.math.BigDecimal

internal object BnsBaseEvaluator : Evaluator {

    private val RUPEES_TO_PAISE_FACTOR = BigDecimal(100)

    /**
     * Base Price * (1 + BUY_SELL_MARGIN_BPS / 10000))
     *
     * Base price can be FMCG_COGS_WOT_PAISA_SET or LPP_WOT_PAISE_ASSORTMENT in absence of COGS.
     */
    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        ValidationUtils.validateInputs(data.strategy, data.inputs)
        val outputMetadata = mutableMapOf<String, String>()

        val cogsWithoutTaxPaiseAtAssortment = (VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.FMCG_COGS_WOT_PAISA_SET
        ) as? BigDecimalValue)?.value

        val basePriceWithoutTaxPaiseAtAssortment = cogsWithoutTaxPaiseAtAssortment ?: run {
            outputMetadata["FALLBACK_TO_LPP"] = "true"
            val lppWithoutTaxPaiseAtAssortment = deriveApplicableLpp(data.inputs)
            outputMetadata["LPP_WOT_PAISE_ASSORTMENT"] = lppWithoutTaxPaiseAtAssortment.toString()
            lppWithoutTaxPaiseAtAssortment
        }

        val bnsMarginBps = (VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.BUY_SELL_MARGIN_BPS
        ) as? BigDecimalValue)?.value
            ?: throw IllegalArgumentException("BUY_SELL_MARGIN_BPS is mandatory for BNS Base evaluator")

        val calculatedPrice = basePriceWithoutTaxPaiseAtAssortment.multiplyWithScale(
            (BigDecimal(1) + bnsMarginBps.divideWithScale(BigDecimal(10000)))
        )

        return EvaluatorOutput(BigDecimalValue(calculatedPrice), outputMetadata)
    }

    /**
     * Derives LPP value at assortment in Paise.
     * This is only called when COGS is not available.
     *
     * Throws IllegalArgumentException if any of the required variables are not present.
     */
    private fun deriveApplicableLpp(inputs: List<EvaluatorConfigInput>) : BigDecimal {
        val conversionRate = (VariableUtils.getApplicableVariableValueForVariable(
            inputs,
            VariableId.CONVERSION_RATE
        ) as? BigDecimalValue)?.value
            ?: throw IllegalArgumentException("CONVERSION_RATE is mandatory when COGS not available for BNS Base evaluator")

        // converting LPP from Rupees to Paise and Unit to Assortment level
        return (VariableUtils.getApplicableVariableValueForVariable(
            inputs,
            VariableId.FMCG_LPP_WOT_RUPEES_UNIT
        ) as? BigDecimalValue)?.value?.multiplyWithScale(conversionRate)
            ?.multiplyWithScale(RUPEES_TO_PAISE_FACTOR)
            ?: throw IllegalArgumentException("FMCG_LPP_WOT_RUPEES_UNIT is mandatory when COGS not available for BNS Base evaluator")
    }
}
