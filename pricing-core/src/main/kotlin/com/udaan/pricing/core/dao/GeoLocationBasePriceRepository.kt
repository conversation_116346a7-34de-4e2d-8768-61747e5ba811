package com.udaan.pricing.core.dao

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.google.inject.Singleton
import com.microsoft.azure.documentdb.ConnectionPolicy
import com.microsoft.azure.documentdb.ConsistencyLevel
import com.microsoft.azure.documentdb.DocumentClient
import com.microsoft.azure.documentdb.bulkexecutor.DocumentBulkExecutor
import com.udaan.common.utils.kotlin.logger
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.GeoLocationBasePrice
import com.udaan.pricing.PriceState
import com.udaan.pricing.core.constants.CosmosDbConfig.COSMOS_DB_NAME
import com.udaan.pricing.core.constants.CosmosDbConfig.GEO_BASE_PRICE_TABLE
import com.udaan.resources.ResourceBuilder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.withContext
import org.apache.commons.lang3.tuple.MutablePair

@Singleton
class GeoLocationBasePriceRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {
    companion object {
        private const val LOG_PREFIX = "[GeoLocationBasePrice] -"
        private val log by logger()
    }

    private val documentDbDao by lazy {
        CosmosDbDao(
            configKey = "pricing",
            databaseName = COSMOS_DB_NAME,
            containerName = GEO_BASE_PRICE_TABLE
        ) { builder ->
            builder.connectionSharingAcrossClientsEnabled(true)
        }
    }

    private val documentBulkExecutor by lazy {
        val documentDbBuilder = ResourceBuilder.documentClient("pricing")

        val documentClient = DocumentClient(
            documentDbBuilder.documentDBHost,
            documentDbBuilder.accessKey,
            ConnectionPolicy.GetDefault(),
            ConsistencyLevel.Session
        )

        val collectionLink = "/dbs/$COSMOS_DB_NAME/colls/$GEO_BASE_PRICE_TABLE"
        val collection = documentClient.readCollection(collectionLink, null).resource

        DocumentBulkExecutor.builder().from(
            documentClient,
            COSMOS_DB_NAME,
            GEO_BASE_PRICE_TABLE,
            collection.partitionKey,
            5 * 1000
        ).build()
    }

    /**
     * Dummy function solely used for client initialisation while starting the service
     */
    suspend fun initialise() {
        documentDbDao.findItem("ID1")
    }

    suspend fun create(geoLocationBasePrice: GeoLocationBasePrice): GeoLocationBasePrice {
        val doc = geoLocationBasePrice.copy(locationTypeId = geoLocationBasePrice.locationTypeId.lowercase())
        return documentDbDao.createOrUpdateItem(doc.toDocument()).toGeoLocationBasePrice()
    }

    suspend fun markDeleted(geoLocationBasePrice: GeoLocationBasePrice) {
        log.info("$LOG_PREFIX - Deleting for listing - ${geoLocationBasePrice.listingId} sales unit - ${geoLocationBasePrice.saleUnitId} and doc id - ${geoLocationBasePrice.id}")
        val updated = geoLocationBasePrice.copy(
            currentActive = 0,
            state = PriceState.INACTIVE,
            updatedAt = System.currentTimeMillis()
        )
        documentDbDao.updateItem(updated.toDocument())
    }

    suspend fun changeState(geoLocationBasePrice: GeoLocationBasePrice, isActive: Int): GeoLocationBasePrice {
        log.info("$LOG_PREFIX - Changing state to $isActive for listing - ${geoLocationBasePrice.listingId} sales unit - ${geoLocationBasePrice.saleUnitId} and doc id - ${geoLocationBasePrice.id}")
        val updated = geoLocationBasePrice.copy(
            currentActive = isActive,
            state = if (isActive == 1) PriceState.ACTIVE else PriceState.INACTIVE,
            updatedAt = System.currentTimeMillis()
        )
        return documentDbDao.updateItem(updated.toDocument()).toGeoLocationBasePrice()
    }

    suspend fun deleteItems(geoLocationBasePrice: Collection<GeoLocationBasePrice>) {
        withContext(Dispatchers.IO) {
            documentBulkExecutor.deleteAll(geoLocationBasePrice.map {
                MutablePair(it.listingId, it.id)
            })
            log.info("Deleted ${geoLocationBasePrice.map { it.id }}")
        }
    }

    suspend fun findByPartition(id: String, listingId: String): List<GeoLocationBasePrice> {
        return documentDbDao.queryItems(
            queryName = "get-geo-location-price-by-id-and-listing",
            querySpec = makeSqlQuerySpec(
                """
                    select * from c where c.id = @id and 
                    c.listingId = @listingId
                """.trimIndent(),
                "@id" to id,
                "@listingId" to listingId
            )
        ).toList().map { it.toGeoLocationBasePrice() }
    }

    suspend fun fetchDataCountByTimeRange(fromTimeStamp: Long, toTimeStamp: Long): Long {
        return documentDbDao.queryItems(
            queryName = "get-geo-base-prices-count-between-time-range",
            querySpec = makeSqlQuerySpec(
                """
                    select count(1) from c where
                    c._ts >= @fromTimeStamp and c._ts <= @toTimeStamp
                """.trimIndent(),
                "@fromTimeStamp" to fromTimeStamp,
                "@toTimeStamp" to toTimeStamp
            )
        ).toList().first().get("$1").asLong()
    }

    suspend fun fetchDataByTimeRange(fromTimeStamp: Long, toTimeStamp: Long): List<GeoLocationBasePrice> {
        return documentDbDao.queryItems(
            queryName = "get-geo-base-prices-by-time-range",
            querySpec = makeSqlQuerySpec(
                """
                    select * from c where
                    c._ts >= @fromTimeStamp and c._ts <= @toTimeStamp
                """.trimIndent(),
                "@fromTimeStamp" to fromTimeStamp,
                "@toTimeStamp" to toTimeStamp
            )
        ).toList().map { it.toGeoLocationBasePrice() }
    }

    suspend fun getAllActiveGeoBasePricesForListing(
        listingId: String
    ): List<GeoLocationBasePrice> {
        return documentDbDao.queryItems(
            queryName = "get-geo-base-prices-for-listing",
            querySpec = makeSqlQuerySpec(
                """
                    select * from c where
                    c.listingId = @listingId and c.state = @state and
                    c.currentActive = @currentActive
                    order by  c.listingId, c.state, c.currentActive, c._ts desc
                """.trimIndent(),
                "@state" to PriceState.ACTIVE,
                "@currentActive" to 1,
                "@listingId" to listingId
            )
        ).toList().map { it.toGeoLocationBasePrice() }
    }

    suspend fun getAllGeoBasePricesForListingSalesUnit(
        listingId: String,
        salesUnitId: String
    ): List<GeoLocationBasePrice> {
        return documentDbDao.queryItems(
            queryName = "get-all-geo-base-prices-for-listing",
            querySpec = makeSqlQuerySpec(
                """
                    select * from c where
                    c.listingId = @listingId and
                    c.saleUnitId = @saleUnitId
                    order by c.listingId, c.saleUnitId, c._ts desc
                """.trimIndent(),
                "@listingId" to listingId,
                "@saleUnitId" to salesUnitId
            )
        ).toList().map { it.toGeoLocationBasePrice() }
    }

    private fun GeoLocationBasePrice.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toGeoLocationBasePrice() = objectMapper.convertValue(this, GeoLocationBasePrice::class.java)
}
