package com.udaan.pricing.core.utils.signals

import com.udaan.pricing.signals.GenericMetadata
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalMetadata
import com.udaan.pricing.signals.SignalState

object SignalUtil {
    /**
     * This method returns signalId derived as:
     *      catalogEntity:locationValue:variableId
     * converted to uppercase.
     */
    fun getSignalId(
        catalogEntity: String,
        locationValue: String,
        variableId: String
    ): String {
        return ("$catalogEntity:$locationValue:$variableId").uppercase()
    }

    /**
     *  This method returns signal partition key derived as:
     *    catalogEntity:locationValue
     *  converted to uppercase.
     */
    fun getSignalPartitionKey(
        catalogEntity: String,
        locationValue: String
    ): String {
        return ("$catalogEntity:$locationValue").uppercase()
    }

    /**
     * This method validates signal state and freshness and returns true if valid, else false.
     * This returns:
     * - false if Signal is null
     * - true if signal is not null but freshness passed is null
     *
     */
    fun validateSignalStateAndFreshness(signal: Signal?, freshnessDurationInMillis: Long?): Boolean {
        val freshnessCutoffInMillis = deriveFreshnessCutoffInMillis(freshnessDurationInMillis)
        return (
            signal != null &&
            signal.state == SignalState.ACTIVE &&
            (freshnessCutoffInMillis == null || signal.createdAt >= freshnessCutoffInMillis)
        )
    }

    private fun deriveFreshnessCutoffInMillis(freshnessDurationInMillis: Long?): Long? {
        return if (freshnessDurationInMillis != null) {
            System.currentTimeMillis() - freshnessDurationInMillis
        } else null
    }

    /**
     * This method returns signal if its valid and fresh, else returns null
     */
    fun signalValuePostExpiryCheck(applicableSignal: Signal?, freshnessDurationInMillis: Long?): Signal? {
        return if(validateSignalStateAndFreshness(applicableSignal, freshnessDurationInMillis)) {
            applicableSignal
        } else null
    }

    /**
     * This method adds warehouseId to the generic metadata and returns the updated metadata.
     */
    fun addWarehouseIdToGenericMetadata(metadata: SignalMetadata, warehouseId: String): SignalMetadata? {
        val existingMetadata = metadata as GenericMetadata
        val existingMetadataMap = existingMetadata.metadataMap.toMutableMap()
        existingMetadataMap["WAREHOUSE_ID"] = warehouseId.uppercase()
        return existingMetadata.copy(
            metadataMap = existingMetadataMap
        )
    }
}
