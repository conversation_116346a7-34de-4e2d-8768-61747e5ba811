package com.udaan.pricing.core.managers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.dao.automation.StrategyRepository
import com.udaan.pricing.strategy.CreateStrategyRequest
import com.udaan.pricing.strategy.GetStrategiesByStatesRequest
import com.udaan.pricing.strategy.Strategy
import com.udaan.pricing.strategy.StrategyListDTO
import com.udaan.pricing.strategy.StrategyState
import com.udaan.pricing.strategy.StrategyType
import com.udaan.pricing.strategy.UpdateStrategyRequest
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await
import javax.ws.rs.BadRequestException
import javax.ws.rs.NotFoundException

@Singleton
class StrategyManager @Inject constructor(
    private val strategyRepository: StrategyRepository,
    @Named(NamedConstants.Caches.STRATEGY_CACHE) private val strategyCache: RedisCache2<Strategy?>
) {
    companion object {
        private val logger by logger()
    }

    /**
     * This creates new strategy if all validations are passed.
     * This creates strategy in STAGED state and one need to activate
     * strategy (move to ACTIVE state) to start using in prod.
     *
     * Validations include:
     * - common validations for create and update requests
     * - there should not be an existing strategy of same name
     *
     * @see commonValidationForStrategyReqs doc
     */
    suspend fun createStrategy(createStrategyRequest: CreateStrategyRequest): Strategy? {
        logger.info("request for creating strategy: $createStrategyRequest")
        val strategy = createStrategyRequest.convert()
        commonValidationForStrategyReqs(strategy)

        // checking if existing strategy exists for same name
        val existingStrategyByName = strategyRepository.getNonDeletedStrategyByName(createStrategyRequest.name)
        if (existingStrategyByName != null) {
            throw BadRequestException("Strategy ${existingStrategyByName.id} with this name already exists")
        }

        return strategyRepository.createStrategy(strategy)
    }

    /**
     * This updates an existing strategy which is in STAGED state provided
     * it passes through all validations.
     *
     * A new strategy is always created in STAGED state, and it can only be
     * used in prod when activated (moved to ACTIVE state).
     * Already active strategy is not allowed to be updated.
     *
     * @see commonValidationForStrategyReqs doc
     */
    suspend fun updateStrategy(updateStrategyRequest: UpdateStrategyRequest): Strategy {
        logger.info("request for updating strategy: $updateStrategyRequest")
        val updatedStrategy = updateStrategyRequest.convert()
        commonValidationForStrategyReqs(updatedStrategy)

        // checking if strategy is in STAGED state or not
        val existingStrategy = strategyRepository.getStrategyById(updateStrategyRequest.id)
            ?: throw NotFoundException("Strategy not found with id ${updateStrategyRequest.id}")

        if (existingStrategy.state != StrategyState.STAGED) {
            throw BadRequestException("Strategy ${updateStrategyRequest.id} is ${existingStrategy.state}. " +
                    "Editing is only allowed when strategy is in STAGED state")
        }

        // creating final strategy instance for update including metadata around changes
        val extendedUpdatedStrategy = updatedStrategy.copy(
            updatedAt = System.currentTimeMillis(),
            metadata = existingStrategy.metadata + updatedStrategy.metadata
        )
        // updating db and invalidating cache
        strategyRepository.updateStrategy(extendedUpdatedStrategy).also {
            strategyCache.invalidate(extendedUpdatedStrategy.id)
        }

        return extendedUpdatedStrategy
    }

    /**
     * This updates State of a strategy.
     * Can be used to execute below transitions:
     * - ACTIVE to DELETED state
     * - STAGED to ACTIVE / DELETED state
     *
     * @see validateStrategyStateTransition
     * @see StrategyState
     */
    suspend fun updateStateForStrategyId(
        strategyId: String,
        newState: StrategyState,
        updatedBy: String
    ): Strategy {
        logger.info("updating strategy id $strategyId to $newState state")
        val strategy = strategyRepository.getStrategyById(strategyId)
            ?: throw NotFoundException("Strategy not found with id $strategyId")

        validateStrategyStateTransition(strategy.state, newState)

        // create updated strategy instance with metadata
        val updatedStrategy = strategy.copy(
            state = newState,
            metadata = strategy.metadata.toMutableMap() + mapOf(
                Pair("${strategy.state}_TO_${newState}_STATE_BY", updatedBy),
                Pair("${strategy.state}_TO_${newState}_STATE_AT", System.currentTimeMillis().toString())
            ),
            updatedAt = System.currentTimeMillis()
        )
        // update state of strategy and invalidate cache
        strategyRepository.updateStrategy(updatedStrategy).also {
            strategyCache.invalidate(updatedStrategy.id)
        }

        return updatedStrategy
    }

    /**
     * This provides all strategies belonging to passed states.
     *
     * @see GetStrategiesByStatesRequest.translateToStrategyEnums
     */
    suspend fun getStrategiesForStates(getStrategiesByStatesRequest: GetStrategiesByStatesRequest)
            : List<StrategyListDTO> {
        val actualStates = try {
            getStrategiesByStatesRequest.translateToStrategyEnums()
        } catch (iae: IllegalArgumentException) {
            throw BadRequestException(iae.message)
        }
        return strategyRepository.getStrategiesForStates(actualStates)
    }

    /**
     * This provides strategy for given ID.
     * This is supported by cache for faster response times.
     */
    suspend fun getStrategyById(strategyId: String): Strategy {
        return strategyCache.get(strategyId) {
            TelemetryScope.future {
                strategyRepository.getStrategyById(strategyId)
            }
        }.await() ?: throw NotFoundException("Strategy not found with id $strategyId")
    }

    /**
     * This provides list of strategies for requested strategyIds.
     * This calls getStrategyById internally fetching strategy one by one for each id.
     * It throws exception (carried over by getStrategyById) if any of the IDs not found in system.
     *
     * @see getStrategyById
     *
     * NOTE:
     * We are not having a separate fun fetching all strategies in single query with a cache layer
     * on top, because the cache key then will be derived from strategy ids and invalidating that
     * cache on any strategy update will be very complex. And without cache, it can induce latencies.
     */
    suspend fun getMultipleStrategiesByIds(strategyIdsString: String): List<Strategy> {
        val strategyIds = strategyIdsString.split(",").map { it.trim() }
        return strategyIds.map { strategyId ->
            getStrategyById(strategyId)
        }
    }

    // TODO :- Making a DB call as size of strategy collection is small and accuracy over time is the pref for consoles.
    suspend fun getAllActiveStrategies(): List<Strategy> {
        return strategyRepository.getActiveStrategies()
    }

    /**
     * This validates common restrictions around strategy create or update requests.
     * Validations include:
     * - both mandatoryVariables and usedVariables should be non-empty
     * - for Formula strategy type, formula definition is mandatory
     * - for Dynamic strategy type, formula definition is not supported and should be empty
     */
    private fun commonValidationForStrategyReqs(strategy: Strategy) {
        if (strategy.usedVariables.isEmpty()) {
            throw BadRequestException("Used variables not defined for strategy name ${strategy.name}")
        }
        if (strategy.type == StrategyType.FORMULA
            && strategy.conditionalFormulae.isEmpty()) {
            throw BadRequestException("No formula defined for strategy name ${strategy.name}. " +
                    "Formula strategy needs formula definition.")
        }
        if (strategy.type == StrategyType.DYNAMIC
            && strategy.conditionalFormulae.isNotEmpty()) {
            throw BadRequestException("Formula definition not supported for Dynamic type strategy.")
        }
    }

    /**
     * This validates state transitions for a strategy state change request.
     * Validations include:
     * - an ACTIVE state strategy can not move to STAGED state
     * - a DELETED state strategy can not move to ACTIVE or STAGED state
     */
    private fun validateStrategyStateTransition(
        currentState: StrategyState,
        newState: StrategyState
    ) {
        if (currentState == StrategyState.ACTIVE && newState == StrategyState.STAGED) {
            throw BadRequestException("Strategy can't be moved from active to staged")
        } else if (currentState == StrategyState.DELETED &&
            newState in listOf(StrategyState.STAGED, StrategyState.ACTIVE)
        ) {
            throw BadRequestException("Strategy can't be moved from deleted to active / staged")
        }
    }
}
