package com.udaan.pricing.core.managers.signals

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.inject.Inject
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.udaan.common.client.extensions.awaitOrNull
import com.udaan.common.utils.getCurrentMillis
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.dao.signals.SignalRepository
import com.udaan.pricing.core.events.EventController
import com.udaan.pricing.core.helpers.DataPlatformHelper
import com.udaan.pricing.core.helpers.signals.ResolvedValuesRedisHelper
import com.udaan.pricing.core.helpers.signals.deletesignalrequestconverters.DeleteSignalRequestConverterFactory
import com.udaan.pricing.core.helpers.signals.rawsignalinputconverters.RawSignalInputConverterFactory
import com.udaan.pricing.core.utils.signals.SignalUtil
import com.udaan.pricing.core.utils.signals.VariableUtil
import com.udaan.pricing.core.utils.signals.launchOnIO
import com.udaan.pricing.signalcreation.CreateSignalResponse
import com.udaan.pricing.signalcreation.RawSignalInput
import com.udaan.pricing.signaldeletion.DeleteSignalRequest
import com.udaan.pricing.signaldeletion.DeleteSignalResponse
import com.udaan.pricing.signaldeletion.SignalDeletionInfo
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState
import com.udaan.pricing.variable.Variable
import com.udaan.pricing.variable.VariableId
import com.udaan.resources.cache.RedisCache2

@Singleton
class SignalWriteManager @Inject constructor(
    private val rawSignalInputConverterFactory: RawSignalInputConverterFactory,
    private val deleteSignalRequestConverterFactory: DeleteSignalRequestConverterFactory,
    private val signalRepository: SignalRepository,
    private val variableManager: VariableManager,
    private val objectMapper: ObjectMapper,
    private val dataPlatformHelper: DataPlatformHelper,
    private val signalAnomaliesManager: SignalAnomaliesManager,
    private val resolvedValuesRedisHelper: ResolvedValuesRedisHelper,
    private val eventController: EventController,
    @Named(NamedConstants.Caches.SIGNALS) private val signalCache: RedisCache2<Signal?>
) {
    companion object {
        private val logger by logger()
        private val eligibleVariableForProductGroupCacheRefresh = listOf(VariableId.TRADING_PRICE_WOT_PAISA_UNIT.name)
        private val excludeRedundantCheckForVariables = listOf(
            VariableId.TRADING_PRICE_WOT_PAISA_UNIT
        )
    }

    private suspend fun createPricingSignal(newPricingSignal: Signal): Signal {
        return signalRepository.createOrUpdateSignal(newPricingSignal).also {
            dataPlatformHelper.trackEvent(
                eventData = it.toMap(),
                eventName = DataPlatformHelper.TrackEventName.SIGNAL_AUDIT,
                referenceId1 = it.referenceId,
                referenceId2 = it.id
            )
        }.launchOnIO {
            invalidateSignal(
                signalId = newPricingSignal.id,
                catalogEntity = newPricingSignal.catalogEntity,
                location = newPricingSignal.location,
                variableId = newPricingSignal.variableId,
                territoryRefIds = null
            )
            eventController.publishSignal(newPricingSignal)
        }
    }

    private suspend fun deleteSignalsAsPerInfo(signalDeletionInfoList: List<SignalDeletionInfo>): List<Signal> {
        return signalDeletionInfoList.mapNotNull { info ->
            val signalId = SignalUtil.getSignalId(info.catalogEntity, info.locationValue, info.signalType)
            val signalPartitionKey = SignalUtil.getSignalPartitionKey(info.catalogEntity, info.locationValue)

            val existingSignal = signalRepository.getSignalByIdAndPartitionKey(signalId, signalPartitionKey)

            // todo: Add validation to stop signal upsert if it's already deleted
            existingSignal?.let { signal ->
                val updatedSignal = signal.copy(
                    state = SignalState.DELETED,
                    updatedBy = info.deletedBy,
                    updatedAt = System.currentTimeMillis()
                )

                logger.info("Deleting signal {}", updatedSignal)
                signalRepository.updateSignal(updatedSignal).also {
                    dataPlatformHelper.trackEvent(
                        eventData = updatedSignal.toMap().plus("DELETION_REASON" to info.deletionReason),
                        eventName = DataPlatformHelper.TrackEventName.SIGNAL_AUDIT,
                        referenceId1 = updatedSignal.referenceId,
                        referenceId2 = updatedSignal.id
                    )
                }.launchOnIO {
                    invalidateSignal(
                        signalId = updatedSignal.id,
                        catalogEntity = updatedSignal.catalogEntity,
                        location = updatedSignal.location,
                        variableId = updatedSignal.variableId,
                        territoryRefIds = null
                    )
                    eventController.publishSignal(existingSignal)
                }
            }
        }
    }

    private suspend fun invalidateSignal(
        signalId: String,
        catalogEntity: String,
        location: Location,
        variableId: String,
        territoryRefIds: List<String>?
    ) {
        try {
            signalCache.invalidate(signalId).awaitOrNull()
        } catch (e: Exception) {
            // Log the error but do not throw an exception to avoid breaking the flow
            logger.error("Error while invalidating signal cache for signalId: $signalId", e)
        }
        try {
            if (eligibleVariableForProductGroupCacheRefresh.contains(variableId)) {
                resolvedValuesRedisHelper.invalidateProductGroupLocationValue(
                    productGroupId = catalogEntity,
                    location = location,
                    variableId = variableId,
                    territoryRefIds = territoryRefIds
                )
            }
        } catch (e: Exception) {
            logger.error(
                "Error while invalidating product-group " +
                        "location cache for signalId: $signalId", e
            )
        }
    }

    private fun validatePriceSignal(newSignal: Signal, variable: Variable) {
        val resolvedValueClassSubtype = variable.resolvedValueType.typeClass
        require(resolvedValueClassSubtype == newSignal.signalData::class) {
            "Signal value should be of $resolvedValueClassSubtype"
        }

        require(variable.hierarchies.contains(Pair(newSignal.catalogEntityType, newSignal.location.locationType))) {
            "Invalid hierarchy for ${newSignal.variableId}, ${newSignal.catalogEntityType}, ${newSignal.location.locationType} " +
                    "for signal creation"
        }

        variable.allowedValuesConstraint?.let {
            VariableUtil.validateValuesForVariableAsPerConstraint(
                variableAllowedValuesConstraint = it,
                valueToValidate = newSignal.signalData
            )
        }
    }

    suspend fun createSignalFromRawInput(rawSignalInput: RawSignalInput): CreateSignalResponse {
        val convertedSignals = rawSignalInputConverterFactory.getConverterImplAndConvert(rawSignalInput)
        val createdSignals = convertedSignals.map { signal ->
            applyValidationsAndSaveSignal(signal)
        }

        return CreateSignalResponse(
            signalReferenceIds = createdSignals.map { it.referenceId }
        )
    }

    suspend fun applyValidationsAndSaveSignal(signal: Signal): Signal {
        val variable = variableManager.getVariable(signal.variableId)
        val convertedSignal = signal.copy(
            validTill = variable.freshnessDurationInMillis?.let {
                getCurrentMillis() + it
            }
        )
        val existingSignal = signalRepository.getSignalByIdAndPartitionKey(signal.id, signal.partitionKey)

        val isNewSignalRedundant = isRedundantSignal(existingSignal, convertedSignal, variable)
        return if (existingSignal != null && isNewSignalRedundant) {
            logger.info(
                "Previous signal value {} and new signal value {} are matching, ignoring the update.",
                existingSignal.signalData, convertedSignal.signalData
            )
            existingSignal
        } else {
            validatePriceSignal(convertedSignal, variable)

            signalAnomaliesManager.validateSignalAgainstGuardrails(
                newSignal = convertedSignal,
                existingSignal = existingSignal
            )
            createPricingSignal(convertedSignal)
        }
    }

    suspend fun parseDeleteSignalRequestAndDelete(deleteSignalRequest: DeleteSignalRequest): DeleteSignalResponse {
        val signalDeletionInfoList = deleteSignalRequestConverterFactory.getConverterImplAndConvert(
            deleteSignalRequest = deleteSignalRequest
        )
        val deletedSignals = deleteSignalsAsPerInfo(signalDeletionInfoList)

        return DeleteSignalResponse(
            signalReferenceIds = deletedSignals.map { it.referenceId }
        )
    }

    suspend fun markSignalAsExpired(
        id: String,
        partitionKey: String,
        updatedBy: String,
        forceExpiry: Boolean = false
    ): Signal? {
        val signal = signalRepository.getSignalByIdAndPartitionKey(id = id, partitionKey = partitionKey)
        if (signal != null) {
            val variable = variableManager.getVariable(signal.variableId)
            val isSignalFresh = SignalUtil.validateSignalStateAndFreshness(
                signal = signal,
                freshnessDurationInMillis = variable.freshnessDurationInMillis
            )
            if (forceExpiry || isSignalFresh.not()) {
                val updatedSignal = signal.copy(
                    state = SignalState.EXPIRED,
                    updatedBy = updatedBy,
                    updatedAt = System.currentTimeMillis()
                )
                signalRepository.updateSignal(updatedSignal).also {
                    dataPlatformHelper.trackEvent(
                        eventData = updatedSignal.toMap().plus("EXPIRED_REASON" to "BREACHED_FRESHNESS"),
                        eventName = DataPlatformHelper.TrackEventName.SIGNAL_AUDIT,
                        referenceId1 = updatedSignal.referenceId,
                        referenceId2 = updatedSignal.id
                    )
                }.launchOnIO {
                    invalidateSignal(
                        signalId = updatedSignal.id,
                        catalogEntity = updatedSignal.catalogEntity,
                        location = updatedSignal.location,
                        variableId = updatedSignal.variableId,
                        territoryRefIds = null
                    )
                    eventController.publishSignal(signal)
                }
                logger.info("Expired signal {} ", updatedSignal)
                return updatedSignal
            } else {
                logger.info("Signal is not valid for expiry {} ", signal)
            }
        }
        return signal
    }

    private fun <T : Any> T.toMap(): Map<String, Any> {
        return objectMapper.convertValue(this)
    }

    private fun isRedundantSignal(
        existingPriceSignal: Signal?,
        newPricingSignal: Signal,
        variable: Variable
    ): Boolean {
        if (excludeRedundantCheckForVariables.contains(variable.id) ||
            existingPriceSignal == null || existingPriceSignal.state != SignalState.ACTIVE
        ) {
            return false
        }
        return existingPriceSignal.signalData.toString() == newPricingSignal.signalData.toString() &&
                existingPriceSignal.metadata == newPricingSignal.metadata &&
                variable.freshnessDurationInMillis == null
    }
}
