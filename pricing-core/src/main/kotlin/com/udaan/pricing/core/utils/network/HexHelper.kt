package com.udaan.pricing.core.utils.network

import com.google.inject.Inject
import com.google.inject.Singleton
import com.uber.h3core.H3Core
import com.uber.h3core.util.LatLng
import com.udaan.pricing.network.LatLong

@Singleton
class HexHelper @Inject constructor(
    private val h3Core: H3Core
) {
    companion object {
        const val MID_RES = 10
    }

    fun getHexIdForLatLong(
        lat: Double,
        long: Double,
        resolution: Int = MID_RES
    ): String {
        return h3Core.latLngToCellAddress(lat, long, resolution)
    }

    fun getH3ForPolygon(points: List<LatLong>): MutableList<String> {
        return h3Core.polygonToCellAddresses(
                points.map { LatLng(it.latitude, it.longitude) },
                listOf(),
                MID_RES
            )
    }
}