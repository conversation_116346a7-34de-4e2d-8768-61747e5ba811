package com.udaan.pricing.core.strategyevaluator.impl.dynamic.meat

import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.variable.VariableId

/**
 * Evaluator for Meat category manual prices.
 * This evaluator retrieves and applies the manual price for meat category products.
 * The price is fetched directly from MEAT_MANUAL_PRICE_WOT_PAISA_UNIT variable without any additional markup.
 */
internal object MeatManualPriceEvaluator : Evaluator {

    /**
     * Evaluates and returns the manual price for meat category products post applying the conversion rate.
     *
     * @param data The evaluator request context containing pricing strategy and inputs
     * @return EvaluatorOutput containing the manual price for the meat category product
     */
    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        ValidationUtils.validateInputs(data.strategy, data.inputs)

        val conversionRate = (VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.CONVERSION_RATE
        ) as BigDecimalValue).value

        val manualPriceWithoutTaxPaiseUnit = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.MEAT_MANUAL_PRICE_WOT_PAISA_UNIT
        ) as BigDecimalValue

        // converting manual price to assortment level
        val manualPriceWithoutTaxPaiseAtAssortment =
            BigDecimalValue(
                manualPriceWithoutTaxPaiseUnit.value.multiplyWithScale(conversionRate)
            )

        return EvaluatorOutput(manualPriceWithoutTaxPaiseAtAssortment, emptyMap())
    }
}
