package com.udaan.pricing.core.controller.network

import com.google.inject.Inject
import com.udaan.pricing.core.cache.network.DemandClusterCacheRepo
import com.udaan.pricing.core.cache.network.DemandClusterLocationCacheRepo
import com.udaan.pricing.core.dao.network.DemandClusterRepository
import com.udaan.pricing.network.DemandCluster
import com.udaan.pricing.network.DemandClusterLocations
import com.udaan.pricing.network.DemandClusterReq
import com.udaan.pricing.network.WarehouseDetails
import javax.ws.rs.NotFoundException

class DemandClusterController @Inject constructor(
    private val demandClusterCacheRepo: DemandClusterCacheRepo,
    private val demandClusterLocationCacheRepo: DemandClusterLocationCacheRepo
) {

    /**
     * Ideally i believe this method wont be called at all, as DemandCluster will always
     * be created internally by system. This req based create flow applies to DemandCLusterLocation
     * and InputLocationChain entities.
     *
     * todo - remove this method if above justifies
     */
    suspend fun createDemandCluster(demandClusterReq: DemandClusterReq): DemandCluster {
        val demandCluster = DemandCluster(
            demandClusterName = demandClusterReq.demandClusterName.lowercase(),
            anchorCityName = demandClusterReq.anchorCityName.lowercase(),
            fulfilmentCenters = demandClusterReq.fulfilmentCenters,
            createdBy = demandClusterReq.createdBy,
            updatedBy = demandClusterReq.updatedBy
        )
        return DemandClusterRepository.createOrUpdate(demandCluster).also {
            demandClusterCacheRepo.invalidateDemandClusterCache(demandCluster)
        }
    }

    suspend fun getAnchorCityForLocation(locationName: String): String {
        val demandClusterLocation = demandClusterLocationCacheRepo.getDemandClusterLocationsForLocationName(
            locationName
        ).firstOrNull()
            ?: throw NotFoundException("No such location found in system")
        val demandCluster = demandClusterCacheRepo.getDemandClusterForClusterId(
            demandClusterLocation.demandClusterId
        ).firstOrNull()
            ?: throw NotFoundException("No demand cluster for location found in system")
        return demandCluster.anchorCityName
    }

    suspend fun getLocationsForAnchorCity(anchorCity: String): Collection<DemandClusterLocations> {
        val demandCluster = demandClusterCacheRepo.getDemandClusterForAnchorCity(anchorCity).firstOrNull()
            ?: throw NotFoundException("No demand cluster found for anchor city")

        return demandClusterLocationCacheRepo.getDemandClusterLocationsForDemandClusterId(demandCluster.id)
    }

    suspend fun getWarehousesForAnchorCity(anchorCity: String): List<WarehouseDetails> {
        val demandCluster = demandClusterCacheRepo.getDemandClusterForAnchorCity(anchorCity).firstOrNull()
            ?: throw NotFoundException("No demand cluster found for anchor city")
        return demandCluster.fulfilmentCenters
    }
}