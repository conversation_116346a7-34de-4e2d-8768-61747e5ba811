package com.udaan.pricing.core.models.strategyevaluator.request

import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.strategy.Strategy
import com.udaan.pricing.commons.GenericValue
import com.udaan.pricing.variable.VariableId

data class EvaluatorRequestContext(
    val strategy: Strategy,
    val inputs: List<EvaluatorConfigInput>,
    val previousOutput: EvaluatorOutput? = null
)

data class EvaluatorConfigInput(
    val variableId: VariableId,
    val value: GenericValue?,
    val referenceId: String? = null
)
