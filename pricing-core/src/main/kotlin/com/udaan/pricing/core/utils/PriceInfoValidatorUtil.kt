package com.udaan.pricing.core.utils

import com.udaan.pricing.commons.AbsolutePriceInfo
import com.udaan.pricing.commons.MrpMarkdownInfo
import com.udaan.pricing.commons.PriceInfo
import java.math.BigDecimal

object PriceInfoValidatorUtil {
    fun validatePriceInfo(priceInfo: PriceInfo) {
        when (priceInfo) {
            is AbsolutePriceInfo -> {
                // Ensure ladder values are greater than 0
                val allLadderValuesValid = priceInfo.priceLadderSlabs.all { it.ladderValue > BigDecimal.ZERO }
                require(allLadderValuesValid) {
                    "Price values should be greater than 0."
                }

                // Ensure ladder values are in decreasing order
                val allLadderValuesDecreasing = priceInfo.priceLadderSlabs
                    .zipWithNext { a, b -> a.ladderValue > b.ladderValue }
                    .all { it }
                require(allLadderValuesDecreasing) {
                    "Price values should be in decreasing order."
                }
            }

            is MrpMarkdownInfo -> {
                val allLadderValuesValid = priceInfo.markdownLadderSlabs.all { it.ladderValue > BigDecimal.ZERO }
                require(allLadderValuesValid) {
                    "MRP markdown values should be greater than 0."
                }
            }
        }
    }
}
