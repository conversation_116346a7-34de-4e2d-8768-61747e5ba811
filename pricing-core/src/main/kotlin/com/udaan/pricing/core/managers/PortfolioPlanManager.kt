package com.udaan.pricing.core.managers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.dao.automation.PortfolioItemRepository
import com.udaan.pricing.core.dao.automation.PortfolioRepository
import com.udaan.pricing.core.dao.automation.StrategyRepository
import com.udaan.pricing.portfolioplan.GetPortfolioPlanRequest
import com.udaan.pricing.portfolioplan.PortfolioPlan
import com.udaan.pricing.portfolioplan.PortfolioPlanRequest
import com.udaan.pricing.portfolioplan.PortfolioPlanState
import com.udaan.pricing.portfolioplan.isDefaultCohortPlan
import com.udaan.pricing.portfolioplan.toPortfolioPlan
import com.udaan.pricing.core.dao.automation.PortfolioPlanRepository
import com.udaan.pricing.core.managers.signals.VariableManager
import com.udaan.pricing.variable.VariableType
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await
import javax.ws.rs.BadRequestException
import javax.ws.rs.NotFoundException

@Singleton
class PortfolioPlanManager @Inject constructor(
    private val portfolioPlanRepository: PortfolioPlanRepository,
    private val portfolioRepository: PortfolioRepository,
    private val strategyRepository: StrategyRepository,
    private val variableManager: VariableManager,
    private val portfolioItemRepository: PortfolioItemRepository,
    @Named(NamedConstants.Caches.PORTFOLIO_PLAN_CACHE) private val portfolioPlanCache: RedisCache2<List<PortfolioPlan>>
) {

    private val log by logger()

    private suspend fun validatePortfolioPlanRequest(portfolioPlanRequest: PortfolioPlanRequest) {
        val strategies = portfolioPlanRequest.strategies.map { strategyId ->
            strategyRepository.getStrategyById(strategyId)
                ?: throw NotFoundException("Given $strategyId is not valid.")
        }
        portfolioRepository.getPortfolioById(portfolioPlanRequest.portfolioId)
            ?: throw BadRequestException("No valid portfolio exists for ${portfolioPlanRequest.portfolioId}")
        val allCohortVariables = variableManager.getAllVariables().filter {
            it.type == VariableType.COHORT
        }.map { it.id }
        val cohortVariables = strategies.map { it.usedVariables.filter {
            allCohortVariables.contains(it)
        } }.distinct().flatten()
        val requestCohortVariables = portfolioPlanRequest.cohortInputs.map { it.variableId }
        if( !cohortVariables.all { cohortVariable -> requestCohortVariables.contains(cohortVariable) }) {
            throw BadRequestException("All required $cohortVariables are not provided.")
        }
    }

    suspend fun createOrUpdatePortfolioPlan(portfolioPlanRequest: PortfolioPlanRequest) {
        validatePortfolioPlanRequest(portfolioPlanRequest)
        val portfolioPlans = portfolioPlanRepository.getPortfolioPlanByPortfolioId(portfolioPlanRequest.portfolioId)
        val buyerCohortPlan = portfolioPlans.firstOrNull { it.buyerCohort == portfolioPlanRequest.buyerCohort }
        if (buyerCohortPlan == null) {
            val portfolioPlan = portfolioPlanRequest.toPortfolioPlan()
            portfolioPlanRepository.createOrUpdatePortfolioPlan(portfolioPlan)
            log.info("Created portfolio-plan $portfolioPlan")
        } else {
            portfolioPlanRepository.createOrUpdatePortfolioPlan(
                buyerCohortPlan.copy(
                    state = PortfolioPlanState.DELETED,
                    updatedBy = portfolioPlanRequest.createdBy
                )
            )
            log.info("Updated portfolio-plan $buyerCohortPlan")
            portfolioPlanRepository.createOrUpdatePortfolioPlan(portfolioPlanRequest.toPortfolioPlan())
        }
        portfolioPlanCache.invalidate(portfolioPlanRequest.portfolioId).await()
        portfolioPlanCache.invalidate(portfolioPlanRequest.portfolioId + portfolioPlanRequest.buyerCohort).await()
    }

    private suspend fun getPortfolioPlanByPortfolioId(portfolioId: String): List<PortfolioPlan> {
        return portfolioPlanCache.get(portfolioId) {
            TelemetryScope.future {
                portfolioPlanRepository.getPortfolioPlanByPortfolioId(portfolioId)
            }
        }.await() ?: emptyList()
    }

    suspend fun getPortfolioPlan(getPortfolioPlanRequest: GetPortfolioPlanRequest): List<PortfolioPlan> {
        val portfolioPlans = getPortfolioPlanByPortfolioId(getPortfolioPlanRequest.portfolioId)
        return portfolioPlans.filter {
            getPortfolioPlanRequest.buyerCohort == it.buyerCohort || it.isDefaultCohortPlan()
        }.distinctBy { it.id }
    }

    suspend fun getAllPortfolioPlans(portfolioId: String): Collection<PortfolioPlan> {
        return getPortfolioPlanByPortfolioId(portfolioId)
    }

    suspend fun deletePortfolioPlan(portfolioId: String, buyerCohort: String?, updatedById: String) {
        val portfolioPlans = getPortfolioPlanByPortfolioId(portfolioId)
        val buyerCohortPlan = portfolioPlans.firstOrNull { it.buyerCohort == buyerCohort }
            ?: throw NotFoundException("No portfolio plan found for $portfolioId and $buyerCohort")
        if(buyerCohortPlan.isDefaultCohortPlan()) {
            val checkForPortfolioItemTagging = portfolioItemRepository.checkPortfolioItemTaggingWithPortfolio(portfolioId)
            if(checkForPortfolioItemTagging)
                throw BadRequestException("Portfolio with ID $portfolioId is tagged with some items. Please untag them before deleting the default portfolio plan.")
        }
        portfolioPlanRepository.createOrUpdatePortfolioPlan(
            buyerCohortPlan.copy(
                state = PortfolioPlanState.DELETED,
                updatedBy = updatedById,
                updatedAt = System.currentTimeMillis()
            )
        )
        portfolioPlanCache.invalidate(portfolioId).await()
        portfolioPlanCache.invalidate(portfolioId + buyerCohort).await()
    }

}
