package com.udaan.pricing.core.helpers.signals.rawsignalinputconverters

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.catalog.model.VerticalCategory
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope.Companion.async
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.helpers.CatalogHelper
import com.udaan.pricing.core.svcinterfaces.FpCatalogSvcInterface
import com.udaan.pricing.core.utils.signals.BigDecimalUtil.roundToDefaultScale
import com.udaan.pricing.core.utils.signals.BigDecimalUtil.toBigDecimalWithScale
import com.udaan.pricing.signalcreation.PurchaseOrderInput
import com.udaan.pricing.signals.GenericMetadata
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState
import com.udaan.pricing.variable.VariableId
import java.math.RoundingMode

@Singleton
class PurchaseOrderInputConverter @Inject constructor(
    private val catalogHelper: CatalogHelper,
    private val fpCatalogServiceInterface: FpCatalogSvcInterface,
): RawSignalInputConverter<PurchaseOrderInput>() {
    companion object {
        private val logger by logger()
    }

    override suspend fun convert(rawSignalInput: PurchaseOrderInput): List<Signal> {
        logger.info("Received PO input {}", rawSignalInput)
        val verticalCategory = catalogHelper.getCategoryForProduct(rawSignalInput.productId)

        // Ignored PO events handling for categories other than FMCG or staples
        if (verticalCategory !in listOf(VerticalCategory.FMCG, VerticalCategory.STAPLES)) {
            return emptyList()
        }

        val signal = getSignalFromPurchaseOrderInput(
            purchaseOrderInput = rawSignalInput,
            verticalCategory = verticalCategory
        )

        logger.info("Converted LPP signal: {}", signal)

        return listOf(signal)
    }

    private suspend fun getSignalFromPurchaseOrderInput(
        purchaseOrderInput: PurchaseOrderInput,
        verticalCategory: VerticalCategory
    ): Signal {
        val productGroupDetailDeferred = async {
            fpCatalogServiceInterface.getProductGroupDetailsForProductId(purchaseOrderInput.productId)
        }
        val productDetailDeferred = async {
            fpCatalogServiceInterface.getProductDetails(purchaseOrderInput.productId)
        }

        val unitPrice = purchaseOrderInput.unitPrice.roundToDefaultScale()
        val pidConversionRate = productDetailDeferred.await()
            .packagingAttributes
            .getOrDefault("conversionRate", "1")
            .toBigDecimalWithScale()

        val convertedUnitPrice = unitPrice.divide(pidConversionRate, 4, RoundingMode.HALF_EVEN)

        return Signal(
            catalogEntity = productGroupDetailDeferred.await()?.uppercase()
                ?: throw IllegalStateException("No mapped GID found for ${purchaseOrderInput.productId}"),
            catalogEntityType = CatalogEntityType.PRODUCT_GROUP_ID,
            variableId = getLppVariableIdBasisCategory(verticalCategory).name,
            signalData = BigDecimalValue(
                value = convertedUnitPrice.roundToDefaultScale()
            ),
            metadata = GenericMetadata(
                mapOf(
                    "SELLER_ORG_ID" to purchaseOrderInput.orgId,
                    "PURCHASE_ORDER_ID" to purchaseOrderInput.purchaseOrderId,
                    "PRODUCT_ID" to purchaseOrderInput.productId,
                    "VENDOR_ID" to purchaseOrderInput.vendorId,
                    "VENDOR_UNIT_ID" to purchaseOrderInput.vendorUnitId
                )
            ),
            location = Location(
                locationType = LocationType.WAREHOUSE,
                locationValue = purchaseOrderInput.orgUnitId.uppercase()
            ),
            state = SignalState.ACTIVE,
            createdBy = purchaseOrderInput.purchaseOrderId,
            updatedBy = purchaseOrderInput.purchaseOrderId
        )
    }

    private fun getLppVariableIdBasisCategory(verticalCategory: VerticalCategory): VariableId {
        return when (verticalCategory) {
            VerticalCategory.STAPLES -> VariableId.STAPLES_LPP_WOT_RUPEES_UNIT
            VerticalCategory.FMCG -> VariableId.FMCG_LPP_WOT_RUPEES_UNIT
            else -> throw IllegalArgumentException("Invalid category found")
        }
    }
}
