package com.udaan.pricing.core.dao.automation

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.core.constants.CosmosDbConfig
import com.udaan.pricing.portfolioitem.PortfolioItem
import com.udaan.pricing.portfolioitem.PortfolioItemPortfolioTaggingCount
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.withContext

@Singleton
class PortfolioItemRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {
    private val cosmosDbDao by lazy {
        CosmosDbDao(
            configKey = "pricing",
            databaseName = CosmosDbConfig.PRICING_AUTOMATION_DB,
            containerName = CosmosDbConfig.PORTFOLIO_ITEM_CONTAINER
        ) { builder ->
            builder.connectionSharingAcrossClientsEnabled(true)
        }
    }

    /**
     * Dummy function solely used for client initialisation while starting the service
     */
    suspend fun initialise() {
        cosmosDbDao.findItem("ID1")
    }

    suspend fun createOrUpdate(portfolioItem: PortfolioItem): PortfolioItem {
        return cosmosDbDao.createOrUpdateItem(portfolioItem.toDocument()).toPortfolioItem()
    }

    suspend fun update(portfolioItem: PortfolioItem): PortfolioItem {
        return cosmosDbDao.updateItem(portfolioItem.toDocument()).toPortfolioItem()
    }

    suspend fun getByIdAndPartitionKey(
        id: String,
        partitionKey: String
    ): PortfolioItem? {
        return cosmosDbDao.getItem(id, partitionKey)?.toPortfolioItem()
    }

    suspend fun getCountOfPortfolioItemsForPortfolios(
    ): List<PortfolioItemPortfolioTaggingCount> {
        return withContext(Dispatchers.IO) {
            cosmosDbDao.queryItems(
                queryName = "get-portfolio-item-portfolio-tagging-count",
                querySpec = makeSqlQuerySpec(
                    """
                    select c.portfolioId, count(1) as countOfPortfolioItemTagged from c 
                    where c.state='ACTIVE' group by c.portfolioId
                    """.trimIndent()
                )
            ).toList().map { it.toPortfolioItemPortfolioTaggingCount() }
        }
    }

    suspend fun checkPortfolioItemTaggingWithPortfolio(portfolioId: String): Boolean {
        return withContext(Dispatchers.IO) {
            cosmosDbDao.queryItems(
                queryName = "get-portfolio-item-tagging-with-portfolio",
                querySpec = makeSqlQuerySpec(
                    """
                    select TOP 1 * from c 
                    where c.state='ACTIVE' and c.portfolioId = @portfolioId
                    """.trimIndent(),
                    "@portfolioId" to portfolioId,
                )
            ).toList().isNotEmpty()
        }
    }

    private fun PortfolioItem.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toPortfolioItem() = objectMapper.convertValue(this, PortfolioItem::class.java)

    private fun ObjectNode.toPortfolioItemPortfolioTaggingCount() = objectMapper.convertValue(this, PortfolioItemPortfolioTaggingCount::class.java)

}