package com.udaan.pricing.core.helpers.signals.rawsignalinputconverters

import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.utils.signals.BigDecimalUtil.roundToDefaultScale
import com.udaan.pricing.signalcreation.AutomatedSourcingInput
import com.udaan.pricing.signalcreation.AutomatedSourcingInputType
import com.udaan.pricing.signals.GenericMetadata
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState
import com.udaan.pricing.variable.VariableId

class AutomatedSourcingInputConverter: RawSignalInputConverter<AutomatedSourcingInput>() {
    companion object {
        private val log by logger()
    }

    private fun getApplicableVariableId(type: AutomatedSourcingInputType): VariableId {
        return when (type) {
            AutomatedSourcingInputType.INVENTORY_FLAG -> VariableId.GRANARY_INVENTORY_FLAG
            AutomatedSourcingInputType.PRICE -> VariableId.TRADING_PRICE_WOT_PAISA_UNIT
            else -> throw IllegalArgumentException("Not a valid $type")
        }
    }

    override suspend fun convert(rawSignalInput: AutomatedSourcingInput): List<Signal> {
        log.info("Got conversion request for $rawSignalInput")
        val convertedSignal = Signal(
            catalogEntity = rawSignalInput.groupId.uppercase(),
            catalogEntityType = CatalogEntityType.PRODUCT_GROUP_ID,
            variableId = getApplicableVariableId(rawSignalInput.typeOfInput).name,
            signalData = BigDecimalValue(
                value = rawSignalInput.value.toBigDecimal().roundToDefaultScale()
            ),
            metadata = GenericMetadata(
                metadataMap = mapOf()
            ),
            location = Location(
                locationType = LocationType.WAREHOUSE,
                locationValue = rawSignalInput.warehouseId.uppercase()
            ),
            state = SignalState.ACTIVE,
            createdBy = rawSignalInput.createdBy,
            updatedBy = rawSignalInput.createdBy
        )

        log.info("Request {} was converted to signal {}", rawSignalInput, convertedSignal)
        return listOf(convertedSignal)
    }
}
