package com.udaan.pricing.core.controller

import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.MaxPromotion
import com.udaan.pricing.MaxPromotionReq
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.dao.MaxPromotionRepository
import com.udaan.pricing.core.utils.generateId
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await

class MaxPromotionController @Inject constructor(
    @Named(NamedConstants.Caches.LISTING_MAX_PROMOTION_CACHE) private val listingMaxPromotionCache: RedisCache2<List<MaxPromotion>>,
    private val maxPromotionRepository: MaxPromotionRepository
) {

    val log by logger()

    suspend fun get(listingId: String): List<MaxPromotion>? {

        return listingMaxPromotionCache.get(listingId) {
            TelemetryScope.future {
                maxPromotionRepository.getByListingId(listingId)
            }
        }.await()
    }

    suspend fun createOrUpdate(maxPromotionReq: MaxPromotionReq): MaxPromotion {
        val data = maxPromotionRepository.getByListingId(maxPromotionReq.listingId).firstOrNull()
       val maxPromotion = data?.copy(value = maxPromotionReq.value,created_by = maxPromotionReq.created_by)
           ?: MaxPromotion(id=generateId("MP"), listingId = maxPromotionReq.listingId,
               orgId = maxPromotionReq.orgId,value = maxPromotionReq.value,created_by = maxPromotionReq.created_by)
        listingMaxPromotionCache.invalidate(maxPromotionReq.listingId)
        return maxPromotionRepository.createOrUpdate(maxPromotion)
    }

    suspend fun delete(listingId: String){
        listingMaxPromotionCache.invalidate(listingId)
        maxPromotionRepository.deleteByListingId(listingId)
    }

}
