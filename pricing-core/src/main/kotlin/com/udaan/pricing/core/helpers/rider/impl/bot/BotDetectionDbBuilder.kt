package com.udaan.pricing.core.helpers.rider.impl.bot

import com.fasterxml.jackson.databind.MappingIterator
import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.github.x25.net.tree.IpSubnetTree
import org.apache.commons.validator.routines.InetAddressValidator

private object BotDetectionDbBTransformer: DataTransformer<DPBotDetectionBlockedId, BotDetectionDB> {
    override fun transform(data: MappingIterator<DPBotDetectionBlockedId>): BotDetectionDB {
        val db = BotDetectionDB()
        with(db) {
            data.forEach {
                when (it.idType) {
                    "IP" -> {
                        botIps.add(it.id)
                    }
                    "USER" -> {
                        userIds.add(it.id)
                    }
                    "ORG" -> {
                        orgIds.add(it.id)
                    }
                    "SUBNET" -> {
                        subnetTree.insert(it.id, "BLOCKED")
                    }
                    else -> {
                        TODO("No handling defined for type: ${it.idType}. Row: $it")
                    }
                }
            }
        }

        return db
    }
}

typealias BotDetectionDbBuilderBase = GenericDataLoader<BotDetectionDB>
class BotDetectionDbBuilderFromDP : BotDetectionDbBuilderBase by DataPlatformLoader(
    probeId = bot_detection_probe_id,
    clazz = DPBotDetectionBlockedId::class,
    transformer = BotDetectionDbBTransformer
)

internal class BotDetectionDbBuilderFromCSV(private val resourceName: String): BotDetectionDbBuilderBase by CSVLoader(
    resourceName = resourceName,
    clazz = DPBotDetectionBlockedId::class,
    transformer = BotDetectionDbBTransformer
)

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy::class)
internal data class DPBotDetectionBlockedId(
    val id: String,
    val idType: String,
    val action: String,
    val actionDate: String
)

data class BotDetectionDB(
    val botIps: MutableSet<String> = mutableSetOf(),
    val orgIds: MutableSet<String> = mutableSetOf(),
    val userIds: MutableSet<String> = mutableSetOf(),
    val subnetTree: IpSubnetTree<String> = IpSubnetTree<String>().also {
        it.setDefaultValue(DEFAULT_VAL)
    }
) {
    fun matchesIp(ip: String): Boolean {
        val ipV4Address = InetAddressValidator.getInstance().isValidInet4Address(ip)
        return botIps.contains(ip) || (ipV4Address && subnetTree.find(ip) != DEFAULT_VAL)
    }

    fun matchesOrgId(orgId: String): Boolean {
        return orgIds.contains(orgId)
    }

    fun matchesUserId(userId: String): Boolean {
        return userIds.contains(userId)
    }

    companion object {
        private const val DEFAULT_VAL = "Unknown"
        val EMPTY = BotDetectionDB()
    }
}

private const val bot_detection_probe_id: String = "d4jeyx"
