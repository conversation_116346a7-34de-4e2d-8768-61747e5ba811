package com.udaan.pricing.core.helpers.rider.impl.bot

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.github.x25.net.tree.IpSubnetTree
import com.google.inject.Inject
import java.net.URL

/** Not USED **/
private class SyncAWSSubnetJob @Inject constructor(private val mapper: ObjectMapper){
    fun buildIpPrefixTree(): IpSubnetTree<AWSPrefixes> {
        val url = URL("https://ip-ranges.amazonaws.com/ip-ranges.json")
        val awsIps = mapper.readValue(url, AWSIpAddresses::class.java)
        val tree = IpSubnetTree<AWSPrefixes>()
        tree.setDefaultValue(AWSPrefixes("Unknown"))
        awsIps.prefixes.forEach {
            tree.insert(it.ipPrefix, it)
        }

        return tree
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy::class)
private data class AWSPrefixes(val ipPrefix: String)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy::class)
private data class AWSIpAddresses(val prefixes: List<AWSPrefixes>)


