package com.udaan.pricing.core.common

import com.udaan.pricing.variable.VariableId

class VariableResolutionException(message: String, variableId: VariableId) : IllegalArgumentException(message)

class ContractPriceValidationException(
    message: String,
    val contractUnitPrice: Long?,
    val cogsUnitPrice: Long?,
    val mrpUnitPriceWithOutTax: Long?
) : IllegalArgumentException(message) {

    override fun toString(): String {
        return "ContractPriceValidationException(message='$message')"
    }
}

fun throwContractPriceValidationException(
    message: Any,
    contractUnitPrice: Long?,
    cogsUnitPrice: Long?,
    mrpUnitPriceWithOutTax: Long?
): Nothing =
    throw ContractPriceValidationException(
        message.toString(),
        contractUnitPrice, cogsUnitPrice, mrpUnitPriceWithOutTax
    )
