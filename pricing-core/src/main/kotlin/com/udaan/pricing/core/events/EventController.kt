package com.udaan.pricing.core.events

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.google.inject.name.Named
import com.microsoft.azure.eventhubs.EventData
import com.microsoft.azure.eventhubs.EventHubClient
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.toSignalEvent
import kotlinx.coroutines.future.await

class EventController @Inject constructor(
    private val objectMapper: ObjectMapper,
    @Named(NamedConstants.Events.SIGNALS_V2_EVENT_HUB_CLIENT) private val eventHubClient: EventHubClient
) {
    companion object {
        private val logger by logger()
    }

    suspend fun publishSignal(
        pricingSignal: Signal
    ) {
        val signalEvent = pricingSignal.toSignalEvent()
        eventHubClient.send(
            EventData.create(objectMapper.writeValueAsBytes(signalEvent))
        ).await()
        logger.info("Published event: {} for signal {}", signalEvent, pricingSignal)
    }
}