package com.udaan.pricing.core.dao

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.MaxPromotion
import com.udaan.pricing.core.constants.CosmosDbConfig.COSMOS_DB_NAME
import com.udaan.pricing.core.constants.CosmosDbConfig.MAX_PROMOTION_TABLE
import kotlinx.coroutines.flow.toList

class MaxPromotionRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {

    private val maxPromotionDbDao by lazy {
        CosmosDbDao(
            configKey = "pricing",
            databaseName = COSMOS_DB_NAME,
            containerName = MAX_PROMOTION_TABLE
        ) { builder ->
            builder.connectionSharingAcrossClientsEnabled(true)
        }
    }

    /**
     * Dummy function solely used for client initialisation while starting the service
     */
    suspend fun initialise() {
        maxPromotionDbDao.findItem("ID1")
    }

    suspend fun createOrUpdate(maxPromotion: MaxPromotion): MaxPromotion {
        return maxPromotionDbDao.createOrUpdateItem(maxPromotion.toDocument()).toMaxPromotion()
    }

    suspend fun getByListingId(listingId:String):List<MaxPromotion>{
       return  maxPromotionDbDao.queryItems(
            queryName = "getByListingId-maxDiscount",
            querySpec = makeSqlQuerySpec(
                """ select * from c 
                    where c.listingId= @listingId
                """.trimIndent(),
                "@listingId" to listingId
            )
        ).toList().map { it.toMaxPromotion() }
    }

    suspend fun deleteByListingId(listingId:String){
        val data =   maxPromotionDbDao.queryItems(
            queryName = "deleteByListingId-maxDiscount",
            querySpec = makeSqlQuerySpec(
                """ select * from c 
                    where c.listingId= @listingId
                """.trimIndent(),
                "@listingId" to listingId
            )
        ).toList().map { it.toMaxPromotion() }

        data.map {
            maxPromotionDbDao.deleteItem(it.id,it.listingId)
        }
    }


    private fun MaxPromotion.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toMaxPromotion() = objectMapper.convertValue(this, MaxPromotion::class.java)

}