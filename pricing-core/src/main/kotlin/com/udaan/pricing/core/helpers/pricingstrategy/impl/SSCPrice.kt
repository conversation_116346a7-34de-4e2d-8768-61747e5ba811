package com.udaan.pricing.core.helpers.pricingstrategy.impl

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.catalog.model.VerticalCategory
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope.Companion.async
import com.udaan.pricing.PriceForListing
import com.udaan.pricing.ContextualPriceRequest
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.helpers.BuyerCohortDetails
import com.udaan.pricing.core.helpers.BuyerTagHelper
import com.udaan.pricing.core.helpers.LocationHelper
import com.udaan.pricing.core.helpers.PricingNetworkHelper
import com.udaan.pricing.core.helpers.StrategyExecutorHelper
import com.udaan.pricing.core.helpers.pricingstrategy.Strategy
import com.udaan.pricing.core.models.CatalogEntityContext
import com.udaan.pricing.portfolioitem.CatalogEntityLevel
import com.udaan.pricing.network.WarehouseDetails
import com.udaan.proto.models.ModelV1
import kotlinx.coroutines.Deferred

@Singleton
class SSCPrice @Inject constructor(
    private val buyerTagHelper: BuyerTagHelper,
    private val strategyExecutorHelper: StrategyExecutorHelper,
    private val locationHelper: LocationHelper,
    private val pricingNetworkHelper: PricingNetworkHelper
) : Strategy() {
    private val log by logger()

    /*
        supports portfolio items at below levels:
        - FMCG: GID x WH, GID x city
        - Staples: GID x WH
        - Fresh: listing x city
        - Meat: listing x city
        - other/unknown: listing x city
     */
    private val verticalToPortfolioItemEntity = mapOf(
        VerticalCategory.FMCG to CatalogEntityLevel.PRODUCT_GROUP_ID,
        VerticalCategory.STAPLES to CatalogEntityLevel.PRODUCT_GROUP_ID,
        VerticalCategory.FRESH to CatalogEntityLevel.LISTING_ID,
        VerticalCategory.MEAT to CatalogEntityLevel.LISTING_ID,
        VerticalCategory.OTHER to CatalogEntityLevel.LISTING_ID
    )

    // todo: should we make this location priority list category agnostic?
    private val categoryToLocationTypePriorityList = mapOf(
        VerticalCategory.FMCG to listOf(LocationType.WAREHOUSE, LocationType.CITY),
        VerticalCategory.STAPLES to listOf(LocationType.WAREHOUSE),
        VerticalCategory.FRESH to listOf(LocationType.CITY),
        VerticalCategory.MEAT to listOf(LocationType.CITY),
        VerticalCategory.OTHER to listOf(LocationType.CITY)
    )

    suspend fun fetchWarehousesForACity(city: String): List<WarehouseDetails> {

        val demandClusterLocations = if (city.lowercase() == "bangalore") {
            pricingNetworkHelper.getLocationsForGeoCity("BANGALORE") + pricingNetworkHelper.getLocationsForGeoCity(
                "MYSORE"
            )
        } else {
            pricingNetworkHelper.getLocationsForGeoCity(city)
        }
        val warehouses = demandClusterLocations.flatMap { demandClusterLocation ->
            demandClusterLocation.fulfilmentCenters
        }.toSet().toList()
        return warehouses
    }

    override suspend fun getPrice(
        catalogEntityContext: CatalogEntityContext,
        contextualPriceRequest: ContextualPriceRequest?,
        fetchInactive: Boolean,
        cluster: List<String>?,
        preferredWarehouseId: String?,
        servingWarehouseId: String?,
        buyerOrgUnit: ModelV1.OrgUnit?,
        mappedBenchmarkListingGuardrailPriceDeferred: Deferred<PriceForListing?>?
    ): List<PriceForListing> {
        val salesUnits = catalogEntityContext.fetchSalesUnits()
        val buyerCohorts = async {
            fetchBuyerCohorts(contextualPriceRequest?.buyerContext?.orgId)
        }
        val catalogEntity = async {
            fetchCatalogEntity(catalogEntityContext)
        }
        val locationsSortedByPriority = locationHelper.fetchApplicableLocations(
            listingOrgId = catalogEntityContext.listingDetail.orgId,
            verticalCategory = catalogEntityContext.verticalCategory,
            preferredWarehouseId = preferredWarehouseId,
            categoryToLocationTypes = categoryToLocationTypePriorityList
        )

        val finalPrices =
            if (salesUnits.isEmpty() || catalogEntity.await() == null || locationsSortedByPriority.isEmpty()) {
                log.info(
                    "SalesUnits {} or CatalogEntity {}  or locations {} are missing for ssc execution",
                    salesUnits, catalogEntity.await(), locationsSortedByPriority
                )
                emptyList()
            } else {
                val sscPrices = salesUnits.mapNotNull { salesUnitId ->
                    strategyExecutorHelper.getPrice(
                        listingId = catalogEntityContext.listingId,
                        salesUnitId = salesUnitId,
                        catalogEntity = catalogEntity.await()!!,
                        locationsSortedByPriority = locationsSortedByPriority.map {
                            Location(
                                it.first,
                                it.second
                            )
                        },
                        allBuyerCohorts = buyerCohorts.await(),
                        verticalCategory = catalogEntityContext.verticalCategory,
                        mappedFpProductDetails = catalogEntityContext.fpProductDetails,
                        buyerOrgUnit = buyerOrgUnit,
                        mappedBenchmarkListingGuardrailPriceDeferred = mappedBenchmarkListingGuardrailPriceDeferred
                    )
                }
                log.info("Final SSC price for {}, {} with riders is ", catalogEntityContext.listingId, sscPrices)
                sscPrices
            }

        return finalPrices
    }

    fun fetchCatalogEntity(
        catalogEntityContext: CatalogEntityContext
    ): String? {
        val catalogEntityLevel = verticalToPortfolioItemEntity[catalogEntityContext.verticalCategory]
        return when (catalogEntityLevel) {
            CatalogEntityLevel.PRODUCT_GROUP_ID -> catalogEntityContext.productGroupId
            CatalogEntityLevel.LISTING_ID -> catalogEntityContext.listingId
            CatalogEntityLevel.PRODUCT_ID -> catalogEntityContext.fpProductDetails?.productId
            else -> null
        }
    }

    private suspend fun fetchBuyerCohorts(buyerOrgId: String?): List<BuyerCohortDetails> {
        return buyerOrgId?.let {
            buyerTagHelper.getBuyerTagsApplicable(
                buyerOrgId = it
            )
        } ?: emptyList()
    }
}
