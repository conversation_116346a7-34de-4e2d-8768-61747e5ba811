package com.udaan.pricing.core.svcinterfaces

import com.google.inject.Inject
import com.udaan.proto.models.ModelV1
import com.udaan.user.client.OrgServiceClient
import com.udaan.user.client.RedisOrgRepository
import com.udaan.user.client.UserServiceClient
import kotlinx.coroutines.future.await

class UserSvcInterface @Inject constructor(
    private val userServiceClient: UserServiceClient,
    private val orgServiceClient: OrgServiceClient,
    private val redisOrgRepository: RedisOrgRepository
) {

    suspend fun getBuyerOrg(buyerNumber: String): ModelV1.OrgAccount? {
        return userServiceClient.getOrgByMobile(buyerNumber).executeAwaitOrNull(3)
    }

    suspend fun getBuyer(buyerOrgId: String): ModelV1.OrgAccount? {
        return orgServiceClient.getOrg(buyerOrgId).executeAwaitOrNull(3)
    }

    /**
     * This uses redisOrgRepository client which is suitable for buyer path.
     */
    suspend fun getBuyerOrgById(buyerOrgId: String): ModelV1.OrgAccount {
        return redisOrgRepository.getOrg(buyerOrgId).await()
    }
}
