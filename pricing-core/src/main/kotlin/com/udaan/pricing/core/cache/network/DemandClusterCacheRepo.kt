package com.udaan.pricing.core.cache.network

import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.dao.network.DemandClusterRepository
import com.udaan.pricing.network.DemandCluster
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await

class DemandClusterCacheRepo @Inject constructor(
    @Named(NamedConstants.Caches.DEMAND_CLUSTER_CACHE) private val demandClusterCache: RedisCache2<Collection<DemandCluster>>
) {
    companion object {
        private const val DC_ANCHOR_CITY_KEY_PREFIX = "DC:ANCHOR_CITY:"
        private const val DC_ID_KEY_PREFIX = "DC:ID:"
    }

    suspend fun getDemandClusterForAnchorCity(
        anchorCity: String
    ): Collection<DemandCluster> {
        val demandCluster = demandClusterCache.get(DC_ANCHOR_CITY_KEY_PREFIX + anchorCity.lowercase()) {
            TelemetryScope.future {
                val demandLocation = DemandClusterRepository.getClusterForAnchorCity(anchorCity.lowercase())
                if (demandLocation == null)
                    emptyList()
                else
                    listOf(demandLocation)
            }
        }.await()
        return demandCluster ?: emptyList()
    }

    suspend fun getDemandClusterForClusterId(
        clusterId: String
    ): Collection<DemandCluster> {
        val demandCluster = demandClusterCache.get(DC_ID_KEY_PREFIX + clusterId.lowercase()) {
            TelemetryScope.future {
                val demandLocation = DemandClusterRepository.getClusterForId(clusterId)
                if (demandLocation == null)
                    emptyList()
                else
                    listOf(demandLocation)
            }
        }.await()
        return demandCluster ?: emptyList()
    }

    suspend fun invalidateDemandClusterCache(demandCluster: DemandCluster) {
        demandClusterCache.invalidate(DC_ID_KEY_PREFIX + demandCluster.id.lowercase()).await()
        demandClusterCache.invalidate(DC_ANCHOR_CITY_KEY_PREFIX + demandCluster.anchorCityName.lowercase()).await()
    }
}
