package com.udaan.pricing.core.helpers.signals.variableresolvers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.parallelMap
import com.udaan.pricing.core.managers.signals.SignalReadManager
import com.udaan.pricing.core.models.signals.CatalogEntityContext
import com.udaan.pricing.core.models.signals.LocationContext
import com.udaan.pricing.core.utils.signals.LocationContextUtil.getAllWarehouses
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState
import com.udaan.pricing.variable.Variable
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.variable.requestreponse.ResolvedValue

@Singleton
class FmcgLppResolver @Inject constructor(
    private val signalReadManager: SignalReadManager
): VariableResolver {
    override suspend fun resolve(
        catalogEntityContext: CatalogEntityContext,
        locationContext: LocationContext,
        variable: Variable
    ): Pair<VariableId, ResolvedValue> {
        var selectedSignal: Signal? = null
        var exceptionMessage: String? = null

        try {
            val mappedWarehouses = locationContext.getAllWarehouses()
            val productGroupId = catalogEntityContext.productGroupId
                ?: throw IllegalStateException("No mapped GID found")

            val allExistingLppSignals = mappedWarehouses.parallelMap {
                signalReadManager.getSignalForEntityLocationAndVariableId(
                    catalogEntity = productGroupId,
                    locationValue = it,
                    variableId = variable.id
                )
            }

            val lppSignalToConsider = allExistingLppSignals.filterNotNull().filter {
                it.state == SignalState.ACTIVE
            }.maxByOrNull {
                it.createdAt
            }

            selectedSignal = lppSignalToConsider
        } catch (ex: Exception) {
            exceptionMessage = ex.message
        }

        val finalValue = selectedSignal?.signalData

        val resolvedValue = ResolvedValue(
            success = (exceptionMessage == null && finalValue != null),
            value = finalValue,
            referenceSignalId = selectedSignal?.referenceId,
            resolverLogic = null,
            exception = exceptionMessage,
            metadata = selectedSignal?.metadata,
            lastRefreshedAt = selectedSignal?.updatedAt
        )

        return Pair(variable.id, resolvedValue)
    }
}
