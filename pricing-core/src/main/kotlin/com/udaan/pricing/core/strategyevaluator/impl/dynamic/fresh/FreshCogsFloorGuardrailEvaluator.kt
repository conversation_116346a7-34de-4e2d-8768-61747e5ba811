package com.udaan.pricing.core.strategyevaluator.impl.dynamic.fresh

import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.toCustomString
import com.udaan.pricing.core.utils.LadderUtils
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.variable.VariableId

internal object FreshCogsFloorGuardrailEvaluator : Evaluator {

    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        require(data.previousOutput != null) {
            "Previous output is mandatory for cogs floor guardrail evaluator"
        }
        val outputMetadata = mutableMapOf<String, String>()
        ValidationUtils.validateInputs(data.strategy, data.inputs)

        val previousOutput = data.previousOutput.output
        val freshCogsWithoutTaxPaisaUnit = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.FRESH_COGS_WOT_PAISA_UNIT
        ) as? BigDecimalValue

        val conversionRate = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.CONVERSION_RATE
        ) as BigDecimalValue

        val freshCogsWithoutTaxPaisaAtAssortment =
            freshCogsWithoutTaxPaisaUnit?.value?.multiplyWithScale(conversionRate.value)

        outputMetadata["FLOOR_GUARDRAIL_WOT_PAISE_AT_ASSORTMENT"] =
            freshCogsWithoutTaxPaisaAtAssortment.toCustomString()
        outputMetadata["FLOOR_GUARDRAIL_HIT"] = "false"

        val guardRailedPrice = when (previousOutput) {
            is BigDecimalValue -> {
                if (
                    freshCogsWithoutTaxPaisaAtAssortment != null
                    && previousOutput.value < freshCogsWithoutTaxPaisaAtAssortment
                ) {
                    outputMetadata["FLOOR_GUARDRAIL_HIT"] = "true"
                    outputMetadata["GUARD_RAILED_TO_FLOOR_PRICE"] =
                        freshCogsWithoutTaxPaisaAtAssortment.toCustomString()
                    BigDecimalValue(freshCogsWithoutTaxPaisaAtAssortment)
                } else {
                    previousOutput
                }
            }

            is LadderValue -> {
                val updatedLadders = previousOutput.value.mapIndexed { index, ladder ->
                    val overridedLadderValue =
                        if (
                            freshCogsWithoutTaxPaisaAtAssortment != null
                            && ladder.ladderValue < freshCogsWithoutTaxPaisaAtAssortment
                        ) {
                            outputMetadata["FLOOR_GUARDRAIL_HIT"] = "true"
                            outputMetadata["${index+1}_SLAB_GUARD_RAILED_TO_FLOOR_PRICE"] =
                                freshCogsWithoutTaxPaisaAtAssortment.toCustomString()
                            freshCogsWithoutTaxPaisaAtAssortment
                        } else {
                            ladder.ladderValue
                        }
                    ladder.copy(
                        ladderValue = overridedLadderValue
                    )
                }
                LadderUtils.mergeLaddersWithSimilarValue(LadderValue(updatedLadders))
            }

            else -> {
                throw IllegalArgumentException(
                    "Expected previous output of type BigDecimalValue or " +
                            "LadderValue but got ${previousOutput::class.simpleName}"
                )
            }
        }
        return EvaluatorOutput(guardRailedPrice, outputMetadata)
    }
}
