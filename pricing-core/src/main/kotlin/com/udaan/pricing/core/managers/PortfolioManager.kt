package com.udaan.pricing.core.managers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.dao.automation.PortfolioItemRepository
import com.udaan.pricing.core.dao.automation.PortfolioRepository
import com.udaan.pricing.portfolio.Portfolio
import com.udaan.pricing.portfolio.PortfolioFlag
import com.udaan.pricing.portfolio.PortfolioRequest
import com.udaan.pricing.portfolio.PortfolioState
import com.udaan.pricing.portfolio.toPortfolio
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await
import javax.ws.rs.BadRequestException
import javax.ws.rs.NotFoundException

@Singleton
class PortfolioManager @Inject constructor(
    private val portfolioRepository: PortfolioRepository,
    private val portfolioPlanManager: PortfolioPlanManager,
    private val portfolioItemRepository: PortfolioItemRepository,
    @Named(NamedConstants.Caches.PORTFOLIO_CACHE) private val portfolioCache: RedisCache2<Portfolio?>
) {

    suspend fun deletePortfolio(portfolioId: String, updatedById: String) {
        val portfolio = getPortfolioById(portfolioId)
        val isAnyItemTaggedOnPortfolio = portfolioItemRepository.checkPortfolioItemTaggingWithPortfolio(portfolioId)
        if(isAnyItemTaggedOnPortfolio) throw BadRequestException("Portfolio with ID $portfolioId is tagged with some items. Please untag them before deleting.")
        val portfolioPlans = portfolioPlanManager.getAllPortfolioPlans(portfolioId)
        portfolioRepository.createOrUpdatePortfolio(
            portfolio.copy(
                state = PortfolioState.DELETED,
                updatedBy = updatedById,
                updatedAt = System.currentTimeMillis()
            )
        ).also { portfolioCache.invalidate(it.id).await() }
        portfolioPlans.forEach { portfolioPlan ->
            portfolioPlanManager.deletePortfolioPlan(
                portfolioPlan.portfolioId,
                portfolioPlan.buyerCohort,
                portfolioPlan.updatedBy
            )
        }
    }

    suspend fun createPortfolio(portfolioRequest: PortfolioRequest) {
        val portfolio = portfolioRepository.getActivePortfolioByName(portfolioRequest.name)
        if(portfolio != null) {
            throw BadRequestException("Portfolio with name ${portfolioRequest.name} already exists.")
        }
        portfolioRepository.createOrUpdatePortfolio(portfolioRequest.toPortfolio()).also {
            portfolioCache.invalidate(it.id).await()
        }
    }

    suspend fun getPortfolioById(portfolioId: String): Portfolio {
        return portfolioCache.get(portfolioId) {
            TelemetryScope.future {
                portfolioRepository.getPortfolioById(portfolioId)
            }
        }.await() ?: throw NotFoundException("Portfolio not found for Id: $portfolioId")
    }

    suspend fun getAllActivePortfolios(): List<Portfolio> {
        return portfolioRepository.getAllActivePortfolios()
    }

    suspend fun isSscPortfolio(portfolioId: String?): Boolean {
        val portfolioDetails = if(portfolioId != null) {
            getPortfolioById(portfolioId)
        } else null
        return portfolioDetails?.flags?.getOrDefault(PortfolioFlag.TRADING_ENABLED, true)?.not() ?: false
    }

    suspend fun isTradingPortfolio(portfolioId: String?): Boolean {
        val portfolioDetails = if(portfolioId != null) {
            getPortfolioById(portfolioId)
        } else null
        return portfolioDetails?.flags?.getOrDefault(PortfolioFlag.TRADING_ENABLED, false) ?: false
    }
}
