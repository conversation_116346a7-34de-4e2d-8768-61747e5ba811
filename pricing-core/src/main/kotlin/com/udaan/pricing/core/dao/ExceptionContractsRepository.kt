package com.udaan.pricing.core.dao

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.core.constants.CosmosDbConfig.COSMOS_DB_NAME
import com.udaan.pricing.core.constants.CosmosDbConfig.EXCEPTION_CONTRACTS
import com.udaan.pricing.core.models.contracts.ExceptionContract
import kotlinx.coroutines.flow.toList

@Singleton
class ExceptionContractsRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {

    private val exceptionContractDao by lazy {
        CosmosDbDao(
            configKey = "pricing",
            databaseName = COSMOS_DB_NAME,
            containerName = EXCEPTION_CONTRACTS
        ) { builder ->
            builder.connectionSharingAcrossClientsEnabled(true)
        }
    }

    suspend fun intialise() {
        exceptionContractDao.findItem("ID1")
    }

    suspend fun createOrUpdateExceptionContract(exceptionContract: ExceptionContract): ExceptionContract {
        return exceptionContractDao.createOrUpdateItem(exceptionContract.toDocument()).toExceptionContract()
    }

    suspend fun getExceptionContract(
        id: String,
        buyerOrgId: String
    ): ExceptionContract? {
        return exceptionContractDao.getItem(id, buyerOrgId)?.toExceptionContract()
    }

    suspend fun deleteExceptionContract(exceptionContract: ExceptionContract) {
        exceptionContractDao.deleteItem(exceptionContract.id, exceptionContract.buyerOrgId)
    }

    suspend fun deleteExceptionContractById(id: String, buyerOrgId: String) {
        exceptionContractDao.deleteItem(id, buyerOrgId)
    }

    suspend fun getExceptionContractsForBuyer(
        buyerOrgId: String
    ): List<ExceptionContract> {
        return exceptionContractDao.queryItems(
            queryName = "get-contracts-for-buyer",
            querySpec = makeSqlQuerySpec(
                """
                select * from c where c.buyerOrgId = @buyerOrgId
            """.trimIndent(),
                "@buyerOrgId" to buyerOrgId
            )
        ).toList().map { it.toExceptionContract() }
    }

    private fun ExceptionContract.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toExceptionContract() = objectMapper.convertValue(this, ExceptionContract::class.java)

}
