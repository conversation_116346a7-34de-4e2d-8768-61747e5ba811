package com.udaan.pricing.core.controller.network

import com.udaan.pricing.core.cache.network.DemandClusterCacheRepo
import com.udaan.pricing.core.cache.network.DemandClusterLocationCacheRepo
import com.udaan.pricing.core.dao.network.DemandClusterLocationsRepository
import com.udaan.pricing.network.DemandClusterLocationReq
import com.udaan.pricing.network.DemandClusterLocationRes
import com.udaan.pricing.network.DemandClusterLocations
import com.udaan.pricing.network.WarehouseDetails
import javax.inject.Inject
import javax.ws.rs.NotFoundException

class DemandClusterLocationController @Inject constructor(
    private val demandClusterLocationCacheRepo: DemandClusterLocationCacheRepo,
    private val demandClusterCacheRepo: DemandClusterCacheRepo
) {

    /**
     * This method is not to be used until supply chain APIs are created and integrated
     */
    suspend fun createDemandClusterLocation(demandClusterLocationReq: DemandClusterLocationReq): DemandClusterLocations {
        // the demandClusterId and serving WHs are supposed to be fetched from supply chain system basis pincode of cluster or city
        // the geoCLusterId can be passed as null for now, will be id of entry of pincode definition of cluster when/if migrated to pricing
        val demandClusterLocation = DemandClusterLocations(
            name = demandClusterLocationReq.name.lowercase(),
            type = demandClusterLocationReq.type,
            city = demandClusterLocationReq.city.lowercase(),
            demandClusterId = "testId",
            geoClusterId = null,
            fulfilmentCenters = emptyList(),
            createdBy = demandClusterLocationReq.createdBy,
            updatedBy = demandClusterLocationReq.updatedBy
        )
        return DemandClusterLocationsRepository.createOrUpdate(demandClusterLocation)
    }

    suspend fun getCityForLocation(locationName: String): String {
        val location = demandClusterLocationCacheRepo.getDemandClusterLocationsForLocationName(
            locationName
        ).firstOrNull()
            ?: throw NotFoundException("No location found in system")
        return location.city
    }

    suspend fun getWarehousesForLocation(locationName: String): List<WarehouseDetails> {
        val location = demandClusterLocationCacheRepo.getDemandClusterLocationsForLocationName(
            locationName
        ).firstOrNull()
            ?: throw NotFoundException("No location found in system")
        return location.fulfilmentCenters
    }

    suspend fun getLocationsForCity(city: String): List<DemandClusterLocations> {
        return demandClusterLocationCacheRepo.getDemandClusterLocationsForCity(city).toList()
    }

    suspend fun getLocationsForWarehouse(warehouseId: String): List<DemandClusterLocations> {
        return demandClusterLocationCacheRepo.getDemandClusterLocationsForWarehouse(warehouseId).toList()
    }

    suspend fun getLocationDetails(locationName: String): DemandClusterLocationRes {
        val location = demandClusterLocationCacheRepo.getDemandClusterLocationsForLocationName(
            locationName
        ).firstOrNull()
            ?: throw NotFoundException("No location found in system")

        val anchorCityName = demandClusterCacheRepo.getDemandClusterForClusterId(
            location.demandClusterId
        ).firstOrNull()?.anchorCityName
            ?: throw NotFoundException("No Demand Cluster found for location in system")

        return DemandClusterLocationRes(
            id = location.id,
            name = location.name,
            type = location.type,
            city = location.city,
            demandClusterId = location.demandClusterId,
            geoClusterId = location.geoClusterId,
            fulfilmentCenters = location.fulfilmentCenters,
            anchorCityName = anchorCityName
        )
    }

    suspend fun getAllLocationNames(): List<String> {
        return demandClusterLocationCacheRepo.getAllDemandClusterLocations().map { it.name }
    }

}