package com.udaan.pricing.core.constants

object CosmosDbConfig {
    const val DB_ACCOUNT_CONFIG = "pricing"

    const val PRICING_COMMON_DB = "pricing-common"
    const val PRICING_AUTOMATION_DB = "pricing-automation"

    const val SIGNALS_COLLECTION = "signals"
    const val ASYNC_JOB_COLLECTION = "async-job"
    const val VARIABLE_COLLECTION = "variable-v2"
    const val PRICING_ASYNC_TASKS_COLLECTION = "pricing-async-tasks"
    const val SIGNAL_ANOMALIES_COLLECTION = "signal-anomalies"

    const val STRATEGY_CONTAINER = "strategy"
    const val PORTFOLIO_CONTAINER = "portfolio"
    const val PORTFOLIO_ITEM_CONTAINER = "portfolio-item"
    const val TRADING_PORTFOLIO_ITEM_CONTAINER = "trading-portfolio-item"
    const val PORTFOLIO_PLAN_CONTAINER = "portfolio-plan"

    const val COSMOS_DB_NAME = "pricing"
    const val BASE_PRICE_TABLE_V2 = "prices-r1"
    const val CONTRACTS = "contracts"
    const val EXCEPTION_CONTRACTS = "exception_contracts"
    const val GEO_BASE_PRICE_TABLE = "geo-base-price"
    const val GEO_ADMIN_TABLE = "geo-pricing"
    const val MAX_PROMOTION_TABLE = "max-promo"
    const val MANUAL_PRICE_COLLECTION = "manual-price"
}
