package com.udaan.pricing.core.cache

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.pricing.core.utils.signals.TelemetryUtil.timedRedis
import io.lettuce.core.AbstractRedisAsyncCommands
import io.lettuce.core.KeyValue
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withTimeout

@Singleton
class RedisHelper @Inject constructor(
    private val redisCommand: AbstractRedisAsyncCommands<String, String>
) {
    companion object {
        private const val REDIS_COMMAND_TIMEOUT_IN_MILLIS = 2000L
    }

    suspend fun getRedisHashValues(
        hashKey: String,
        fieldNames: List<String>
    ): List<KeyValue<String, String>>? {
        return withTimeout(REDIS_COMMAND_TIMEOUT_IN_MILLIS) {
            redisCommand.hmget(hashKey, *fieldNames.toTypedArray()).await()
        }
    }

    suspend fun setHashValuesAndExpiryForKey(
        cacheKey: String,
        valueMap: Map<String, String>,
        expiryInSeconds: Long
    ) {
        setHashValuesInRedis(
            cacheKey = cacheKey,
            valueMap = valueMap
        )

        setExpiryForKey(
            cacheKey = cacheKey,
            duration = expiryInSeconds
        )
    }

    private suspend fun setHashValuesInRedis(
        cacheKey: String,
        valueMap: Map<String, String>
    ) = timedRedis(cacheKey, "HMSET") {
        withTimeout(REDIS_COMMAND_TIMEOUT_IN_MILLIS) {
            redisCommand.hmset(cacheKey, valueMap).await()
        }
    }

    suspend fun delHashValuesInRedis(
        cacheKey: String,
        field: String
    ) = timedRedis(cacheKey, "HDEL") {
        withTimeout(REDIS_COMMAND_TIMEOUT_IN_MILLIS) {
            redisCommand.hdel(cacheKey, field).await()
        }
    }

    private suspend fun setExpiryForKey(
        cacheKey: String,
        duration: Long
    ) = timedRedis(cacheKey, "EXPIRE") {
        withTimeout(REDIS_COMMAND_TIMEOUT_IN_MILLIS) {
            redisCommand.expire(cacheKey, duration).await()
        }
    }

}
