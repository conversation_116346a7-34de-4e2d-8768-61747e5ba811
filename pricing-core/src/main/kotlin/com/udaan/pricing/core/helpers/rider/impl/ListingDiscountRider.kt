package com.udaan.pricing.core.helpers.rider.impl

import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.catalog.model.VerticalCategory
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope
import com.udaan.listingtag.client.RedisListingTagsRepository
import com.udaan.pricing.BuyerContext
import com.udaan.pricing.PriceRiderForListing
import com.udaan.pricing.PromotionDetails
import com.udaan.pricing.RiderCode
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.helpers.rider.PricingRequestContext
import com.udaan.pricing.core.helpers.rider.Rider
import com.udaan.pricing.core.models.CatalogEntityContext
import com.udaan.promotions.api.representations.FlatDiscountPromoResponse
import com.udaan.promotions.api.representations.order.PromotionPlatform
import com.udaan.promotions.api.representations.v2.ListingRequest
import com.udaan.promotions.api.representations.v2.SearchListingPromotionRequest
import com.udaan.promotions.client.PromotionsServiceClient
import com.udaan.promotions.client.PromotionsServiceClientV2
import com.udaan.promotions.dto.coupon.getAllMaxBps
import com.udaan.proto.models.ModelV1
import kotlinx.coroutines.future.await
import kotlin.math.absoluteValue
import kotlin.time.ExperimentalTime
import kotlin.time.measureTimedValue

class ListingDiscountRider @Inject constructor(
    private val redisListingTagsRepository: RedisListingTagsRepository,
    private val promotionsServiceClient: PromotionsServiceClient,
    @Named(NamedConstants.Clients.DISCOVERY_PROMOTIONS_CLIENT_V2) private val promotionsDiscoveryServiceClientV2: PromotionsServiceClientV2,
    @Named(NamedConstants.Clients.DISCOVERY_PROMOTIONS_CLIENT) private val promotionsDiscoveryServiceClient: PromotionsServiceClient
) : Rider() {
    override val isAdditive: Boolean = false
    val log by logger()

    override suspend fun getPriceRider(
        catalogEntityContext: CatalogEntityContext,
        requestContext: PricingRequestContext,
        fetchRiderDetails: Boolean,
        cluster: List<String>?,
        preferredWarehouseId: String?,
        servingWarehouseId: String?
    ): PriceRiderForListing? {
        return try {
            val flatDiscountPromoResponse = getFlatDiscountPromotion(
                listingId = catalogEntityContext.listingId,
                buyerContext = requestContext.buyerContext,
                warehouseId = servingWarehouseId,
                verticalCategory = catalogEntityContext.verticalCategory
            )
            val absoluteDiscountPercentage = getAbsoluteDiscount(flatDiscountPromoResponse)
            val ladderBreakerMoq = flatDiscountPromoResponse?.condition?.minQuantity

            if (absoluteDiscountPercentage > 0) {
                PriceRiderForListing(
                    listingId = catalogEntityContext.listingId,
                    saleUnitId = "",
                    bpsInPercentage = absoluteDiscountPercentage,
                    flatVal = 0,
                    isAdditive = false,
                    riderCode = RiderCode.PROMOTION.name,
                    riderDetails = if (fetchRiderDetails) flatDiscountPromoResponse else null,
                    riderAdditionalDetails = null,
                    ladderBreakerMoq = ladderBreakerMoq
                )
            } else null
        } catch (ex: Exception) {
            log.error("getPriceRider ListingDiscountRider Exception {} ", ex.message)
            null
        }
    }

    @OptIn(ExperimentalTime::class)
    override suspend fun getPriceRiderWithAdditionalDetails(
        catalogEntityContext: CatalogEntityContext,
        requestContext: PricingRequestContext,
        fetchRiderDetails: Boolean,
        cluster: List<String>?,
        fetchRiderAdditionalDetails: Boolean,
        preferredWarehouseId: String?,
        servingWarehouseId: String?
    ): PriceRiderForListing? {
        val buyerContext = requestContext.buyerContext
        return try {
            val additionalDetailsDeferred = if (fetchRiderAdditionalDetails) {
                TelemetryScope.async {
                    val promoAdditionalDetailsTimedValue = measureTimedValue {
                        promotionsDiscoveryServiceClientV2.searchListingPromotionsV2(
                            SearchListingPromotionRequest(
                                buyerOrgId = buyerContext?.orgId,
                                sellerOrgId = catalogEntityContext.listingDetail.orgId,
                                excludeCoupons = false,
                                listingMatchingRequest = ListingRequest(
                                    listingId = catalogEntityContext.listingId,
                                    salesUnitId = null
                                ),
                                includeFuturePromos = false,
                                categoryGroupId = buyerContext?.categoryGroupId,
                                applyListingCapOnOrderLinePromos = false
                            ),
                            toPromoPlatform(buyerContext?.platform)
                        ).executeAwait()
                    }
                    log.info(
                        "Promo additional details fetch time: {}",
                        promoAdditionalDetailsTimedValue.duration.inWholeMilliseconds
                    )

                    promoAdditionalDetailsTimedValue.value
                }
            } else null

            val flatDiscountPromoResponseTimedValue = measureTimedValue {
                getFlatDiscountPromotion(
                    listingId = catalogEntityContext.listingId,
                    buyerContext = buyerContext,
                    warehouseId = servingWarehouseId,
                    verticalCategory = catalogEntityContext.verticalCategory
                )
            }

            log.info(
                "Flat discount promo fetch time: {}",
                flatDiscountPromoResponseTimedValue.duration.inWholeMilliseconds
            )

            val absoluteDiscountPercentage = getAbsoluteDiscount(flatDiscountPromoResponseTimedValue.value)
            val details = additionalDetailsDeferred?.await()
            val ladderBreakerMoq = flatDiscountPromoResponseTimedValue.value?.condition?.minQuantity

            if (absoluteDiscountPercentage > 0 || details?.coupons?.isNotEmpty() == true) {
                PriceRiderForListing(
                    listingId = catalogEntityContext.listingId,
                    saleUnitId = "",
                    bpsInPercentage = absoluteDiscountPercentage,
                    flatVal = 0,
                    isAdditive = false,
                    riderCode = RiderCode.PROMOTION.name,
                    riderDetails = if (fetchRiderDetails) flatDiscountPromoResponseTimedValue.value else null,
                    riderAdditionalDetails = details?.let {
                        PromotionDetails(details.coupons.getAllMaxBps() ?: 0, details)
                    },
                    ladderBreakerMoq = ladderBreakerMoq
                )
            } else null
        } catch (ex: Exception) {
            log.error("getPriceRiderWithAdditionalDetails ListingDiscountRider Exception {} ", ex.message)
            null
        }
    }

    private suspend fun getFlatDiscountPromotion(
        listingId: String,
        buyerContext: BuyerContext?,
        warehouseId: String?,
        verticalCategory: VerticalCategory
    ): FlatDiscountPromoResponse? {
        // get the namespace of the promo
        val promoTagNameSpace = promotionsServiceClient.getPromoTaggingNamespace()

        /**
         * Explicit hard code to pass null WH for staples category, since we have stopped wh level promos
         * todo: @om.raj - figure out the right solve with @shashwat and @ravi
         */
        val servingWarehouseId = if (verticalCategory != VerticalCategory.STAPLES)
            warehouseId
        else
            null

        return redisListingTagsRepository
            .getListingTags(listingId, promoTagNameSpace["namespace"])
            .await()
            ?.let {
                if (it.tags.isNullOrEmpty().not()) {
                    promotionsDiscoveryServiceClient
                        .getBpsForListingTagV2(
                            listingTags = it.tags,
                            buyerOrgId = buyerContext?.orgId,
                            warehouseId = servingWarehouseId
                        ).executeAwaitOrNull()
                } else {
                    null
                }
            }?.getMaximumBpsPromo()
    }

    private fun getAbsoluteDiscount(flatDiscountPromoResponse: FlatDiscountPromoResponse?): Int {
        return (flatDiscountPromoResponse?.bps ?: 0).absoluteValue
    }

    private fun toPromoPlatform(platform: String?): PromotionPlatform {
        return when (platform) {
            ModelV1.SellingPlatform.UDAAN_MARKETPLACE.name -> PromotionPlatform.UDAAN_MARKETPLACE
            ModelV1.SellingPlatform.UDAAN_SUPERCLUB.name -> PromotionPlatform.UDAAN_MARKETPLACE
            ModelV1.SellingPlatform.SOCIALMART.name -> PromotionPlatform.SOCIALMART
            ModelV1.SellingPlatform.WONDERMART.name -> PromotionPlatform.WONDERMART
            else -> PromotionPlatform.UDAAN_MARKETPLACE
        }
    }
}

/**
 * extensions for getting max discount
 */
fun List<FlatDiscountPromoResponse>.getMaximumBpsPromo(): FlatDiscountPromoResponse? =
    this.maxByOrNull { it.bps }
