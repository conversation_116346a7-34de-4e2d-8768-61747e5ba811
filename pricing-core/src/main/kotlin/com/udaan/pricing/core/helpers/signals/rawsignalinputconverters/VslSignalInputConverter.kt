package com.udaan.pricing.core.helpers.signals.rawsignalinputconverters

import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.utils.signals.BigDecimalUtil.roundToDefaultScale
import com.udaan.pricing.signalcreation.VslPriceInput
import com.udaan.pricing.signals.GenericMetadata
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState
import com.udaan.pricing.variable.VariableId

class VslSignalInputConverter: RawSignalInputConverter<VslPriceInput>() {
    companion object {
        private val log by logger()
    }

    override suspend fun convert(rawSignalInput: VslPriceInput): List<Signal> {
        log.info("Got conversion request for $rawSignalInput")
        val convertedSignal = Signal(
            catalogEntity = rawSignalInput.groupId.uppercase(),
            catalogEntityType = CatalogEntityType.PRODUCT_GROUP_ID,
            variableId = VariableId.VSL_PRICE_WOT_PAISA_UNIT.name,
            signalData = BigDecimalValue(
                value = rawSignalInput.priceInPaisa.toBigDecimal().roundToDefaultScale()
            ),
            metadata = GenericMetadata(
                metadataMap = mapOf(
                    "LISTING_ID" to rawSignalInput.listingId,
                    "SELECTED_CHANNEL" to rawSignalInput.selectedChannel
                )
            ),
            location = Location(
                locationType = LocationType.WAREHOUSE,
                locationValue = rawSignalInput.warehouseId.uppercase()
            ),
            state = SignalState.ACTIVE,
            createdBy = rawSignalInput.vslDocumentId,
            updatedBy = rawSignalInput.vslDocumentId
        )

        log.info("Request {} was converted to signal {}", rawSignalInput, convertedSignal)
        return listOf(convertedSignal)
    }
}
