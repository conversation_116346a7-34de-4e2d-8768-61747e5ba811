package com.udaan.pricing.core.helpers.signals.variableresolvers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.client.UdaanServiceException
import com.udaan.pricing.core.managers.signals.SignalReadManager
import com.udaan.pricing.core.models.signals.CatalogEntityContext
import com.udaan.pricing.core.models.signals.LocationContext
import com.udaan.pricing.core.utils.signals.CatalogEntityContextUtil.getCatalogEntityBasisTypeEnum
import com.udaan.pricing.core.utils.signals.LocationContextUtil.getLocationValueBasisTypeEnum
import com.udaan.pricing.core.utils.signals.OnlineCompetitionInputsUtil
import com.udaan.pricing.core.utils.signals.SignalUtil
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.variable.Variable
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.variable.requestreponse.ResolvedValue
import com.udaan.pricing.variable.requestreponse.ResolverLogic

@Singleton
class CompMetadataResolver @Inject constructor(
    private val signalReadManager: SignalReadManager
): VariableResolver {

    override suspend fun resolve(
        catalogEntityContext: CatalogEntityContext,
        locationContext: LocationContext,
        variable: Variable
    ): Pair<VariableId, ResolvedValue> {
        var selectedSignal: Signal? = null
        var resolverLogic: ResolverLogic? = null
        var exceptionMessage: String? = null

        try {
            val baseCompVariableId = OnlineCompetitionInputsUtil.getBaseCompVariableForMetadataVariable(variable.id)
            /*
                @todo - ideally, we should be able to fetch base comp variable and then use that for hierarchy and freshnessDuration.
                    Current way of doing this requires metadata variable to be created with same hierarchy and freshnessDuration which has possibility of manual errors.
                    Not able to do it right now as VariableManager can't be injected here without circular dependency issue. Need to refactor this.
             */
            run hierarchiesEvaluator@{
                variable.hierarchies.forEach {

                    // resolve location and catalog entity basis hierarchy of variable, else go to next hierarchy
                    val resolvedLocationAsPerHierarchy = locationContext.getLocationValueBasisTypeEnum(it.second)
                    val catalogEntityAsPerHierarchy = catalogEntityContext.getCatalogEntityBasisTypeEnum(it.first)
                    if(resolvedLocationAsPerHierarchy != null && catalogEntityAsPerHierarchy != null) {
                        val signal = signalReadManager.getSignalForEntityLocationAndVariableId(
                            catalogEntity = catalogEntityAsPerHierarchy,
                            locationValue = resolvedLocationAsPerHierarchy,
                            variableId = baseCompVariableId
                        )

                        if (signal != null) {
                            // validate signal state and expiry
                            val isSignalFresh = SignalUtil.validateSignalStateAndFreshness(
                                signal = signal,
                                freshnessDurationInMillis = variable.freshnessDurationInMillis
                            )

                            if (isSignalFresh) {
                                selectedSignal = signal
                                return@hierarchiesEvaluator
                            }
                        }
                    }
                }
            }
        } catch (ex: UdaanServiceException) {
            exceptionMessage = ex.message
            resolverLogic = ResolverLogic.NO_VALUE_AS_DOWNSTREAM_SVC_ERROR
        } catch (ex: Exception) {
            exceptionMessage = ex.message
        }

        val finalValue = if (selectedSignal != null) {
            OnlineCompetitionInputsUtil.getValueForApplicableMetadataInput(
                selectedSignal!!.metadata,
                variable.id
            )
        } else {
            resolverLogic = if (exceptionMessage != null) {
                ResolverLogic.DEFAULT_VALUE_AS_EXCEPTION_OCCURRED
            } else {
                ResolverLogic.DEFAULT_VALUE_AS_SIGNAL_ABSENT_OR_EXPIRED
            }
            variable.defaultValue
        }

        val resolvedValue = ResolvedValue(
            success = (exceptionMessage == null && finalValue != null),
            value = finalValue,
            referenceSignalId = selectedSignal?.referenceId,
            resolverLogic = resolverLogic,
            exception = exceptionMessage,
            metadata = selectedSignal?.metadata,
            lastRefreshedAt = selectedSignal?.updatedAt
        )

        return Pair(variable.id, resolvedValue)
    }
}
