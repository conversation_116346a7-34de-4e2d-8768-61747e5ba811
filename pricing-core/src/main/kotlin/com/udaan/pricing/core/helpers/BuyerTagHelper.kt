package com.udaan.pricing.core.helpers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.model.orgs.BusinessClass
import com.udaan.model.orgs.OrgBusinessType
import com.udaan.pricing.BuyerCohortResponse
import com.udaan.pricing.BuyerContext
import com.udaan.pricing.core.helpers.ConfigHelper
import com.udaan.pricing.core.svcinterfaces.VerticalSvcInterface
import com.udaan.pricing.portfolioplan.DEFAULT_COHORT
import com.udaan.user.client.OrgInternalIdentityCacheClient
import com.udaan.user.client.UserServiceClient

@Singleton
class BuyerTagHelper @Inject constructor(
    private val orgInternalIdentityCacheClient: OrgInternalIdentityCacheClient,
    private val configHelper: ConfigHelper,
    private val userServiceClient: UserServiceClient,
    private val preferredWarehouseHelper: Preferred<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    private val verticalSvcInterface: VerticalSvcInterface
) {

    companion object {
        private val logger by logger()
        const val PRICING_TAGS = "pricingTags"
        const val HORECA = "horeca"
        const val PHARMA = "pharma"
        const val PACMAN = "pacman"
        const val HORECA_A = "horeca_a"
        const val DEFAULT_COHORT_PRIORITY = 1   // lowest possible non-zero priority
    }

    /**
     * Provides buyer cohorts applicable for buyerOrgId.
     * Custom buyer tags supported by identity svc pricing buyer tags field in data.
     * Static buyerTag is created basis Category (horeca, pacman, pharma) and class attribute.
     * Static buyerTags are assigned default (1) priority while custom tags priority is fetched from config svc.
     *
     * NOTE:
     * - only pricingBuyerTags available in config svc are considered
     * - UNKNOWN and DEFAULT values of classes are also categorised as C
     */
    // todo: @om.raj - identify the use-cases and refactor to inject the buyer cohorts in the beginning
    suspend fun getBuyerTagsApplicable(buyerOrgId: String): List<BuyerCohortDetails> {
        val buyerOrgIdentityObject = orgInternalIdentityCacheClient.getOrgInternalIdentityByOrgId(buyerOrgId)

        // fetching custom buyer cohort value from identity svc pricing_buyer_tags data String
        // @todo - this needs to come from a separate flow later, switch to that when available
        val customBuyerCohort = fetchValidPricingBuyerTag(buyerOrgIdentityObject?.data?.toMap(), buyerOrgId)

        // a horeca a/b buyer list saved in config is used for identification as orgIdentitySvc
        // does not have correct data right now
        // @todo - remove once orgIdentity Data cleanup is done
        val isHorecaAbBuyer = configHelper.isBuyerHorecaAb(buyerOrgId)
        val staticBuyerCohort = when {
            isHorecaAbBuyer -> {
                BuyerCohortDetails(
                    buyerCohort = HORECA_A,
                    cohortPriority = DEFAULT_COHORT_PRIORITY
                )
            }
            (buyerOrgIdentityObject != null) -> {
                // derive components (Horeca vs Pacman and Class) and create buyerTag
                val categoryComponent = deriveCategoryComponent(
                    buyerOrgIdentityObject.isUnifiedHorecaBuyer(),
                    buyerOrgIdentityObject.businessType
                )
                val classComponent = deriveClassComponent(buyerOrgIdentityObject.businessClass)
                BuyerCohortDetails(
                    buyerCohort = categoryComponent + "_" + classComponent,
                    cohortPriority = DEFAULT_COHORT_PRIORITY
                )
            }
            else -> null
        }
        return staticBuyerCohort?.let { customBuyerCohort.plus(staticBuyerCohort) } ?: customBuyerCohort
    }

    fun fetchAllPossibleBuyerTagsForCategoryAndClass(): List<BuyerCohortDetails> {
        val allCategoryComponents = listOf(HORECA, PACMAN)
        val allClassComponents = listOf("a", "b", "c", "d")
        return allCategoryComponents.flatMap { categoryComponent ->
            allClassComponents.map { classComponent ->
                val buyerCohort = (categoryComponent + "_" + classComponent).uppercase()
                BuyerCohortDetails(
                    buyerCohort = buyerCohort,
                    cohortPriority = DEFAULT_COHORT_PRIORITY
                )
            }
        }.plus(
            BuyerCohortDetails(
                buyerCohort = DEFAULT_COHORT.uppercase(),
                cohortPriority = DEFAULT_COHORT_PRIORITY
            )
        )
    }

    suspend fun getBuyerCohortAndLocationForBuyerAndListingId(
        buyerNumber : String,
        listingId: String,
        salesUnitId: String
    ) : BuyerCohortResponse {
        logger.info("Got request to fetch buyer cohort for SSC Price visibility for buyer $buyerNumber")

        val buyerOrg = try {
            userServiceClient.getOrgByMobile(buyerNumber).executeAwait(3)
        } catch (ex: Exception) {
            logger.error("Exception while fetching buyer org for buyer $buyerNumber Exception $ex")
            null
        }

        val preferredWarehouseId = buyerOrg?.let {
            preferredWarehouseHelper.getPreferredWarehouseId(
                listingId = listingId,
                salesUnitIds = listOf(salesUnitId),
                verticalCategory = verticalSvcInterface.getVerticalCategoryForLid(listingId),
                buyerContext = BuyerContext(
                    orgId = buyerOrg.orgId,
                    orgUnitId = buyerOrg.orgUnitsMap.values.first().orgUnitId,
                    pincode = buyerOrg.orgUnitsMap.values.first().unitAddress.pincode
                )
            )
        }

        // passing least priority cohort which is static cohort for now
        // this will continue to not break ssc visibility console till we start adding plans for custom cohorts
        // @todo - put multi cohort support here and show all cohorts in console
        val buyerCohort = buyerOrg?.orgId?.let { getBuyerTagsApplicable(it) }?.minByOrNull { it.cohortPriority }?.buyerCohort

        return BuyerCohortResponse(
            buyerNumber = buyerNumber,
            buyerOrgId = buyerOrg?.orgId,
            buyerCohort = buyerCohort,
            buyerServingWarehouse = preferredWarehouseId?.uppercase(),
            errorMessage = if(buyerOrg == null) "Not able to fetch buyer org for buyer $buyerNumber" else null
        )
    }

    /**
     * This returns buyerTags passed as BuyerCohortDetails list.
     * It picks priority from config svc if buyer tag present there, else default priority.
     */
    suspend fun getBuyerDetailsForBuyerCohorts(buyerTags: List<String>): List<BuyerCohortDetails> {
        // fetch all custom cohorts from config svc
        val allCustomTagsWithPriority = configHelper.getAllBuyerTagsPriority()
        return buyerTags.map { buyerTag ->
            // assign custom buyer tag priority if found in config svc, else default priority
            val buyerTagPriority = allCustomTagsWithPriority?.entries
                ?.firstOrNull { it.key.equals(buyerTag, ignoreCase = true) }
                ?.value?.toInt() ?: DEFAULT_COHORT_PRIORITY
            BuyerCohortDetails(
                buyerCohort = buyerTag.lowercase(),
                cohortPriority = buyerTagPriority
            )
        }
    }

    /**
     * This return valid buyerTags basis availability in config svc, pricingBuyerTag field in data
     * and priority (derived from config svc).
     */
    private suspend fun fetchValidPricingBuyerTag(
        orgIdentityData: Map<String, Any>?,
        buyerOrgId: String
    ): List<BuyerCohortDetails> {
        val pricingBuyerTags = try {
            (orgIdentityData?.get(PRICING_TAGS) as? String)
                ?.split(",")
                ?: emptyList()
        } catch (ex: Exception) {
            logger.error("Unable to get pricing buyer tags for buyer {}, {}", buyerOrgId, ex.message)
            emptyList<String>()
        }

        return if (pricingBuyerTags.isNotEmpty()) {
            /*
                fetching all valid buyer tags

                NOTE: The assumption is any new pricingBuyerTag added in identity svc, should be added to config svc with priority.
                Choices:
                    1. If configSvc returns priorities, only that set of tags will be considered; if applicable tag doesn't have priority
                    it will be ignored.
                    2. If configSvc returns null (some exception scenario), we ignore pricingBuyerTag (we can pick last pricingBuyerTag,
                    but currently we have a lot of random tags in orgIdentityData; post cleanup we can do this)
             */
            val allTagsWithPriority = configHelper.getAllBuyerTagsPriority()
            if (allTagsWithPriority != null) {
                val getTagsWithPriority = allTagsWithPriority.filter { (key, _) ->
                    pricingBuyerTags.any { it.equals(key, ignoreCase = true) }
                }
                getTagsWithPriority.map { (buyerTag, prioValue) ->
                    BuyerCohortDetails(
                        buyerCohort = buyerTag.lowercase(),
                        cohortPriority = prioValue.toInt()
                    )
                }
            } else emptyList()

        } else emptyList()
    }

    /**
     * This returns biz category component of buyerTag (Pacman vs Horeca)
     */
    private fun deriveCategoryComponent(isHoreca: Boolean?, orgBusinessType: OrgBusinessType): String {
        return when {
            orgBusinessType == OrgBusinessType.PHARMACY -> PHARMA
            isHoreca == true -> HORECA
            else -> PACMAN
        }
    }

    /**
     * This returns class component of buyerTag basis BusinessClass in orgIdentityData
     *
     * NOTE: UNKNOWN and DEFAULT values of classes are also categorised as C
     *
     * UPDATE (8th Apr, 2025):
     * - Org Svc is introducing ENTERPRISE, LARGE, MEDIUM, SMALL class types for HORECA separate from existing A,B, C, D
     * - For now, we are mapping them to A, B, C, D respectively to support our plans with similar names
     * @todo We need to though migrate to new class names for HORECA in both plans and here when time permits
     */
    private fun deriveClassComponent(businessClass: BusinessClass?): String {
        return when (businessClass) {
            BusinessClass.CLASS_A,
            BusinessClass.ENTERPRISE -> "a"
            BusinessClass.CLASS_B,
            BusinessClass.LARGE -> "b"
            BusinessClass.CLASS_D,
            BusinessClass.SMALL -> "d"
            BusinessClass.CLASS_C,
            BusinessClass.MEDIUM,
            BusinessClass.DEFAULT,
            BusinessClass.UNKNOWN -> "c"
            else -> "c"
        }
    }
}

data class BuyerCohortDetails(
    val buyerCohort: String,
    val cohortPriority: Int
)
