package com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg

import com.udaan.catalog.model.VerticalCategory
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.CompMatchUtils
import com.udaan.pricing.core.utils.LadderUtils
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.variable.VariableId

//@todo - we have got two Metro strategies now, one for FMCG, other for Staples, with minor differences. Can we consolidate by adding may be a category input?
/**
 * Evaluator for metro comp match.
 * Logic:
 * - MRP match needed, else return previous output in ladder format (if not already)
 * - comp input needed as non-null, else return previous output in ladder format (if not already)
 * - apply comp match logic along with cogs at slab to slab level
 * - return ladders merged where slabs have same value, no capping of slab count
 */
internal object FmcgMetroCompMatchEvaluator : Evaluator {

    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        ValidationUtils.validateInputs(data.strategy, data.inputs)
        require(data.previousOutput != null) {
            "Previous output is mandatory for Metro match."
        }
        val outputMetadata = mutableMapOf<String, String>()

        // fetching conversion rate
        val conversionRate = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.CONVERSION_RATE
        ) as BigDecimalValue

        // previous input in ladder format (if not already)
        val ladderInputWithoutTaxPaisaAssortment = LadderUtils.convertToLadderValue(
            inputValue = data.previousOutput.output,
            outputMetadata = outputMetadata
        )

        // no comp match if MRP mismatches
        val mrpMatchStatus = CompMatchUtils.isCompMrpMatching(
            compMrpVariableId = VariableId.METRO_COMP_MRP_WT_PAISA_UNIT,
            conversionRate = conversionRate.value,
            inputs = data.inputs,
            outputMetadata = outputMetadata
        )
        if(mrpMatchStatus.not()) return EvaluatorOutput(ladderInputWithoutTaxPaisaAssortment, outputMetadata)

        // comp price without tax at assortment level
        val compPriceWithoutTaxPaisaAssortment = CompMatchUtils.getCompLadderPriceWithoutTaxAtAssortment(
            compVariableId = VariableId.METRO_COMP_LADDER_PRICE_WT_PAISA_UNIT,
            conversionRate = conversionRate.value,
            inputs = data.inputs,
            verticalCategory = VerticalCategory.FMCG,
            outputMetadata = outputMetadata
        ) ?: return EvaluatorOutput(ladderInputWithoutTaxPaisaAssortment, outputMetadata)

        // comp floor guardrail price
        val floorGuardrailWithoutTaxPaiseAtAssortment = CompMatchUtils.getFloorGuardrailForCompInPaiseAtAssortment(
            inputs = data.inputs,
            variableId = VariableId.FMCG_COGS_WOT_PAISA_SET,
            conversionRate = conversionRate.value
        )

        outputMetadata["METRO_LADDER_PRICE_WOT_PAISE_ASSORTMENT"] = compPriceWithoutTaxPaisaAssortment.toString()
        outputMetadata["COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT"] = floorGuardrailWithoutTaxPaiseAtAssortment.toString()

        // deriving final ladders post comparison and merge, no slab capping applied
        val finalLadders = LadderUtils.deriveFinalLaddersPostComparisonAndMerge(
            compFloorGuardrailPrice = floorGuardrailWithoutTaxPaiseAtAssortment,
            ladderInputWithoutTaxPaisaAssortment = ladderInputWithoutTaxPaisaAssortment,
            compPriceWithoutTaxPaisaAssortment = compPriceWithoutTaxPaisaAssortment,
            outputMetadata = outputMetadata,
            applyLadderCountCap = false
        )
        return EvaluatorOutput(finalLadders, outputMetadata)
    }
}
