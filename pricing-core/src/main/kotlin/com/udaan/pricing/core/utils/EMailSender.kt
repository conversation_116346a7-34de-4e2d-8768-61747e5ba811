package com.udaan.pricing.core.utils

import com.udaan.common.utils.MailSender
import com.udaan.common.utils.SendMailRequest
import com.udaan.common.utils.kotlin.logger
import com.udaan.config.Configuration
import java.io.File
import java.util.*
import javax.mail.Authenticator
import javax.mail.PasswordAuthentication
import javax.mail.internet.AddressException
import javax.mail.internet.InternetAddress

object EMailSender {

    private val log by logger()
    private val apiKey = Configuration.get("comm/smtp/apikey")
    private const val defaultSenderEmail = "<EMAIL>"
    private val mailSender = MailSender(
        Properties().also {
            it["mail.transport.protocol"] = "smtp"
            it["mail.smtp.host"] = "smtp.sendgrid.net"
            it["mail.smtp.port"] = 587
            it["mail.smtp.auth"] = "true"
        },
        object : Authenticator() {
            override fun getPasswordAuthentication(): PasswordAuthentication {
                check(apiKey != null) {
                    throw IllegalStateException("SMTP Credentials Missing")
                }
                return PasswordAuthentication("apikey", apiKey)
            }
        }
    )

    fun sendMail(
        tos: List<String>,
        subject: String,
        body: String,
        cc: List<String>,
        bcc: List<String>,
        attachments: List<File>? = null
    ) {
        val toValidEmailIds = tos.toValidEmails()
        if (toValidEmailIds.isEmpty()) {
            log.info("No valid to email addresses found to send email. Skipping email send.")
            return
        }
        mailSender.sendMail(
            SendMailRequest(
                sender = InternetAddress(defaultSenderEmail),
                tos = toValidEmailIds,
                subject = subject,
                textBody = body,
                ccs = cc.toValidEmails(),
                bccs = bcc.toValidEmails(),
                attachments = attachments ?: emptyList()
            )
        )
    }

    private fun List<String>.toValidEmails(): List<InternetAddress> {
        return this.filter { it.isNotBlank() && it.contains("@") }.mapNotNull {
            try {
                InternetAddress(it)
            } catch (e: AddressException) {
                log.error("Invalid email address: $it", e)
                null
            }
        }
    }
}