package com.udaan.pricing.core.utils

import com.udaan.catalog.model.VerticalCategory
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.GenericValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.core.strategyevaluator.Constants
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.min
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import java.math.BigDecimal

object LadderUtils {

    private const val MAX_LADDER_COUNT_THRESHOLD = 4

    /**
     * Merges consecutive ladder slabs with similar value.
     */
    fun mergeLaddersWithSimilarValue(ladderValue: LadderValue): LadderValue {
        return LadderValue(
            ladderValue.value
                .groupBy { it.ladderValue }
                .map { (_, laddersWithSameValue) ->
                    Ladder(
                        minQuantity = laddersWithSameValue.first().minQuantity,
                        maxQuantity = laddersWithSameValue.last().maxQuantity,
                        ladderValue = laddersWithSameValue.first().ladderValue
                    )
                })
    }

    /**
     * Applies conversion rate adjustments on ladder.
     * - for FMCG, creates new slabs with min and max qtys adjusted according to conversion rate
     * - for all, multiplies values with conversion rate for each slab
     */
    fun applyConversionRateOnLadders(
        conversionRate: BigDecimal,
        ladders: List<Ladder>,
        verticalCategory: VerticalCategory,
        outputMetadata: MutableMap<String, String>
    ): List<Ladder> {
        val assortmentAdjustedLadder = if (verticalCategory == VerticalCategory.FMCG) {
            outputMetadata["COMP_SLABS_ADJUSTED_FOR_ASSORTMENT"] = "true"
            getAssortmentAdjustedLadder(conversionRate = conversionRate.toLong(), inputLadder = ladders)
        } else ladders
        return assortmentAdjustedLadder.map {
            Ladder(
                minQuantity = it.minQuantity,
                maxQuantity = it.maxQuantity,
                ladderValue = it.ladderValue.multiplyWithScale(conversionRate)
            )
        }
    }

    /**
     * This fun modifies input ladder to adjust min and max qtys of slabs
     * according to conversion rate.
     *
     * Ex:
     * Input ladder: [(1, 8, 1.0), (9, 25, 2.0), (26, 40, 3.0), (41, MaxInt, 3.5)]
     * conversion rate: 10
     * updated ladder: [(1, 2, 2.0), (3, 4, 3.0), (5, MaxInt, 3.5)]
     *
     * logic:
     * - derive slabs which have multiples of conversion rate (like second, third and forth slabs in ex)
     * - create new slabs by picking conversion rate multipliers with respective slab values
     */
    fun getAssortmentAdjustedLadder(
        conversionRate: Long,
        inputLadder: List<Ladder>
    ): List<Ladder> {
        val maxQuantity = Int.MAX_VALUE.toLong()

        // this var denotes count in chunks of conversionRate
        // ex: if conversion rate is 10, then assortmentLevelCount values denote
        // 1 == 10 ladder qtys, 2 == 20 ladder qtys, and so on
        var assortmentLevelCount = 1

        // this denotes the last ladder index which satisfies the assortmentLevelCount being within min and max qtys
        // ex:
        // if assortmentLevelCount = 1, ladder is [(1,8, 1.0), (9,18, 2.0), (19, MAXINT, 3.0)], conversion rate is 10
        // then assortmentLevelCount 1 translates to 1*10 = 10 which fits in second slab
        // so this will store 1 index denoting second slab of ladder
        var lastSelectedLadderIndexForAssortmentCount = 0

        // this stores assortmentLevelCount with applicable ladder value
        // ex:
        // if assortmentLevelCount = 1, ladder is [(1,8, 1.0), (9,18, 2.0), (19, MAXINT, 3.0)], conversion rate is 10
        // then assortmentLevelCount 1 translates to 1*10 = 10 which fits in second slab
        // so this will store 1 with second slab value which is 2.0, hence [(1, 2.0)]
        val assortmentLevelCountWithApplicableLadderValue = mutableListOf<Pair<Int, BigDecimal>>()


        // Iterate over all possible assortment level counts until
        // 1. you reach max quantity possible for current assortment (which would be Int_max) or,
        // 2. you reach last element of all the ladders

        // For case, where there is a single ladder, get value for assortmentLevelCount = 1 only
        // (because, for all greater assortment counts, the values will be same only)
        while (
            assortmentLevelCount == 1 ||
            (assortmentLevelCount * conversionRate <= maxQuantity &&
                    lastSelectedLadderIndexForAssortmentCount < inputLadder.size - 1)
        ) {
            // increase ladder index until assortmentLevelCount * conversionRate fits within min and max qty of slab
            // continue till end of all ladders reached
            var bestPossibleLadderForThisPuCollection = lastSelectedLadderIndexForAssortmentCount
            while (
                bestPossibleLadderForThisPuCollection < inputLadder.size &&
                assortmentLevelCount * conversionRate >= inputLadder[bestPossibleLadderForThisPuCollection].minQuantity
            ) {
                lastSelectedLadderIndexForAssortmentCount = bestPossibleLadderForThisPuCollection
                bestPossibleLadderForThisPuCollection++
            }
            assortmentLevelCountWithApplicableLadderValue.add(
                Pair(
                    assortmentLevelCount,
                    inputLadder[lastSelectedLadderIndexForAssortmentCount].ladderValue
                )
            )

            assortmentLevelCount++
        }

        // For each distinct value, take the first of the assortmentLevelCount (to remove duplicate value pairs)
        // Example - [(2, 1.0), (3, 1.0), (4, 1.5), (5, 1.5)] => [(2, 1.0), (4, 1.5)]
        val filteredAssortmentLevelCountWithValues = assortmentLevelCountWithApplicableLadderValue
            .groupBy { it.second }
            .map { (_, v) -> v.first() }

        // Create Ladders with some default max quantity
        val laddersWithDefaultMaxQty = filteredAssortmentLevelCountWithValues.map {
            Ladder(
                minQuantity = it.first.toLong(),
                maxQuantity = maxQuantity,
                ladderValue = it.second
            )
        }

        // Fix max quantity in all the quantity Ladders
        return laddersWithDefaultMaxQty.mapIndexed { index, ladder ->
            if (index < laddersWithDefaultMaxQty.size - 1) {
                ladder.copy(maxQuantity = laddersWithDefaultMaxQty[index + 1].minQuantity - 1)
            } else {
                ladder
            }
        }
    }

    /**
     * Converts input value to LadderValue if it is not already in LadderValue format.
     *
     * Throws exception if inputValue is not of type BigDecimalValue or LadderValue.
     */
    fun convertToLadderValue(inputValue: GenericValue?, outputMetadata: MutableMap<String, String>): LadderValue {
        return when (inputValue) {
            is BigDecimalValue -> {
                outputMetadata["DEFAULT_LADDER_CREATED"] = "true"
                LadderValue(
                    listOf(
                        Ladder(
                            minQuantity = 1,
                            maxQuantity = Int.MAX_VALUE.toLong(),
                            ladderValue = inputValue.value
                        )
                    )
                )
            }
            is LadderValue -> {
                inputValue
            }
            else -> throw IllegalArgumentException(
                "Expected previous output to be BigDecimalValue or LadderValue but got $inputValue"
            )
        }
    }

    /**
     * Derives final ladders post comparison and merge.
     *
     * If floorGuardrail value is null, ladderInput is returned post merging slabs with similar value.
     * If floorGuardrail present,
     * - compares ladderInput with compPrice and creates exhaustive ladders
     * - applies max ladder count check
     * - merges and returns
     *
     * @see compareAndCreateLadders
     * @see applyLadderCountThreshold
     * @see mergeLaddersWithSimilarValue
     */
    fun deriveFinalLaddersPostComparisonAndMerge(
        compFloorGuardrailPrice: BigDecimal?,
        ladderInputWithoutTaxPaisaAssortment: LadderValue,
        compPriceWithoutTaxPaisaAssortment: LadderValue,
        outputMetadata: MutableMap<String, String>,
        applyLadderCountCap: Boolean = true
    ): LadderValue {
        val newLadders = if (compFloorGuardrailPrice != null) {
            // do comp match if floor guardrail exists
            // compare and merge ladders
            val exhaustiveMergedLadders = compareAndCreateLadders(
                ladderPriceInput = ladderInputWithoutTaxPaisaAssortment.value,
                benchmarkLadderPriceInput = compPriceWithoutTaxPaisaAssortment.value,
                floorGuardRailPrice = compFloorGuardrailPrice,
                outputMetadata = outputMetadata
            )
            // apply ladder count threshold check
            if (applyLadderCountCap) {
                applyLadderCountThreshold(exhaustiveMergedLadders.value, outputMetadata)
            } else {
                exhaustiveMergedLadders
            }
        } else {
            // return input as is since no floor guardrail (no comp match)
            outputMetadata["NO_COMP_COMPARISON_AS_NO_GUARDRAIL"] = "true"
            ladderInputWithoutTaxPaisaAssortment
        }
        // merge similar value slabs and return
        return mergeLaddersWithSimilarValue(newLadders)
    }

    /**
     * Derives final ladders which will be ladders similar to cogs.
     * We take comp price and guardrail with cogs.
     * if we end with similar values in some ladders then we merge the ladders.
     * if there is no guardrail price then we return the input as is by ignoring the comp price.
     */
    fun deriveFinalLaddersWithDirectCompAndMerge(
        compFloorGuardrailPrice: BigDecimal?,
        ladderInputWithoutTaxPaisaAssortment: LadderValue,
        compPriceWithoutTaxPaisaAssortment: LadderValue,
        outputMetadata: MutableMap<String, String>,
        applyLadderCountCap: Boolean = true
    ): LadderValue {
        val newLadders = if (compFloorGuardrailPrice != null) {

            // direct comp match with guardrail
            val exhaustiveMergedLadders = directCompMatchWithGuardrail(
                compPriceWithoutTaxPaisaAssortment.value, compFloorGuardrailPrice, outputMetadata
            )
            // apply ladder count threshold check
            if (applyLadderCountCap) {
                applyLadderCountThreshold(exhaustiveMergedLadders.value, outputMetadata)
            } else {
                exhaustiveMergedLadders
            }
        } else {
            // return input as is since no floor guardrail (no comp match)
            outputMetadata["NO_COMP_COMPARISON_AS_NO_GUARDRAIL"] = "true"
            ladderInputWithoutTaxPaisaAssortment
        }
        // merge similar value slabs and return
        return mergeLaddersWithSimilarValue(newLadders)
    }


    /**
     * Compares ladderPriceInput with benchmarkLadderPriceInput and creates exhaustive ladders.
     *
     * Comparison happen at each slab level following below logic:
     * - MIN( input, MAX( benchmark, floorGuardrail ) )
     * - this allows to match with benchmark only till floor guardrail while still allowing input to go below
     *
     * This merges slabs with similar value before returning.
     *
     * @see getLadderPriceAndIndex
     */
    fun compareAndCreateLadders(
        ladderPriceInput: List<Ladder>,
        benchmarkLadderPriceInput: List<Ladder>,
        floorGuardRailPrice: BigDecimal?,
        outputMetadata: MutableMap<String, String>
    ): LadderValue {
        // fetch ordered minQty from both ladders
        val allMinQuantity = listOf(
            ladderPriceInput.map { it.minQuantity } + benchmarkLadderPriceInput.map { it.minQuantity }
        ).flatten().distinct().sorted()

        val exhaustiveLadders = allMinQuantity.mapIndexed { index, minQuantity ->
            // derive max qty for the current slab basis next min qty
            val maxQuantity = if (index == allMinQuantity.size - 1) {
                Int.MAX_VALUE.toLong()
            } else {
                allMinQuantity[index + 1] - 1
            }
            // fetch comp and input price values and slab index for current slab
            val (benchmarkPrice, benchmarkLadderIndex) = getLadderPriceAndIndex(
                ladderPriceInput = benchmarkLadderPriceInput,
                minQuantity = minQuantity,
                maxQuantity = maxQuantity
            )
            val (inputPrice, _) = getLadderPriceAndIndex(ladderPriceInput, minQuantity, maxQuantity)

            // compare using logic MIN( input, MAX( comp, floorGuardrail ) )
            // this allows to match with comp only till floor guardrail while still allowing input to go below
            val floorAppliedBenchmarkPrice = if (floorGuardRailPrice != null && benchmarkPrice < floorGuardRailPrice) {
                outputMetadata["COMP_PRICE_GUARD_RAILED_TO_FLOOR_FOR_LADDER_${benchmarkLadderIndex + 1}"] = "true"
                floorGuardRailPrice
            } else benchmarkPrice
            val bestPrice = listOf(floorAppliedBenchmarkPrice, inputPrice).min()
                ?: error("Error in getting minvalue for $floorAppliedBenchmarkPrice and $inputPrice")
            Ladder(
                minQuantity = minQuantity,
                maxQuantity = maxQuantity,
                ladderValue = bestPrice
            )
        }
        // merge same value slabs and return
        return mergeLaddersWithSimilarValue(LadderValue(exhaustiveLadders))
    }

    fun directCompMatchWithGuardrail(
        compLadderPriceInput: List<Ladder>,
        floorGuardRailPrice: BigDecimal,
        outputMetadata: MutableMap<String, String>
    ): LadderValue {
        val updatedLadders = compLadderPriceInput.mapIndexed { index, compLadder ->

            // this allows to match with comp only till floor guardrail.
            val bestPrice = listOf(compLadder.ladderValue, floorGuardRailPrice).max()
            if (bestPrice == floorGuardRailPrice) {
                outputMetadata["COMP_PRICE_GUARD_RAILED_TO_FLOOR_FOR_LADDER_${index + 1}"] = "true"
            }
            Ladder(
                minQuantity = compLadder.minQuantity,
                maxQuantity = compLadder.maxQuantity,
                ladderValue = bestPrice
            )
        }
        // merge same value slabs and return
        return mergeLaddersWithSimilarValue(LadderValue(updatedLadders))
    }

    /**
     * Applies discount ladders on absolute price.
     */
    fun applyDiscountLadders(
        absolutePrice: BigDecimal,
        discountLaddersInBps: List<Ladder>,
        outputMetadata: MutableMap<String, String>
    ): LadderValue {
        /**
         * We are hence, applying it as a markdown on the base price with its absolute value as a markdown bps.
         * It means discount slabs itself doesn't dictate whether to be used as a markup or markdown.
         * So, 2 discount slabs with same absolute value and opposite of the integer scale will
         * output same final values.
         */
        val ladders = discountLaddersInBps.map { ladder ->
            val ladderPriceInPaise = absolutePrice
                .multiplyWithScale(
                    (BigDecimal(1) - (ladder.ladderValue.abs()).divideWithScale(
                        BigDecimal(Constants.BPS_NORMALISER)
                    ))
                )
            Ladder(
                minQuantity = ladder.minQuantity,
                maxQuantity = ladder.maxQuantity,
                ladderValue = ladderPriceInPaise
            )
        }
        outputMetadata["POST_ADJUSTMENT_LADDER_VALUES"] = LadderValue(ladders).toString()
        return LadderValue(ladders)
    }

    /**
     * Create default ladders on absolute price.
     */
    fun applyDefaultLadder(
        absolutePrice: BigDecimal,
        outputMetadata: MutableMap<String, String>
    ): LadderValue {
        outputMetadata["DEFAULT_LADDER_PASSED"] = "true"
        return LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = absolutePrice
                )
            )
        )
    }

    /**
     * This applies max ladder count threshold check limiting ladders to max count.
     *
     * Reason:
     * This is required to limit no of slabs shown to user to avoid clutter and bad UX.
     *
     * Logic:
     * Currently, if threshold breached, it picks min value from remaining slabs and creates a new last slab.
     */
    private fun applyLadderCountThreshold(
        exhaustiveMergedLadders: List<Ladder>,
        outputMetadata: MutableMap<String, String>
    ): LadderValue {
        val laddersCappedBySize = if (exhaustiveMergedLadders.size > MAX_LADDER_COUNT_THRESHOLD) {
            // threshold breached, cap ladders
            outputMetadata["LADDERS_SIZE_CAPPED"] = "true"
            // get min value from remaining slabs of ladder
            val minLadderValue =
                exhaustiveMergedLadders.subList(MAX_LADDER_COUNT_THRESHOLD - 1, exhaustiveMergedLadders.size)
                    .map { it.ladderValue }.min()
                    ?: error("Error in getting minvalue for $exhaustiveMergedLadders")
            // slabs till max slab count remain as is
            val firstSet = exhaustiveMergedLadders.subList(0, MAX_LADDER_COUNT_THRESHOLD - 1)
            // create new last slab with min value covering qtys in all breached slabs
            val lstLadder = listOf(
                Ladder(
                    minQuantity = exhaustiveMergedLadders[MAX_LADDER_COUNT_THRESHOLD - 1].minQuantity,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = minLadderValue
                )
            )
            // merge non-breached and new slab to create final ladder value
            firstSet + lstLadder
        } else {
            // return as is if threshold not breached
            exhaustiveMergedLadders
        }
        return LadderValue(laddersCappedBySize)
    }

    /**
     *  Fetches slab value and index in the provided ladder which includes given min and max quantity.
     *  If no slab found, it throws error.
     *
     *  NOTE:
     *  - The method assumes the min and max qty will be part of same slab of ladder.
     *  - This is usually called when iterating on ordered minQtys to make sure above assumption holds.
     */
    private fun getLadderPriceAndIndex(
        ladderPriceInput: List<Ladder>,
        minQuantity: Long,
        maxQuantity: Long
    ): Pair<BigDecimal, Int> {
        ladderPriceInput.mapIndexed { index, it ->
            if (minQuantity >= it.minQuantity && maxQuantity <= it.maxQuantity) {
                return Pair(it.ladderValue, index)
            }
        }
        error("No ladder price found for $minQuantity, $maxQuantity within $ladderPriceInput")
    }

    /**
     * Converts a comp ladder to listing level ladder.
     * Translation happens in steps:
     *  1. convert slab values to listing pack size level
     *  2. convert slab qtys to unitQty level of comp
     *  3. convert slab qtys in unitQty level of comp to listing unit level
     *
     * ex:
     * - comp ladder: 1-1, a; 2-2, b; 3-max, c
     * - comp qtyPerUnit: 22Kgs
     * - listing qtyPerUnit: 10kgs
     * - step1: 1-1, (10a/22); 2-2, (10b/22); 3-max, (10c/22)
     * - step2: 1-22, (10a/22); 23-44, (10b/22); 45-max, (10c/22)
     * - step3: 1-2, (10a/22); 3-4, (10b/22); 5-max, (10c/22)   (final form)
     *
     * @see convertCompLadderValuesToListingPackSize
     * @see convertLadderQtysToUnitPackSize
     * @see getAssortmentAdjustedLadder
     */
    fun translateCompLaddersToListingPackSize(
        compLadders: List<Ladder>,
        compQtyPerUnit: BigDecimal,
        listingQtyPerUnit: BigDecimal
    ): List<Ladder> {
        // convert slab values to fit listing pack size
        val ladderValueConvertedToListingPackSize = convertCompLadderValuesToListingPackSize(
            compLadders = compLadders,
            compQtyPerUnit = compQtyPerUnit,
            listingQtyPerUnit = listingQtyPerUnit
        )
        // convert slab qtys to first comp pack size level
        val qtyConvertedLadderBasisUnitPackSize = convertLadderQtysToUnitPackSize(
            ladders = ladderValueConvertedToListingPackSize,
            qtyPerUnit = compQtyPerUnit
        )
        // convert slab qtys to unit as per listing pack size level
        return getAssortmentAdjustedLadder(
            conversionRate = listingQtyPerUnit.toLong(),
            inputLadder = qtyConvertedLadderBasisUnitPackSize
        )
    }

    /**
     * Converts ladder slab min and max qtys from unit level to qtyPerUnit level.
     *
     * For each slab in ladder, the qtys are multiplied with qtyPerUnit value to
     * derive qty level value of it.
     * Ex:
     *      For a ladder: 1-1, a; 2-2, b; 3-max, c
     *      qtyPerUnit: 22Kgs
     *      qty level ladder: 1-22, a; 23-44, b; 45-max, c
     *
     * The method assumes the ladder fed has first slab minQty 1 and last slab maxQty MaxValue
     *
     * @param ladders List of Ladder to be translated
     * @param qtyPerUnit quantity per unit
     * @return List of translated Ladder with adjusted qtys
     */
    private fun convertLadderQtysToUnitPackSize(
        ladders: List<Ladder>,
        qtyPerUnit: BigDecimal
    ): List<Ladder> {
        // start with emptyList and create translated list progressively
        return ladders.foldIndexed(emptyList()) { index, final, ladder ->
            // minQty to be 1 for first slab, then basis maxQty of last slab
            val minQuantity = if (index == 0) 1 else final.last().maxQuantity + 1
            // maxQty to be max value for last slab, else original value multiplied by qtyPerUnit
            val maxQuantity = if (ladder.maxQuantity == Long.MAX_VALUE) {
                Long.MAX_VALUE
            } else {
                ladder.maxQuantity * qtyPerUnit.toLong()
            }
            final + ladder.copy(
                minQuantity = minQuantity,
                maxQuantity = maxQuantity
            )
        }
    }

    /**
     * Translates ladder values from competitor's pack size level to listing pack size.
     *
     * For each ladder in the list, translates the ladder value using the formula:
     * (ladder.ladderValue / compQtyPerUnit) * listingQtyPerUnit
     *
     * @param compLadders List of Ladder to be translated
     * @param compQtyPerUnit Competitor's quantity per unit
     * @param listingQtyPerUnit Listing's quantity per unit
     * @return List of translated Ladder with adjusted values
     */
    private fun convertCompLadderValuesToListingPackSize(
        compLadders: List<Ladder>,
        compQtyPerUnit: BigDecimal,
        listingQtyPerUnit: BigDecimal
    ): List<Ladder> {
        return compLadders.map { ladder ->
            ladder.copy(
                ladderValue = ladder.ladderValue
                    .divideWithScale(compQtyPerUnit)
                    .multiplyWithScale(listingQtyPerUnit)
            )
        }
    }
}
