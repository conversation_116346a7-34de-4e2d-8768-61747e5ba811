package com.udaan.pricing.core.helpers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.pricing.core.svcinterfaces.UserSvcInterface
import com.udaan.proto.models.CommonV1
import com.udaan.proto.models.ModelV1.OrgUnit
import com.udaan.proto.models.ModelV1.OrgAccount
import com.udaan.user.client.headOfficeUnit

@Singleton
class UserHelper @Inject constructor(
    private val userSvcInterface: UserSvcInterface
) {
    suspend fun getBuyerOrgById(buyerOrgId: String): OrgAccount? {
        if (buyerOrgId.isBlank()) return null
        return userSvcInterface.getBuyerOrgById(buyerOrgId)
    }

    fun getBuyerOrgUnitPincode(orgUnit: OrgUnit): String? {
        return orgUnit.let { it.unitAddress?.pincode }
    }

    fun getBuyerOrgUnitGeoLocation(orgUnit: OrgUnit): CommonV1.GeoLoc? {
        return orgUnit.let { it.unitAddress?.geoLoc }
    }

    fun getBuyerOrgUnit(buyerOrgDetails: OrgAccount, orgUnitId: String): OrgUnit? {
        if (orgUnitId.isBlank()) return null
        return buyerOrgDetails.orgUnitsMap[orgUnitId]
    }

    fun getHeadOfficeBuyerOrgUnit(buyerOrgDetails: OrgAccount): OrgUnit? {
        return buyerOrgDetails.headOfficeUnit
    }
 }
