package com.udaan.pricing.core.helpers.rider

import com.google.inject.Inject
import com.udaan.pricing.BuyerContext
import com.udaan.pricing.PriceRiderForListing
import com.udaan.pricing.core.helpers.rider.impl.*
import com.udaan.pricing.core.models.CatalogEntityContext

enum class PriceRider {
    LISTING_DISCOUNT_RIDER,
    GEO_PRICE_RIDER,
    SUPERCLUB_DISCOUNT_RIDER,
    CREDIT_PRICE_RIDER,
    BOT_PRICE_RIDER,
}

abstract class Rider {
    abstract val isAdditive: Boolean

    // TODO: Get rid of multiple versions
    abstract suspend fun getPriceRider(
        catalogEntityContext: CatalogEntityContext,
        requestContext: PricingRequestContext,
        fetchRiderDetails: Boolean,
        cluster: List<String>? = null,
        preferredWarehouseId: String?,
        servingWarehouseId: String?
    ): PriceRiderForListing?

    open suspend fun getPriceRiderWithAdditionalDetails(
        catalogEntityContext: CatalogEntityContext,
        requestContext: PricingRequestContext,
        fetchRiderDetails: Boolean,
        cluster: List<String>? = null,
        fetchRiderAdditionalDetails: Boolean = false,
        preferredWarehouseId: String?,
        servingWarehouseId: String?
    ): PriceRiderForListing? {
        return getPriceRider(
            catalogEntityContext = catalogEntityContext,
            requestContext = requestContext,
            fetchRiderDetails = fetchRiderDetails,
            cluster = cluster,
            preferredWarehouseId = preferredWarehouseId,
            servingWarehouseId = servingWarehouseId
        )
    }
}

class RiderFactory @Inject constructor(
    private val listingDiscountRider: ListingDiscountRider,
    private val geoPriceRider: GeoPriceRider,
    private val superClubDiscountRider: SuperClubDiscountRider,
    private val creditPriceRider: CreditPriceRider,
    private val botPriceRider: BotPriceRider
) {
    fun getRiderImpl(priceRider: PriceRider): Rider {
        return when(priceRider) {
            PriceRider.LISTING_DISCOUNT_RIDER -> listingDiscountRider
            PriceRider.GEO_PRICE_RIDER -> geoPriceRider
            PriceRider.SUPERCLUB_DISCOUNT_RIDER -> superClubDiscountRider
            PriceRider.CREDIT_PRICE_RIDER -> creditPriceRider
            PriceRider.BOT_PRICE_RIDER -> botPriceRider
        }
    }
}

data class ComputedRiderValue(
    val bpsToAdd: Int = 0,
    val flatValToAdd: Int = 0,
    val bpsToSubtract: Int = 0,
    val flatValToSubtract: Int = 0
) {
    fun netDifferentialsForPrice(pricePaise: Long): Long {
        return additiveDifferentialsForPrice(pricePaise) + subtractiveDifferentialsForPrice(pricePaise)
    }

    fun additiveDifferentialsForPrice(pricePaise: Long): Long {
        val addValue = (bpsToAdd.times(pricePaise)).div(10000)
        return addValue + flatValToAdd
    }

    fun subtractiveDifferentialsForPrice(pricePaise: Long): Long {
        val subtractiveValue = (bpsToSubtract.times(pricePaise)).div(10000)
        return subtractiveValue + flatValToSubtract
    }
}

fun List<PriceRiderForListing>.calculate(): ComputedRiderValue {
    var bpsValSubtract = 0
    var flatValSubtract = 0
    //@TODO:HACK for just trails of GEO
    var bpsValAdd = 0
    var flatValAdd = 0

    forEach { y ->
        if (y.isAdditive) {
            bpsValAdd += y.bpsInPercentage
            flatValAdd += y.flatVal
        } else {
            bpsValSubtract -= y.bpsInPercentage
            flatValSubtract -= y.flatVal
        }
    }

    return ComputedRiderValue(
        bpsToAdd = bpsValAdd,
        flatValToAdd = flatValAdd,
        bpsToSubtract = bpsValSubtract,
        flatValToSubtract = flatValSubtract
    )
}


data class RootRequestContext(
    val rootPrincipal: String? = null,
    val rootClientIp: String? = null,
    val rootUserAgent: String? = null
) {
    companion object {
        internal val empty = RootRequestContext()
    }

    fun getRootUserId(): String? = rootPrincipal?.let {
        if (it.startsWith("USR")) {
            it
        } else {
            null
        }
    }

    fun objOrNull(): RootRequestContext? {
        return if (listOfNotNull(rootPrincipal, rootClientIp, rootUserAgent).isEmpty()) {
            null
        } else {
            this
        }
    }
}

data class PricingRequestContext(
    val buyerContext: BuyerContext?,
    val rootRequestContext: RootRequestContext?,
    val refId:String?
)
