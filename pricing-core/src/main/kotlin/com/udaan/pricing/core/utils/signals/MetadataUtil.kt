package com.udaan.pricing.core.utils.signals

import com.udaan.catalog.model.VerticalCategory
import com.udaan.firstpartycatalog.models.Product
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.GenericValue
import com.udaan.pricing.commons.QuantityType
import com.udaan.pricing.commons.StringValue
import com.udaan.pricing.core.models.signals.CatalogEntityContext
import java.math.BigDecimal
import com.udaan.pricing.core.utils.signals.BigDecimalUtil.toBigDecimalWithScale
import com.udaan.pricing.variable.VariableId

object MetadataUtil {
    // Constants
    private const val PACKAGING_TYPE_KEY = "packagingType"
    private const val CONVERSION_RATE_KEY = "conversionRate"
    private const val PRICING_ATTRIBUTES_GST_KEY = "gst"
    private const val PRICING_ATTRIBUTES_CESS_KEY = "cess"
    private const val MEAT_PACK_SIZE_KEY = "mea_pack_size"
    private const val FRESH_QUANTITY_KEY = "fav_quantity"
    private const val KG_TO_GRAM_MULTIPLIER = 1000

    /**
     * Retrieves the MRP value in paise for a given sales unit.
     * 
     * @param catalogEntityContext The catalog context containing listing details
     * @return BigDecimal value representing MRP in paise
     * @throws IllegalStateException if listing details or sales unit price details are missing
     */
    private fun getMrpValue(catalogEntityContext: CatalogEntityContext): BigDecimal {
        val listingDetail = catalogEntityContext.listingDetail
            ?: throw IllegalStateException("Unable to get listing detail from context")

        // Find matching sales unit and extract MRP in paise
        val mrpPaise = listingDetail.salesUnitList?.firstOrNull {
            it.salesUnitId == catalogEntityContext.salesUnitId
        }?.priceDetails?.mrpPaise ?: throw IllegalStateException("MRP not found for salesUnit")

        return mrpPaise.toBigDecimalWithScale()
    }

    /**
     * Retrieves the GST value in BPS (basis points).
     * Attempts to fetch from listing details first, falls back to FP product details.
     * 
     * @param catalogEntityContext The catalog context containing tax details
     * @return BigDecimal value representing GST in BPS, or null if not found
     * @throws IllegalStateException if neither listing nor FP product details are available
     */
    private fun getGstValue(catalogEntityContext: CatalogEntityContext): BigDecimal? {
        // Try getting GST from listing first, then fallback to FP product
        return when {
            catalogEntityContext.listingDetail != null -> {
                val gstBps = catalogEntityContext.listingDetail.taxDetails?.gstBps
                gstBps?.toLong()?.toBigDecimalWithScale()
            }
            catalogEntityContext.fpProductDetails != null -> {
                val gstBps = catalogEntityContext.fpProductDetails.getGstInBps()
                gstBps.toLong().toBigDecimalWithScale()
            }
            else -> throw IllegalStateException("Unable to fetch gst details, missing listing and fp product details.")
        }
    }

    /**
     * Retrieves the CESS tax value in BPS (basis points).
     * Attempts to fetch from listing details first, falls back to FP product details.
     * 
     * @param catalogEntityContext The catalog context containing tax details
     * @return BigDecimal value representing CESS in BPS, or null if not found
     * @throws IllegalStateException if neither listing nor FP product details are available
     */
    private fun getCessValue(catalogEntityContext: CatalogEntityContext): BigDecimal? {
        // Try getting CESS from listing first, then fallback to FP product
        return when {
            catalogEntityContext.listingDetail != null -> {
                val cessBps = catalogEntityContext.listingDetail.taxDetails?.cessBps
                cessBps?.toLong()?.toBigDecimalWithScale()
            }
            catalogEntityContext.fpProductDetails != null -> {
                val cessBps = catalogEntityContext.fpProductDetails.getCessInBps()
                cessBps.toLong().toBigDecimalWithScale()
            }
            else -> throw IllegalStateException("Unable to fetch cess details, missing listing and fp product details.")
        }
    }

    /**
     * Retrieves the packaging unit type from product packaging attributes.
     * 
     * @param catalogEntityContext The catalog context containing FP product details
     * @return String representing the packaging unit type
     * @throws IllegalStateException if FP product details or packaging type is missing
     */
    private fun getPackagingUnitType(catalogEntityContext: CatalogEntityContext): String {
        val fpProductDetails = catalogEntityContext.fpProductDetails
            ?: throw IllegalStateException("Unable to get mapped FP product details")

        return fpProductDetails.packagingAttributes[PACKAGING_TYPE_KEY]
            ?: throw IllegalStateException("Unable to get Packaging Type from mapped product")
    }

    /**
     * Retrieves the conversion rate from product packaging attributes.
     * Ensures the rate is at least 1 to prevent invalid conversions.
     * 
     * @param catalogEntityContext The catalog context containing FP product details
     * @return BigDecimal value representing the conversion rate
     * @throws IllegalStateException if FP product details or conversion rate is missing
     */
    private fun getConversionRate(catalogEntityContext: CatalogEntityContext): BigDecimal {
        val fpProductDetails = catalogEntityContext.fpProductDetails
            ?: throw IllegalStateException("Unable to get mapped FP product details")

        val conversionRate = fpProductDetails.packagingAttributes[CONVERSION_RATE_KEY]
            ?: throw IllegalStateException("Unable to get Conversion rate from mapped product")

        // Ensure conversion rate is at least 1 to prevent invalid calculations
        return conversionRate.toLong().coerceAtLeast(1).toBigDecimalWithScale()
    }

    /**
     * Common utility function to extract quantity and type information from product attributes
     * based on vertical category (MEAT/FRESH).
     *
     * @param catalogEntityContext The catalog context containing product details
     * @return Pair of quantity value and type as uppercase strings
     * @throws IllegalStateException if required product details or attributes are missing
     */
    private fun getQuantityDetails(catalogEntityContext: CatalogEntityContext): Pair<String, String> {
        // Get FP product details
        val fpProductDetails = catalogEntityContext.fpProductDetails 
            ?: throw IllegalStateException("Unable to get FP product details")
        
        // Extract raw quantity string based on vertical category
        val rawQuantityStr = when(catalogEntityContext.verticalCategory) {
            VerticalCategory.MEAT -> fpProductDetails.idAttributes[MEAT_PACK_SIZE_KEY]
            VerticalCategory.FRESH -> fpProductDetails.idAttributes[FRESH_QUANTITY_KEY]
            else -> null
        } ?: throw IllegalStateException(
            "Unable to get quantity details for category ${catalogEntityContext.verticalCategory}"
        )

        // Handle empty strings and invalid formats
        val trimmedStr = rawQuantityStr.trim()
        if (trimmedStr.isEmpty()) {
            throw IllegalStateException("Empty quantity string for category ${catalogEntityContext.verticalCategory}")
        }

        val parts = trimmedStr.split(" ", limit = 2)
        if (parts.size != 2) {
            throw IllegalStateException("Invalid quantity format - missing value or unit for category ${catalogEntityContext.verticalCategory}")
        }

        return Pair(parts[0].trim().uppercase(), parts[1].trim().uppercase())
    }

    /**
     * Retrieves the quantity value from product attributes and converts it to BigDecimal.
     * If the quantity type is KG, converts it to grams by multiplying with 1000.
     * 
     * @param catalogEntityContext The catalog context containing product details
     * @return BigDecimal value representing the quantity (in grams for weight, as is for pieces)
     * @throws IllegalStateException if required data is missing or invalid
     */
    private fun getQuantityPerUnit(catalogEntityContext: CatalogEntityContext): BigDecimal {
        val (quantity, type) = getQuantityDetails(catalogEntityContext)
        return try {
            val baseQuantity = quantity.toBigDecimal()
            // Convert KG to grams, leave other units as is
            when (type) {
                "KG", "KGS" -> baseQuantity.multiply(BigDecimal(KG_TO_GRAM_MULTIPLIER))
                else -> baseQuantity
            }
        } catch (e: NumberFormatException) {
            throw IllegalStateException("Invalid quantity value format", e)
        }
    }


    /**
     * Retrieves and maps the quantity type to CompQuantityType enum.
     * udaan supported units as confirmed by catalog:
     * kg, gm, pack, pc, pcs, bunch, Tray
     * Here fun considers some extended versions of the units as well.
     *
     * @param catalogEntityContext The catalog context containing product details
     * @return CompQuantityType enum value
     * @throws IllegalStateException if type mapping fails
     */
    private fun getQuantityType(catalogEntityContext: CatalogEntityContext): QuantityType {
        return when(getQuantityDetails(catalogEntityContext).second) {
            "PC", "PCS", "PACK", "PACKS", "BUNCH", "BUNCHES", "TRAY", "TRAYS"  -> QuantityType.PIECE
            "KG", "KGS", "GM", "GMS", "GRAM", "GRAMS" -> QuantityType.GRAM
            else -> throw IllegalStateException(
                "Unknown quantity type: ${getQuantityDetails(catalogEntityContext).second}"
            )
        }
    }

    /**
     * Main router function that maps variable IDs to their corresponding metadata values.
     * Acts as a facade for various metadata extraction functions.
     * 
     * @param catalogEntityContext The catalog context containing all necessary product details
     * @param variableId The ID of the metadata variable to retrieve
     * @return GenericValue containing the requested metadata, or null for optional fields
     * @throws IllegalArgumentException if the variable ID is not supported
     */
    fun getMetadataValue(
        catalogEntityContext: CatalogEntityContext,
        variableId: VariableId
    ): GenericValue? {
        // Map each variable ID to its corresponding extraction function
        return when(variableId) {
            VariableId.MRP_WT_PAISA_SET -> BigDecimalValue(getMrpValue(catalogEntityContext))
            VariableId.GST_BPS -> getGstValue(catalogEntityContext)?.let { BigDecimalValue(it) }
            VariableId.CESS_BPS -> getCessValue(catalogEntityContext)?.let { BigDecimalValue(it) }
            VariableId.PACKAGING_UNIT_TYPE -> StringValue(getPackagingUnitType(catalogEntityContext))
            VariableId.CONVERSION_RATE -> BigDecimalValue(getConversionRate(catalogEntityContext))
            VariableId.QUANTITY_PER_UNIT -> BigDecimalValue(getQuantityPerUnit(catalogEntityContext))
            VariableId.QUANTITY_TYPE -> StringValue(getQuantityType(catalogEntityContext).name)
            else -> throw IllegalArgumentException("Unsupported metadata variable ID: $variableId")
        }
    }

    /**
     * Extension function to get GST value in BPS (basis points) from product pricing attributes.
     * Converts the stored percentage value to BPS by multiplying with 100.
     * 
     * @return Double value representing GST in BPS
     */
    private fun Product.getGstInBps(): Double {
        return (this.pricingAttributes[PRICING_ATTRIBUTES_GST_KEY]?.toDouble() ?: 0.0) * 100
    }

    /**
     * Extension function to get CESS value in BPS (basis points) from product pricing attributes.
     * Converts the stored percentage value to BPS by multiplying with 100.
     * 
     * @return Double value representing CESS in BPS
     */
    private fun Product.getCessInBps(): Double {
        return (this.pricingAttributes[PRICING_ATTRIBUTES_CESS_KEY]?.toDouble() ?: 0.0) * 100
    }
}
