package com.udaan.pricing.core.strategyevaluator.impl.dynamic

import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator

internal object DynamicEvaluator : Evaluator {

    override fun evaluate(
        data: EvaluatorRequestContext
    ): EvaluatorOutput? {
        return DynamicEvaluatorFactory.getDynamicEvaluator(data).evaluate(data)
    }
}
