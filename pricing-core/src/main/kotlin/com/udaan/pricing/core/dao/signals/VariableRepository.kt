package com.udaan.pricing.core.dao.signals

import com.azure.cosmos.implementation.BadRequestException
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.core.constants.CosmosDbConfig
import com.udaan.pricing.variable.Variable
import com.udaan.pricing.variable.VariableState
import kotlinx.coroutines.flow.toList

@Singleton
class VariableRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {

    companion object {
        private val variableCosmosDao by lazy {
            CosmosDbDao(
                configKey = CosmosDbConfig.DB_ACCOUNT_CONFIG,
                databaseName = CosmosDbConfig.PRICING_AUTOMATION_DB,
                containerName = CosmosDbConfig.VARIABLE_COLLECTION
            ) { builder ->
                builder.connectionSharingAcrossClientsEnabled(true)
            }
        }
    }

    /**
     * This method is used to reduce the connection time to cosmos, used at the time of warmup
     */
    suspend fun initialise() {
        try {
            getVariableById("")
        } catch (e: BadRequestException) {
            // Ignore the exception as it is expected when the variable with empty id is not found
        }
    }

    suspend fun createVariable(variable: Variable): Variable {
        return variableCosmosDao.createOrUpdateItem(variable.toDocument()).toVariable()
    }

    suspend fun getVariableById(id: String): Variable? {
        return variableCosmosDao.getItem(id, id)?.toVariable()
    }

    suspend fun getAllVariables(): List<Variable> {
        return variableCosmosDao.queryItems(
            queryName = "get-variables",
            querySpec = makeSqlQuerySpec(
                """
                select * from c where c.state = @state
            """.trimIndent(),
            "@state" to VariableState.ACTIVE)
        ).toList().map { it.toVariable() }
    }

    private fun Variable.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toVariable() = objectMapper.convertValue(this, Variable::class.java)
}
