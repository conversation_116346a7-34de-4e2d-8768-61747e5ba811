package com.udaan.pricing.core

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.inject.AbstractModule
import com.google.inject.Provides
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.microsoft.azure.eventhubs.EventHubClient
import com.microsoft.azure.storage.CloudStorageAccount
import com.microsoft.azure.storage.queue.CloudQueueClient
import com.uber.h3core.H3Core
import com.udaan.catalog.client.CatalogServiceClient
import com.udaan.catalog.client.CatalogServiceClientV2
import com.udaan.catalog.client.CategoryTreeV2
import com.udaan.catalog.client.RedisListingRepository
import com.udaan.common.client.UdaanClientConfig
import com.udaan.common.loc.PincodeCityDataset
import com.udaan.common.loc.PostalDB
import com.udaan.common.server.UdaanServerConfig
import com.udaan.config.client.BusinessConfigClient
import com.udaan.config.client.ConfigClientFactory
import com.udaan.firstparty.client.FirstPartyClient
import com.udaan.fulfilmentcatalog.client.ProductMasterServiceClient
import com.udaan.listingtag.client.RedisListingTagsRepository
import com.udaan.orchestrator.client.ServingTenantsClient
import com.udaan.pricing.core.helpers.rider.impl.bot.BotDetectionDbBuilderBase
import com.udaan.pricing.core.helpers.rider.impl.bot.BotDetectionDbBuilderFromDP
import com.udaan.promotions.client.PromotionsServiceClient
import com.udaan.catalog.lot.client.CatalogLotClient
import com.udaan.common.auth.roles.ConsoleUsersRolesManagerConfigV2
import com.udaan.common.auth.roles.UserRoleAttributesManagerV2
import com.udaan.config.Configuration
import com.udaan.dataplatform.client.DataPlatformDownloadClient
import com.udaan.dataplatform.client.DataPlatformUtils
import com.udaan.firstparty.client.SellerClient
import com.udaan.firstparty.trading.TradingPriceClient
import com.udaan.firstpartycatalog.client.FirstPartyCatalogClient
import com.udaan.firstpartycatalog.client.FirstPartyCatalogRedisClient
import com.udaan.firstpartycatalog.client.FirstPartyCatalogRedisRepository
import com.udaan.fulfilmentcatalog.client.RedisProductMasterRepository
import com.udaan.instrumentation.PrometheusClient
import com.udaan.orchestrator.client.servingTenants.SearchServingWarehousesCacheClient
import com.udaan.orchestrator.client.servingTenants.ServingTenantsCacheClient
import com.udaan.planning.DemandClusterClient
import com.udaan.planning.DemandClusterHubMappingClient
import com.udaan.planning.SelectionClient
import com.udaan.pricing.core.constants.EventHubConfig
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.promotions.client.PromotionsServiceClientV2
import com.udaan.resources.CloudBlobClientProxy
import com.udaan.resources.ResourceBuilder
import com.udaan.scnetwork.client.facilityEdge.FacilityEdgeServiceClient
import com.udaan.user.client.OrgInternalIdentityCacheClient
import com.udaan.user.client.OrgServiceClient
import com.udaan.user.client.RedisOrgRepository
import com.udaan.user.client.UserServiceClient
import com.udaan.vertical.client.VerticalServiceClient
import com.udaan.vertical.client.cache.CentralVerticalCache
import com.udaan.warehouse.client.WarehouseClientV2
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.core.kotlin.KotlinPlugin
import org.jdbi.v3.jackson2.Jackson2Plugin
import org.jdbi.v3.postgres.PostgresPlugin
import org.jdbi.v3.sqlobject.SqlObjectPlugin
import org.jdbi.v3.sqlobject.kotlin.KotlinSqlObjectPlugin
import java.util.concurrent.Executors

class PricingCoreModule: AbstractModule() {
    override fun configure() {
        install(PricingCacheModule())
    }

    @Provides
    @Singleton
    fun getObjectMapper(): ObjectMapper = jacksonObjectMapper()
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        .configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true)
        .registerModule(JavaTimeModule())

    @Provides
    @Singleton
    fun getCatalogServiceClientV2(): CatalogServiceClientV2 {
        val clientConfig = getClientConfig("catalog-readpath-service")
        return CatalogServiceClientV2(clientConfig)
    }

    @Provides
    @Singleton
    fun getVerticalServiceClient(): VerticalServiceClient {
        val clientConfig = getClientConfig("vertical")
        return VerticalServiceClient(clientConfig)
    }

    @Provides
    @Singleton
    fun getCatalogLotClient(): CatalogLotClient {
        val clientConfig = getClientConfig("fulfilment-catalog-legacy")
        return CatalogLotClient(clientConfig)
    }

    @Provides
    @Singleton
    fun getFirstPartyCatalogRedisRepository(): FirstPartyCatalogRedisRepository {
        val clientConfig = getClientConfig("first-party-catalog-service")
        return FirstPartyCatalogRedisRepository(clientConfig)
    }

    @Provides
    @Singleton
    fun getTradingPriceClient(): TradingPriceClient {
        val clientConfig = getClientConfig("trading-service")
        return TradingPriceClient(clientConfig)
    }

    @Provides
    @Singleton
    fun getRedisListingTaggingAsyncRepo(): RedisListingTagsRepository {
        val clientConfig = getClientConfig("listing-tags")
        return RedisListingTagsRepository(clientConfig)
    }


    @Provides
    @Singleton
    fun getPromotionsServiceClient(): PromotionsServiceClient {
        val clientConfig = getClientConfig("promotions-service")
        return PromotionsServiceClient(clientConfig)
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Clients.DISCOVERY_PROMOTIONS_CLIENT)
    fun getPromotionsDiscoveryServiceClient(): PromotionsServiceClient {
        val clientConfig = getClientConfig("promotions-discovery-service")
        return PromotionsServiceClient(clientConfig)
    }

    @Provides
    @Singleton
    fun getPromotionsServiceClientV2(): PromotionsServiceClientV2 {
        val clientConfig = getClientConfig("promotions-service")
        return PromotionsServiceClientV2(clientConfig)
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Clients.DISCOVERY_PROMOTIONS_CLIENT_V2)
    fun getPromotionsDiscoveryServiceClientV2(): PromotionsServiceClientV2 {
        val clientConfig = getClientConfig("promotions-discovery-service")
        return PromotionsServiceClientV2(clientConfig)
    }

    @Provides
    @Singleton
    fun getOrchestratorServingTenantsClient(): ServingTenantsClient {
        val clientConfig = getClientConfig("orchestrator-service")
        return ServingTenantsClient(clientConfig)
    }

    @Provides
    @Singleton
    fun getCentralVerticalCache(): CentralVerticalCache {
        val clientConfig = getClientConfig("vertical")
        return CentralVerticalCache(VerticalServiceClient(clientConfig))
    }

    @Provides
    @Singleton
    fun getCategoryTreeV2(): CategoryTreeV2 {
        val clientConfig = getClientConfig("catalog-readpath-service")
        return CategoryTreeV2(CatalogServiceClient(clientConfig))
    }

    @Provides
    @Singleton
    fun getProductMasterServiceClient(): ProductMasterServiceClient {
        val clientConfig = getClientConfig("fulfilment-catalog-service")
        return ProductMasterServiceClient(clientConfig)
    }

    @Provides
    @Singleton
    fun getProductMasterRedisRepo(): RedisProductMasterRepository {
        val clientConfig = getClientConfig("fulfilment-catalog-service")
        return RedisProductMasterRepository(clientConfig)
    }

    @Provides
    @Singleton
    fun getRedisOrgRepository(): RedisOrgRepository {
        val clientConfig = getClientConfig("user-service")
        return RedisOrgRepository(clientConfig, localOnly = false)
    }

    @Provides
    @Singleton
    fun getUserServiceClient(): UserServiceClient {
        val clientConfig = getClientConfig("user-service")
        return UserServiceClient(clientConfig)
    }

    @Provides
    @Singleton
    fun getOrgServiceClient(): OrgServiceClient {
        val clientConfig = getClientConfig("user-service")
        return OrgServiceClient(clientConfig)
    }


    @Provides
    @Singleton
    @Named(NamedConstants.Config.PRICING_SERVICE_CONFIG)
    fun getPricingConfigClient(): BusinessConfigClient {
        val clientConfig = getClientConfig("config-service")
        return ConfigClientFactory.getBusinessConfigClientV2(
            config = clientConfig,
            moduleName = "pricing-service"
        )
    }

    @Provides
    @Singleton
    @Named(NamedConstants.Events.PRICE_CHANGE_EVENTS)
    fun getPriceChangeEventhubClient(): EventHubClient {
        return ResourceBuilder.eventHubClient(
            "pricing/change",
            Executors.newScheduledThreadPool(1)
        )
    }


    @Provides
    @Singleton
    @Named(NamedConstants.Events.SIGNALS_V2_EVENT_HUB_CLIENT)
    fun getPricingCriticalSignalsEventhubClient(): EventHubClient {
        return ResourceBuilder.eventHubClient(
            EventHubConfig.EVENT_HUB_RESOURCE_ID,
            Executors.newScheduledThreadPool(32)
        )
    }

    @Provides
    @Singleton
    fun getPincodeDataset(): PincodeCityDataset {
        return PincodeCityDataset(PostalDB())
    }

    @Provides
    @Singleton
    fun getFirstPartyClient(): FirstPartyClient {
        val clientConfig = getClientConfig("first-party-service")
        return FirstPartyClient(clientConfig)
    }

    @Provides
    @Singleton
    fun getSellerClient(): SellerClient {
        val clientConfig = getClientConfig("first-party-service")
        return SellerClient(clientConfig)
    }

    @Provides
    @Singleton
    private fun getListingRedisRepositoryClient(): RedisListingRepository {
        val clientConfig = getClientConfig("catalog-readpath-service")
        return RedisListingRepository(clientConfig)
    }

    @Provides
    @Singleton
    fun getOrchestratorServingWarehouseCacheClient(): SearchServingWarehousesCacheClient {
        val clientConfig = getClientConfig("orchestrator-service")
        return SearchServingWarehousesCacheClient(
            config = clientConfig
        )
    }

    @Provides
    @Singleton
    fun getDemandClusterClient(): DemandClusterClient {
        val clientConfig = getClientConfig("planning-service")
        return DemandClusterClient(clientConfig)
    }

    @Provides
    @Singleton
    fun getDemandClusterHubMappingClient(): DemandClusterHubMappingClient {
        val clientConfig = getClientConfig("planning-service")
        return DemandClusterHubMappingClient(clientConfig)
    }

    @Provides
    @Singleton
    private fun provideFacilityEdgeServiceClient(): FacilityEdgeServiceClient {
        val clientConfig = getClientConfig("sc-network-service")
        return FacilityEdgeServiceClient(clientConfig)
    }

    @Provides
    @Singleton
    private fun provideWarehouseClientV2(): WarehouseClientV2 {
        val clientConfig = getClientConfig("warehouse-service")
        return WarehouseClientV2(clientConfig)
    }

    @Provides
    @Singleton
    fun getOrchestratorServingTenantsCacheClient(): ServingTenantsCacheClient {
        val clientConfig = getClientConfig("orchestrator-service")
        return ServingTenantsCacheClient(
            config = clientConfig
        )
    }

    @Provides
    @Singleton
    fun getOrgInternalIdentityCacheClient(): OrgInternalIdentityCacheClient {
        val orgServiceClient = OrgServiceClient(getClientConfig("user-service"))
        return OrgInternalIdentityCacheClient(
            orgServiceClient = orgServiceClient,
            cacheMetrics = PrometheusClient.cacheMetricsCollector
        )
    }

    @Provides
    @Singleton
    fun bindAbstractBotDetectionDBBuilder(): BotDetectionDbBuilderBase {
        return BotDetectionDbBuilderFromDP()
    }

    @Provides
    @Singleton
    fun getFirstPartyCatalogClient(): FirstPartyCatalogClient {
        val clientConfig = getClientConfig("first-party-catalog-service")
        return FirstPartyCatalogClient(clientConfig)
    }

    @Provides
    @Singleton
    fun getFirstPartyCatalogRedisClient(): FirstPartyCatalogRedisClient {
        val clientConfig = getClientConfig("first-party-catalog-service")
        return FirstPartyCatalogRedisClient(clientConfig)
    }

    @Provides
    @Singleton
    fun getAsyncJobQueueClient(): CloudQueueClient {
        val connectionString = Configuration.get("resources/first-party-job/event/storage")
        val storageAccount: CloudStorageAccount = CloudStorageAccount.parse(connectionString)
        return storageAccount.createCloudQueueClient()
    }

    @Provides
    @Singleton
    fun getCloudBlobClient(): CloudBlobClientProxy {
        return ResourceBuilder.cloudBlobClient("first-party")
    }

    @Provides
    @Singleton
    fun getDataPlatformDownloadClient(): DataPlatformDownloadClient {
        val clientConfig = getClientConfig("dataplatform-service")
        return DataPlatformDownloadClient(clientConfig)
    }

    @Provides
    @Singleton
    fun getDataPlatformUtils(): DataPlatformUtils {
        val clientConfig = getClientConfig("dataplatform-service")
        return DataPlatformUtils(clientConfig)
    }

    @Singleton
    @Provides
    private fun buildPGSQLJDBIClientProd(): Jdbi {
        val dbi = ResourceBuilder.psqlClient("buyer-pricing").build()
        dbi.installPlugin(KotlinPlugin())
            .installPlugin(KotlinSqlObjectPlugin())
            .installPlugin(Jackson2Plugin())
            .installPlugin(PostgresPlugin())
            .installPlugin(SqlObjectPlugin())
        return dbi
    }

    @Provides
    @Singleton
    private fun provideH3Core() = H3Core.newInstance()

    @Provides
    @Singleton
    fun userRoleAttributesManager(): UserRoleAttributesManagerV2 {
        val config = ConsoleUsersRolesManagerConfigV2(
            appName = "PRICING",
            refreshTimeInMinutes = 30,
            isUserAttributesEnabled = true
        )
        return UserRoleAttributesManagerV2(config)
    }

    @Provides
    @Singleton
    fun getSelectionClient(): SelectionClient {
        val clientConfig = getClientConfig("planning-service")
        return SelectionClient(clientConfig)
    }

    private fun getClientConfig(service: String): UdaanClientConfig {
        return UdaanServerConfig[service]!!
    }

}
