package com.udaan.pricing.core.managers.signals

import javax.ws.rs.NotFoundException
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.core.dao.signals.SignalAnomaliesRepository
import com.udaan.pricing.core.dao.signals.SignalRepository
import com.udaan.pricing.core.helpers.ConfigHelper
import com.udaan.pricing.core.svcinterfaces.CatalogSvcInterface
import com.udaan.pricing.core.svcinterfaces.FpCatalogSvcInterface
import com.udaan.pricing.signalanomaly.Guardrail
import com.udaan.pricing.signalanomaly.SignalAnomalyAuditState
import com.udaan.pricing.signalanomaly.SignalAnomalyEntry
import com.udaan.pricing.signalanomaly.SignalAnomalyReviewAction
import com.udaan.pricing.signalanomaly.SignalAnomalyType
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState
import javax.ws.rs.BadRequestException

@Singleton
class SignalAnomaliesManager @Inject constructor(
    private val signalAnomaliesRepository: SignalAnomaliesRepository,
    private val fpCatalogServiceInterface: FpCatalogSvcInterface,
    private val catalogServiceInterface: CatalogSvcInterface,
    private val signalRepository: SignalRepository,
    private val configHelper: ConfigHelper,
    private val validationHelper: ValidationHelper
) {

    companion object {
        private val logger by logger()
    }

    suspend fun validateSignalAgainstGuardrails(newSignal: Signal, existingSignal: Signal?): ValidationFlag {
        if (existingSignal == null) return ValidationFlag.Green
        val guardrail = configHelper.getGuardrailForVariable(newSignal.variableId) ?: return ValidationFlag.Green
        val validationFlag = validationHelper.getValidationFlag(newSignal, existingSignal, guardrail)
        when(validationFlag) {
            ValidationFlag.Green -> handleGreenValidation(newSignal)
            ValidationFlag.Yellow -> handleYellowValidation(
                newSignal = newSignal,
                existingSignal = existingSignal,
                guardrail = guardrail
            )
            ValidationFlag.Red -> handleRedValidation(
                newSignal = newSignal,
                existingSignal = existingSignal,
                guardrail = guardrail
            )
        }
        return validationFlag
    }

    private suspend fun handleGreenValidation(newSignal: Signal) {
        val existingSignalAnomalyEntry = signalAnomaliesRepository.getSignalByIdAndPartitionKey(newSignal.id, newSignal.partitionKey)
        if (existingSignalAnomalyEntry != null) {
            signalAnomaliesRepository.archiveEntry(existingSignalAnomalyEntry)
        }
    }

    private suspend fun handleYellowValidation(
        newSignal: Signal,
        existingSignal: Signal,
        guardrail: Guardrail
    ) {
        val timeStamp = System.currentTimeMillis()
        signalAnomaliesRepository.createOrUpdateSignal(
            SignalAnomalyEntry(
                newSignal = newSignal,
                oldSignal = existingSignal,
                guardrail = guardrail,
                state = SignalAnomalyAuditState.TO_REVIEW,
                catalogEntityTitle = getCatalogEntityName(
                    newSignal.catalogEntityType,
                    newSignal.catalogEntity
                ).orEmpty(),
                signalAnomalyType = SignalAnomalyType.YELLOW,
                createdAt = timeStamp,
                updatedAt = timeStamp
            )
        )
    }

    private suspend fun handleRedValidation(
        newSignal: Signal,
        existingSignal: Signal,
        guardrail: Guardrail
    ) {
        val timeStamp = System.currentTimeMillis()
        signalAnomaliesRepository.createOrUpdateSignal(
            SignalAnomalyEntry(
                newSignal = newSignal,
                oldSignal = existingSignal,
                guardrail = guardrail,
                state = SignalAnomalyAuditState.TO_REVIEW,
                catalogEntityTitle = getCatalogEntityName(newSignal.catalogEntityType, newSignal.catalogEntity).orEmpty(),
                signalAnomalyType = SignalAnomalyType.RED,
                createdAt = timeStamp,
                updatedAt = timeStamp
            )
        )

        throw SignalValidationException("Signal breaching red guardrails ${newSignal.referenceId}")
    }

    suspend fun reviewAnomalousSignal(
        referenceId: String,
        reviewAction: SignalAnomalyReviewAction,
        updatedBy: String
    ) {
        val signalAnomalyEntry = signalAnomaliesRepository.getSignalByReferenceId(referenceId)
            ?: throw NotFoundException("Signal with referenceId $referenceId not found")
        if (signalAnomalyEntry.state != SignalAnomalyAuditState.TO_REVIEW) {
            throw BadRequestException("Signal anomaly $referenceId is not in review state")
        }

        when (signalAnomalyEntry.signalAnomalyType) {
            SignalAnomalyType.YELLOW -> handleYellowSignal(signalAnomalyEntry, reviewAction, updatedBy)
            SignalAnomalyType.RED -> handleRedSignal(signalAnomalyEntry, reviewAction, updatedBy)
        }
    }

    private suspend fun handleRedSignal(
        signalAnomalyEntry: SignalAnomalyEntry,
        newState: SignalAnomalyReviewAction,
        updatedBy: String
    ) : SignalAnomalyEntry? {
        return when (newState) {
            SignalAnomalyReviewAction.REJECT -> signalAnomaliesRepository.archiveEntry(signalAnomalyEntry)
            SignalAnomalyReviewAction.APPROVE -> {
                signalRepository.createOrUpdateSignal(
                    signalAnomalyEntry.newSignal.copy(
                        state = SignalState.ACTIVE,
                        createdBy = updatedBy,
                        updatedBy = updatedBy,
                        createdAt = System.currentTimeMillis(),
                        updatedAt = System.currentTimeMillis()
                    )
                )
                signalAnomaliesRepository.createOrUpdateSignal(
                    signalAnomalyEntry.copy(
                        state = SignalAnomalyAuditState.REVIEWED,
                        updatedAt = System.currentTimeMillis()
                    )
                )
            }
            SignalAnomalyReviewAction.REVIEWED ->
                throw IllegalStateException("Red signal anomaly ${signalAnomalyEntry.newSignal.referenceId} can't be marked reviewed")
        }
    }

    private suspend fun handleYellowSignal(
        signalAnomalyEntry: SignalAnomalyEntry,
        reviewAction: SignalAnomalyReviewAction,
        updatedBy: String
    ): SignalAnomalyEntry? {
        return when (reviewAction) {
            SignalAnomalyReviewAction.REVIEWED -> {
                logger.info("$updatedBy has reviewed the signal anomaly ${signalAnomalyEntry.newSignal.referenceId}")
                signalAnomaliesRepository.archiveEntry(signalAnomalyEntry)
            }
            SignalAnomalyReviewAction.APPROVE, SignalAnomalyReviewAction.REJECT -> {
                throw IllegalStateException("Yellow signal anomaly ${signalAnomalyEntry.newSignal.referenceId} can't be marked approve/rejected")
            }
        }
    }

    private suspend fun getCatalogEntityName(catalogEntityType: CatalogEntityType, catalogEntityId: String): String? {
        return when (catalogEntityType) {
            CatalogEntityType.PRODUCT_GROUP_ID -> fpCatalogServiceInterface.getProductGroup(catalogEntityId)?.title
            CatalogEntityType.LISTING_ID -> catalogServiceInterface.getTradeListing(catalogEntityId).title
            CatalogEntityType.VERTICAL -> catalogEntityId
            else -> null
        }
    }

    suspend fun getSignalsPendingForReview(): List<SignalAnomalyEntry> {
        return signalAnomaliesRepository.getSignalsPendingForReview()
    }

}
