package com.udaan.pricing.core.models

import com.udaan.pricing.BasicPrice
import com.udaan.pricing.PUInfo
import com.udaan.pricing.PriceForListing
import com.udaan.pricing.PriceInPaisa
import com.udaan.pricing.QtyBasedPrice
import com.udaan.pricing.core.helpers.pricingstrategy.PricingStrategy
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.roundToLong
import java.math.BigDecimal

data class SlabPrice(
    val minQty: Int,
    val maxQty: Int,
    val priceInPaise: BigDecimal
)

fun List<SlabPrice>.transformToPriceForListingDTO(
    listingId: String,
    salesUnitId: String,
    puInfo: PUInfo?,
    pricingStrategy: PricingStrategy
): PriceForListing {
    return PriceForListing(
        listingId = listingId,
        saleUnitId = salesUnitId,
        prices = this.map {
            QtyBasedPrice(
                minQty = it.minQty,
                maxQty = it.maxQty,
                priceInPaisa = PriceInPaisa(
                    onCredit = it.priceInPaise.roundToLong(),
                    onCOD = it.priceInPaise.roundToLong(),
                    onPrepayment = it.priceInPaise.roundToLong(),
                    basicPrice = BasicPrice(
                        onCreditBasePrice = it.priceInPaise.roundToLong(),
                        onCODBasePrice = it.priceInPaise.roundToLong(),
                        onPrepaymentBasePrice = it.priceInPaise.roundToLong()
                    ),
                    priceRiders = null
                ),
                pricePerKgInPaisa = null,
                packagingUnit = puInfo,
                taxableAmountPaise = it.priceInPaise.roundToLong(),
                bulkLadder = null
            )
        },
        strategyRef = pricingStrategy.name
    )
}
