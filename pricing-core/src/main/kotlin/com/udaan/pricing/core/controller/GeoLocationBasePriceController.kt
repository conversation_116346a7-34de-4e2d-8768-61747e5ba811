package com.udaan.pricing.core.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.google.inject.name.Named
import com.microsoft.azure.eventhubs.EventData
import com.microsoft.azure.eventhubs.EventHubClient
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.instrumentation.TelemetryScope.Companion.async
import com.udaan.planning.DemandClusterHubMappingClient
import com.udaan.pricing.BasicPrice
import com.udaan.pricing.DCsHavingActivePriceResponse
import com.udaan.pricing.EventChangeType
import com.udaan.pricing.GeoLocationBasePrice
import com.udaan.pricing.GeoLocationBasePriceResponse
import com.udaan.pricing.GeoLocationBasePriceStateChangeRequest
import com.udaan.pricing.GeoLocationType
import com.udaan.pricing.MetaData
import com.udaan.pricing.PriceChangeEvent
import com.udaan.pricing.PriceDeleteResponseDto
import com.udaan.pricing.PriceForListing
import com.udaan.pricing.PriceInPaisa
import com.udaan.pricing.PriceState
import com.udaan.pricing.QtyBasedPrice
import com.udaan.pricing.SalesUnitWithDemandClusters
import com.udaan.pricing.UpdateEmptyActiveGeoBasePriceRequest
import com.udaan.pricing.UpsertGeoLocationBasePriceRequest
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.controller.cache.GeoBasePriceCacheController
import com.udaan.pricing.core.dao.GeoLocationBasePriceRepository
import com.udaan.pricing.core.dao.toUpsertResponse
import com.udaan.pricing.core.helpers.pricingstrategy.PricingStrategy
import com.udaan.pricing.core.svcinterfaces.CatalogSvcInterface
import com.udaan.pricing.core.utils.CriticalSectionHandler
import com.udaan.pricing.core.utils.generateId
import com.udaan.proto.models.ModelV1
import com.udaan.scnetwork.client.facilityEdge.FacilityEdgeServiceClient
import com.udaan.scnetwork.models.category.NetworkCategory
import com.udaan.warehouse.client.WarehouseClientV2
import kotlinx.coroutines.future.await
import java.util.concurrent.*
import javax.ws.rs.BadRequestException

class GeoLocationBasePriceController @Inject constructor(
    private val geoLocationBasePriceRepository: GeoLocationBasePriceRepository,
    private val objectMapper: ObjectMapper,
    @Named(NamedConstants.Events.PRICE_CHANGE_EVENTS) private val priceChangeEventhubClient: EventHubClient,
    private val geoBasePriceCacheController: GeoBasePriceCacheController,
    private val criticalSectionHandler: CriticalSectionHandler,
    private val demandClusterHubMappingClient: DemandClusterHubMappingClient,
    private val facilityEdgeServiceClient: FacilityEdgeServiceClient,
    private val warehouseClientV2: WarehouseClientV2,
    private val catalogSvcInterface: CatalogSvcInterface
) {
    val log by logger()

    suspend fun getByPartition(id: String, listingId: String) =
        geoLocationBasePriceRepository.findByPartition(id, listingId)

    suspend fun changeGeoListingState(
        id: String,
        request: GeoLocationBasePriceStateChangeRequest
    ): GeoLocationBasePriceResponse {
        val geoLocationBasePriceList = getByPartition(id, request.listingId)
        if (geoLocationBasePriceList.isEmpty()) {
            throw BadRequestException()
        }
        return geoLocationBasePriceRepository.changeState(
            geoLocationBasePriceList.first(),
            request.isActive
        ).toUpsertResponse().also {
            geoBasePriceCacheController.invalidateCachedGeoBasePriceForListing(request.listingId)
        }
    }

    /**
     * Add a price entry to the system
     */
    suspend fun upsertEntry(request: UpsertGeoLocationBasePriceRequest): GeoLocationBasePriceResponse {
        val listingId = request.listingId
        val salesUnitId = request.saleUnitId
        val locType = request.locationType
        val locTypId = request.locationTypeId

        return criticalSectionHandler.executeSuspend(
            lockKey = "$listingId:$salesUnitId:$locType:${locTypId.lowercase()}",
            ttlInSec = 60
        ) {
            val existingPrice = geoLocationBasePriceRepository.getAllGeoBasePricesForListingSalesUnit(
                listingId = listingId,
                salesUnitId = salesUnitId
            ).firstOrNull {
                it.locationType == locType && it.locationTypeId.equals(locTypId, ignoreCase = true)
            }

            val qtyBasedPrice = request.qtyBasedPrice.map { r ->
                QtyBasedPrice(
                    minQty = r.minQty,
                    maxQty = r.maxQty,
                    priceInPaisa = PriceInPaisa(
                        onCOD = r.pricePaise,
                        onCredit = r.pricePaise,
                        onPrepayment = r.pricePaise,
                        priceRiders = emptyList(),
                        basicPrice = BasicPrice(
                            onCODBasePrice = r.pricePaise,
                            onCreditBasePrice = r.pricePaise,
                            onPrepaymentBasePrice = r.pricePaise
                        )
                    ),
                    pricePerKgInPaisa = r.pricePerKgInPaise?.let { pricePerKgInPaise ->
                        PriceInPaisa(
                            onCOD = pricePerKgInPaise,
                            onCredit = pricePerKgInPaise,
                            onPrepayment = pricePerKgInPaise,
                            priceRiders = emptyList(),
                            basicPrice = BasicPrice(
                                onCODBasePrice = pricePerKgInPaise,
                                onCreditBasePrice = pricePerKgInPaise,
                                onPrepaymentBasePrice = pricePerKgInPaise
                            )
                        )
                    },
                    taxableAmountPaise = 0L,
                    packagingUnit = r.packagingUnit
                )
            }

            createOrUpdateGeoBasePrice(
                listingId = listingId,
                salesUnitId = salesUnitId,
                locationType = locType,
                locationTypeId = locTypId,
                existingPrice = existingPrice,
                newPriceConditions = qtyBasedPrice,
                updateMetaData = request.metaData

            ).toUpsertResponse()
        }
    }

    private suspend fun createOrUpdateGeoBasePrice(
        listingId: String,
        salesUnitId: String,
        locationType: GeoLocationType,
        locationTypeId: String,
        existingPrice: GeoLocationBasePrice?,
        newPriceConditions: List<QtyBasedPrice>,
        updateMetaData: MetaData?
    ): GeoLocationBasePrice {
        val createOrUpdate = existingPrice?.let { EventChangeType.UPDATE } ?: EventChangeType.CREATE
        val updatedPrice = existingPrice?.copy(
            updatedAt = System.currentTimeMillis(),
            qtyBasedPrice = newPriceConditions,
            metaData = updateMetaData,
            currentActive = 1,
            state = PriceState.ACTIVE
        ) ?: GeoLocationBasePrice(
            id = generateId("GBP"),
            listingId = listingId,
            saleUnitId = salesUnitId,
            locationType = locationType,
            locationTypeId = locationTypeId,
            qtyBasedPrice = newPriceConditions,
            metaData = updateMetaData
        )
        val updatedGeoBasePrice = geoLocationBasePriceRepository.create(updatedPrice).also {
            geoBasePriceCacheController.invalidateCachedGeoBasePriceForListing(listingId)
            sendNotification(it, createOrUpdate)
        }
        return updatedGeoBasePrice
    }

    private suspend fun sendNotification(price: GeoLocationBasePrice, createOrUpdate: EventChangeType) {
        try {
            val makeEventData = price.makeEventData(createOrUpdate)
            priceChangeEventhubClient.send(EventData.create(makeEventData)).await()
        } catch (e: Exception) {
            log.error("Failed sending event of contract price update {}", e.message)
        }
    }

    private suspend fun GeoLocationBasePrice.makeEventData(createOrUpdate: EventChangeType): ByteArray? {
        return CompletableFuture.supplyAsync {
            objectMapper.writeValueAsBytes(
                PriceChangeEvent(
                    this.listingId,
                    this.saleUnitId,
                    createOrUpdate,
                    System.currentTimeMillis(),
                    this.locationType.name,
                    this.locationTypeId
                )
            )
        }.await()
    }

    suspend fun getAllActiveGeoBasePrices(listingId: String, salesUnitId: String?): List<GeoLocationBasePrice> {
        return if (salesUnitId != null) {
            getGeoBasePricesForListingSalesUnit(listingId, salesUnitId)
        } else {
            geoBasePriceCacheController.getCachedGeoBasePricesForListing(listingId)
        }
    }

    suspend fun getApplicableGeoBasePrices(
        listingId: String,
        salesUnitId: String?,
        locationData: List<Pair<GeoLocationType, String>>,
        buyerCohortLocation: List<String>?
    ): List<PriceForListing> {
        val locationFilteredGeoBasePrices =
            getLocationFilteredGeoBasePricesForListing(listingId, salesUnitId, locationData)

        return locationFilteredGeoBasePrices
            .groupBy { it.saleUnitId }
            .map { (_, geoBasePrice) ->
                val filteredLocation = if (buyerCohortLocation != null) {
                    geoBasePrice.filter { it.locationTypeId in buyerCohortLocation }.sortedWith(
                        comparator = compareBy(GeoLocationBasePrice::updatedAt)
                    ).firstOrNull()
                } else {
                    null
                }
                val priceConfig = filteredLocation
                    ?: geoBasePrice.sortedWith(
                        comparator = compareBy(GeoLocationBasePrice::locationType).thenComparing(
                            compareBy(
                                GeoLocationBasePrice::updatedAt
                            )
                        )
                    ).first()

                PriceForListing(
                    listingId = priceConfig.listingId,
                    saleUnitId = priceConfig.saleUnitId,
                    prices = priceConfig.qtyBasedPrice,
                    strategyRef = PricingStrategy.LOCATION.name,
                    metaData = priceConfig.metaData
                )
            }
    }

    private suspend fun getLocationFilteredGeoBasePricesForListing(
        listingId: String,
        salesUnitId: String?,
        locationData: List<Pair<GeoLocationType, String>>
    ): List<GeoLocationBasePrice> {
        val geoBasePrices = getAllActiveGeoBasePrices(listingId, salesUnitId)

        val locationDataStrings = locationData.map { (geoLocationType, geoLocationValue) ->
            "$geoLocationType::${geoLocationValue.lowercase()}"
        }

        val locationFilteredGeoBasePrices = geoBasePrices.filter {
            val geoBasePriceLocationString = "${it.locationType}::${it.locationTypeId.lowercase()}"
            geoBasePriceLocationString in locationDataStrings
        }

        return locationFilteredGeoBasePrices
    }

    suspend fun getGeoBasePricesForListingSalesUnit(
        listingId: String,
        salesUnitId: String
    ): List<GeoLocationBasePrice> {
        val geoBasePricesForListing = geoBasePriceCacheController.getCachedGeoBasePricesForListing(listingId)

        val geoBasePricesForListingSalesUnit = geoBasePricesForListing.filter {
            it.saleUnitId == salesUnitId
        }

        return geoBasePricesForListingSalesUnit
    }

    suspend fun deleteGeoLocationBasePriceForListingSalesUnitId(
        listingId: String,
        salesUnitId: String,
        locationTypeId: String,
        locationType: GeoLocationType
    ): PriceDeleteResponseDto {
        val deletedGeoBasePriceIds = deleteGeoBasePricesForListingSalesUnitId(
            listingId = listingId,
            salesUnitId = salesUnitId,
            locationTypeId = locationTypeId, locationType = locationType
        )

        return PriceDeleteResponseDto(
            listingId = listingId,
            salesUnitId = salesUnitId,
            deletedBasePriceId = null,
            deactivatedGeoBasePriceIds = deletedGeoBasePriceIds,
            deactivatedContractPriceIds = emptyList()
        )
    }

    private suspend fun deleteGeoBasePricesForListingSalesUnitId(
        listingId: String,
        salesUnitId: String,
        locationTypeId: String,
        locationType: GeoLocationType
    ): List<String> {
        val existingGeoBasePricesForListingSalesUnit = getAllActiveGeoBasePrices(listingId, salesUnitId).filter {
            it.locationTypeId == locationTypeId && it.locationType == locationType
        }

        if (existingGeoBasePricesForListingSalesUnit.isNotEmpty()) {
            existingGeoBasePricesForListingSalesUnit.forEach { geoLocationBasePrice ->
                log.info("Soft deleting existing geo base price {}", geoLocationBasePrice)
                geoLocationBasePriceRepository.markDeleted(geoLocationBasePrice).also {
                    geoBasePriceCacheController.invalidateCachedGeoBasePriceForListing(listingId)
                    sendNotification(geoLocationBasePrice, EventChangeType.UPDATE)
                }
            }
        } else {
            log.info("No existing geo base price found for {} and {}", listingId, salesUnitId)
        }

        return existingGeoBasePricesForListingSalesUnit.map { it.id }
    }

    suspend fun deleteGeoBasePricesForListingSalesUnitId(
        listingId: String,
        salesUnitId: String
    ): List<String> {
        val existingGeoBasePricesForListingSalesUnit = getAllActiveGeoBasePrices(listingId, salesUnitId)

        if (existingGeoBasePricesForListingSalesUnit.isNotEmpty()) {
            existingGeoBasePricesForListingSalesUnit.forEach { geoLocationBasePrice ->
                log.info("Soft deleting existing geo base price {}", geoLocationBasePrice)
                geoLocationBasePriceRepository.markDeleted(geoLocationBasePrice).also {
                    geoBasePriceCacheController.invalidateCachedGeoBasePriceForListing(listingId)
                    sendNotification(geoLocationBasePrice, EventChangeType.UPDATE)
                }
            }
        } else {
            log.info("No existing geo base price found for {} and {}", listingId, salesUnitId)
        }

        return existingGeoBasePricesForListingSalesUnit.map { it.id }
    }

    suspend fun updateEmptyActiveGeoBasePrice(updateEmptyActiveGeoBasePriceRequest: UpdateEmptyActiveGeoBasePriceRequest) {
        criticalSectionHandler.executeSuspend(
            lockKey = "${updateEmptyActiveGeoBasePriceRequest.listingId}:" +
                    "${updateEmptyActiveGeoBasePriceRequest.saleUnitId}:" +
                    "${updateEmptyActiveGeoBasePriceRequest.locationType}:" +
                    updateEmptyActiveGeoBasePriceRequest.locationTypeId.lowercase(),
            ttlInSec = 60
        ) {
            val existingActivePrice = getGeoBasePricesForListingSalesUnit(
                listingId = updateEmptyActiveGeoBasePriceRequest.listingId,
                salesUnitId = updateEmptyActiveGeoBasePriceRequest.saleUnitId
            ).firstOrNull {
                it.locationType == updateEmptyActiveGeoBasePriceRequest.locationType &&
                        it.locationTypeId.equals(updateEmptyActiveGeoBasePriceRequest.locationTypeId, ignoreCase = true)
            }

            createOrUpdateGeoBasePrice(
                listingId = updateEmptyActiveGeoBasePriceRequest.listingId,
                salesUnitId = updateEmptyActiveGeoBasePriceRequest.saleUnitId,
                locationType = updateEmptyActiveGeoBasePriceRequest.locationType,
                locationTypeId = updateEmptyActiveGeoBasePriceRequest.locationTypeId.lowercase(),
                existingPrice = existingActivePrice,
                newPriceConditions = emptyList(),
                updateMetaData = MetaData(
                    priceIntentId = updateEmptyActiveGeoBasePriceRequest.priceIntentId
                )
            )
        }
    }

    suspend fun getDCsHavingActivePriceForFoodListing(
        listingId: String
    ): DCsHavingActivePriceResponse {
        val operationalWarehouses = async {
            warehouseClientV2.getWarehouses(operational = true).executeAwait().map {
                it.id
            }
        }
        val listingDetails = catalogSvcInterface.getTradeListing(listingId)

        val salesUnitWithDemandClusters = listingDetails.salesUnitList.map { salesUnit ->
            val activeGeoBasePrices = getAllActiveGeoBasePrices(listingId, salesUnit.salesUnitId)

            val warehousesWithNonEmptyActivePrices = activeGeoBasePrices.filter { geoLocationBasePrice ->
                geoLocationBasePrice.locationType == GeoLocationType.WAREHOUSE &&
                        geoLocationBasePrice.qtyBasedPrice.isNotEmpty()
            }.map { it.locationTypeId.uppercase() }

            log.info("Available Prices for warehouses for {}, {}", listingId, warehousesWithNonEmptyActivePrices)

            val activeWarehousesWithNonEmptyPrices = warehousesWithNonEmptyActivePrices.filter {
                operationalWarehouses.await().contains(it)
            }

            log.info(
                "Filtered activeWarehousesWithNonEmptyPrices for {}, {}",
                listingId,
                activeWarehousesWithNonEmptyPrices
            )

            val mappedHubs = facilityEdgeServiceClient.bulkGetHubsMappedToWarehouse(
                warehouseOrgUnitIds = activeWarehousesWithNonEmptyPrices,
                category = NetworkCategory.FOOD,
                tenant = ModelV1.SellingPlatform.UDAAN_MARKETPLACE
            ).executeAwait().map { facilityToFacilityEdgesResponse ->
                facilityToFacilityEdgesResponse.mappedFacilities.map { it.orgUnitId }
            }.flatten().distinct()

            val demandClusters = mappedHubs.parallelMap { hubId ->
                try {
                    demandClusterHubMappingClient.fetchDemandClusterByHubCached(
                        hubId = hubId
                    ).await()?.response?.map { it.demandCluster }
                } catch (ex: Exception) {
                    log.info("Demand cluster not found for hub {}", hubId)
                    null
                }
            }.filterNotNull().flatten().distinct()

            log.info("Demand clusters are {} ", demandClusters)
            SalesUnitWithDemandClusters(
                salesUnitId = salesUnit.salesUnitId,
                demandClusters = demandClusters
            )
        }

        return DCsHavingActivePriceResponse(listingId, salesUnitWithDemandClusters)
    }
}
