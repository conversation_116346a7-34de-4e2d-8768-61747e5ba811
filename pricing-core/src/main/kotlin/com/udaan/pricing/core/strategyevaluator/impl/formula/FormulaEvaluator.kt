package com.udaan.pricing.core.strategyevaluator.impl.formula

import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorConfigInput
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.median
import com.udaan.pricing.strategy.AggregationType
import com.udaan.pricing.strategy.ConditionalFormulae
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.LadderValue
import com.udojava.evalex.Expression
import java.math.BigDecimal

internal object FormulaEvaluator : Evaluator {

    private fun validateInputs(inputs: List<EvaluatorConfigInput>) {
        require(inputs.filter { it.value != null }.all { it.value!!::class != LadderValue::class }) {
            "Formula based evaluator doesnt support ladder type inputs."
        }
    }

    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput? {
        validateInputs(data.inputs)
        val outputMetadata = mutableMapOf<String, String>()
        var validConditionalFormula: ConditionalFormulae? = null

        run conditions@{
            data.strategy.conditionalFormulae.forEach { conditionalFormula ->
                val expression = Expression(conditionalFormula.condition.expression)
                data.inputs.forEach { expression.setVariable(it.variableId.name, it.value.toString()) }
                val result = expression.eval()
                if (result.toInt() == 1) {
                    validConditionalFormula = conditionalFormula
                    outputMetadata["CONDITION_MET"] = validConditionalFormula?.name ?: ""
                    return@conditions
                }
            }
        }

        val aggregatedStrategyOutput = validConditionalFormula?.let { validCondition ->
            val formulaeOutputs = validCondition.formulae.map { formula ->
                val expression = Expression(formula.expression)
                data.inputs.forEach { expression.setVariable(it.variableId.name, it.value.toString()) }
                data.inputs.forEach {
                    outputMetadata[it.variableId.name] = it.value.toString()
                }
                expression.eval()
            }
            applyAggregationOverFormulaeOutputs(formulaeOutputs, validCondition.aggregationType)
        }
        return aggregatedStrategyOutput?.let {
            EvaluatorOutput(BigDecimalValue(aggregatedStrategyOutput), outputMetadata)
        }
    }

    private fun applyAggregationOverFormulaeOutputs(
        formulaeOutputs: List<BigDecimal>,
        aggregationType: AggregationType
    ): BigDecimal {
        return when (aggregationType) {
            AggregationType.NONE -> formulaeOutputs.first()
            AggregationType.MEDIAN -> formulaeOutputs.median()
            AggregationType.MAX -> formulaeOutputs.max() ?: error("Max operation on empty list")
            AggregationType.MIN -> formulaeOutputs.min() ?: error("Min operation on empty list")
            else -> error("Aggregation strategy not yet implemented")
        }
    }
}
