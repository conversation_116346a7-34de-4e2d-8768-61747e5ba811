package com.udaan.pricing.core.helpers.pricingstrategy.impl

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.catalog.model.VerticalCategory
import com.udaan.catalog.models.ModelV2
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.PriceBaseStrategy
import com.udaan.pricing.ContextualPriceRequest
import com.udaan.pricing.PUInfo
import com.udaan.pricing.PriceForListing
import com.udaan.pricing.QtyBasedPrice
import com.udaan.pricing.core.models.common.getMrpWithOutTax
import com.udaan.pricing.core.controller.contract.ContractController
import com.udaan.pricing.core.helpers.CatalogHelper
import com.udaan.pricing.core.helpers.pricingstrategy.PricingStrategy
import com.udaan.pricing.core.helpers.pricingstrategy.Strategy
import com.udaan.pricing.core.models.CatalogEntityContext
import com.udaan.pricing.core.models.contracts.Contract
import com.udaan.pricing.core.models.contracts.ContractLadderMrpMarkdownBps
import com.udaan.pricing.core.models.contracts.ContractLadderPrice
import com.udaan.pricing.core.models.contracts.toQtyBasedPrice
import com.udaan.pricing.core.strategyevaluator.Constants.BPS_NORMALISER
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import com.udaan.proto.models.ModelV1
import kotlinx.coroutines.Deferred
import java.math.BigDecimal

@Singleton
class ContractualPrice @Inject constructor(
    private val contractController: ContractController,
    private val catalogHelper: CatalogHelper
) : Strategy() {
    val log by logger()

    /**
     * Retrieves the price for a given listing based on the buyer contract and other parameters.
     *
     * @return A list of prices for the listing.
     */
    override suspend fun getPrice(
        catalogEntityContext: CatalogEntityContext,
        contextualPriceRequest: ContextualPriceRequest?,
        fetchInactive: Boolean,
        cluster: List<String>?,
        preferredWarehouseId: String?,
        servingWarehouseId: String?,
        buyerOrgUnit: ModelV1.OrgUnit?,
        mappedBenchmarkListingGuardrailPriceDeferred: Deferred<PriceForListing?>?
    ): List<PriceForListing> {
        val buyerOrgId = contextualPriceRequest?.buyerContext?.orgId
        if (buyerOrgId == null) {
            log.info("No buyerContext to serve contract price for {}", catalogEntityContext.listingId)
            return emptyList()
        }
        val contract = contractController.getContract(
            buyerOrgId = buyerOrgId,
            catalogEntityContext = catalogEntityContext
        )
        if (contract == null) {
            log.info(
                "There is no available contract to serve contract price for {} and {}",
                buyerOrgId,
                catalogEntityContext.listingId
            )
            return emptyList()
        }
        val salesUnits = catalogEntityContext.fetchSalesUnits()
        if (salesUnits.isEmpty()) {
            log.info(
                "There is no sales-unit available to serve contract price for {}, {}",
                buyerOrgId,
                catalogEntityContext.listingId
            )
            return emptyList()
        }
        val puInfo = if (catalogEntityContext.verticalCategory == VerticalCategory.FMCG) {
            catalogHelper.fetchPuInfoFromFpProduct(catalogEntityContext.fpProductDetails)
        } else {
            null
        }
        return salesUnits.map { salesUnitId ->
            val slabsPrices = getFinalPrice(
                contract = contract,
                listingDetails = catalogEntityContext.listingDetail,
                salesUnitId = salesUnitId,
                puInfo = puInfo
            )
            PriceForListing(
                listingId = catalogEntityContext.listingId,
                saleUnitId = salesUnitId,
                prices = slabsPrices,
                strategyRef = PricingStrategy.CONTRACT.name,
                contractReferenceId = contract.referenceId,
                applyPromotions = false,
                priceBaseStrategy = PriceBaseStrategy.CONTRACT
            )
        }
    }

    /**
     * Calculates the final price of listing based on the contract unit price or mrp markdown.
     */
    private fun getFinalPrice(
        contract: Contract,
        listingDetails: ModelV2.TradeListing,
        salesUnitId: String,
        puInfo: PUInfo?
    ): List<QtyBasedPrice> {
        return when (contract.price) {
            is ContractLadderPrice -> {
                val assortmentSize = listingDetails.salesUnitList?.firstOrNull {
                    it.salesUnitId == salesUnitId
                }?.assortmentDetails?.numItemsAssortment?.coerceAtLeast(1)
                    ?: error("No assortment details found for $salesUnitId")
                contract.price.value.map {
                    val price = it.ladderValue * assortmentSize
                    it.toQtyBasedPrice(price, puInfo)
                }
            }
            is ContractLadderMrpMarkdownBps -> {
                val listingTaxDetails = catalogHelper.getListingTaxDetails(
                    listingDetails = listingDetails,
                    salesUnitId = salesUnitId
                )
                val mrpWithOutTax = listingTaxDetails.getMrpWithOutTax()?.toBigDecimal()
                    ?: error("No MRP found for listingId ${listingDetails.listingId}")
                contract.price.value.map {
                    val price = mrpWithOutTax.multiplyWithScale(
                        BigDecimal.ONE.minus(it.ladderValue.toBigDecimal().divideWithScale(BigDecimal(BPS_NORMALISER)))
                    )
                    it.toQtyBasedPrice(price.toLong(), puInfo)
                }
            }
        }
    }
}
