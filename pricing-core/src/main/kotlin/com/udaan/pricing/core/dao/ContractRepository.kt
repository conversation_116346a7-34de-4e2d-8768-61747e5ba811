package com.udaan.pricing.core.dao

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.core.constants.CosmosDbConfig.CONTRACTS
import com.udaan.pricing.core.constants.CosmosDbConfig.COSMOS_DB_NAME
import com.udaan.pricing.core.models.contracts.Contract
import com.udaan.pricing.core.models.contracts.ContractState
import kotlinx.coroutines.flow.toList

@Singleton
class ContractRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {

    private val contractDbDao by lazy {
        CosmosDbDao(
            configKey = "pricing",
            databaseName = COSMOS_DB_NAME,
            containerName = CONTRACTS
        ) { builder ->
            builder.connectionSharingAcrossClientsEnabled(true)
        }
    }

    suspend fun intialise() {
        contractDbDao.findItem("ID1")
    }

    suspend fun createOrUpdateContract(contract: Contract): Contract {
        return contractDbDao.createOrUpdateItem(contract.toDocument()).toContract()
    }

    suspend fun getContract(id: String, buyerOrgId: String): Contract? {
        return contractDbDao.getItem(id = id, partitionKey = buyerOrgId)?.toContract()
    }

    suspend fun getContractsForBuyer(
        buyerOrgId: String
    ): List<Contract> {
        return contractDbDao.queryItems(
            queryName = "get-contracts-for-buyer",
            querySpec = makeSqlQuerySpec(
                """
                select * from c where c.buyerOrgId = @buyerOrgId and c.state = @state
            """.trimIndent(),
                "@buyerOrgId" to buyerOrgId,
                "@state" to ContractState.ACTIVE
            )
        ).toList().map { it.toContract() }
    }

    private fun Contract.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toContract() = objectMapper.convertValue(this, Contract::class.java)

}
