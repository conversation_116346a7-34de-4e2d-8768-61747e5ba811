package com.udaan.pricing.core.utils

import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode

internal object BigDecimalExtensionsUtil {
    /**
     * This extension fun:
     * - converts Long value to BigDecimal
     * - divides the value with denominator value passed as param
     * - rounds basis scale and RoundingMode given
     *
     * Default config for rounding is
     * - scale 0 (impacts left side of decimal)
     * - RoundingMode.HALF_EVEN (banker's rounding)
     *
     * This allows preserving precision (decimals) post division.
     * It returns BigDecimal value.
     */
    fun Long.toBigDecimalDivConst(
        constDenominator: Int,
        scale: Int = 0,
        roundingMode: RoundingMode = RoundingMode.HALF_EVEN
    ): BigDecimal = this.toBigDecimal().divide(BigDecimal(constDenominator), scale, roundingMode)

    /**
     * This extension fun:
     * - rounds off BigDecimal value
     * - scale and RoundingMode can be passed
     * - converts final value to Long
     *
     * Default config for rounding is
     * - scale 0 (impacts left side of decimal)
     * - RoundingMode.HALF_EVEN (banker's rounding)
     */
    fun BigDecimal.roundToLong(scale: Int = 0, roundingMode: RoundingMode = RoundingMode.HALF_EVEN): Long =
        this.setScale(scale, roundingMode).toLong()

    /**
     * This extension fun allows adding Long value to BigDecimal value and returns BigDecimal.
     *
     * NOTE: If ref BigDecimal value is null, it returns the param Long value converted to BigDecimal as result.
     */
    fun BigDecimal?.plus(value: Long) = this?.plus(value.toBigDecimal()) ?: value.toBigDecimal()

    /**
     * This extension fun allows adding Int value to BigDecimal value and returns BigDecimal.
     *
     * NOTE: If ref BigDecimal value is null, it returns the param Int value converted to BigDecimal as result.
     */
    fun BigDecimal?.plus(value: Int) = this?.plus(value.toBigDecimal()) ?: value.toBigDecimal()

    /**
     * This extension fun:
     * - multiply the value with multiplicand passed as param
     * - rounds basis scale and RoundingMode given
     *
     * Default config for rounding is
     * - scale 4 (impacts left side of decimal)
     * - RoundingMode.HALF_EVEN (banker's rounding)
     *
     * It returns BigDecimal value.
     */
    fun BigDecimal.multiplyWithScale(
        multiplicand: BigDecimal,
        scale: Int = 4,
        roundingMode: RoundingMode = RoundingMode.HALF_EVEN
    ): BigDecimal = this.multiply(multiplicand).setScale(scale, roundingMode)

    /**
     * This extension fun:
     * - multiply the value with divisor passed as param
     * - rounds basis scale and RoundingMode given
     *
     * Default config for rounding is
     * - scale 4 (impacts left side of decimal)
     * - RoundingMode.HALF_EVEN (banker's rounding)
     *
     * It returns BigDecimal value.
     */
    fun BigDecimal.divideWithScale(
        divisor: BigDecimal,
        scale: Int = 4,
        roundingMode: RoundingMode = RoundingMode.HALF_EVEN
    ): BigDecimal = this.divide(divisor, scale, roundingMode)

    fun List<BigDecimal>.median(): BigDecimal {
        val sortedArr = this.sorted()
        return sortedArr.let {
            if (it.size % 2 == 0)
                (it[it.size / 2] + it[(it.size - 1) / 2]) / BigDecimal(2)
            else
                it[it.size / 2]
        }
    }

    fun List<BigDecimal>.max(): BigDecimal? {
        val sortedArr = this.sorted()
        return sortedArr.lastOrNull()
    }

    fun List<BigDecimal>.min(): BigDecimal? {
        val sortedArr = this.sorted()
        return sortedArr.firstOrNull()
    }

    fun BigDecimal?.toCustomString(): String {
        return this?.stripTrailingZeros()?.toPlainString() ?: this.toString()
    }

}
