package com.udaan.pricing.core.helpers.signals.rawsignalinputconverters

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.pricing.signalcreation.AutomatedSourcingInput
import com.udaan.pricing.signalcreation.CompOfflineSignalInput
import com.udaan.pricing.signalcreation.CompSignalInput
import com.udaan.pricing.signalcreation.FpJitVendorPriceInput
import com.udaan.pricing.signalcreation.GenericGidLevelInput
import com.udaan.pricing.signalcreation.GenericLidLevelInput
import com.udaan.pricing.signalcreation.GenericVerticalLevelInput
import com.udaan.pricing.signalcreation.PurchaseOrderInput
import com.udaan.pricing.signalcreation.RawSignalInput
import com.udaan.pricing.signalcreation.SchemeSignalInput
import com.udaan.pricing.signalcreation.StaplesLipSignalInput
import com.udaan.pricing.signalcreation.VolumetricSignalInput
import com.udaan.pricing.signalcreation.VslPriceInput
import com.udaan.pricing.signals.Signal


@Singleton
class RawSignalInputConverterFactory @Inject constructor(
    private val compSignalInputConverter: CompSignalInputConverter,
    private val schemeSignalInputConverter: SchemeSignalInputConverter,
    private val purchaseOrderInputConverter: PurchaseOrderInputConverter,
    private val genericGidLevelInputConverter: GenericGidLevelInputConverter,
    private val genericLidLevelInputConverter: GenericLidLevelInputConverter,
    private val vslSignalInputConverter: VslSignalInputConverter,
    private val automatedSourcingInputConverter: AutomatedSourcingInputConverter,
    private val fpJitVendorPriceInputConverter: FpJitVendorPriceInputConverter,
    private val compOfflineInputConverter: CompOfflineInputConverter,
    private val staplesLipSignalInputConverter: StaplesLipSignalInputConverter,
    private val volumetricSignalInputConverter: VolumetricSignalInputConverter,
    private val genericVerticalLevelInputConverter: GenericVerticalLevelInputConverter
) {
    suspend fun getConverterImplAndConvert(rawSignalInput: RawSignalInput): List<Signal> {
        return when (rawSignalInput) {
            is CompSignalInput -> compSignalInputConverter.convert(rawSignalInput)
            is SchemeSignalInput -> schemeSignalInputConverter.convert(rawSignalInput)
            is PurchaseOrderInput -> purchaseOrderInputConverter.convert(rawSignalInput)
            is GenericGidLevelInput -> genericGidLevelInputConverter.convert(rawSignalInput)
            is GenericLidLevelInput -> genericLidLevelInputConverter.convert(rawSignalInput)
            is VslPriceInput -> vslSignalInputConverter.convert(rawSignalInput)
            is AutomatedSourcingInput -> automatedSourcingInputConverter.convert(rawSignalInput)
            is FpJitVendorPriceInput -> fpJitVendorPriceInputConverter.convert(rawSignalInput)
            is CompOfflineSignalInput -> compOfflineInputConverter.convert(rawSignalInput)
            is StaplesLipSignalInput -> staplesLipSignalInputConverter.convert(rawSignalInput)
            is GenericVerticalLevelInput -> genericVerticalLevelInputConverter.convert(rawSignalInput)
            is VolumetricSignalInput -> volumetricSignalInputConverter.convert(rawSignalInput)
        }
    }
}
