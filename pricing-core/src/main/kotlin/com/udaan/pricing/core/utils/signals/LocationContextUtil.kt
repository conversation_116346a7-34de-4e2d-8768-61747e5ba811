package com.udaan.pricing.core.utils.signals

import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.models.signals.LocationContext
import com.udaan.pricing.variable.VariableId

object LocationContextUtil {
    private val logger by logger()

    /**
     * This function is used to get the location value basis type enum for a given location context.
     *
     * For territory enabled inputs, it will return CLUSTER type value from the territory map which requires
     * isTerritoryEnabled flag to be true and variableId to be passed.
     * For non-territory enabled inputs, defaults apply for these.
     */
    fun LocationContext.getLocationValueBasisTypeEnum(
        locationType: LocationType, isTerritoryEnabled: Boolean = false, variableId: VariableId? = null
    ): String? {
        return when (locationType) {
            LocationType.CLUSTER -> getCluster(isTerritoryEnabled, variableId)
            LocationType.WAREHOUSE -> getWarehouse()
            LocationType.CITY -> getCity()
            LocationType.CENTRAL -> "CENTRAL"
        }
    }

    /**
     * This function is used to get the cluster value from the location context.
     *
     * If territory enabled input (isTerritoryEnabled == true), the value is fetched from territory map using variableId.
     *
     * @todo - if we start having actual cluster locations with separate input value while territory remains too, cluster level values may
     *      not be right. We either need to make sure we provide cluster level inputs always based on territory for these inputs, or we carve out
     *      a separate territory locationType. Not required right now, hence skipping.
     */
    private fun LocationContext.getCluster(isTerritoryEnabled: Boolean, variableId: VariableId?): String? {
        return when {
            isTerritoryEnabled -> variableId?.let { territoryMap?.get(variableId) }
            locationType == LocationType.CLUSTER -> locationValue.uppercase()
            else -> {
                logger.info("Cluster location can't be derived from location context")
                null
            }
        }
    }

    private fun LocationContext.getWarehouse(): String? {
        return if (locationType == LocationType.WAREHOUSE) {
            locationValue.uppercase()
        } else {
            logger.info("Warehouse location can't be derived from location context")
            null
        }
    }

    private fun LocationContext.getCity(): String {
        return city
    }


    fun LocationContext.getAllWarehouses(): List<String> {
        return (mfcWarehouses + anchorWarehouses).distinct()
    }
}
