package com.udaan.pricing.core.utils

import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorConfigInput
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.network.TerritoryType
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.GenericValue
import com.udaan.pricing.variable.VariableId
import java.math.BigDecimal

internal object VariableUtils {

    private val log by logger()

    fun getApplicableVariableValueForVariable(
        inputs: List<EvaluatorConfigInput>,
        variableId: VariableId
    ): GenericValue? {
        val input = inputs.firstOrNull {
            it.variableId == variableId
        }?.value
        return when (input) {
            is BigDecimalValue -> {
                /**
                 * This brings all big-decimal values with equal scale,
                 * this helps in generic comparison like "="
                 */
                if (input.value.compareTo(BigDecimal(Int.MAX_VALUE)) == 0) {
                    log.warn("Given {} has default value {}, considering it as null", variableId, input.value)
                    return null
                } else {
                    BigDecimalValue(input.value.divideWithScale(BigDecimal(1)))
                }
            }
            else -> {
                input
            }
        }
    }

    fun getApplyMaxLadderCountThresholdVariable(inputs: List<EvaluatorConfigInput>): Boolean {
        val applyMaxDiscountThreshold = getApplicableVariableValueForVariable(
            inputs,
            VariableId.APPLY_MAX_LADDER_COUNT_THRESHOLD
        ) as? BigDecimalValue
        if (applyMaxDiscountThreshold == null || applyMaxDiscountThreshold.value.compareTo(BigDecimal(1)) == 0) {
            return true
        }
        return false
    }

    /**
     * This function is used to get the territory types for the given variable ids.
     * This is primarily used to fetch territoryRefIds for territory enabled inputs.
     */
    fun territoryTypesForVariableIds(variableIds: List<VariableId>): List<TerritoryType> {
        return variableIds.mapNotNull { variableId ->
            when (variableId) {
                VariableId.JUMBOTAIL_COMP_LADDER_PRICE_WT_PAISA_UNIT -> TerritoryType.JUMBOTAIL
                else -> null
            }
        }.distinct()
    }
}
