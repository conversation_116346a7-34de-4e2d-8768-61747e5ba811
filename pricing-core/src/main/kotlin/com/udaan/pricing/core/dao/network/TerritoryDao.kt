package com.udaan.pricing.core.dao.network

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.pricing.core.utils.network.DefaultRowMapper
import com.udaan.pricing.network.Territory
import com.udaan.pricing.network.TerritoryType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.jdbi.v3.core.Jdbi
import java.sql.Timestamp

@Singleton
class TerritoryDao @Inject constructor(
    private val jdbi: Jdbi
) {
    companion object {
        private const val TABLE_NAME = "territories"
    }

    suspend fun createOrUpdate(territory: Territory): Territory {
        return withContext(Dispatchers.IO) {
            jdbi.useHandle<Exception> { handle ->
                handle.createUpdate(
                    """
                INSERT INTO $TABLE_NAME (
                    id, type, name, status, created_at, updated_at, created_by, updated_by
                ) VALUES (
                    :id, :territory_type, :territory_name, :status, :created_at, :updated_at, :created_by, :updated_by
                )
                ON CONFLICT (id) DO UPDATE SET
                    type = :territory_type,
                    name = :territory_name,
                    status = :status,
                    updated_at = :updated_at,
                    updated_by = :updated_by
            """.trimIndent()
                )
                    .bind("id", territory.id)
                    .bind("territory_type", territory.type.name)
                    .bind("territory_name", territory.name)
                    .bind("status", territory.status.name)
                    .bind("created_at", territory.createdAt)
                    .bind("updated_at", Timestamp(System.currentTimeMillis()))
                    .bind("created_by", territory.createdBy)
                    .bind("updated_by", territory.updatedBy)
                    .execute()
            }
            territory
        }
    }

    suspend fun getTerritoryById(territoryId: String): Territory? {
        return withContext(Dispatchers.IO) {
            jdbi.withHandle<Territory, Exception> { handle ->
                handle.createQuery("""
                    SELECT * FROM $TABLE_NAME 
                    WHERE id = :territory_id
                """.trimIndent()
                )
                    .bind("territory_id", territoryId)
                    .map(TerritoryMapper())
                    .findOnly()
            }
        }
    }

    suspend fun getTerritoryByName(territoryType: TerritoryType, territoryName: String): Territory? {
        return withContext(Dispatchers.IO) {
            jdbi.withHandle<Territory, Exception> { handle ->
                handle.createQuery("""
                    SELECT * FROM $TABLE_NAME 
                    WHERE type = :territory_type AND name = :territory_name
                """.trimIndent()
                )
                    .bind("territory_type", territoryType.name)
                    .bind("territory_name", territoryName)
                    .map(TerritoryMapper())
                    .list()
                    .firstOrNull()
            }
        }
    }

    class TerritoryMapper : DefaultRowMapper<Territory>(Territory::class.java)
}