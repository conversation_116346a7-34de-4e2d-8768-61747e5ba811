package com.udaan.pricing.core.helpers

import com.udaan.common.client.UdaanServiceException
import com.udaan.common.client.UdaanServiceRequest
import com.udaan.common.utils.kotlin.logger
import java.net.ConnectException
import java.util.concurrent.TimeoutException

internal object UdaanServiceRequestExtension {
    private val logger by logger()

    suspend fun <T> UdaanServiceRequest<T>.executeAwaitWithRetry(
        retries: Int = 0
    ): T {
        return try {
            executeAwait()
        } catch (ex: Exception) {
            if (retries > 0 && shouldRetry(ex)) {
                logger.error("Error while executing: {}, remaining retry count: {}", ex.message, retries)
                executeAwaitWithRetry(retries - 1)
            } else {
                throw ex
            }
        }
    }

    private fun shouldRetry(ex: Exception): Boolean {
        return when(ex) {
            // 503 -> Service Unavailable, code indicates that the server is not ready to handle the request
            // In our case, this is the status code when pod is killed while a request was being processed
            is UdaanServiceException -> ex.httpStatusCode == 503

            // TimeoutException retries are done for UdaanServiceRequest, but have to handle explicitly since we are not passing any retry count
            // ConnectTimeoutException -> Unable to connect to server or waiting for an available connection (includes timeout as well)
            is TimeoutException, is ConnectException -> true
            else -> false
        }
    }
}
