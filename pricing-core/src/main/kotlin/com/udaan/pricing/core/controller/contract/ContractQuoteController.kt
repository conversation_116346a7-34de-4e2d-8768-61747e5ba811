package com.udaan.pricing.core.controller.contract

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.catalog.model.VerticalCategory
import com.udaan.catalog.models.ModelV2.TradeListing
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope.Companion.async
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.contracts.BestCompetitorQuote
import com.udaan.pricing.contracts.ContractQuoteRequest
import com.udaan.pricing.contracts.ContractQuoteResponse
import com.udaan.pricing.core.models.common.ListingTaxDetails
import com.udaan.pricing.core.models.common.getMrpWithOutTax
import com.udaan.pricing.core.managers.StrategyManager
import com.udaan.pricing.core.helpers.CatalogHelper
import com.udaan.pricing.core.helpers.ConfigHelper
import com.udaan.pricing.core.helpers.PreferredWarehouseHelper
import com.udaan.pricing.core.helpers.PricingNetworkHelper
import com.udaan.pricing.core.helpers.StrategyExecutorHelper
import com.udaan.pricing.core.helpers.UserHelper
import com.udaan.pricing.core.managers.signals.VariableManager
import com.udaan.pricing.core.models.contracts.toContractQuoteInfo
import com.udaan.pricing.core.models.contracts.toContractRequest
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorConfigInput
import com.udaan.pricing.core.strategyevaluator.Constants.BPS_NORMALISER
import com.udaan.pricing.core.svcinterfaces.CatalogSvcInterface
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.portfolioplan.CohortInput
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.variable.VariableType
import java.math.BigDecimal
import java.time.Instant
import java.util.concurrent.TimeUnit
import kotlin.time.measureTimedValue


@Singleton
@Suppress("TooManyFunctions", "LongParameterList")
class ContractQuoteController @Inject constructor(
    private val variableManager: VariableManager,
    private val configHelper: ConfigHelper,
    private val catalogHelper: CatalogHelper,
    private val preferredWarehouseHelper: PreferredWarehouseHelper,
    private val pricingNetworkHelper: PricingNetworkHelper,
    private val strategyManager: StrategyManager,
    private val strategyExecutorHelper: StrategyExecutorHelper,
    private val catalogSvcInterface: CatalogSvcInterface,
    private val contractController: ContractController,
    private val cogsHelper: CogsHelper,
    private val userHelper: UserHelper
) {

    private val log by logger()

    suspend fun generateQuote(
        contractQuoteRequest: ContractQuoteRequest,
        strategies: List<String>,
        updateContractWithQuoteInfo: Boolean = true
    ): ContractQuoteResponse {
        val (output, duration) = measureTimedValue {
            pricingNetworkHelper.isSellerOrgCity(contractQuoteRequest.city)
            val verticalCategory = catalogHelper.fetchVerticalCategory(
                CatalogEntityType.valueOf(contractQuoteRequest.contractCatalogEntity),
                contractQuoteRequest.contractCatalogEntityId
            )
            val activeAnchorCityListing = async {
                fetchActiveListingAndSalesUnitIdWithCogs(
                    contractQuoteRequest,
                    verticalCategory = verticalCategory
                )
            }
            val priceValidity = async {
                getPriceValidity(contractQuoteRequest)
            }
            val quotePriceInfo = async {
                getSystemQuotePrice(
                    contractQuoteRequest, strategies, verticalCategory,
                    listingId = activeAnchorCityListing.await().first,
                    salesUnitId = activeAnchorCityListing.await().second
                )
            }
            ContractQuoteResponse(
                buyerOrgId = contractQuoteRequest.buyerOrgId,
                city = contractQuoteRequest.city,
                contractCatalogEntityId = contractQuoteRequest.contractCatalogEntityId,
                contractCatalogEntity = contractQuoteRequest.contractCatalogEntity,
                volumeCommitted = contractQuoteRequest.volumeCommitted,
                targetUnitPriceInPaisa = contractQuoteRequest.targetUnitPrice,
                quotePriceInPaisa = quotePriceInfo.await().quotePriceInPaisa,
                priceValidity = priceValidity.await(),
                customerSavingsInBps = calculateMarinInBps(
                    basePrice = contractQuoteRequest.targetUnitPrice
                        ?: quotePriceInfo.await().bestCompetitorQuote.priceInPaisa,
                    referencePrice = quotePriceInfo.await().quotePriceInPaisa
                ),
                cogsUnitPriceInPaisa = quotePriceInfo.await().cogsUnitPriceInPaisa,
                marginInBps = calculateMarinInBps(
                    referencePrice = quotePriceInfo.await().cogsUnitPriceInPaisa,
                    basePrice = quotePriceInfo.await().quotePriceInPaisa
                ),
                bestCompetitorQuote = quotePriceInfo.await().bestCompetitorQuote,
                quotePriceInPaisaWithTax = quotePriceInfo.await().quotePriceInPaisaWithTax,
                mrpMarkDownBps = quotePriceInfo.await().mrpMarkDownBps,
                referenceListingId = activeAnchorCityListing.await().first
            ).let {
                it.copy(
                    contractCreationRemarks = createContractFromQuoteResponse(contractQuoteRequest, it)
                )
            }.also {
                if (updateContractWithQuoteInfo) {
                    contractController.updateQuoteInfoForContract(
                        buyerOrgId = it.buyerOrgId,
                        catalogEntityId = it.contractCatalogEntityId,
                        quoteInfo = it.toContractQuoteInfo()
                    )
                }
            }
        }
        duration.inWholeMilliseconds.let {
            log.info("Generated quote for contract quote request: $contractQuoteRequest in $it ms")
        }
        return output
    }

    private suspend fun createContractFromQuoteResponse(
        contractQuoteRequest: ContractQuoteRequest,
        contractQuoteResponse: ContractQuoteResponse
    ): String? {
        return try {
            if (contractQuoteRequest.autoCreateContract) {
                log.info("triggered auto create contract for contract quote request: $contractQuoteRequest")
                contractController.createOrUpdateContract(
                    contractQuoteResponse.toContractRequest(
                        requestedBy = contractQuoteRequest.requestedBy,
                        lockInDays = contractQuoteRequest.lockInDays,
                        expiryInDays = contractQuoteRequest.expiryInDays
                    ),
                    skipPriceValidation = true
                )
            }
            null
        } catch (e: Exception) {
            log.error("Error while creating contract for contract quote request: $contractQuoteRequest", e)
            "Failed to create contract with error ${e.message}"
        }
    }

    private fun calculateMarinInBps(basePrice: Long?, referencePrice: Long?): Long? {
        if (basePrice == null || basePrice == 0L) {
            return null
        }
        return referencePrice?.let {
            ((basePrice - referencePrice) * BPS_NORMALISER) / basePrice
        }
    }

    private suspend fun fetchPreferredWarehouse(
        listingId: String,
        verticalCategory: VerticalCategory,
        buyerOrgId: String
    ): String? {
        val listingDetails = catalogSvcInterface.getTradeListingMinimal(listingId)
        val salesUnits = catalogHelper.fetchActiveSalesUnits(listingDetails).map { it.salesUnitId }
        return preferredWarehouseHelper.getPreferredWarehouseIdForBuyerOrg(
            listingId = listingDetails.listingId,
            salesUnitIds = salesUnits,
            buyerOrgId = buyerOrgId,
            verticalCategory = verticalCategory
        )
    }

    private suspend fun fetchApplicableLocation(
        city: String,
        buyerOrgId: String,
        listingId: String,
        verticalCategory: VerticalCategory?
    ): Location {
        return when (verticalCategory) {
            VerticalCategory.FMCG -> {
                Location(
                    locationType = LocationType.CITY,
                    locationValue = city
                )
            }
            VerticalCategory.STAPLES -> {
                val preferredWarehouseId = fetchPreferredWarehouse(
                    listingId = listingId,
                    verticalCategory = verticalCategory,
                    buyerOrgId = buyerOrgId
                ) ?: error("No preferred warehouse found for buyerOrgId: $buyerOrgId")
                Location(
                    locationType = LocationType.WAREHOUSE,
                    locationValue = preferredWarehouseId
                )
            }
            else -> {
                Location(
                    locationType = LocationType.CITY,
                    locationValue = city
                )
            }
        }
    }

    private suspend fun fetchActiveListingAndSalesUnitIdWithCogs(
        contractQuoteRequest: ContractQuoteRequest,
        verticalCategory: VerticalCategory?
    ): Pair<String, String> {
        val activeListings = catalogHelper.fetchActiveListingsByCity(
            CatalogEntityType.valueOf(contractQuoteRequest.contractCatalogEntity),
            contractQuoteRequest.contractCatalogEntityId,
            contractQuoteRequest.city
        )
        val overidedActiveListings = if (contractQuoteRequest.referenceListingId != null) {
            val referenceListingDetails =
                catalogHelper.getListingDetailsOrNull(contractQuoteRequest.referenceListingId!!)
             listOfNotNull(referenceListingDetails)
        } else {
            activeListings
        }
        val listingToSalesUnitMap = overidedActiveListings.mapNotNull { listingDetails ->
            getSalesUnitOrNull(listingDetails)?.let { salesUnitId ->
                listingDetails to salesUnitId
            }
        }.toMap()
        val cogsDetails = cogsHelper.fetchCogsUnitPriceForCategory(
            buyerOrgId = contractQuoteRequest.buyerOrgId,
            catalogEntityId = contractQuoteRequest.contractCatalogEntityId,
            catalogEntity = CatalogEntityType.valueOf(contractQuoteRequest.contractCatalogEntity),
            verticalCategory = verticalCategory,
            activeCityListings = overidedActiveListings
        )
        val activeListingWithCogs = if (cogsDetails != null) {
            cogsDetails.listingDetails.listingId to cogsDetails.salesUnitId
        } else {
            listingToSalesUnitMap.entries.firstOrNull()?.let {
                it.key.listingId to it.value
            } ?: error(
                "No active listing found for groupId: ${contractQuoteRequest.contractCatalogEntityId} " +
                        "and city ${contractQuoteRequest.city}"
            )
        }
        log.info(
            "Picked active listing: ${activeListingWithCogs.first} with " +
                    "salesUnitId: ${activeListingWithCogs.second} with cogs: ${cogsDetails?.cogsUnitPriceWithOutTax}"
        )
        return activeListingWithCogs
    }

    private suspend fun getDefaultInputForCohortVariables(strategies: Collection<String>): List<CohortInput> {
        val usedStrategies = strategies.map { strategyId ->
            strategyManager.getStrategyById(strategyId)
        }
        val cohortVariables = usedStrategies.map { strategy ->
            strategy.usedVariables
        }.flatten().filter {
            variableManager.getVariableOrNull(it.name)?.type == VariableType.COHORT
        }
        return cohortVariables.map {
            CohortInput(
                variableId = it,
                value = BigDecimalValue(BigDecimal.ZERO)
            )
        }
    }


    private suspend fun getSystemQuotePrice(
        contractQuoteRequest: ContractQuoteRequest,
        strategies: List<String>,
        verticalCategory: VerticalCategory?,
        listingId: String,
        salesUnitId: String
    ): QuotePriceInfo {
        val buyerOrgDetails = userHelper.getBuyerOrgById(contractQuoteRequest.buyerOrgId)
        val headOfficeBuyerOrgUnit = buyerOrgDetails?.let { userHelper.getHeadOfficeBuyerOrgUnit(buyerOrgDetails) }
        val strategyOutput = strategyExecutorHelper.getPrice(
            listingId = listingId,
            salesUnitId = salesUnitId,
            location = fetchApplicableLocation(
                city = contractQuoteRequest.city,
                buyerOrgId = contractQuoteRequest.buyerOrgId,
                listingId = listingId,
                verticalCategory = verticalCategory
            ),
            strategies = strategies,
            cohortInputs = getDefaultInputForCohortVariables(strategies),
            extraInputs = listOf(
                EvaluatorConfigInput(
                    VariableId.APPLY_MAX_LADDER_COUNT_THRESHOLD,
                    BigDecimalValue(BigDecimal(0))
                )
            ),
            buyerOrgUnit = headOfficeBuyerOrgUnit,
            verticalCategory = verticalCategory
        )
        log.info(
            "Generated quote price for contract " +
                    "quote request: $contractQuoteRequest is ${strategyOutput.evaluatorOutput}"
        )
        val finalPriceWithOutTax = when (strategyOutput.evaluatorOutput?.output) {
            is LadderValue -> {
                pickLadderValueBasedOnGivenQuantity(
                    (strategyOutput.evaluatorOutput.output as LadderValue).value,
                    contractQuoteRequest.volumeCommitted
                )
            }
            is BigDecimalValue -> {
                (strategyOutput.evaluatorOutput.output as BigDecimalValue).value
            }
            else -> {
                null
            }
        }
        val strategyInputs = strategyOutput.requestContext.map { it.inputs }.flatten()
        val conversionRate = (VariableUtils.getApplicableVariableValueForVariable(
            inputs = strategyInputs,
            variableId = VariableId.CONVERSION_RATE
        ) as BigDecimalValue).value
        log.info("Conversion rate: $conversionRate for $listingId and $salesUnitId")
        val cogsUnitPriceInPaisa = fetchCogsFromEvaluatorInput(
            inputs = strategyInputs,
            conversionRate = conversionRate,
            verticalCategory = verticalCategory ?: error("Vertical category not found")
        )
        val listingTaxDetails = catalogHelper.getListingTaxDetails(
            listingDetails = catalogSvcInterface.getTradeListingMinimal(listingId),
            salesUnitId = salesUnitId
        )
        val taxFactor = getTaxFactor(listingTaxDetails)
        val bestCompData = deriveBestCompPrice(
            strategyInputs,
            volumeCommitted = contractQuoteRequest.volumeCommitted,
            taxFactor = taxFactor.toLong()
        )
        val mrpMarkMarkdownBps = if (verticalCategory == VerticalCategory.FMCG) {
            calculateMarinInBps(
                basePrice = listingTaxDetails.getMrpWithOutTax(),
                referencePrice = finalPriceWithOutTax?.toLong()
            )
        } else {
            null
        }
        log.info(
            "Final price without tax: $finalPriceWithOutTax and " +
                    "cogs unit price: $cogsUnitPriceInPaisa and best comp unit price is $bestCompData"
        )

        return QuotePriceInfo(
            quotePriceInPaisa = finalPriceWithOutTax?.divideWithScale(conversionRate)?.toLong(),
            quotePriceInPaisaWithTax = finalPriceWithOutTax?.divideWithScale(
                conversionRate
            )?.multiplyWithScale(taxFactor)?.toLong(),
            cogsUnitPriceInPaisa = cogsUnitPriceInPaisa?.toLong(),
            metadata = strategyOutput.evaluatorOutput?.metadata ?: emptyMap(),
            bestCompetitorQuote = bestCompData,
            mrpMarkDownBps = mrpMarkMarkdownBps
        )
    }

    private fun deriveBestCompPrice(
        inputs: List<EvaluatorConfigInput>,
        volumeCommitted: Long,
        taxFactor: Long
    ): BestCompetitorQuote {
        val jumboTailLadderValue = (VariableUtils.getApplicableVariableValueForVariable(
            inputs = inputs,
            variableId = VariableId.JUMBOTAIL_COMP_LADDER_PRICE_WT_PAISA_UNIT
        ) as? LadderValue)?.value?.let {
            "JUMBO_TAIL" to pickLadderValueBasedOnGivenQuantity(it, volumeCommitted)
        }
        val hyperPureLadderValue = (VariableUtils.getApplicableVariableValueForVariable(
            inputs = inputs,
            variableId = VariableId.HYPERPURE_COMP_LADDER_PRICE_WT_PAISA_UNIT
        ) as? LadderValue)?.value?.let {
            "HYPER_PURE" to pickLadderValueBasedOnGivenQuantity(it, volumeCommitted)
        }
        val metroLadderValue = (VariableUtils.getApplicableVariableValueForVariable(
            inputs = inputs,
            variableId = VariableId.METRO_COMP_LADDER_PRICE_WT_PAISA_UNIT
        ) as? LadderValue)?.value?.let {
            "METRO" to pickLadderValueBasedOnGivenQuantity(it, volumeCommitted)
        }
        val bestCompPrice = listOfNotNull(jumboTailLadderValue, hyperPureLadderValue, metroLadderValue).minByOrNull {
            it.second ?: Int.MAX_VALUE.toBigDecimal()
        }
        return BestCompetitorQuote(
            priceInPaisa = bestCompPrice?.let {
                it.second?.divideWithScale(taxFactor.toBigDecimal())?.toLong()
            },
            competitor = bestCompPrice?.first
        )
    }

    private fun getTaxFactor(listingTaxDetails: ListingTaxDetails): BigDecimal {
        return (listingTaxDetails.gstBps + listingTaxDetails.cessBps).toBigDecimal().divideWithScale(
            BigDecimal(BPS_NORMALISER)
        ).add(BigDecimal(1))
    }

    private fun pickLadderValueBasedOnGivenQuantity(ladders: List<Ladder>, quantity: Long): BigDecimal? {
        return ladders.firstOrNull {
            quantity >= it.minQuantity
                    && quantity <= it.maxQuantity
        }?.ladderValue
    }

    private fun fetchCogsFromEvaluatorInput(
        inputs: List<EvaluatorConfigInput>,
        conversionRate: BigDecimal,
        verticalCategory: VerticalCategory
    ): BigDecimal? {
        return when (verticalCategory) {
            VerticalCategory.STAPLES -> {
                (VariableUtils.getApplicableVariableValueForVariable(
                    inputs = inputs,
                    variableId = VariableId.STAPLES_LIP_WOT_PAISA_UNIT
                ) as? BigDecimalValue)?.value
            }
            VerticalCategory.FMCG -> {
                (VariableUtils.getApplicableVariableValueForVariable(
                    inputs = inputs,
                    variableId = VariableId.FMCG_COGS_WOT_PAISA_SET
                ) as? BigDecimalValue)?.value?.divideWithScale(conversionRate)
            }
            else -> {
                null
            }
        }
    }

    private fun getSalesUnitOrNull(activeListing: TradeListing): String? {
        return catalogHelper.fetchActiveSalesUnits(activeListing).map { it.salesUnitId }.firstOrNull()
    }

    private suspend fun getPriceValidity(contractQuoteRequest: ContractQuoteRequest): String {
        val verticalId = catalogHelper.fetchVerticalIdForCatalogId(
            CatalogEntityType.valueOf(contractQuoteRequest.contractCatalogEntity),
            contractQuoteRequest.contractCatalogEntityId
        )
        return configHelper.getContractQuotePriceValidityInDays(verticalId).let {
            Instant.ofEpochMilli(System.currentTimeMillis() + TimeUnit.DAYS.toMillis(it.toLong())).toString()
        }
    }

    private data class QuotePriceInfo(
        val quotePriceInPaisa: Long?,
        val quotePriceInPaisaWithTax: Long?,
        val metadata: Map<String, String>,
        val cogsUnitPriceInPaisa: Long?,
        val mrpMarkDownBps: Long?,
        val bestCompetitorQuote: BestCompetitorQuote
    )
}
