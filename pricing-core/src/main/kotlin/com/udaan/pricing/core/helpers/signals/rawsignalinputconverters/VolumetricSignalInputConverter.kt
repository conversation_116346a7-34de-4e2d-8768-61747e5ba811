package com.udaan.pricing.core.helpers.signals.rawsignalinputconverters

import com.udaan.common.utils.getCurrentMillis
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.signalcreation.VolumetricSignalInput
import com.udaan.pricing.signals.GenericMetadata
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState
import com.udaan.pricing.variable.VariableId

class VolumetricSignalInputConverter : RawSignalInputConverter<VolumetricSignalInput>() {
    companion object {
        private val logger by logger()
    }

    override suspend fun convert(volumetricSignalInput: VolumetricSignalInput): List<Signal> {
        val convertedSignal = Signal(
            catalogEntity = volumetricSignalInput.catalogEntityId.uppercase(),
            catalogEntityType = volumetricSignalInput.catalogEntityType,
            variableId = VariableId.VOLUMETRIC_LADDER_DISCOUNT_BPS.name,
            signalData = LadderValue(volumetricSignalInput.ladders),
            metadata = GenericMetadata(emptyMap()),
            location = volumetricSignalInput.location.copy(
                locationValue = volumetricSignalInput.location.locationValue.uppercase()
            ),
            state = SignalState.ACTIVE,
            createdBy = volumetricSignalInput.updatedBy,
            updatedBy = volumetricSignalInput.updatedBy,
            updatedAt = getCurrentMillis()
        )

        logger.info("converted signal {}", convertedSignal)
        return listOf(convertedSignal)
    }

}
