package com.udaan.pricing.core.controller

import com.google.inject.Inject
import com.udaan.catalog.client.CategoryTreeV2
import com.udaan.catalog.model.VerticalCategory
import com.udaan.catalog.models.ModelV2
import com.udaan.common.client.extensions.awaitOrNull
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.firstpartycatalog.client.FirstPartyCatalogRedisClient
import com.udaan.firstpartycatalog.client.FirstPartyCatalogRedisRepository
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.AvailableLocationsForListingResponse
import com.udaan.pricing.BuyerCohortsAndLocationsResponse
import com.udaan.pricing.BuyerContext
import com.udaan.pricing.ContextualPriceRequest
import com.udaan.pricing.ListingDetailsForPricingResponseDto
import com.udaan.pricing.LocationAndCohortPricingResponseForSSCVisibility
import com.udaan.pricing.PriceDetailsForVisibilityResponse
import com.udaan.pricing.PriceListingVisibilityForBuyerResponseDto
import com.udaan.pricing.PriceListingVisibilityResponse
import com.udaan.pricing.PriceRidersForVisibility
import com.udaan.pricing.SSCPriceVisibilityResponse
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.contracts.ContractVisibilitySummary
import com.udaan.pricing.contracts.ExceptionContractResponse
import com.udaan.pricing.core.controller.contract.ContractController
import com.udaan.pricing.core.helpers.BlobStorageHelper
import com.udaan.pricing.core.helpers.BuyerCohortDetails
import com.udaan.pricing.core.helpers.BuyerTagHelper
import com.udaan.pricing.core.helpers.BuyerTagHelper.Companion.DEFAULT_COHORT_PRIORITY
import com.udaan.pricing.core.helpers.CatalogHelper
import com.udaan.pricing.core.helpers.SellerOrgCityHelper
import com.udaan.pricing.core.helpers.StrategyExecutorHelper
import com.udaan.pricing.core.helpers.pricingstrategy.impl.SSCPrice
import com.udaan.pricing.core.models.contracts.toContractPriceResponse
import com.udaan.pricing.core.models.contracts.toExceptionContractResponse
import com.udaan.pricing.core.svcinterfaces.CatalogSvcInterface
import com.udaan.pricing.core.managers.signals.AsyncJobController
import com.udaan.pricing.job.AsyncJobStatus
import com.udaan.pricing.job.AsyncJobType
import com.udaan.pricing.job.requests.CreateAsyncJobRequest
import com.udaan.user.client.UserServiceClient
import com.udaan.vertical.client.cache.CentralVerticalCache
import java.io.InputStream

@Suppress("TooManyFunctions", "LongParameterList")
class PricingConsoleController @Inject constructor(
    private val userServiceClient: UserServiceClient,
    private val firstPartyCatalogRedisRepository: FirstPartyCatalogRedisRepository,
    private val firstPartyCatalogRedisClient: FirstPartyCatalogRedisClient,
    private val priceController: PriceController,
    private val buyerTagHelper: BuyerTagHelper,
    private val sscPrice: SSCPrice,
    private val strategyExecutorHelper: StrategyExecutorHelper,
    private val sellerOrgCityHelper: SellerOrgCityHelper,
    private val categoryTreeV2: CategoryTreeV2,
    private val centralVerticalCache: CentralVerticalCache,
    private val blobStorageHelper: BlobStorageHelper,
    private val catalogSvcInterface: CatalogSvcInterface,
    private val contractController: ContractController,
    private val catalogHelper: CatalogHelper,
    private val asyncJobController: AsyncJobController
) {

    companion object {
        private val log by logger()
    }

    suspend fun getExceptionContractVisibilityForBuyer(buyerNumber: String): List<ExceptionContractResponse> {
        val buyerOrg = userServiceClient.getOrgByMobile(buyerNumber).executeAwait(3)
        return contractController.getExceptionContractsForBuyer(buyerOrg.orgId).map {
            val catalogTitle = catalogHelper.getCatalogTitle(
                catalogEntity = CatalogEntityType.valueOf(it.contract.contractCatalogEntity.name),
                catalogEntityId = it.contract.catalogEntityId
            )
            it.toExceptionContractResponse(catalogTitle ?: "")
        }
    }

    suspend fun getContractVisibilitySummary(
        buyerNumber: String
    ): ContractVisibilitySummary {
        val buyerOrg = userServiceClient.getOrgByMobile(buyerNumber).executeAwait(3)
        val contracts = contractController.getContractsForBuyer(buyerOrg.orgId)
        val catalogTitleMap = contracts.parallelMap {
            it.catalogEntityId to catalogHelper.getCatalogTitle(
                CatalogEntityType.valueOf(it.contractCatalogEntity.name),
                it.catalogEntityId
            )
        }.toMap()
        val (activeContracts, expiredContracts) = contracts.partition {
            it.duration.endTime > System.currentTimeMillis()
        }
        return ContractVisibilitySummary(
            buyerNumber = buyerNumber,
            buyerOrgId = buyerOrg.orgId,
            buyerOrgName = buyerOrg.displayName,
            activeContracts = activeContracts.map {
                it.toContractPriceResponse(catalogTitleMap[it.catalogEntityId] ?: "")
            },
            expiredContracts = expiredContracts.map {
                it.toContractPriceResponse(catalogTitleMap[it.catalogEntityId] ?: "")
            }
        )
    }

    suspend fun getSSCPriceVisibility(
        listingId: String,
        buyerCohort: String,
        locationId: String,
        locationType: String
    ): SSCPriceVisibilityResponse {
        val location = if (locationType == LocationType.CITY.name) {
            Location(LocationType.CITY, locationId)
        } else {
            Location(LocationType.WAREHOUSE, locationId)
        }
        val buyerCohorts = if (buyerCohort == "ALL") {
            buyerTagHelper.fetchAllPossibleBuyerTagsForCategoryAndClass()
        } else {
            listOf(BuyerCohortDetails(buyerCohort, DEFAULT_COHORT_PRIORITY))
        }

        log.info(
            "Got request to fetch SSC Price visibility for listing {}, buyerCohort {}, location {}, locationType {}",
            listingId,
            buyerCohort,
            locationId,
            locationType
        )

        val catalogEntityContext = catalogHelper.getCatalogEntityContextFromLidSuid(
            listingId = listingId,
            salesUnitId = null
        )

        val salesUnitId = catalogEntityContext.fetchSalesUnits().firstOrNull()
            ?: throw IllegalArgumentException("Not able to fetch sales unit from listing $listingId")

        log.info("Fetched sales unit for SSC price visibility for listing {} is {}", listingId, salesUnitId)

        val catalogEntity = TelemetryScope.async {
            sscPrice.fetchCatalogEntity(catalogEntityContext)
        }

        val resultResponse = emptyList<LocationAndCohortPricingResponseForSSCVisibility>().toMutableList()

        buyerCohorts.forEach { buyerCohortDetail ->
            val price = try {
                strategyExecutorHelper.getPrice(
                    listingId = listingId,
                    salesUnitId = salesUnitId,
                    catalogEntity = catalogEntity.await()!!,
                    locationsSortedByPriority = listOf(location),
                    allBuyerCohorts = listOf(buyerCohortDetail),
                    verticalCategory = catalogEntityContext.verticalCategory,
                    mappedFpProductDetails = catalogEntityContext.fpProductDetails,
                    buyerOrgUnit = null,     // no buyer context in this flow
                    mappedBenchmarkListingGuardrailPriceDeferred = null // todo: omraj-tec - figure out how to pass this for better debugging
                )
            } catch (ex: Exception) {
                log.info(
                    "Exception while fetching price for buyer cohort {} and location {} for listing {} Exception {}",
                    buyerCohortDetail,
                    location,
                    listingId,
                    ex.message
                )
                null
            }
            if (price != null) {
                resultResponse.add(
                    LocationAndCohortPricingResponseForSSCVisibility(
                        cohortValue = buyerCohortDetail.buyerCohort.uppercase(),
                        locationValue = locationId,
                        locationType = locationType,
                        priceForListing = price
                    )
                )
            }
        }

        return SSCPriceVisibilityResponse(
            listingId = listingId,
            salesUnitId = salesUnitId,
            pricesForLocationsAndCohorts = resultResponse.toList()
        )
    }

    suspend fun getAllPossibleBuyerCohortsAndLocations(listingId: String): BuyerCohortsAndLocationsResponse {
        val allBuyerCohortDetails = buyerTagHelper.fetchAllPossibleBuyerTagsForCategoryAndClass()

        val listingDetails = catalogSvcInterface.getTradeListing(listingId)

        val vertical = try {
            centralVerticalCache.getVerticalForListing2(listingId)
        } catch (e: Exception) {
            log.info("Exception while getting vertical for {} Exception {}", listingId, e.message)
            throw e
        } ?: throw IllegalStateException("Vertical not found for listing $listingId")

        val verticalCategory = categoryTreeV2.getVerticalCategory(vertical.name)

        /*
             Brutal Hack:
            - Meat and Fresh listings have orgs associated which return India as city.
            - Since that is not a valid city, for now we return hardcoded Bangalore city for them.
            @todo - hack to be removed when city values fixed for these categories or we want to expand beyond Bangalore.
         */
        val city = if (verticalCategory in listOf(VerticalCategory.FRESH, VerticalCategory.MEAT)) {
            "BANGALORE"
        } else {
            sellerOrgCityHelper.getCityForSellerOrgId(listingDetails.orgId)
        } ?: throw IllegalArgumentException(
            "Not able to fetch city details from listing $listingId " +
                    "and orgId ${listingDetails.orgId}"
        )

        //@todo - can we use LocationHelper here (may be available funs or create new one if required)
        val listOfLocationsWithIdentifiers = when (verticalCategory) {
            VerticalCategory.FMCG,
            VerticalCategory.FRESH,
            VerticalCategory.MEAT -> listOf(Pair(Location(LocationType.CITY, city), city))
            VerticalCategory.STAPLES -> {
                val listOfWarehouses = sscPrice.fetchWarehousesForACity(city)
                listOfWarehouses.map { Pair(Location(LocationType.WAREHOUSE, it.whId), it.name + " " + it.whId) }
            }
            else -> null
        } ?: throw IllegalArgumentException("Vertical category $verticalCategory not supported for SSC price visibility")

        return BuyerCohortsAndLocationsResponse(
            buyerCohorts = allBuyerCohortDetails.map { it.buyerCohort }.plus("ALL"),
            locations = listOfLocationsWithIdentifiers.map { (location, locationName) ->
                AvailableLocationsForListingResponse(
                    locationId = location.locationValue.uppercase(),
                    locationType = location.locationType.name,
                    locationName = locationName.uppercase()
                )
            }
        )

    }

    suspend fun getListingDetailsForPricingConsole(
        listingId: String
    ) : ListingDetailsForPricingResponseDto {

        val listingDetails = catalogSvcInterface.getTradeListing(listingId)

        val product = firstPartyCatalogRedisRepository.getProductByListingId(
            listingId = listingId,
            retryCount = 1
        ).awaitOrNull() ?: throw IllegalStateException("No Product unit found for listing Id $listingId")


        val productGroupId = firstPartyCatalogRedisClient.getGroupId(
            productId = product.productId
        ).awaitOrNull()?.groupId

        val salesUnitIdToConsider = listingDetails.salesUnitList.filter {
            it.status in listOf(ModelV2.EleStatus.ENABLED, ModelV2.EleStatus.INACTIVE)
        }.map { it.salesUnitId }.firstOrNull()

        return ListingDetailsForPricingResponseDto(
            listingId = listingId,
            salesUnitId = salesUnitIdToConsider ?: throw IllegalStateException("No sales unit found for listing $listingId"),
            productId = product.productId,
            groupId = productGroupId,
            title = product.title,
            status = listingDetails.status.name,
            mrp = product.pricingAttributes["mrp"]?.let { if (it != "IGNORE") "Rs $it" else null } ?: "",
            gst = product.pricingAttributes["gst"]?.let { "$it%" } ?: ""
        )

    }

    suspend fun getPriceListingVisibilityForBuyer(
        listingId: String,
        buyerNumber: String
    ): PriceListingVisibilityForBuyerResponseDto {

        val listingDetails = catalogSvcInterface.getTradeListing(listingId)

        val buyerOrg = try {
            userServiceClient.getOrgByMobile(buyerNumber).executeAwait(3)
        } catch (ex: Exception) {
            log.error("Exception while fetching buyer org for buyer $buyerNumber Exception $ex")
            throw ex
        }

        val buyerOrgUnit = buyerOrg.orgUnitsMap.values.firstOrNull {
            it.data.toString().contains(buyerNumber)
        } ?: buyerOrg.orgUnitsMap.values.firstOrNull()
        ?: throw IllegalStateException("OrgUnit not found for buyer $buyerNumber")

        val pincode = buyerOrgUnit.unitAddress.pincode

        val buyerContext = BuyerContext(
            orgId = buyerOrg.orgId,
            orgUnitId = buyerOrgUnit.orgUnitId,
            pincode = pincode
        )

        val salesUnitIdToConsider = listingDetails.salesUnitList.filter {
            it.status in listOf(ModelV2.EleStatus.ENABLED, ModelV2.EleStatus.INACTIVE)
        }.map { it.salesUnitId }.firstOrNull()

        if (salesUnitIdToConsider.isNullOrBlank()) throw IllegalStateException("No sales unit found for listing $listingId")


        val priceForListingList = priceController.getPriceListing(
            listingId = listingId,
            saleUnitId = salesUnitIdToConsider,
            contextualPriceRequest = ContextualPriceRequest(
                transactionContext = null,
                buyerContext = buyerContext
            ),
            rootRequestContext = null,
            fetchInactive = false,
            fetchRiderDetails = true,
            fetchRiderAdditionalDetails = true,
            orderReadyForCheckout = false,
            filterSSCMetaData = false
        )

        val priceListingVisibilityResponseList = priceForListingList.map { priceForListing ->
            PriceListingVisibilityResponse(
                strategyRef = priceForListing.strategyRef,
                sscMetaData = priceForListing.sscMetadata,
                prices = priceForListing.prices.map { qtyBasedPrice ->

                    val validPriceRiders = qtyBasedPrice.priceInPaisa.priceRiders?.filter { priceRider ->
                        priceRider?.bpsInPercentage != null && priceRider.bpsInPercentage != 0
                    }?.filterNotNull() ?: emptyList()

                    PriceDetailsForVisibilityResponse(
                        minQty = qtyBasedPrice.minQty,
                        maxQty = qtyBasedPrice.maxQty,
                        priceInPaisa = qtyBasedPrice.priceInPaisa.defaultPrice,
                        priceRiders = validPriceRiders.map { priceRider ->
                            PriceRidersForVisibility(
                                riderCode = priceRider.riderCode ?: "",
                                bpsInPercentage = priceRider.bpsInPercentage,
                                riderDetails = priceRider.riderDetails
                            )
                        }
                    )
                }
            )
        }

        val product = firstPartyCatalogRedisRepository.getProductByListingId(
            listingId = listingId,
            retryCount = 1
        ).awaitOrNull() ?: throw IllegalStateException("No Product unit found for listing Id $listingId")


        val productGroupId = firstPartyCatalogRedisClient.getGroupId(
            productId = product.productId
        ).awaitOrNull()?.groupId

        return PriceListingVisibilityForBuyerResponseDto(
            listingId = listingId,
            salesUnitId = salesUnitIdToConsider,
            productId = product.productId,
            groupId = productGroupId,
            title = product.title,
            status = listingDetails.status.name,
            mrp = product.pricingAttributes["mrp"]?.let { if (it != "IGNORE") "Rs $it" else null } ?: "",
            gst = product.pricingAttributes["gst"]?.let { "$it%" } ?: "",
            orgId = listingDetails.orgId,
            orgUnitId = buyerOrgUnit.orgUnitId,
            pincode = pincode,
            prices = priceListingVisibilityResponseList
        )

    }

    /**
     * Uploads file to blob storage and creates a job in async job table and pushes the job to queue
     */
    suspend fun uploadFileAndCreateJob(
        jobType: AsyncJobType,
        additionalInfo: Map<String, String>,
        requestedBy: String,
        fileInputStream: InputStream,
        fileLength: Long
    ) {
        val asyncJobRequest = CreateAsyncJobRequest(
            type = jobType,
            attributesMap = additionalInfo,
            inputFilePath = null,
            requestedBy = requestedBy
        )
        val asyncJob = asyncJobController.createAsyncJob(asyncJobRequest)
        val inputFileName = "${asyncJob.id}_input.xlsx"
        blobStorageHelper.uploadInputStreamToBlobStorage(
            containerReference = jobType.toString().lowercase().replace("_", "-"),
            directoryReference = jobType.toString().lowercase().replace("_", "-"),
            inputStream = fileInputStream,
            fileName = inputFileName,
            fileLength = fileLength
        )
        asyncJobController.updateAsyncJobStatusAndMetadata(
            jobId = asyncJob.id,
            status = AsyncJobStatus.NEW,
            inputFilePath = inputFileName,
            updatedBy = requestedBy
        ).also {
            try {
                asyncJobController.pushAsyncJobToQueue(asyncJob.id)
            } catch (e: Exception) {
                log.error("Push to queue failed for job {} of type {} with error {}", it.id, it.type, e.message)
                asyncJobController.updateAsyncJobStatusAndMetadata(
                    jobId = asyncJob.id,
                    status = AsyncJobStatus.FAILED,
                    remarks = "Push to queue failed for job ${it.id}",
                    updatedBy = "SYSTEM"
                )
            }

        }
    }
}
