package com.udaan.pricing.core.controller.contract

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.catalog.model.VerticalCategory
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope.Companion.async
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.core.models.common.getMrpWithOutTax
import com.udaan.pricing.core.common.throwContractPriceValidationException
import com.udaan.pricing.core.helpers.CatalogHelper
import com.udaan.pricing.core.helpers.ConfigHelper
import com.udaan.pricing.core.models.contracts.Contract
import com.udaan.pricing.core.models.contracts.ContractCatalogEntity
import com.udaan.pricing.core.models.contracts.ContractLadderMrpMarkdownBps
import com.udaan.pricing.core.models.contracts.ContractLadderPrice
import com.udaan.pricing.core.strategyevaluator.Constants.BPS_NORMALISER
import com.udaan.pricing.core.svcinterfaces.CatalogSvcInterface
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import java.math.BigDecimal

@Singleton
class ContractPriceValidator @Inject constructor(
    private val catalogHelper: CatalogHelper,
    private val catalogSvcInterface: CatalogSvcInterface,
    private val cogsHelper: CogsHelper,
    private val configHelper: ConfigHelper
) {

    private val log by logger()

    /**
     * Validates the contract price with COGS price if the contract is not configured to skip COGS validation.
     */
    suspend fun validateContractPrice(contract: Contract) {
        validateContractWithCogsPrice(contract)
    }

    private suspend fun validateContractWithCogsPrice(contract: Contract) {
        val verticalCategory = catalogHelper.fetchVerticalCategory(
            catalogEntity = CatalogEntityType.valueOf(contract.contractCatalogEntity.name),
            catalogEntityId = contract.catalogEntityId
        )
        if (verticalCategory == null) {
            log.warn("No vertical category found for contract: $contract")
            return
        }
        val upperThresholdInBpsDeferred = async {
            configHelper.getContractPriceUpperThresholdInBps()
        }
        val activeListings = catalogHelper.fetchActiveListingsByCity(
            CatalogEntityType.valueOf(contract.contractCatalogEntity.name),
            contract.catalogEntityId,
            contract.metadata.city ?: error("City not found")
        )
        val cogsUnitPriceWithOutTax = cogsHelper.fetchCogsUnitPriceForCategory(
            contract.catalogEntityId,
            contract.buyerOrgId, verticalCategory,
            CatalogEntityType.valueOf(contract.contractCatalogEntity.name),
            activeListings
        )?.cogsUnitPriceWithOutTax
        val mrpUnitPriceWotTax = fetchMrpUnitPriceInPaisaWotTax(
            contract.catalogEntityId,
            contract.contractCatalogEntity,
            contract.metadata.city
        )
        val upperThresholdInPaisa = upperThresholdInBpsDeferred.await()?.let { upperThresholdInBps ->
            cogsUnitPriceWithOutTax?.multiplyWithScale(
                BigDecimal.ONE + upperThresholdInBps.toBigDecimal().divideWithScale(BigDecimal(BPS_NORMALISER))
            )
        }
        log.info("Unit price for contract: ${contract.catalogEntityId} and ${contract.buyerOrgId} is $cogsUnitPriceWithOutTax")
        val contractPriceInPaisa = when (contract.price) {
            is ContractLadderPrice -> {
                contract.price.value.minOfOrNull { it.ladderValue }?.toBigDecimal() ?: error("Ladders cannot be empty")
            }

            is ContractLadderMrpMarkdownBps -> {
                getCogsUnitPriceAfterMrpMarkdown(
                    contract.price,
                    mrpUnitPriceWotTax
                )
            }
        }
        contractPriceInPaisa?.let {
            validateContractPrice(
                contractPriceInPaisa = contractPriceInPaisa,
                lowerThresholdInPaisa = cogsUnitPriceWithOutTax,
                upperThresholdInPaisa = if (verticalCategory == VerticalCategory.STAPLES) {
                    upperThresholdInPaisa
                } else {
                    mrpUnitPriceWotTax ?: upperThresholdInPaisa
                }
            )
        }
    }

    private fun validateContractPrice(
        contractPriceInPaisa: BigDecimal,
        lowerThresholdInPaisa: BigDecimal?,
        upperThresholdInPaisa: BigDecimal?
    ) {
        if (lowerThresholdInPaisa != null && contractPriceInPaisa < lowerThresholdInPaisa) {
            throwContractPriceValidationException(
                "Contract price $contractPriceInPaisa should not be less than lower threshold $lowerThresholdInPaisa",
                contractPriceInPaisa.toLong(), lowerThresholdInPaisa.toLong(), upperThresholdInPaisa?.toLong()
            )
        }
        if (upperThresholdInPaisa != null && contractPriceInPaisa > upperThresholdInPaisa) {
            throwContractPriceValidationException(
                "Contract price $contractPriceInPaisa should not be greater than upper threshold $upperThresholdInPaisa",
                contractPriceInPaisa.toLong(), lowerThresholdInPaisa?.toLong(), upperThresholdInPaisa.toLong()
            )
        }
    }

    private fun getCogsUnitPriceAfterMrpMarkdown(
        price: ContractLadderMrpMarkdownBps,
        mrpUnitPriceWotTax: BigDecimal? = null
    ): BigDecimal? {
        val minLadderMrpMarkdownBps = price.value.minOfOrNull { it.ladderValue } ?: error("Ladders cannot be empty")
        return mrpUnitPriceWotTax?.let {
            mrpUnitPriceWotTax.multiplyWithScale(
                BigDecimal.ONE - minLadderMrpMarkdownBps.toBigDecimal().divideWithScale(
                    BigDecimal(BPS_NORMALISER)
                )
            )
        }
    }

    /**
     * Fetches the MRP unit price in paisa without tax for a product group or listing.
     */
    private suspend fun fetchMrpUnitPriceInPaisaWotTax(
        catalogEntityId: String,
        contractCatalogEntity: ContractCatalogEntity,
        city: String
    ): BigDecimal? {
        return when (contractCatalogEntity) {
            ContractCatalogEntity.PRODUCT_GROUP_ID -> {
                val activeListings = catalogHelper.getActiveListingsForGroupId(catalogEntityId, city)
                activeListings.firstOrNull()?.let {
                    getListingMrpInUnitPricePaisaWithOutTax(listingId = it.listingId)
                }
            }

            ContractCatalogEntity.LISTING_ID -> {
                getListingMrpInUnitPricePaisaWithOutTax(listingId = catalogEntityId)
            }
        }
    }

    /**
     * Fetches the MRP unit price in paisa without tax for a listing.
     */
    private suspend fun getListingMrpInUnitPricePaisaWithOutTax(listingId: String): BigDecimal? {
        val listingDetails = catalogSvcInterface.getTradeListingMinimal(listingId)
        val salesUnits = catalogHelper.fetchActiveSalesUnits(listingDetails = listingDetails)
        salesUnits.map {
            val listingTaxDetails = catalogHelper.getListingTaxDetails(
                listingDetails,
                salesUnitId = it.salesUnitId
            )
            val mrpInPaisaWithOutTax = listingTaxDetails.getMrpWithOutTax()
            val assortment = it.numItemsAssortment.coerceAtLeast(1)
            val mrpInUnitPriceWithoutTax = mrpInPaisaWithOutTax?.div(assortment)
            if (mrpInUnitPriceWithoutTax != null && mrpInUnitPriceWithoutTax > 0) {
                return mrpInUnitPriceWithoutTax.toBigDecimal()
            }
        }
        return null
    }
}
