package com.udaan.pricing.core.strategyevaluator.impl.dynamic.staples

import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.variable.VariableId
import java.math.BigDecimal

internal object VslMarkupPriceEvaluator : Evaluator {

    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        ValidationUtils.validateInputs(data.strategy, data.inputs)
        val vslPriceInPaisa = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.VSL_PRICE_WOT_PAISA_UNIT
        ) as BigDecimalValue
        val vslMarkupPrice = applyVslMarkup(vslPriceInPaisa, data)
        return EvaluatorOutput(vslMarkupPrice, emptyMap())
    }


    private fun applyVslMarkup(price: BigDecimalValue, data: EvaluatorRequestContext): BigDecimalValue {

        val vslMarkUpBps = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.VSL_MARKUP_BPS
        ) as? BigDecimalValue ?: BigDecimalValue(BigDecimal.ZERO)

        val vslPriceWithMarkup = price.value.multiplyWithScale(
            BigDecimal(1).plus(
                vslMarkUpBps.value.divideWithScale(BigDecimal("10000"))
            )
        )
        return BigDecimalValue(vslPriceWithMarkup)
    }
}
