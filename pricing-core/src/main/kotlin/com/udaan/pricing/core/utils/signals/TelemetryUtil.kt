package com.udaan.pricing.core.utils.signals

import com.udaan.instrumentation.MetricsManager
import com.udaan.instrumentation.Telemetry
import java.time.Instant

object TelemetryUtil {

    private const val ErrorMessage = "Collector already registered that provides name"

    suspend fun <T> timedRedis(key: String, method: String, block: suspend () -> T): T? {
        val startTime = System.currentTimeMillis()
        var exception = false
        val value = try {
            time("signal-redis", Pair("redis-operation", method)) {
                block.invoke()
            }
        } catch (e: Exception) {
            exception = true
            Telemetry.trackException(e, mapOf("redisKey" to key, "method" to method), emptyMap())
            e.printStackTrace()
            null
        }
        Telemetry.trackDependency(
            protocol = "redis",
            host = "signal-redis",
            method = method,
            path = key,
            properties = mapOf("hit" to (value != null).toString()),
            success = !exception,
            durationSecs = (System.currentTimeMillis() - startTime).div(1000.0),
            requestTime = Instant.ofEpochMilli(startTime),
            measures = mapOf(),
            queryParams = null,
            sdkVersion = "lettuce-6"
        )
        return value
    }

    private suspend fun <T> time(
        name: String,
        vararg labels: Pair<String, String>,
        block: suspend () -> T
    ): T = try {
        MetricsManager.withTimingSuspended(name, *labels) {
            block.invoke()
        }
    } catch (e: Exception) {
        if (e is IllegalArgumentException && e.message?.contains(ErrorMessage) == true) {
            e.printStackTrace()
            // We are not executing the block twice, since Collector Registry exception occurs before block is executed
            block.invoke()
        } else throw e
    }
}
