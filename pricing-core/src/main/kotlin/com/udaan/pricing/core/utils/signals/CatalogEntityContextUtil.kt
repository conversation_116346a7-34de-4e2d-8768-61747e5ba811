package com.udaan.pricing.core.utils.signals

import com.udaan.catalog.model.VerticalCategory
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.core.models.signals.CatalogEntityContext

object CatalogEntityContextUtil {
    fun CatalogEntityContext.getCatalogEntityBasisTypeEnum(catalogEntityType: CatalogEntityType): String? {
        return when (catalogEntityType) {
            CatalogEntityType.LISTING_ID -> listingId
            CatalogEntityType.PRODUCT_GROUP_ID -> productGroupId
            CatalogEntityType.VERTICAL -> vertical
            else -> throw IllegalArgumentException("$catalogEntityType not present in context")
        } ?: run {
            // in case of FRESH and MEAT, GID is allowed to be null, for other categories its not
            if(catalogEntityType == CatalogEntityType.PRODUCT_GROUP_ID && this.verticalCategory in listOf(VerticalCategory.MEAT, VerticalCategory.FRESH)){
                null
            } else throw IllegalStateException("$catalogEntityType is null in catalog context")
        }
    }
}
