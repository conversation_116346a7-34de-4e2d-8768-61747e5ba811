package com.udaan.pricing.core.helpers.signals.deletesignalrequestconverters

import com.udaan.pricing.core.utils.signals.OnlineCompetitionInputsUtil
import com.udaan.pricing.signaldeletion.DeleteCompSignalRequest
import com.udaan.pricing.signaldeletion.DeletionReason
import com.udaan.pricing.signaldeletion.SignalDeletionInfo

class DeleteCompSignalRequestConverter: DeleteSignalRequestConverter<DeleteCompSignalRequest>() {

    override suspend fun convert(deleteSignalRequest: DeleteCompSignalRequest): List<SignalDeletionInfo> {
        return OnlineCompetitionInputsUtil.getVariableIdForLadderCompPrice(
            competitionName = deleteSignalRequest.competitionName
        )?.let{ variableIdForCompetition ->
            listOf(
                SignalDeletionInfo(
                    catalogEntity = deleteSignalRequest.productGroupId.uppercase(),
                    locationValue = deleteSignalRequest.location.locationValue.uppercase(),
                    signalType = variableIdForCompetition.name,
                    deletionReason = DeletionReason.DEACTIVATION,
                    deletedBy = deleteSignalRequest.benchmarkRefId ?: "SYSTEM"
                )
            )
        } ?: emptyList()
    }
}
