package com.udaan.pricing.core.controller.cache

import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.common.utils.parallelMap
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.GeoLocationBasePrice
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.dao.GeoLocationBasePriceRepository
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await

class GeoBasePriceCacheController @Inject constructor(
    @Named(NamedConstants.Caches.LISTING_GEO_BASE_PRICE_CACHE) private val listingGeoBasePriceCache: RedisCache2<List<GeoLocationBasePrice>>,
    private val geoLocationBasePriceRepository: GeoLocationBasePriceRepository
) {

    suspend fun getCachedGeoBasePricesForListing(listingId: String): List<GeoLocationBasePrice> {
        val geoBasePrices = listingGeoBasePriceCache.get(listingId) {
            TelemetryScope.future {
                geoLocationBasePriceRepository.getAllActiveGeoBasePricesForListing(listingId)
            }
        }.await() ?: throw IllegalArgumentException("Geo base price not found for $listingId")

        return geoBasePrices
    }

    suspend fun invalidateCachedGeoBasePriceForListing(listingId: String) {
        listingGeoBasePriceCache.invalidate(listingId).await()
    }

    suspend fun invalidateCachedGeoBasePriceForListings(listingIds: Collection<String>) {
        listingIds.parallelMap { listingId ->
            invalidateCachedGeoBasePriceForListing(listingId)
        }
    }

}
