package com.udaan.pricing.core.utils

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.auth.roles.UserRoleAttributesManagerV2
import com.udaan.common.auth.roles.UserRoleView

@Singleton
class AuthorizationUtils @Inject constructor(
    private val userRoleAttributesManagerV2: UserRoleAttributesManagerV2
) {
    fun fetchUserRoles(userEmail: String): List<UserRoleView> {
        return userRoleAttributesManagerV2.getRolesForUser(userEmail)
    }
}
