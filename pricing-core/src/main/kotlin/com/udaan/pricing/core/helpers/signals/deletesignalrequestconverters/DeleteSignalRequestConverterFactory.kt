package com.udaan.pricing.core.helpers.signals.deletesignalrequestconverters

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.pricing.signaldeletion.DeleteCompSignalRequest
import com.udaan.pricing.signaldeletion.DeleteFpJitVendorPriceRequest
import com.udaan.pricing.signaldeletion.DeleteSignalRequest
import com.udaan.pricing.signaldeletion.SignalDeletionInfo

@Singleton
class DeleteSignalRequestConverterFactory @Inject constructor(
    private val deleteCompSignalRequestConverter: DeleteCompSignalRequestConverter,
    private val deleteFpJitVendorPriceRequestConverter: DeleteFpJitVendorPriceRequestConverter
) {
    suspend fun getConverterImplAndConvert(deleteSignalRequest: DeleteSignalRequest): List<SignalDeletionInfo> {
        return when (deleteSignalRequest) {
            is DeleteCompSignalRequest -> deleteCompSignalRequestConverter.convert(deleteSignalRequest)
            is DeleteFpJitVendorPriceRequest -> deleteFpJitVendorPriceRequestConverter.convert(deleteSignalRequest)
            else -> throw IllegalArgumentException("Unsupported DeleteSignalRequest type: ${deleteSignalRequest::class}")
        }
    }
}
