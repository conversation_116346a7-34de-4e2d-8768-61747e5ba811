package com.udaan.pricing.core.utils

import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.common.utils.kotlin.logger
import com.udaan.resources.RedisLettuce6Client
import io.lettuce.core.SetArgs
import kotlinx.coroutines.future.await

class CriticalSectionHandler @Inject constructor(
    private val pricingLockLettuceClient: RedisLettuce6Client
) {
    companion object {
        private val logger by logger()
    }

    suspend fun <T> executeSuspend(
        lockKey: String,
        ttlInSec: Long,
        block: suspend () -> T
    ): T {
        tryAcquireLock(lockKey, ttlInSec)
        try {
            return block()
        } finally {
            releaseLock(lockKey)
        }
    }

    private suspend fun tryAcquireLock(lockKey: String, ttlInSec: Long) {
        val lockAcquired = pricingLockLettuceClient.asyncCommands.set(lockKey, lockKey, SetArgs().nx().ex(ttlInSec))
            .await()
            .also { setResult ->
                logger.info(
                    "[tryAcquireLock] Acquired lock for lockKey: $lockKey setResult: $setResult ttlInSec: $ttlInSec"
                )
            }
        if (lockAcquired != "OK") {
            logger.info("[tryAcquireLock] Unable to acquire lock for lockKey: $lockKey already exists.")
            throw ConcurrentModificationException("Unable to acquire lock for lockKey: $lockKey")
        }
    }

    private suspend fun releaseLock(lockKey: String) {
        try {
            pricingLockLettuceClient.asyncCommands.del(lockKey).await().also { deleteResult ->
                logger.info("[releaseLock] Release lock for lockKey:$lockKey deleteResult: $deleteResult")
            }
        } catch (t: Throwable) {
            logger.error("[releaseLock] Error during removal of lockKey:$lockKey", t)
        }
    }
}
