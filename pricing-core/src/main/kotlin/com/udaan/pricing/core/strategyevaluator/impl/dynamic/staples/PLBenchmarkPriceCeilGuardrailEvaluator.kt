package com.udaan.pricing.core.strategyevaluator.impl.dynamic.staples

import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.core.utils.LadderUtils
import com.udaan.pricing.variable.VariableId
import java.math.BigDecimal

internal object PLBenchmarkListingCeilGuardrailEvaluator : Evaluator {

    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        ValidationUtils.validateInputs(data.strategy, data.inputs)
        val outputMetadata = mutableMapOf<String, String>()

        val previousOutput = data.previousOutput?.output
        require(previousOutput != null) {
            "Previous output is mandatory for Cohort adjustment evaluator"
        }
        require(previousOutput::class in listOf(BigDecimalValue::class, LadderValue::class)) {
            "Previous evaluator output for Cohort adjustment should be of BigDecimal or Ladder type"
        }

        val benchmarkListingPrice = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.BENCHMARK_LISTING_PRICE_WOT_PAISA_SET
        ) as? LadderValue

        val cohortAdjustmentBps = (VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.COHORT_ADJUSTMENT_BPS
        ) as? BigDecimalValue)?.value
            ?: throw IllegalArgumentException("COHORT_ADJUSTMENT_BPS is mandatory for adjustment markup evaluator")

        val adjustedBenchmarkListingPrice = benchmarkListingPrice?.value?.map { slabPriceBeforeMarkup ->
            val slabPricePostCohortAdjustment = slabPriceBeforeMarkup.ladderValue.multiplyWithScale(
                (BigDecimal(1) + cohortAdjustmentBps.divideWithScale(BigDecimal(10000)))
            )

            slabPriceBeforeMarkup.copy(
                ladderValue = slabPricePostCohortAdjustment
            )
        }

        outputMetadata["PL_BENCHMARK_LISTING_CEIL_GUARDRAIL_HIT"] = "false"

        val guardrailedSlabPrice = adjustedBenchmarkListingPrice?.let {
            when (previousOutput) {
                is BigDecimalValue -> {
                    val previousOutputAsLadderSlabs = listOf(
                        Ladder(
                            minQuantity = 1,
                            maxQuantity = Int.MAX_VALUE.toLong(),
                            ladderValue = previousOutput.value
                        )
                    )

                    val updatedSlabPrice = LadderUtils.compareAndCreateLadders(
                        ladderPriceInput = previousOutputAsLadderSlabs,
                        benchmarkLadderPriceInput = adjustedBenchmarkListingPrice,
                        floorGuardRailPrice = null,
                        outputMetadata = outputMetadata
                    )

                    if (previousOutputAsLadderSlabs != updatedSlabPrice.value) {
                        outputMetadata["PL_BENCHMARK_LISTING_CEIL_GUARDRAIL_HIT"] = "true"
                    }

                    updatedSlabPrice
                }

                is LadderValue -> {
                    val updatedSlabPrice = LadderUtils.compareAndCreateLadders(
                        ladderPriceInput = previousOutput.value,
                        benchmarkLadderPriceInput = adjustedBenchmarkListingPrice,
                        floorGuardRailPrice = null,
                        outputMetadata = outputMetadata
                    )

                    if (previousOutput != updatedSlabPrice) {
                        outputMetadata["PL_BENCHMARK_LISTING_CEIL_GUARDRAIL_HIT"] = "true"
                    }

                    updatedSlabPrice
                }
                else -> throw IllegalArgumentException(
                    "Expected previous output of type BigDecimalValue or " +
                            "LadderValue but got ${previousOutput::class.simpleName}"
                )
            }
        } ?: previousOutput

        return EvaluatorOutput(guardrailedSlabPrice, outputMetadata)
    }
}
