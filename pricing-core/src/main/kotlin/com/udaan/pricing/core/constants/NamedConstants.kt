package com.udaan.pricing.core.constants

object NamedConstants {

    object Caches {
        const val SIGNALS = "signals-cache"
        const val CONTRACT_CACHE = "contract-cache"
        const val MANUAL_PRICES_CACHE = "manual-prices-cache"
        const val ORG_INPUT_TERRITORY_CACHE = "org-input-territory-cache"
        const val PORTFOLIO_ITEM_CACHE = "portfolio-item-cache"
        const val TRADING_PORTFOLIO_ITEM_CACHE = "trading-portfolio-item-cache"
        const val STRATEGY_CACHE = "strategy-cache"
        const val PORTFOLIO_CACHE = "portfolio-cache"
        const val PORTFOLIO_PLAN_CACHE = "portfolio-plan-cache"
        const val BOT_MARKUP_CACHE = "bot-markup-cache"
        const val LISTING_BASE_PRICE_CACHE = "listing-base-price-cache"
        const val LISTING_GEO_DIFF_PRICE_CACHE = "listing-geo-diff-price-cache"
        const val LISTING_GEO_BASE_PRICE_CACHE = "listing-geo-base-price-cache"
        const val LISTING_MAX_PROMOTION_CACHE = "listing-max-promotion-cache"
        const val VARIABLE_CACHE = "variable-cache"
        const val DEMAND_CLUSTER_CACHE = "demand-cluster-cache"
        const val DEMAND_CLUSTER_LOCATIONS_CACHE = "demand-cluster-locations-cache"
        const val GID_CITY_TO_LISTING_CACHE = "gid_city_to_listing_cache"
    }

    object Clients {
        const val DISCOVERY_PROMOTIONS_CLIENT = "discovery-promotions-client"
        const val DISCOVERY_PROMOTIONS_CLIENT_V2 = "discovery-promotions-client-v2"
    }

    object Config {
        const val PRICING_SERVICE_CONFIG = "pricing-service-config"
    }

    object Events {
        const val PRICE_CHANGE_EVENTS = "price-change-events"
        const val SIGNALS_V2_EVENT_HUB_CLIENT = "signals-v2-event-hub-client"
    }
}
