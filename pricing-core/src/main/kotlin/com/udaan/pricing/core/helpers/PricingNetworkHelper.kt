package com.udaan.pricing.core.helpers

import com.google.inject.Inject
import com.udaan.common.client.UdaanServiceException
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.controller.network.DemandClusterController
import com.udaan.pricing.core.controller.network.DemandClusterLocationController
import com.udaan.pricing.core.controller.network.TerritoryController
import com.udaan.pricing.core.models.signals.LocationContext
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.core.utils.getSuspended
import com.udaan.pricing.network.DemandClusterLocationRes
import com.udaan.pricing.network.DemandClusterLocations
import com.udaan.pricing.network.WarehouseDetails
import com.udaan.pricing.variable.VariableId
import com.udaan.resources.cache.RedisCache2
import jakarta.inject.Named
import kotlin.collections.distinct
import kotlin.collections.filter
import kotlin.collections.map
import kotlin.text.lowercase
import kotlin.text.uppercase

class PricingNetworkHelper @Inject constructor(
    private val demandClusterController: DemandClusterController,
    private val demandClusterLocationController: DemandClusterLocationController,
    private val territoryController: TerritoryController,
    @Named(NamedConstants.Caches.ORG_INPUT_TERRITORY_CACHE)
    private val orgInputTerritoryCache: RedisCache2<Map<String, String>?>
) {
    companion object {
        private val logger by logger()
    }

    /**
     * Fetches all Whs for geo city provided as applicable for Staples Cogs (LIP or LPP).
     *
     * HACK:
     * - For Bangalore, Bangalore locations should exclude Mysore WH, while Mysore locations should
     * have all Bangalore WHs.
     * - For non-bangalore, currently there is no distinction basis geo cities between locations as
     * all locations have seller org city as geo city.
     *
     * NOTE:
     * - The above hack is present as fun is only used for LIP and LPP use cases where this applies.
     * - For any other use case, please use [fetchAllWhsForGeoCity].
     */
    suspend fun fetchStaplesCogsWhsForGeoCity(geoCity: String): List<String> {
        val geoCities = if (geoCity.equals("mysore", true)) {
            listOf(geoCity, "bangalore")
        } else listOf(geoCity)

        return geoCities.map { city ->
            getLocationsForGeoCity(city)
                .filter { it.type == LocationType.WAREHOUSE }
                .map { it.name.uppercase() }
        }.flatten()
    }


    /**
     * Fetches all warehouses for a given geo city.
     *
     * @param geoCity The geographic city name to fetch warehouses for
     * @return List of warehouse names in uppercase for the given geo city
     */
    suspend fun fetchAllWhsForGeoCity(geoCity: String): List<String> {
        return getLocationsForGeoCity(geoCity)
            .filter { it.type == LocationType.WAREHOUSE }
            .map { it.name.uppercase() }
    }

    suspend fun getLocationsForGeoCity(geoCity: String): List<DemandClusterLocations> {
        return demandClusterLocationController.getLocationsForCity(geoCity)
    }

    suspend fun fetchWarehousesForAnchorCity(anchorCity: String): Collection<String> {
        try {
            return demandClusterController.getLocationsForAnchorCity(anchorCity.lowercase()).filter {
                it.type == LocationType.WAREHOUSE
            }.map { it.name.uppercase() }.distinct()
        } catch (ex: UdaanServiceException) {
            if (ex.httpStatusCode == 404) {
                logger.error("No warehouses found for the city $anchorCity")
            }
            throw ex
        }
    }

    suspend fun getLocationDetails(locationValue: String): DemandClusterLocationRes {
        return demandClusterLocationController.getLocationDetails(locationValue)
    }

    suspend fun getAnchorCityForLocation(locationValue: String): String {
        return demandClusterController.getAnchorCityForLocation(locationValue)
    }

    suspend fun getWarehousesForCity(city: String): List<WarehouseDetails> {
        return demandClusterController.getWarehousesForAnchorCity(city)
    }


    suspend fun getWarehousesForAnchorCity(city: String): List<String> {
        return demandClusterController.getWarehousesForAnchorCity(city).map {
            it.whId
        }
    }

    suspend fun isSellerOrgCity(city: String) {
        val locationsMapped = demandClusterController.getLocationsForAnchorCity(city)
        require(locationsMapped.isNotEmpty()) { "City $city is not an anchor city" }
    }


    /**
     * This function is used to get the territoryRefIds for the given variable ids.
     * It returns only the territoryRefIds which are not null.
     * Returns null for an empty map.
     *
     */
    private suspend fun getInputTerritoryMapForBuyer(
        latitude: Double, longitude: Double, inputs: List<VariableId>
    ): Map<String, String>? {
        // convert input list to territoryType list
        val territoryTypes = VariableUtils.territoryTypesForVariableIds(inputs).toSet()
        // fetch input to territoryRefId mapping
        val inputToTerritoryRefId = territoryController.getTerritoriesForLatLong(
            latitude = latitude,
            longitude = longitude,
            territoryTypes = territoryTypes
        )
        // filter out null values + convert keys to String + return null if empty map
        return inputToTerritoryRefId.filterValues { it != null }
            .mapValues { it.value as String }
            .mapKeys { it.key.toString() }
            .takeIf { it.isNotEmpty() }
    }

    private fun deriveTerritoryInputCacheKey(inputs: List<VariableId>, orgUnitId: String): String {
        return "$orgUnitId::${
            inputs.map { it.toString().uppercase() }
                .sorted()
                .joinToString(separator = "::")
        }"
    }

    /**
     * This function is a cache wrapper over [getInputTerritoryMapForBuyer].
     * It derives cacheKey from orgUnitId and inputs passed.
     * @todo - rethink caching at pricing nw end and remove from here.
     *
     */
    suspend fun getCachedInputTerritoryMapForBuyer(
        latitude: Double, longitude: Double, inputs: List<VariableId>, orgUnitId: String
    ): Map<String, String>? {
        if (inputs.isEmpty()) return null
        val cacheKey = deriveTerritoryInputCacheKey(inputs, orgUnitId)
        return orgInputTerritoryCache.getSuspended(cacheKey) {
            getInputTerritoryMapForBuyer(
                latitude = latitude,
                longitude = longitude,
                inputs = inputs
            )
        }
    }

    suspend fun getLocationContext(
        location: Location,
        territoryMap: Map<VariableId, String>?
    ): LocationContext {
        return when (location.locationType) {
            LocationType.CLUSTER, LocationType.WAREHOUSE -> {
                val locationDetails = getLocationDetails(location.locationValue)

                LocationContext(
                    locationType = location.locationType,
                    locationValue = location.locationValue.uppercase(),
                    city = locationDetails.city.uppercase(),
                    sellerOrgCity = locationDetails.anchorCityName.uppercase(),
                    mfcWarehouses = filterMfcWarehouses(locationDetails.fulfilmentCenters),
                    anchorWarehouses = filterAnchorWarehouses(locationDetails.fulfilmentCenters),
                    territoryMap = territoryMap
                )
            }

            LocationType.CITY -> {
                val warehouseDetails = getWarehousesForCity(location.locationValue)

                LocationContext(
                    locationType = location.locationType,
                    locationValue = location.locationValue.uppercase(),
                    city = location.locationValue.uppercase(),
                    sellerOrgCity = location.locationValue.uppercase(),
                    mfcWarehouses = filterMfcWarehouses(warehouseDetails),
                    anchorWarehouses = filterAnchorWarehouses(warehouseDetails),
                    territoryMap = territoryMap
                )
            }

            else -> throw IllegalArgumentException("Can't derive location context for type ${location.locationType}")
        }
    }

    private fun filterMfcWarehouses(warehouses: List<WarehouseDetails>): List<String> {
        return warehouses.filter {
            it.type != "ANCHOR"
        }.map { it.whId.uppercase() }
    }

    private fun filterAnchorWarehouses(warehouses: List<WarehouseDetails>): List<String> {
        return warehouses.filter {
            it.type == "ANCHOR"
        }.map { it.whId.uppercase() }
    }

    suspend fun getAnchorWhDetailsFromLoc(
        locationType: LocationType,
        locationValue: String
    ): WarehouseDetails? {
        return when (locationType) {
            LocationType.CITY -> {
                // handling both Mysore (non sellerOrg city) and Bangalore (sellerOrg city) kinda cases
                val associatedLocation = demandClusterLocationController.getLocationsForCity(locationValue)
                associatedLocation.firstOrNull()?.fulfilmentCenters?.first {
                    it.type == "ANCHOR"
                }
            }
            LocationType.CLUSTER, LocationType.WAREHOUSE -> {
                demandClusterLocationController.getWarehousesForLocation(locationValue).first {
                    it.type == "ANCHOR"
                }
            }
            else -> {
                null
            }
        }
    }

}
