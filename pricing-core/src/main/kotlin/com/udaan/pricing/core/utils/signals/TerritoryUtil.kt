package com.udaan.pricing.core.utils.signals

import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.network.TerritoryType


object TerritoryUtil {

    /**
     * Maps the territory keys to their corresponding variable IDs.
     *
     * @throws IllegalArgumentException if key doesn't match any territoryType or applicable variableId not found.
     *
     * Note: This fun assumes any new TerritoryType creation will be added to this function before flowing to prod.
     */
    fun mapTerritoryKeysToVariableIds(territoryMap: Map<String, String>): Map<VariableId, String> {
        return territoryMap.mapKeys { entry ->
            val territoryType = TerritoryType.valueOf(entry.key)
            when (territoryType) {
                TerritoryType.JUMBOTAIL -> VariableId.JUMBOTAIL_COMP_LADDER_PRICE_WT_PAISA_UNIT
                else -> throw IllegalArgumentException("Unsupported territory type: $territoryType")
            }
        }
    }
}