package com.udaan.pricing.core.helpers.rider.impl.bot

import com.fasterxml.jackson.databind.MappingIterator
import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.server.getSync
import java.time.Duration

/** Not USED **/
@Singleton
class BotMarkupStore @Inject constructor(private val loader: BotMarkupDataloaderBase) {
    private val markupDB by periodically<BotMarkupLimitDB>(Duration.ofHours(12)) {
        loader.loadData() ?: BotMarkupLimitDB.EMPTY
    }

    fun getMinMaxMarkupBps(vertical: String): BotMarkupBpsLimit? {
        val db = markupDB.getSync()
        val response = db.table[vertical] ?: db.table[DEFAULT_ID]
        return response?.let {
            val min = it.minMarkupBps
            val max = it.maxMarkupBps

            if (min == null && max == null) {
                null
            } else {
                it
            }
        }
    }
}

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy::class)
data class BotMarkupBpsLimit(
    val id: String,
    val idType: String,
    val minMarkupBps: Int?,
    val maxMarkupBps: Int?
)

data class BotMarkupLimitDB(
    val table: Map<String, BotMarkupBpsLimit> = mapOf()
) {
    companion object {
        val EMPTY = BotMarkupLimitDB()
    }
}

private const val DEFAULT_ID = "default"

private object MarkupDataTransformer : DataTransformer<BotMarkupBpsLimit, BotMarkupLimitDB> {
    override fun transform(data: MappingIterator<BotMarkupBpsLimit>): BotMarkupLimitDB {
        val map = data.asSequence().associateBy { it.id }
        return BotMarkupLimitDB(map)
    }
}

typealias BotMarkupDataloaderBase = GenericDataLoader<BotMarkupLimitDB>

internal class CSVBotMarkupDataLoader(private val resourceName: String) : BotMarkupDataloaderBase by CSVLoader(
    resourceName = resourceName,
    clazz = BotMarkupBpsLimit::class,
    transformer = MarkupDataTransformer
)

class DPBotMarkupDataLoader : BotMarkupDataloaderBase by DataPlatformLoader(
    probeId = bot_markup_csv_rawtable,
    clazz = BotMarkupBpsLimit::class,
    transformer = MarkupDataTransformer
)

private const val bot_markup_csv_rawtable = "d5aizl"
