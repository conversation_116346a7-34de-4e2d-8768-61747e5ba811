package com.udaan.pricing.core.helpers.pricingstrategy

import com.google.inject.Inject
import com.udaan.pricing.ContextualPriceRequest
import com.udaan.pricing.PriceForListing
import com.udaan.pricing.core.helpers.pricingstrategy.impl.ContractualPrice
import com.udaan.pricing.core.helpers.pricingstrategy.impl.DefaultPrice
import com.udaan.pricing.core.helpers.pricingstrategy.impl.GeoLocationBasePriceStrategy
import com.udaan.pricing.core.helpers.pricingstrategy.impl.ManualPriceStrategy
import com.udaan.pricing.core.helpers.pricingstrategy.impl.SSCPrice
import com.udaan.pricing.core.models.CatalogEntityContext
import com.udaan.proto.models.ModelV1
import kotlinx.coroutines.Deferred

enum class PricingStrategy(private val priority: Int) {
    CONTRACT(0),
    MANUAL(1),
    SSC(2),
    LOCATION(3),
    BASE(4);

    fun getPriority() = this.priority
}

abstract class Strategy {

    abstract suspend fun getPrice(
        catalogEntityContext: CatalogEntityContext,
        contextualPriceRequest: ContextualPriceRequest?,
        fetchInactive: Boolean,
        cluster: List<String>? = null,
        preferredWarehouseId: String?,
        servingWarehouseId: String?,
        buyerOrgUnit: ModelV1.OrgUnit? = null,
        mappedBenchmarkListingGuardrailPriceDeferred: Deferred<PriceForListing?>?
    ): List<PriceForListing>
}

class StrategyFactory @Inject constructor(
    private val contractualPrice: ContractualPrice,
    private val manualPriceStrategy: ManualPriceStrategy,
    private val defaultPrice: DefaultPrice,
    private val geoLocationBasePriceStrategy: GeoLocationBasePriceStrategy,
    private val sscPrice: SSCPrice
) {
    fun getAppropriateStrategy(pricingStrategy: PricingStrategy): Strategy {
        return when (pricingStrategy) {
            PricingStrategy.CONTRACT -> contractualPrice
            PricingStrategy.MANUAL -> manualPriceStrategy
            PricingStrategy.LOCATION -> geoLocationBasePriceStrategy
            PricingStrategy.BASE -> defaultPrice
            PricingStrategy.SSC -> sscPrice
        }
    }
}

