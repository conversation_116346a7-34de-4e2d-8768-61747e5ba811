package com.udaan.pricing.core.dao.automation

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.core.constants.CosmosDbConfig
import com.udaan.pricing.strategy.Strategy
import com.udaan.pricing.strategy.StrategyListDTO
import com.udaan.pricing.strategy.StrategyState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.withContext

@Singleton
class StrategyRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {

    private val strategyCosmosDao by lazy {
        CosmosDbDao(
            configKey = "pricing",
            databaseName = CosmosDbConfig.PRICING_AUTOMATION_DB,
            containerName = CosmosDbConfig.STRATEGY_CONTAINER
        ) { builder ->
            builder.connectionSharingAcrossClientsEnabled(true)
        }
    }

    /**
     * Dummy function solely used for client initialisation while starting the service
     */
    suspend fun initialise() {
        strategyCosmosDao.findItem("ID1")
    }

    suspend fun createStrategy(strategy: Strategy): Strategy? {
        return strategyCosmosDao.createItem(strategy.toDocument()).toStrategy()
    }

    suspend fun updateStrategy(strategy: Strategy): Strategy? {
        return strategyCosmosDao.createOrUpdateItem(strategy.toDocument()).toStrategy()
    }

    suspend fun getStrategiesForStates(states: List<StrategyState>): List<StrategyListDTO> {
        return withContext(Dispatchers.IO) {
            strategyCosmosDao.queryItems(
                queryName = "get-strategies-for-states",
                querySpec = makeSqlQuerySpec(
                    """
                    select c.id, c.name, c.state from c where 
                    ARRAY_CONTAINS(@strategyStates, c.state)
                """.trimIndent(),
                    "@strategyStates" to states
                )
            ).toList().map { it.toStrategyListDTO() }
        }
    }

    suspend fun getActiveStrategies(): List<Strategy> {
        return withContext(Dispatchers.IO) {
            strategyCosmosDao.queryItems(
                queryName = "get-active-strategies",
                querySpec = makeSqlQuerySpec(
                    """
                    select * from c where 
                    ARRAY_CONTAINS(@strategyStates, c.state)
                """.trimIndent(),
                    "@strategyStates" to listOf(StrategyState.ACTIVE)
                )
            ).toList().map { it.toStrategy() }
        }
    }

    suspend fun getNonDeletedStrategyByName(strategyName: String): Strategy? {
        return withContext(Dispatchers.IO) {
            strategyCosmosDao.queryItems(
                queryName = "get-non-deleted-strategy-by-name",
                querySpec = makeSqlQuerySpec(
                    """
                    select * from c where c.name = @strategyName and
                    c.state != @strategyState
                """.trimIndent(),
                    "@strategyName" to strategyName,
                    "@strategyState" to StrategyState.DELETED
                )
            ).toList().map { it.toStrategy() }.firstOrNull()
        }
    }

    suspend fun getStrategyById(id: String): Strategy? {
        return withContext(Dispatchers.IO) {
            strategyCosmosDao.getItem(id, id)?.toStrategy()
        }
    }

    private fun Strategy.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toStrategy() = objectMapper.convertValue(this, Strategy::class.java)
    private fun ObjectNode.toStrategyListDTO() = objectMapper.convertValue(this, StrategyListDTO::class.java)
}