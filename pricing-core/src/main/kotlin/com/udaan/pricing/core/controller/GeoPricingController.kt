package com.udaan.pricing.core.controller

import com.google.inject.Inject
import com.udaan.pricing.*
import com.udaan.common.loc.PincodeCityDataset
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.core.controller.cache.GeoDifferentialPriceCacheController
import com.udaan.pricing.core.dao.GeoPriceRepository
import com.udaan.pricing.core.dao.filterRecords
import com.udaan.pricing.core.dao.regionsMasterList
import com.udaan.pricing.core.dao.sortByPolicy
import com.udaan.pricing.core.dao.validate
import com.udaan.pricing.core.utils.generateId
import com.udaan.vertical.client.cache.CentralVerticalCache
import javax.ws.rs.NotFoundException

class GeoPricingController @Inject constructor(
    private val geoDifferentialPriceCacheController: GeoDifferentialPriceCacheController,
    private val geoPriceRepository: GeoPriceRepository,
    private val pincodeDataset: PincodeCityDataset,
    private val centralVerticalCache: CentralVerticalCache
) {
    companion object {
        private val logger by logger()
    }

    suspend fun upsertGeoPricing(geoPricingReq: GeoPricingUpsertReq): GeoPricing {
        return createOrUpdateGeoAdmin(
            GeoPricingReq(
                id = geoPricingReq.id,
                orgId = geoPricingReq.orgId,
                vertical = geoPricingReq.vertical,
                listingId = geoPricingReq.listingId,
                salesUnitId = geoPricingReq.salesUnitId,
                geoTypeId = geoPricingReq.geoTypeId.uppercase(),
                priceValue = geoPricingReq.priceValue,
                priceType = geoPricingReq.priceType,
                state = geoPricingReq.state,
                geoType = geoPricingReq.geoType,
                additive = geoPricingReq.additive
            )
        )!!
    }


    suspend fun createGeoPricing(geoPricingReq: GeoPricingCreateReq): GeoPricing {
        return createOrUpdateGeoAdmin(
            GeoPricingReq(
                id = null,
                orgId = geoPricingReq.orgId,
                vertical = geoPricingReq.vertical,
                listingId = geoPricingReq.listingId,
                salesUnitId = geoPricingReq.salesUnitId,
                geoTypeId = geoPricingReq.geoTypeId.uppercase(),
                priceValue = geoPricingReq.priceValue,
                priceType = geoPricingReq.priceType,
                state = GeoPricingState.ACTIVE,
                geoType = geoPricingReq.geoType,
                additive = geoPricingReq.additive
            )
        )!!
    }

    suspend fun updateGeoPricing(geoPricingReq: GeoPricingUpdateReq): GeoPricing {

        val currentRecord = geoPriceRepository.getGeoPricingById(geoPricingReq.id, geoPricingReq.orgId)
            ?: throw NotFoundException("No record found for id: ${geoPricingReq.id}")

        return geoPriceRepository.createGeoPricing(
            currentRecord.copy(
                priceType = geoPricingReq.priceType,
                priceValue = geoPricingReq.priceValue,
                state = geoPricingReq.state,
                additive = currentRecord.additive
            )
        )!!.also {
            // this will invalidate only listing level geo differentials
            geoDifferentialPriceCacheController.invalidateGeoDiffForListing(it.listingId)
        }
    }

    private suspend fun createOrUpdateGeoAdmin(geoPricingReq: GeoPricingReq): GeoPricing? {
        val data = geoPriceRepository.getFilteredPricingAdmin(geoPricingReq)
        return if (data.isEmpty()) {
            geoPriceRepository.createGeoPricing(geoPricingReq.convert())
        } else {
            geoPriceRepository.createGeoPricing(
                data[0].copy(
                    priceType = geoPricingReq.priceType,
                    priceValue = geoPricingReq.priceValue,
                    state = geoPricingReq.state,
                    additive = geoPricingReq.additive
                )
            )
        }.also {
            // this will invalidate only listing level geo differentials
            geoDifferentialPriceCacheController.invalidateGeoDiffForListing(it?.listingId)
        }
    }


    suspend fun getGeoPricing(fetchGeoPricingRequest: FetchGeoPricingRequest): List<GeoPricing> {

        val pinCodeLookupResult = pincodeDataset.lookup(fetchGeoPricingRequest.pincode)

        if (pinCodeLookupResult?.state == null) {
            throw Exception("state missing for pincode: ${fetchGeoPricingRequest.pincode}")
        }

        val geoTypeList = mutableListOf(
            pinCodeLookupResult.city.uppercase(),
            pinCodeLookupResult.state.uppercase()
        )

        if (fetchGeoPricingRequest.cluster !=null){
            fetchGeoPricingRequest.cluster!!.map {
                geoTypeList.add(it)
            }
        }

        val applicableRegionList = regionsMasterList.filter {
            it.listOfStates.any {
                st -> st ==  pinCodeLookupResult.state
            }
        }.map {
            it.id
        }

        if (applicableRegionList.isNotEmpty()) {
            geoTypeList.add(applicableRegionList.first())
        }

        val allActiveGeoDifferentialPrices = geoDifferentialPriceCacheController.getActiveGeoDiffPriceForListing(fetchGeoPricingRequest)

        val filteredGeoPriceByGeoLocation = allActiveGeoDifferentialPrices.filter { activeGeoDifferentialPrice ->
            geoTypeList.any { activeGeoDifferentialPrice.geoTypeId.equals(it, ignoreCase = true) }
        }
        return filteredGeoPriceByGeoLocation.filterRecords(fetchGeoPricingRequest.vertical).sortByPolicy()
    }

    suspend fun getNegativeGeoForListingAndSu(
        listingId: String,
        salesUnit: String,
        orgId: String
    ): List<GeoPricing> {
        val activeGeoDifferentialsForListing = geoDifferentialPriceCacheController.getActiveGeoDiffPriceForListing(
            fetchGeoPricingRequest = FetchGeoPricingRequest(
                orgId = orgId,
                listingId = listingId,
                vertical = centralVerticalCache.getVerticalForListing2(listingId)?.name?.lowercase(),
                pincode = "", // this field is not used while fetching data from db
                state = listOf(GeoPricingState.ACTIVE)
           )
        )

        val activeNegativeGeoDiffForListingSalesUnit = activeGeoDifferentialsForListing.filter {
            (it.salesUnitId == salesUnit || it.salesUnitId.isNullOrEmpty()) &&
                    (!it.additive || it.priceValue < 0)
        }
        return activeNegativeGeoDiffForListingSalesUnit
    }

    suspend fun getGeoPricingByListingAndSU(
        listingId: String,
        salesUnit: String,
        orgId: String
    ): List<GeoPricing> {
        val activeGeoDifferentialsForListing = geoDifferentialPriceCacheController.getActiveGeoDiffPriceForListing(
            fetchGeoPricingRequest = FetchGeoPricingRequest(
                orgId = orgId,
                listingId = listingId,
                vertical = centralVerticalCache.getVerticalForListing2(listingId)?.name?.lowercase(),
                pincode = "", // this field is not used while fetching data from db
                state = listOf(GeoPricingState.ACTIVE)
            )
        )

        return activeGeoDifferentialsForListing.filter {
            it.salesUnitId == salesUnit || it.salesUnitId.isNullOrEmpty()
        }
    }

    suspend fun queryGeoPricing(queryGeoPricingReq: QueryGeoPricingReq):List<GeoPricing>{
        queryGeoPricingReq.validate()
        return geoPriceRepository.queryGeoPricing(queryGeoPricingReq)
    }

    suspend fun getGeoPricingByIds(sellerOrgId: String, geoPriceIds: String): List<GeoPricing> {
        return geoPriceIds
            .split(",")
            .mapNotNull {
                geoPriceRepository.getGeoPricingById(it, sellerOrgId)
            }
    }

    fun GeoPricingReq.convert() = GeoPricing(
            generateId("GP"),
            this.orgId,
            this.vertical,
            this.listingId,
            this.salesUnitId,
            this.geoTypeId,
            this.geoType,
            this.priceType,
            this.priceValue,
            this.state,
            this.additive
    )

}
