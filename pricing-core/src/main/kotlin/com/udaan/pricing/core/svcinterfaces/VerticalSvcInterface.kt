package com.udaan.pricing.core.svcinterfaces

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.catalog.client.CategoryTreeV2
import com.udaan.catalog.model.VerticalCategory
import com.udaan.vertical.client.VerticalServiceClient
import com.udaan.vertical.client.cache.CentralVerticalCache
import com.udaan.vertical.model.CentralVertical
import javax.ws.rs.NotFoundException

@Singleton
class VerticalSvcInterface @Inject constructor(
    private val centralVerticalCache: CentralVerticalCache,
    private val categoryTreeV2: CategoryTreeV2,
    private val verticalServiceClient: VerticalServiceClient
) {
    suspend fun getVerticalForLid(listingId: String): CentralVertical {
        return centralVerticalCache.getVerticalForListing2(listingId)
            ?: throw NotFoundException("Vertical not found for listing $listingId")
    }

    suspend fun getVerticalCategoryForLid(listingId: String): VerticalCategory {
        val vertical = getVerticalForLid(listingId)
        return categoryTreeV2.getVerticalCategory(vertical.name)
    }

    suspend fun fetchVerticalIdFromListingId(listingId: String): String {
        return getVerticalForLid(listingId).name.lowercase()
    }

    suspend fun getFpVertical(fpVertical: String): CentralVertical {
        return verticalServiceClient.getFPVertical(fpVertical, "ACTIVE").executeAwait(1)
    }
}
