package com.udaan.pricing.core.dao.automation

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.pricing.core.constants.CosmosDbConfig
import com.udaan.pricing.portfolioitem.TradingPortfolioItem

@Singleton
class TradingPortfolioItemRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {
    private val cosmosDbDao by lazy {
        CosmosDbDao(
            configKey = "pricing",
            databaseName = CosmosDbConfig.PRICING_AUTOMATION_DB,
            containerName = CosmosDbConfig.TRADING_PORTFOLIO_ITEM_CONTAINER
        ) { builder ->
            builder.connectionSharingAcrossClientsEnabled(true)
        }
    }

    /**
     * Dummy function solely used for client initialisation while starting the service
     */
    suspend fun initialise() {
        cosmosDbDao.findItem("ID1")
    }

    suspend fun createOrUpdate(tradingPortfolioItem: TradingPortfolioItem): TradingPortfolioItem {
        return cosmosDbDao.createOrUpdateItem(tradingPortfolioItem.toDocument()).toTradingPortfolioItem()
    }

    suspend fun update(tradingPortfolioItem: TradingPortfolioItem): TradingPortfolioItem {
        return cosmosDbDao.updateItem(tradingPortfolioItem.toDocument()).toTradingPortfolioItem()
    }

    suspend fun getByIdAndPartitionKey(
        id: String,
        partitionKey: String
    ): TradingPortfolioItem? {
        return cosmosDbDao.getItem(id, partitionKey)?.toTradingPortfolioItem()
    }

    private fun TradingPortfolioItem.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toTradingPortfolioItem() = objectMapper.convertValue(this, TradingPortfolioItem::class.java)

}