package com.udaan.pricing.core.strategyevaluator.impl.dynamic.staples

import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.core.strategyevaluator.abstraction.Evaluator
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.toCustomString
import com.udaan.pricing.core.utils.LadderUtils
import com.udaan.pricing.core.utils.ValidationUtils
import com.udaan.pricing.core.utils.VariableUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.variable.VariableId
import java.math.BigDecimal

internal object LipGuardrailEvaluator : Evaluator {

    override fun evaluate(data: EvaluatorRequestContext): EvaluatorOutput {
        val outputMetadata = mutableMapOf<String, String>()
        ValidationUtils.validateInputs(data.strategy, data.inputs)
        val previousOutput = data.previousOutput?.output
        val staplesLipInPaisa = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.STAPLES_LIP_WOT_PAISA_UNIT
        ) as? BigDecimalValue

        val conversionRate = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.CONVERSION_RATE
        ) as BigDecimalValue

        val staplesLip = staplesLipInPaisa?.value?.multiplyWithScale(conversionRate.value)

        val staplesLipFloorBps = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.LIP_FLOOR_GUARDRAIL_BPS
        ) as? BigDecimalValue

        val staplesCeilFloorBps = VariableUtils.getApplicableVariableValueForVariable(
            data.inputs,
            VariableId.LIP_CEIL_GUARDRAIL_BPS
        ) as? BigDecimalValue

        val floorGuardrailPrice = if (staplesLipFloorBps != null && staplesLip != null) {
            staplesLip.multiplyWithScale(
                BigDecimal(1).plus(
                    staplesLipFloorBps.value.divideWithScale(BigDecimal(10000))
                )
            ).divideWithScale(BigDecimal(1))
        } else {
            null
        }
        outputMetadata["FLOOR_GUARDRAIL_PRICE"] = floorGuardrailPrice.toCustomString()
        outputMetadata["FLOOR_GUARDRAIL_HIT"] = "false"

        val ceilGuardrailPrice = if (staplesCeilFloorBps != null && staplesLip != null) {
            staplesLip.multiplyWithScale(
                BigDecimal(1).plus(
                    staplesCeilFloorBps.value.divideWithScale(BigDecimal(10000))
                )
            ).divideWithScale(BigDecimal(1))
        } else {
            null
        }
        outputMetadata["CEIL_GUARDRAIL_PRICE"] = ceilGuardrailPrice.toCustomString()
        outputMetadata["CEIL_GUARDRAIL_HIT"] = "false"

        val guardRailedPrice = when (previousOutput) {
            is BigDecimalValue -> {
                if (floorGuardrailPrice != null && previousOutput.value < floorGuardrailPrice) {
                    outputMetadata["FLOOR_GUARDRAIL_HIT"] = "true"
                    outputMetadata["GUARD_RAILED_TO_FLOOR_PRICE"] = floorGuardrailPrice.toCustomString()
                    BigDecimalValue(floorGuardrailPrice)
                } else if (ceilGuardrailPrice != null && previousOutput.value > ceilGuardrailPrice) {
                    outputMetadata["CEIL_GUARDRAIL_HIT"] = "true"
                    outputMetadata["GUARD_RAILED_TO_CEIL_PRICE"] = ceilGuardrailPrice.toCustomString()
                    BigDecimalValue(ceilGuardrailPrice)
                } else {
                    previousOutput
                }
            }
            is LadderValue -> {
                val updatedLadders = previousOutput.value.mapIndexed { index, ladder ->
                    val overridedLadderValue =
                        if (floorGuardrailPrice != null && ladder.ladderValue < floorGuardrailPrice) {
                            outputMetadata["FLOOR_GUARDRAIL_HIT"] = "true"
                            outputMetadata["$index" + "_LADDER_GUARD_RAILED_TO_FLOOR_PRICE"] =
                                floorGuardrailPrice.toCustomString()
                            floorGuardrailPrice
                        } else if (ceilGuardrailPrice != null && ladder.ladderValue > ceilGuardrailPrice) {
                            outputMetadata["CEIL_GUARDRAIL_HIT"] = "true"
                            outputMetadata["$index" + "_LADDER_GUARD_RAILED_TO_CEIL_PRICE"] =
                                ceilGuardrailPrice.toCustomString()
                            ceilGuardrailPrice
                        } else {
                            ladder.ladderValue
                        }
                    ladder.copy(
                        ladderValue = overridedLadderValue
                    )
                }
                LadderUtils.mergeLaddersWithSimilarValue(LadderValue(updatedLadders))
            }
            else -> {
                throw IllegalArgumentException("Expected previous output of type BigDecimalValue or " +
                        "LadderValue but got ${previousOutput!!::class.simpleName}")
            }
        }

        return EvaluatorOutput(guardRailedPrice, outputMetadata)
    }
}
