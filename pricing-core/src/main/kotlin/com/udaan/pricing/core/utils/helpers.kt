package com.udaan.pricing.core.utils

import com.udaan.common.utils.idgen.UUIDUtils
import com.udaan.instrumentation.TelemetryScope
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.future.await

fun generateId(s: String): String = UUIDUtils.uuidBase32(s)


internal suspend fun <V> RedisCache2<V>.getSuspended(
    k: String,
    suspendableLoader: suspend (String) -> V
): V? {
    val loader = { k: String ->
        TelemetryScope.future {
            suspendableLoader(k)
        }
    }
    return get(k, loader).await()
}

