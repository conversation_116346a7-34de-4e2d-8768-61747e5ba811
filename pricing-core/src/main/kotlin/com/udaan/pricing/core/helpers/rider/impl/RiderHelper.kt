package com.udaan.pricing.core.helpers.rider.impl

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.instrumentation.Telemetry
import com.udaan.pricing.BasicPrice
import com.udaan.pricing.ContextualPriceRequest
import com.udaan.pricing.PriceForListing
import com.udaan.pricing.PriceInPaisa
import com.udaan.pricing.PriceRiderForListing
import com.udaan.pricing.PricingAudit
import com.udaan.pricing.QtyBasedPrice
import com.udaan.pricing.core.controller.PriceAuditController
import com.udaan.pricing.core.helpers.pricingstrategy.PricingStrategy
import com.udaan.pricing.core.helpers.rider.PriceRider
import com.udaan.pricing.core.models.CatalogEntityContext
import com.udaan.pricing.core.models.common.SelectedListingSalesUnitPrice
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.plus
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.roundToLong
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.toBigDecimalDivConst
import com.udaan.pricing.core.utils.generateId
import com.udaan.vertical.model.CentralVertical
import java.math.BigDecimal

@Singleton
class RiderHelper @Inject constructor(
    private val priceAuditController: PriceAuditController
) {

    companion object {
        private const val BPS_DIVISOR = 10000

        private val priceTypeToApplicableRidersMap = mapOf(
            PricingStrategy.CONTRACT to emptyList(),

            PricingStrategy.MANUAL to listOf(
                PriceRider.GEO_PRICE_RIDER,
                PriceRider.LISTING_DISCOUNT_RIDER
            ),

            PricingStrategy.SSC to listOf(
                PriceRider.GEO_PRICE_RIDER,
                PriceRider.LISTING_DISCOUNT_RIDER
            ),

            PricingStrategy.LOCATION to listOf(
                PriceRider.GEO_PRICE_RIDER,
                PriceRider.LISTING_DISCOUNT_RIDER,
                PriceRider.CREDIT_PRICE_RIDER
            ),

            PricingStrategy.BASE to listOf(
                PriceRider.GEO_PRICE_RIDER,
                PriceRider.LISTING_DISCOUNT_RIDER,
                PriceRider.BOT_PRICE_RIDER,
                PriceRider.CREDIT_PRICE_RIDER,
                PriceRider.SUPERCLUB_DISCOUNT_RIDER
            )
        )

        private val priceStrategyToStrategyRefMap = mapOf(
            PricingStrategy.CONTRACT.name to "CONTRACT",
            PricingStrategy.MANUAL.name to "MANUAL_WITH_RIDER",
            PricingStrategy.SSC.name to "SSC_WITH_RIDER",
            PricingStrategy.LOCATION.name to "GEO_BASE_WITH_RIDER",
            PricingStrategy.BASE.name to "BASE_WITH_RIDER"
        )
    }

    /**
     * This function is used to get the final prices with riders.
     * It takes a list of selected prices and a list of riders with adjustments.
     * The function is currently empty and needs to be implemented.
     *
     * @param contextualPriceRequest Contextual price request
     * @param catalogEntityContext Catalog entity context
     * @param finalSelectedListingSalesUnitPrices List of selected prices for different salesUnitIds
     * @param nonNullRidersWithAdjustments List of riders with adjustments
     */
    fun getFinalPricesWithRiders(
        contextualPriceRequest: ContextualPriceRequest?,
        catalogEntityContext: CatalogEntityContext,
        finalSelectedListingSalesUnitPrices: List<SelectedListingSalesUnitPrice>,
        nonNullRidersWithAdjustments: List<Pair<PriceRider, PriceRiderForListing>>,
        orderReadyForCheckout: Boolean
    ): List<PriceForListing> {
        return finalSelectedListingSalesUnitPrices.map { selectedPriceForListing ->
            val applicableRiders = priceTypeToApplicableRidersMap[selectedPriceForListing.strategy]!!
            val applicableRiderWithAdjustments = nonNullRidersWithAdjustments.filter { it.first in applicableRiders }

            val finalPricesWithRiders = getRiderAdjustedFinalPrices(
                priceForListing = selectedPriceForListing.priceForListing,
                applicableRiderWithAdjustments = applicableRiderWithAdjustments,
                vertical = catalogEntityContext.vertical
            )

            trackFinalPriceInfo(
                contextualPriceRequest = contextualPriceRequest,
                catalogEntityContext = catalogEntityContext,
                finalPricesWithRiders = finalPricesWithRiders,
                applicableRiderWithAdjustments = applicableRiderWithAdjustments
            )

            pushPriceAudit(
                finalPricesWithRiders = finalPricesWithRiders,
                orderReadyForCheckout = orderReadyForCheckout
            )
        }
    }

    private fun modifyLadder(
        priceForListing: PriceForListing,
        riderPair: Pair<PriceRider, PriceRiderForListing>?,
        riderWithAdjustments: List<Pair<PriceRider, PriceRiderForListing>>
    ): PriceForListing {
        val newPriceRiders = riderWithAdjustments.map { it.second }
        return if (riderPair != null) {
            if (priceForListing.prices.isEmpty()) {
                priceForListing
            } else {
                val finalLadder = priceForListing.prices.last()

                if (finalLadder.minQty > riderPair.second.ladderBreakerMoq!!) {
                    priceForListing
                } else {
                    val otherLadders = priceForListing.prices.dropLast(1)

                    val newOtherLadders = otherLadders.map { qtyBasedPriceLadder ->
                        val newPriceInPaise = qtyBasedPriceLadder.priceInPaisa.copy(priceRiders = newPriceRiders)
                        val newKgPrice = qtyBasedPriceLadder.pricePerKgInPaisa?.copy(priceRiders = newPriceRiders)
                        qtyBasedPriceLadder.copy(priceInPaisa = newPriceInPaise, pricePerKgInPaisa = newKgPrice)
                    }

                    val newLadderDiscount = -riderPair.second.bpsInPercentage
                        .times(finalLadder.priceInPaisa.defaultPrice)
                        .div(10000)

                    val newLadderPricePerKg = if (finalLadder.pricePerKgInPaisa != null) {
                        PriceInPaisa(
                            finalLadder.pricePerKgInPaisa!!.defaultPrice + newLadderDiscount,
                            finalLadder.pricePerKgInPaisa!!.defaultPrice + newLadderDiscount,
                            finalLadder.pricePerKgInPaisa!!.defaultPrice + newLadderDiscount,
                            BasicPrice(
                                finalLadder.pricePerKgInPaisa!!.defaultPrice + newLadderDiscount,
                                finalLadder.pricePerKgInPaisa!!.defaultPrice + newLadderDiscount,
                                finalLadder.pricePerKgInPaisa!!.defaultPrice + newLadderDiscount
                            ), finalLadder.pricePerKgInPaisa!!.priceRiders
                        )
                    } else {
                        null
                    }

                    val newLadderPrice = PriceInPaisa(
                        finalLadder.priceInPaisa.defaultPrice + newLadderDiscount,
                        finalLadder.priceInPaisa.defaultPrice + newLadderDiscount,
                        finalLadder.priceInPaisa.defaultPrice + newLadderDiscount,
                        BasicPrice(
                            finalLadder.priceInPaisa.defaultPrice + newLadderDiscount,
                            finalLadder.priceInPaisa.defaultPrice + newLadderDiscount,
                            finalLadder.priceInPaisa.defaultPrice + newLadderDiscount
                        ), newPriceRiders
                    )

                    val newLadder = QtyBasedPrice(
                        minQty = riderPair.second.ladderBreakerMoq!!,
                        maxQty = Int.MAX_VALUE,
                        priceInPaisa = newLadderPrice,
                        pricePerKgInPaisa = newLadderPricePerKg,
                        taxableAmountPaise = finalLadder.taxableAmountPaise,
                        packagingUnit = finalLadder.packagingUnit,
                        bulkLadder = true
                    )

                    val finalLadderNewPriceInPaise = finalLadder.priceInPaisa.copy(priceRiders = newPriceRiders)
                    val finalLadderNewKgPrice = finalLadder.pricePerKgInPaisa?.copy(priceRiders = newPriceRiders)
                    val modifiedPrice = if (finalLadder.minQty == riderPair.second.ladderBreakerMoq!!) {
                        priceForListing.copy(prices = newOtherLadders + newLadder)
                    } else {
                        val modifiedLadder = finalLadder.copy(
                            maxQty = riderPair.second.ladderBreakerMoq!! - 1,
                            priceInPaisa = finalLadderNewPriceInPaise,
                            pricePerKgInPaisa = finalLadderNewKgPrice
                        )
                        priceForListing.copy(prices = newOtherLadders + modifiedLadder + newLadder)
                    }

                    modifiedPrice
                }
            }
        } else {
            priceForListing
        }
    }

    private fun getRiderAdjustedFinalPrices(
        priceForListing: PriceForListing,
        applicableRiderWithAdjustments: List<Pair<PriceRider, PriceRiderForListing>>,
        vertical: CentralVertical?
    ): PriceForListing {
        // Dropping ladder based promotion as they don't need to be applied as riders
        val newFilteredRidersWithAdjustments = applicableRiderWithAdjustments.dropWhile {
            it.second.ladderBreakerMoq != null
        }

        val riderPair = applicableRiderWithAdjustments.firstOrNull { it.second.ladderBreakerMoq != null }

        val ladderModifiedPriceForListing = modifyLadder(priceForListing, riderPair, newFilteredRidersWithAdjustments)

        var bpsVal = 0
        var flatVal = 0
        var bpsValAdd = 0
        var flatValAdd = 0

        newFilteredRidersWithAdjustments.map { y ->
            if (y.second.isAdditive) {
                bpsValAdd += y.second.bpsInPercentage
                flatValAdd += y.second.flatVal
            } else {
                bpsVal -= y.second.bpsInPercentage
                flatVal -= y.second.flatVal
            }
        }
        val hasWeightBasedPricing = vertical?.metadata?.hasWeightBasedPricing ?: false

        return PriceForListing(
            listingId = ladderModifiedPriceForListing.listingId,
            saleUnitId = ladderModifiedPriceForListing.saleUnitId,
            prices = ladderModifiedPriceForListing.prices.map { w ->
                val riderInfo = if (w.bulkLadder == true) {
                    (applicableRiderWithAdjustments.map { z -> z.second })
                } else {
                    (newFilteredRidersWithAdjustments.map { z -> z.second })
                }

                val finalRiders = getFinalApplicableRiders(
                    bpsVal = bpsVal,
                    bpsValAdd = bpsValAdd,
                    flatVal = flatVal,
                    flatValAdd = flatValAdd,
                    defaultPrice = w.priceInPaisa.defaultPrice,
                    hasWeightBasedPricing = hasWeightBasedPricing,
                    pricePerKgInPaisa = w.pricePerKgInPaisa
                )

                QtyBasedPrice(
                    minQty = w.minQty,
                    maxQty = w.maxQty,
                    priceInPaisa = PriceInPaisa(
                        onCredit = finalRiders.nonBasicPriceRidersValue.plus(w.priceInPaisa.onCredit).roundToLong(),
                        onCOD = finalRiders.nonBasicPriceRidersValue.plus(w.priceInPaisa.onCOD).roundToLong(),
                        onPrepayment = finalRiders.nonBasicPriceRidersValue.plus(w.priceInPaisa.onPrepayment)
                            .roundToLong(),
                        basicPrice = BasicPrice(
                            onCreditBasePrice = finalRiders.basicPriceRidersValue.plus(w.priceInPaisa.onCredit)
                                .roundToLong(),
                            onCODBasePrice = finalRiders.basicPriceRidersValue.plus(w.priceInPaisa.onCOD)
                                .roundToLong(),
                            onPrepaymentBasePrice = finalRiders.basicPriceRidersValue.plus(w.priceInPaisa.onPrepayment)
                                .roundToLong()
                        ),
                        priceRiders = riderInfo
                    ),
                    pricePerKgInPaisa = if (hasWeightBasedPricing) {
                        w.pricePerKgInPaisa?.let {
                            PriceInPaisa(
                                onCredit = finalRiders.nonBasicPerKgPriceRidersValue.plus(it.onCredit)
                                    .roundToLong(),
                                onCOD = finalRiders.nonBasicPerKgPriceRidersValue.plus(it.onCOD).roundToLong(),
                                onPrepayment = finalRiders.nonBasicPerKgPriceRidersValue.plus(it.onPrepayment)
                                    .roundToLong(),
                                basicPrice = BasicPrice(
                                    onCreditBasePrice = finalRiders.basicPerKgPriceRidersValue.plus(it.onCredit)
                                        .roundToLong(),
                                    onCODBasePrice = finalRiders.basicPerKgPriceRidersValue.plus(it.onCOD)
                                        .roundToLong(),
                                    onPrepaymentBasePrice = finalRiders.basicPerKgPriceRidersValue.plus(it.onPrepayment)
                                        .roundToLong()
                                ),
                                priceRiders = riderInfo
                            )
                        }
                    } else null,
                    taxableAmountPaise = w.taxableAmountPaise,
                    packagingUnit = w.packagingUnit,
                    bulkLadder = w.bulkLadder
                )
            },
            strategyRef = ladderModifiedPriceForListing.strategyRef,
            metaData = ladderModifiedPriceForListing.metaData,
            sscMetadata = ladderModifiedPriceForListing.sscMetadata
        )
    }

    /**
     * This method derives absolute values of riders in paise and
     * calculates the final rider value to be applied on base price.
     */
    private fun getFinalApplicableRiders(
        bpsVal:Int,
        bpsValAdd: Int,
        flatVal: Int,
        flatValAdd: Int,
        defaultPrice: Long,
        hasWeightBasedPricing: Boolean,
        pricePerKgInPaisa: PriceInPaisa?
    ): FinalRidersMap {
        val disValue = bpsVal.times(defaultPrice).toBigDecimalDivConst(BPS_DIVISOR)
        val addValue = bpsValAdd.times(defaultPrice).toBigDecimalDivConst(BPS_DIVISOR)

        val pricePerKgDisValue = if (hasWeightBasedPricing) {
            pricePerKgInPaisa?.let {
                bpsVal.times(it.defaultPrice).toBigDecimalDivConst(BPS_DIVISOR) } ?: BigDecimal.ZERO
        }
        else BigDecimal.ZERO

        val pricePerKgAddValue = if (hasWeightBasedPricing) {
            pricePerKgInPaisa?.let {
                bpsValAdd.times(it.defaultPrice).toBigDecimalDivConst(BPS_DIVISOR) } ?: BigDecimal.ZERO
        }
        else BigDecimal.ZERO

        return FinalRidersMap(
            basicPriceRidersValue = addValue.plus(flatValAdd),
            nonBasicPriceRidersValue = (disValue + addValue).plus(flatVal + flatValAdd),
            basicPerKgPriceRidersValue = pricePerKgAddValue.plus(flatValAdd),
            nonBasicPerKgPriceRidersValue = (pricePerKgDisValue + pricePerKgAddValue).plus(flatValAdd + flatVal)
        )
    }

    private fun trackFinalPriceInfo(
        contextualPriceRequest: ContextualPriceRequest?,
        catalogEntityContext: CatalogEntityContext,
        finalPricesWithRiders: PriceForListing,
        applicableRiderWithAdjustments: List<Pair<PriceRider, PriceRiderForListing>>
    ) {
        val requestHasBuyerContext = (
                !contextualPriceRequest?.buyerContext?.orgId.isNullOrBlank() &&
                        !contextualPriceRequest?.buyerContext?.orgUnitId.isNullOrBlank()
                ).toString()

        Telemetry.trackEvent(
            name = "FINAL_PRICE_INFO",
            properties = mapOf(
                "listingId" to finalPricesWithRiders.listingId,
                "salesUnitId" to finalPricesWithRiders.saleUnitId,
                "hasBuyerContext" to requestHasBuyerContext,
                "vertical" to catalogEntityContext.vertical.name,
                "verticalCategory" to catalogEntityContext.verticalCategory.name,
                "emptyPrice" to finalPricesWithRiders.prices.isEmpty().toString(),
                "priceType" to (finalPricesWithRiders.strategyRef ?: ""),
                "riders" to applicableRiderWithAdjustments.map { it.first }.joinToString(",")
            )
        )
    }

    private fun pushPriceAudit(
        finalPricesWithRiders: PriceForListing,
        orderReadyForCheckout: Boolean
    ): PriceForListing {
        val pricingAudit = PricingAudit(
            id = generateId("PA"),
            refId = finalPricesWithRiders.listingId,
            listingId = finalPricesWithRiders.listingId,
            saleUnitId = finalPricesWithRiders.saleUnitId,
            strategyName = finalPricesWithRiders.strategyRef!!,
            strategyRef = priceStrategyToStrategyRefMap[finalPricesWithRiders.strategyRef]!!,
            prices = finalPricesWithRiders.prices,
            metaData = finalPricesWithRiders.metaData,
            sscMetadata = finalPricesWithRiders.sscMetadata,
            contractReferenceId = finalPricesWithRiders.contractReferenceId
        )
        val pricingAuditId = priceAuditController.createPriceAudit(pricingAudit, orderReadyForCheckout)
        return finalPricesWithRiders.copy(
            strategyRef = priceStrategyToStrategyRefMap[finalPricesWithRiders.strategyRef],
            pricingAuditId = pricingAuditId
        )
    }

    private data class FinalRidersMap(
        val basicPriceRidersValue: BigDecimal,
        val nonBasicPriceRidersValue: BigDecimal,
        val basicPerKgPriceRidersValue: BigDecimal,
        val nonBasicPerKgPriceRidersValue: BigDecimal
    )

}
