package com.udaan.pricing.core.helpers.rider.impl

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.google.inject.Inject
import com.google.inject.name.Named
import com.udaan.common.annotations.BlockingCall
import com.udaan.common.server.getSync
import com.udaan.common.utils.kotlin.logger
import com.udaan.config.Configuration
import com.udaan.instrumentation.Telemetry
import com.udaan.pricing.PriceRiderForListing
import com.udaan.pricing.RiderCode
import com.udaan.pricing.core.constants.NamedConstants
import com.udaan.pricing.core.helpers.rider.PricingRequestContext
import com.udaan.pricing.core.helpers.rider.Rider
import com.udaan.pricing.core.helpers.rider.RootRequestContext
import com.udaan.pricing.core.helpers.rider.impl.bot.BotIdentifier
import com.udaan.pricing.core.helpers.rider.impl.bot.periodically
import com.udaan.pricing.core.models.CatalogEntityContext
import com.udaan.resources.RedisLettuce6Client
import com.udaan.resources.cache.RedisCache2
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withContext
import java.time.Duration
import java.util.concurrent.CompletableFuture
import kotlin.math.absoluteValue
import kotlin.math.min
import kotlin.random.Random

class BotPriceRider @Inject constructor(
    private val botIdentifier: BotIdentifier,
    @Named(NamedConstants.Caches.BOT_MARKUP_CACHE) private val markupCache: RedisCache2<BotMarkupData>,
    private val redisClient: RedisLettuce6Client
) : Rider() {
    override val isAdditive: Boolean = true

    override suspend fun getPriceRider(
        catalogEntityContext: CatalogEntityContext,
        requestContext: PricingRequestContext,
        fetchRiderDetails: Boolean,
        cluster: List<String>?,
        preferredWarehouseId: String?,
        servingWarehouseId: String?
    ): PriceRiderForListing? {
        val startTime = System.currentTimeMillis()
        val rootRequestContext = requestContext.rootRequestContext?.objOrNull() ?: run {
            log.debug(
                "Not a bot, listingId={}, listingOrgId={}, {}",
                catalogEntityContext.listingId,
                catalogEntityContext.listingDetail.orgId,
                requestContext.rootRequestContext
            )
            return null
        }

        try {
            val userId = rootRequestContext.getRootUserId()
            val clientIp = rootRequestContext.rootClientIp?.also {
                Telemetry.injectProperties(mapOf("rootClientIp" to it) )
            }

            val isBot = try {
                botIdentifier.isBot(catalogEntityContext.listingDetail.orgId, userId, clientIp)
            } catch (e: Exception) {
                log.warn("Exception while detecting bot for $rootRequestContext", e)
                return null
            }

            if (!isBot) {
                log.info(
                    "Not a bot, listingId={}, listingOrgId={}, {}",
                    catalogEntityContext.listingId,
                    catalogEntityContext.listingDetail.orgId,
                    rootRequestContext
                )
                return null
            }

            val markupBpsFromVertical = markupCache.get(catalogEntityContext.vertical.name.lowercase()) {
                CompletableFuture.completedFuture( run {
                    val markupBps = getRandomBpsValue(startTime, catalogEntityContext.listingId)
                    BotMarkupData(markupBps)
                })
            }.await()
                ?.markupBps
                ?: withContext(Dispatchers.IO) {
                    getRandomBpsValue(startTime, catalogEntityContext.listingId)
                }

            val markupBpsFromListing = if (
                botRiderConfig.await().enableListingMarkup
                && clientIp != null
                && botIdentifier.isBotIdentifiedByIp(clientIp)
            ) {
                // Experiment with very drastic, listing level markdowns
                redisClient.asyncCommands.hget("bot:exp-jt:markupBps", catalogEntityContext.listingId)
                    .toCompletableFuture()
                    .await()
                    ?.toInt()
            } else {
                null
            }

            val markupBps = markupBpsFromListing ?: markupBpsFromVertical

            log.info(
                "Bot found, listing={}, listingOrgId={}, {}, markup: {}",
                catalogEntityContext.listingId,
                catalogEntityContext.listingDetail.orgId,
                rootRequestContext,
                markupBps
            )
            Telemetry.injectProperties(mapOf("isBot" to "true", "markupBps" to markupBps.toString()))

            return PriceRiderForListing(
                listingId = catalogEntityContext.listingId,
                saleUnitId = "",
                bpsInPercentage = markupBps,
                isAdditive = true,
                riderCode = RiderCode.BOT.name,
                riderDetails = BotRiderDetails(
                    markupBps,
                    isBot,
                    rootRequestContext
                )
            )
        } catch (ex: Exception) {
            log.error("getPriceRider: Exception while evaluating rider for bot", ex)
            return null
        }
        finally {
            log.info("Time to compute bot-pricing in millis: ${System.currentTimeMillis() - startTime}")
        }
    }

    @BlockingCall
    private fun getRandomBpsValue(startTime: Long, listingId: String): Int {
        val random = Random(startTime + listingId.hashCode())
        val config = if (botRiderConfig.isCompletedExceptionally) BotPriceConfig.DEFAULT else botRiderConfig.getSync()
        val minMarkupBps = config.minBps.absoluteValue
        val maxMarkupBps = config.maxBps.absoluteValue

        //[shashwat] ensure that minMarkupBps < maxMarkupBps; otherwise Random will throw exception
        val markupBps = if (minMarkupBps < maxMarkupBps) {
            random.nextInt(minMarkupBps, maxMarkupBps)
        } else {
            min(minMarkupBps, maxMarkupBps)
        }
        return markupBps
    }

    companion object {
        private val botRiderConfig by periodically<BotPriceConfig>(Duration.ofHours(1)) {
            (Configuration.getMap("pricing/bot/rider/config") ?: mapOf()).let {
                BotPriceConfig(
                    minBps = it["minBps"]?.toInt() ?: 50,
                    maxBps = it["maxBps"]?.toInt() ?: 150,
                    enableListingMarkup = it["enableListingMarkup"]?.toBoolean() ?: false
                )
            }
        }

        private val log by logger()
    }
}

@JsonIgnoreProperties
data class BotRiderDetails(
    val markupBps: Int,
    val isBot: Boolean,
    val requestCtx: RootRequestContext
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class BotPriceConfig(
    val minBps: Int = 50,
    val maxBps: Int = 150,
    val enableListingMarkup: Boolean = false
) {
    companion object {
        val DEFAULT = BotPriceConfig()
    }

}

@JsonIgnoreProperties(ignoreUnknown = true)
data class BotMarkupData(
    val markupBps: Int
)
