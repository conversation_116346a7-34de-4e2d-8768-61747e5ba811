package com.udaan.pricing.core.dao

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import com.udaan.pricing.core.constants.CosmosDbConfig.COSMOS_DB_NAME
import com.udaan.pricing.core.constants.CosmosDbConfig.MANUAL_PRICE_COLLECTION
import com.udaan.pricing.manualprice.ManualPrice
import com.udaan.pricing.manualprice.ManualPriceState
import kotlinx.coroutines.flow.toList

@Singleton
class ManualPriceRepository @Inject constructor(
    private val objectMapper: ObjectMapper
) {
    companion object {
        private val cosmosDbDao by lazy {
            CosmosDbDao(
                configKey = "pricing",
                databaseName = COSMOS_DB_NAME,
                containerName = MANUAL_PRICE_COLLECTION
            ) { builder ->
                builder.connectionSharingAcrossClientsEnabled(true)
            }
        }
    }

    /**
     * Dummy function solely used for client initialisation while starting the service
     */
    suspend fun initialise() {
        cosmosDbDao.findItem("ID1")
    }

    suspend fun createOrUpdateManualPrice(manualPrice: ManualPrice): ManualPrice {
        return cosmosDbDao.createOrUpdateItem(manualPrice.toDocument()).toManualPrice()
    }

    suspend fun updateManualPrice(
        manualPrice: ManualPrice,
        ttlInSeconds: Long?
    ): ManualPrice {
        val manualPriceObjectNode = manualPrice.toDocument()

        if (ttlInSeconds != null) {
            manualPriceObjectNode.put("ttl", ttlInSeconds)
        }
        return cosmosDbDao.updateItem(manualPriceObjectNode).toManualPrice()
    }

    suspend fun getManualPriceByIdAndPartitionKey(
        id: String,
        partitionKey: String
    ): ManualPrice? {
        return cosmosDbDao.getItem(id, partitionKey)?.toManualPrice()
    }

    suspend fun getManualPricesForCatalogEntityAndState(
        catalogEntity: String,
        manualPriceState: ManualPriceState
    ): List<ManualPrice> {
        return cosmosDbDao.queryItems(
            queryName = "get-manual-prices-for-catalog-entity",
            querySpec = makeSqlQuerySpec(
                """
                select * from c where c.catalogEntity = @catalogEntity
                and c.state = @manualPriceState
            """.trimIndent(),
                "@catalogEntity" to catalogEntity,
                "@manualPriceState" to manualPriceState)
        ).toList().map { it.toManualPrice() }
    }

    suspend fun getManualPriceByReferenceId(
        referenceId: String
    ): ManualPrice? {
        return cosmosDbDao.queryItems(
            queryName = "get-manual-price-by-reference-id",
            querySpec = makeSqlQuerySpec(
                """
                select * from c where c.referenceId = @referenceId
            """.trimIndent(),
                "@referenceId" to referenceId)
        ).toList().map { it.toManualPrice() }.firstOrNull()
    }


    private fun ManualPrice.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toManualPrice() = objectMapper.convertValue(this, ManualPrice::class.java)
}
