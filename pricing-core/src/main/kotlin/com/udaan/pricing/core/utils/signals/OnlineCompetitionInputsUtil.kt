package com.udaan.pricing.core.utils.signals

import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.GenericValue
import com.udaan.pricing.commons.StringValue
import com.udaan.pricing.core.utils.signals.BigDecimalUtil.toBigDecimalWithScale
import com.udaan.pricing.signals.CompSignalMetadata
import com.udaan.pricing.signals.SignalMetadata
import com.udaan.pricing.variable.VariableId
import java.lang.instrument.IllegalClassFormatException

object OnlineCompetitionInputsUtil {

    fun getVariableIdForLadderCompPrice(competitionName: String): VariableId? {
        return when(competitionName.uppercase()) {
            "JUMBOTAIL" -> VariableId.JUMBOTAIL_COMP_LADDER_PRICE_WT_PAISA_UNIT
            "HYPERPURE" -> VariableId.HYPERPURE_COMP_LADDER_PRICE_WT_PAISA_UNIT
            "METRO" -> VariableId.METRO_COMP_LADDER_PRICE_WT_PAISA_UNIT
            "NINJACART" -> VariableId.NINJACART_COMP_LADDER_PRICE_WT_PAISA_UNIT
            else -> null
        }
    }

    /**
     * Fun returns actual comp variableId for a metadata based dynamic variableId.
     */
    fun getBaseCompVariableForMetadataVariable(variableId: VariableId): VariableId {
        return when(variableId) {
            VariableId.JUMBOTAIL_COMP_MRP_WT_PAISA_UNIT -> VariableId.JUMBOTAIL_COMP_LADDER_PRICE_WT_PAISA_UNIT
            VariableId.METRO_COMP_MRP_WT_PAISA_UNIT -> VariableId.METRO_COMP_LADDER_PRICE_WT_PAISA_UNIT
            VariableId.NINJACART_QUANTITY_PER_UNIT,
            VariableId.NINJACART_QUANTITY_TYPE,
            VariableId.NINJACART_WEIGHT_PER_PIECE_GRAMS -> VariableId.NINJACART_COMP_LADDER_PRICE_WT_PAISA_UNIT
            VariableId.HYPERPURE_QUANTITY_PER_UNIT,
            VariableId.HYPERPURE_QUANTITY_TYPE,
            VariableId.HYPERPURE_WEIGHT_PER_PIECE_GRAMS -> VariableId.HYPERPURE_COMP_LADDER_PRICE_WT_PAISA_UNIT
            else -> throw IllegalArgumentException("Variable $variableId is not a known metadata variable")
        }
    }

    /**
     * Fun returns value for applicable metadata input derived from metadata passed
     * and converted to correct GenericValue child class.
     *
     * @throws IllegalClassFormatException if metadata passed is not of type CompSignalMetadata.
     */
    fun getValueForApplicableMetadataInput(
        compSignalMetadata: SignalMetadata?,
        metadataVariableId: VariableId
    ): GenericValue? {
        // only CompSignalMetadata and null are allowed
        if(compSignalMetadata !is CompSignalMetadata?) {
            throw IllegalClassFormatException("Metadata passed is not of type CompSignalMetadata")
        }
        return when(metadataVariableId) {
            VariableId.JUMBOTAIL_COMP_MRP_WT_PAISA_UNIT,
            VariableId.METRO_COMP_MRP_WT_PAISA_UNIT -> {
                compSignalMetadata?.mrpInPaisa?.toBigDecimalWithScale()?.let { BigDecimalValue(it) }
            }
            VariableId.NINJACART_QUANTITY_PER_UNIT,
            VariableId.HYPERPURE_QUANTITY_PER_UNIT -> {
                compSignalMetadata?.quantityPerUnit?.toBigDecimalWithScale()?.let { BigDecimalValue(it) }
            }
            VariableId.NINJACART_QUANTITY_TYPE,
            VariableId.HYPERPURE_QUANTITY_TYPE -> {
                compSignalMetadata?.quantityType?.let { StringValue(it.name) }
            }
            VariableId.NINJACART_WEIGHT_PER_PIECE_GRAMS,
            VariableId.HYPERPURE_WEIGHT_PER_PIECE_GRAMS -> {
                compSignalMetadata?.weightPerPcGrams?.toBigDecimalWithScale()?.let { BigDecimalValue(it) }
            }
            else -> throw IllegalArgumentException("Variable $metadataVariableId is not a known metadata variable")
        }
    }
}
