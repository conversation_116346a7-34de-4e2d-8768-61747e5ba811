package com.udaan.pricing.core.strategyevaluator.fresh

import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fresh.FreshManualPriceEvaluator
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.commons.BigDecimalValue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal

class FreshManualPriceEvaluatorTest {

    @Test
    fun `test fresh manual price with null manual price input`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshManualPriceEvaluatorConfig(
            freshManualPriceWotPaisaUnit = null,
            conversionRate = BigDecimalValue(BigDecimal("1"))
        )
        assertThrows<IllegalArgumentException> {
            FreshManualPriceEvaluator.evaluate(evaluatorConfig)
        }
    }

    @Test
    fun `test fresh manual price with valid manual price input`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshManualPriceEvaluatorConfig(
            freshManualPriceWotPaisaUnit = BigDecimalValue(BigDecimal("1000")),
            conversionRate = BigDecimalValue(BigDecimal("1"))
        )
        val result = FreshManualPriceEvaluator.evaluate(evaluatorConfig)
        assertEquals(
            BigDecimalValue(BigDecimal("1000.0000")),
            result.output,
            "Output value mismatch"
        )

        assertEquals(emptyMap<String, String>(), result.metadata, "Metadata should be empty")
    }

    @Test
    fun `test fresh manual price with conversion rate greater than 1`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshManualPriceEvaluatorConfig(
            freshManualPriceWotPaisaUnit = BigDecimalValue(BigDecimal("1000")),
            conversionRate = BigDecimalValue(BigDecimal("10"))
        )
        val result = FreshManualPriceEvaluator.evaluate(evaluatorConfig)
        assertEquals(
            BigDecimalValue(BigDecimal("10000.0000")),
            result.output,
            "Output value mismatch"
        )

        assertEquals(emptyMap<String, String>(), result.metadata, "Metadata should be empty")
    }
}
