package com.udaan.pricing.core.strategyevaluator.staples

import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorConfigInput
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.staples.PLBenchmarkListingCeilGuardrailEvaluator
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.core.strategyevaluator.utils.MapComparatorUtils
import com.udaan.pricing.variable.VariableId
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal

class PLBenchmarkListingCeilGuardrailEvaluatorTest {

    @Test
    fun `test evaluator with BigDecimal previous output, no guardrail hit`() {
        val previousOutput = BigDecimalValue(BigDecimal("100"))
        val benchmarkListingPrice = LadderValue(
            listOf(
                Ladder(1, Int.MAX_VALUE.toLong(), BigDecimal("120"))
            )
        )
        val cohortAdjustmentBps = BigDecimalValue(BigDecimal("-1000")) // 10%

        val evaluatorConfig = EvaluatorRequestContextUtils.createPLBenchmarkListingCeilGuardrailEvaluatorConfig(
            previousOutput = previousOutput,
            benchmarkListingPrice = benchmarkListingPrice,
            cohortAdjustmentBps = cohortAdjustmentBps
        )

        val result = PLBenchmarkListingCeilGuardrailEvaluator.evaluate(evaluatorConfig)

        // Expected: 120 * (1 - 1000/10000) = 108.
        val expectedOutput = LadderValue(listOf(Ladder(1, Int.MAX_VALUE.toLong(), BigDecimal("100.0"))))
        val expectedMetadata = mapOf(
            "PL_BENCHMARK_LISTING_CEIL_GUARDRAIL_HIT" to "false"
        )

        assertTrue((result.output as LadderValue).toString() == expectedOutput.toString())
        MapComparatorUtils.compareMaps(result.metadata, expectedMetadata)
    }

    @Test
    fun `test evaluator with BigDecimal previous output, guardrail hit`() {
        val previousOutput = BigDecimalValue(BigDecimal("100"))
        val benchmarkListingPrice = LadderValue(
            listOf(
                Ladder(1, Int.MAX_VALUE.toLong(), BigDecimal("105"))
            )
        )
        val cohortAdjustmentBps = BigDecimalValue(BigDecimal("-1000")) // 10%

        val evaluatorConfig = EvaluatorRequestContextUtils.createPLBenchmarkListingCeilGuardrailEvaluatorConfig(
            previousOutput = previousOutput,
            benchmarkListingPrice = benchmarkListingPrice,
            cohortAdjustmentBps = cohortAdjustmentBps
        )

        val result = PLBenchmarkListingCeilGuardrailEvaluator.evaluate(evaluatorConfig)

        // Expected: 105 * (1 - 1000/10000) = 94.5
        val expectedOutput = LadderValue(listOf(Ladder(1, Int.MAX_VALUE.toLong(), BigDecimal("94.5"))))
        val expectedMetadata = mapOf(
            "PL_BENCHMARK_LISTING_CEIL_GUARDRAIL_HIT" to "true"
        )

        assertTrue((result.output as LadderValue).toString() == expectedOutput.toString())
        MapComparatorUtils.compareMaps(result.metadata, expectedMetadata)
    }

    @Test
    fun `test evaluator with Ladder previous output, no guardrail hit`() {
        val previousOutput = LadderValue(
            listOf(
                Ladder(1, 10, BigDecimal("100")),
                Ladder(11, Int.MAX_VALUE.toLong(), BigDecimal("90"))
            )
        )
        val benchmarkListingPrice = LadderValue(
            listOf(
                Ladder(1, Int.MAX_VALUE.toLong(), BigDecimal("120"))
            )
        )
        val cohortAdjustmentBps = BigDecimalValue(BigDecimal("1000")) // 10%

        val evaluatorConfig = EvaluatorRequestContextUtils.createPLBenchmarkListingCeilGuardrailEvaluatorConfig(
            previousOutput = previousOutput,
            benchmarkListingPrice = benchmarkListingPrice,
            cohortAdjustmentBps = cohortAdjustmentBps
        )

        val result = PLBenchmarkListingCeilGuardrailEvaluator.evaluate(evaluatorConfig)

        // Expected: Adjusted benchmark is 120 * (1+0.1) = 132.
        // Previous output values: 100, 90. Ceil is 132. So output is 100, 90
        val expectedOutput = LadderValue(
            listOf(
                Ladder(1, 10, BigDecimal("100")),
                Ladder(11, Int.MAX_VALUE.toLong(), BigDecimal("90"))
            )
        )
        val expectedMetadata = mapOf(
            "PL_BENCHMARK_LISTING_CEIL_GUARDRAIL_HIT" to "false"
        )

        assertTrue((result.output as LadderValue).toString() == expectedOutput.toString())
        MapComparatorUtils.compareMaps(result.metadata, expectedMetadata)
    }

    @Test
    fun `test evaluator with Ladder previous output, guardrail hit on one slab`() {
        val previousOutput = LadderValue(
            listOf(
                Ladder(1, 10, BigDecimal("100")),
                Ladder(11, Int.MAX_VALUE.toLong(), BigDecimal("115"))
            )
        )
        val benchmarkListingPrice = LadderValue(
            listOf(
                Ladder(1, Int.MAX_VALUE.toLong(), BigDecimal("105"))
            )
        )
        val cohortAdjustmentBps = BigDecimalValue(BigDecimal("-1000")) // -10%

        val evaluatorConfig = EvaluatorRequestContextUtils.createPLBenchmarkListingCeilGuardrailEvaluatorConfig(
            previousOutput = previousOutput,
            benchmarkListingPrice = benchmarkListingPrice,
            cohortAdjustmentBps = cohortAdjustmentBps
        )

        val result = PLBenchmarkListingCeilGuardrailEvaluator.evaluate(evaluatorConfig)

        // Expected: Adjusted benchmark is 105 * (1-0.1) = 94.5.
        // Previous output values: 100, 115. Both slabs are above the ceiling of 94.5.
        // So output should be 94.5 for all quantities (guardrailed)
        val expectedOutput = LadderValue(
            listOf(
                Ladder(1, Int.MAX_VALUE.toLong(), BigDecimal("94.5"))
            )
        )
        val expectedMetadata = mapOf(
            "PL_BENCHMARK_LISTING_CEIL_GUARDRAIL_HIT" to "true"
        )

        assertTrue((result.output as LadderValue).toString() == expectedOutput.toString())
        MapComparatorUtils.compareMaps(result.metadata, expectedMetadata)
    }

    @Test
    fun `test missing previous output`() {
        val benchmarkListingPrice = LadderValue(listOf(Ladder(1, Int.MAX_VALUE.toLong(), BigDecimal("100"))))
        val cohortAdjustmentBps = BigDecimalValue(BigDecimal("1000"))

        val evaluatorConfig = EvaluatorRequestContextUtils.createPLBenchmarkListingCeilGuardrailEvaluatorConfig(
            previousOutput = com.udaan.pricing.commons.StringValue("dummy"), // Invalid previous output
            benchmarkListingPrice = benchmarkListingPrice,
            cohortAdjustmentBps = cohortAdjustmentBps
        )
        // Override previousOutput to be null to test the require block for previousOutput
        val contextWithNullPreviousOutput = evaluatorConfig.copy(previousOutput = null)

        assertThrows<IllegalArgumentException> {
            PLBenchmarkListingCeilGuardrailEvaluator.evaluate(contextWithNullPreviousOutput)
        }
    }

    @Test
    fun `test invalid previous output type`() {
        val benchmarkListingPrice = LadderValue(listOf(Ladder(1, Int.MAX_VALUE.toLong(), BigDecimal("100"))))
        val cohortAdjustmentBps = BigDecimalValue(BigDecimal("1000"))

        val evaluatorConfig = EvaluatorRequestContextUtils.createPLBenchmarkListingCeilGuardrailEvaluatorConfig(
            previousOutput = com.udaan.pricing.commons.StringValue("dummy"),
            benchmarkListingPrice = benchmarkListingPrice,
            cohortAdjustmentBps = cohortAdjustmentBps
        )
        assertThrows<IllegalArgumentException> {
            PLBenchmarkListingCeilGuardrailEvaluator.evaluate(evaluatorConfig)
        }
    }

    @Test
    fun `test missing cohort adjustment bps`() {
        val previousOutput = BigDecimalValue(BigDecimal("100"))
        val benchmarkListingPrice = LadderValue(listOf(Ladder(1, Int.MAX_VALUE.toLong(), BigDecimal("120"))))

        val evaluatorConfig = EvaluatorRequestContext(
            strategy = EvaluatorRequestContextUtils.createPLBenchmarkListingCeilGuardrailEvaluatorConfig(
                previousOutput = previousOutput,
                benchmarkListingPrice = benchmarkListingPrice,
                cohortAdjustmentBps = BigDecimalValue(BigDecimal("0")) // Dummy, will be replaced
            ).strategy,
            inputs = listOf(
                EvaluatorConfigInput(VariableId.BENCHMARK_LISTING_PRICE_WOT_PAISA_SET, benchmarkListingPrice)
                // COHORT_ADJUSTMENT_BPS is intentionally omitted
            ),
            previousOutput = com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput(previousOutput, emptyMap())
        )

        assertThrows<IllegalArgumentException> {
            PLBenchmarkListingCeilGuardrailEvaluator.evaluate(evaluatorConfig)
        }
    }

    @Test
    fun `test benchmark listing price not a ladder`() {
        val previousOutput = BigDecimalValue(BigDecimal("100"))
        val benchmarkListingPrice = BigDecimalValue(BigDecimal("120")) // Not a ladder
        val cohortAdjustmentBps = BigDecimalValue(BigDecimal("1000"))

         val evaluatorConfig = EvaluatorRequestContext(
            strategy = EvaluatorRequestContextUtils.createPLBenchmarkListingCeilGuardrailEvaluatorConfig(
                previousOutput = previousOutput,
                benchmarkListingPrice = null, // Will use direct input below
                cohortAdjustmentBps = cohortAdjustmentBps
            ).strategy,
            inputs = listOf(
                EvaluatorConfigInput(VariableId.BENCHMARK_LISTING_PRICE_WOT_PAISA_SET, benchmarkListingPrice),
                EvaluatorConfigInput(VariableId.COHORT_ADJUSTMENT_BPS, cohortAdjustmentBps)
            ),
            previousOutput = com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput(previousOutput, emptyMap())
        )

        val result = PLBenchmarkListingCeilGuardrailEvaluator.evaluate(evaluatorConfig)
        assertEquals(previousOutput, result.output)
        assertEquals("false", result.metadata["PL_BENCHMARK_LISTING_CEIL_GUARDRAIL_HIT"])
    }

    @Test
    fun `test benchmark listing price is null`() {
        val previousOutput = BigDecimalValue(BigDecimal("100"))
        val cohortAdjustmentBps = BigDecimalValue(BigDecimal("1000"))

         val evaluatorConfig = EvaluatorRequestContext(
            strategy = EvaluatorRequestContextUtils.createPLBenchmarkListingCeilGuardrailEvaluatorConfig(
                previousOutput = previousOutput,
                benchmarkListingPrice = null, // Null benchmark
                cohortAdjustmentBps = cohortAdjustmentBps
            ).strategy,
            inputs = listOf(
                // BENCHMARK_LISTING_PRICE_WOT_PAISA_SET is null because not provided if benchmarkListingPrice is null in util
                 EvaluatorConfigInput(VariableId.COHORT_ADJUSTMENT_BPS, cohortAdjustmentBps)
            ),
            previousOutput = com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput(previousOutput, emptyMap())
        )

        val result = PLBenchmarkListingCeilGuardrailEvaluator.evaluate(evaluatorConfig)
        // If benchmarkListingPrice is null, adjustedBenchmarkListingPrice is null, guardrailedSlabPrice is previousOutput
        assertEquals(previousOutput, result.output)
        assertEquals("false", result.metadata["PL_BENCHMARK_LISTING_CEIL_GUARDRAIL_HIT"])
    }

    @Test
    fun `test evaluator with four slabs in previousOutput and benchmarkPrice, mixed guardrail hits`() {
        // Create previousOutput with four slabs with different min/max quantities
        val previousOutput = LadderValue(
            listOf(
                Ladder(1, 4, BigDecimal("80")),    // Below adjusted benchmark (90)
                Ladder(5, 9, BigDecimal("85")),    // Below adjusted benchmark (90)
                Ladder(10, 14, BigDecimal("95")),  // Below adjusted benchmark (99)
                Ladder(15, Int.MAX_VALUE.toLong(), BigDecimal("110")) // Above adjusted benchmark (103.5)
            )
        )

        // Create benchmarkListingPrice with four slabs with different min/max quantities
        val benchmarkListingPrice = LadderValue(
            listOf(
                Ladder(1, 6, BigDecimal("100")),   // Adjusted: 100 * 0.9 = 90
                Ladder(7, 12, BigDecimal("100")),  // Adjusted: 100 * 0.9 = 90
                Ladder(13, 18, BigDecimal("110")), // Adjusted: 110 * 0.9 = 99
                Ladder(19, Int.MAX_VALUE.toLong(), BigDecimal("115")) // Adjusted: 115 * 0.9 = 103.5
            )
        )

        val cohortAdjustmentBps = BigDecimalValue(BigDecimal("-1000")) // -10%

        val evaluatorConfig = EvaluatorRequestContextUtils.createPLBenchmarkListingCeilGuardrailEvaluatorConfig(
            previousOutput = previousOutput,
            benchmarkListingPrice = benchmarkListingPrice,
            cohortAdjustmentBps = cohortAdjustmentBps
        )

        val result = PLBenchmarkListingCeilGuardrailEvaluator.evaluate(evaluatorConfig)

        // Expected output after merging the different quantity ranges:
        // The compareAndCreateLadders function will create new slabs at each unique min quantity
        // from both ladders, and apply the guardrail logic to each slab.
        // 
        // Unique min quantities: 1, 5, 7, 10, 13, 15, 19
        // Resulting slabs:
        // [1-4]: previousOutput = 80, adjusted benchmark = 90, no guardrail hit -> 80
        // [5-6]: previousOutput = 85, adjusted benchmark = 90, no guardrail hit -> 85
        // [7-9]: previousOutput = 85, adjusted benchmark = 90, no guardrail hit -> 85
        // [10-12]: previousOutput = 95, adjusted benchmark = 90, guardrail hit -> 90
        // [13-14]: previousOutput = 95, adjusted benchmark = 99, no guardrail hit -> 95
        // [15-18]: previousOutput = 110, adjusted benchmark = 99, guardrail hit -> 99
        // [19-MAX]: previousOutput = 110, adjusted benchmark = 103.5, guardrail hit -> 103.5
        //
        // After merging slabs with similar values:
        val expectedOutput = LadderValue(
            listOf(
                Ladder(1, 4, BigDecimal("80")),
                Ladder(5, 9, BigDecimal("85")),
                Ladder(10, 12, BigDecimal("90")),
                Ladder(13, 14, BigDecimal("95")),
                Ladder(15, 18, BigDecimal("99")),
                Ladder(19, Int.MAX_VALUE.toLong(), BigDecimal("103.5"))
            )
        )
        val expectedMetadata = mapOf(
            "PL_BENCHMARK_LISTING_CEIL_GUARDRAIL_HIT" to "true"
        )

        assertTrue((result.output as LadderValue).toString() == expectedOutput.toString())
        MapComparatorUtils.compareMaps(result.metadata, expectedMetadata)
    }
} 
