package com.udaan.pricing.core.managers

import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.StringValue
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.dao.signals.VariableRepository
import com.udaan.pricing.core.helpers.PricingNetworkHelper
import com.udaan.pricing.core.helpers.signals.CatalogEntityHelper
import com.udaan.pricing.core.helpers.signals.ResolvedValuesRedisHelper
import com.udaan.pricing.core.helpers.signals.variableresolvers.VariableResolverFactory
import com.udaan.pricing.core.managers.signals.SignalReadManager
import com.udaan.pricing.core.managers.signals.VariableManager
import com.udaan.pricing.signals.GenericMetadata
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState
import com.udaan.pricing.variable.ListValuesConstraint
import com.udaan.pricing.variable.Variable
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.variable.VariableRedisUtils
import com.udaan.pricing.variable.VariableRequest
import com.udaan.pricing.variable.VariableResolvedValueType
import com.udaan.pricing.variable.VariableState
import com.udaan.pricing.variable.VariableType
import com.udaan.resources.cache.RedisCache2
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Assert.fail
import org.junit.Test
import java.math.BigDecimal
import java.util.concurrent.CompletableFuture
import javax.ws.rs.BadRequestException

class VariableManagerTest {

    private val variableRepository: VariableRepository = mockk()
    private val variableCache: RedisCache2<List<Variable>> = mockk()
    private val resolvedValuesRedisHelper: ResolvedValuesRedisHelper = mockk()
    private val variableResolverFactory: VariableResolverFactory = mockk()
    private val catalogEntityHelper: CatalogEntityHelper = mockk()
    private val pricingNetworkHelper: PricingNetworkHelper = mockk()
    private val signalReadManager: SignalReadManager = mockk()

    private val variableManager = VariableManager(
        variableRepository = variableRepository,
        variableCache = variableCache,
        resolvedValuesRedisHelper = resolvedValuesRedisHelper,
        variableResolverFactory = variableResolverFactory,
        catalogEntityHelper = catalogEntityHelper,
        pricingNetworkHelper = pricingNetworkHelper,
        signalReadManager = signalReadManager
    )

    @Test
    fun `should throw BadRequestException when variable with the same ID already exists`() = runBlocking {
        val variableRequest = VariableRequest(
            id = VariableId.GST_BPS,
            resolvedValueType = VariableResolvedValueType.BIG_DECIMAL,
            type = VariableType.SIGNAL,
            hierarchies = emptyList(),
            createdBy = ""
        )
        coEvery {
            variableRepository.getVariableById(VariableId.GST_BPS.name)
        } returns Variable(
            id = VariableId.GST_BPS,
            resolvedValueType = VariableResolvedValueType.BIG_DECIMAL,
            defaultValue = null,
            freshnessDurationInMillis = null,
            type = VariableType.SIGNAL,
            hierarchies = emptyList(),
            allowedValuesConstraint = null,
            state = VariableState.ACTIVE,
            createdBy = "",
            createdAt = System.currentTimeMillis()
        )

        try {
            variableManager.createVariable(variableRequest)
            fail("Variable creation with same id should have failed")
        } catch (ex: BadRequestException) {
            assertEquals("Variable with id ${variableRequest.id} already exists", ex.message)
        }

        coVerify (exactly = 1) { variableRepository.getVariableById(variableRequest.id.name) }
    }


    @Test
    fun `should create variable successfully when ID is unique`() = runBlocking {
        val variableRequest = VariableRequest(
            id = VariableId.GST_BPS,
            resolvedValueType = VariableResolvedValueType.BIG_DECIMAL,
            type = VariableType.SIGNAL,
            hierarchies = emptyList(),
            createdBy = ""
        )
        val createdVariable = mockk<Variable>()
        coEvery { variableCache.invalidate(VariableRedisUtils.VARIABLE_ALL_KEY) } returns CompletableFuture.completedFuture("")
        coEvery { variableRepository.getVariableById(VariableId.GST_BPS.name) } returns null
        coEvery { variableRepository.createVariable(any()) } returns createdVariable

        val result = variableManager.createVariable(variableRequest)

        assertEquals(createdVariable, result)

        coVerify {
            variableRepository.getVariableById(VariableId.GST_BPS.name)
            variableRepository.createVariable(any())
        }
    }

    @Test
    fun `should create variable if default value class matches defined variable type class`() = runBlocking {
        val variableRequest = VariableRequest(
            id = VariableId.GST_BPS,
            resolvedValueType = VariableResolvedValueType.STRING,
            defaultValue = StringValue(""),
            type = VariableType.SIGNAL,
            hierarchies = emptyList(),
            createdBy = ""
        )
        val createdVariable = mockk<Variable>()
        coEvery { variableCache.invalidate(VariableRedisUtils.VARIABLE_ALL_KEY) } returns CompletableFuture.completedFuture("")
        coEvery { variableRepository.getVariableById(VariableId.GST_BPS.name) } returns null
        coEvery { variableRepository.createVariable(any()) } returns createdVariable

        try {
            val variable = variableManager.createVariable(variableRequest)
            assert(variable == createdVariable)
        } catch (ex: IllegalArgumentException) {
            fail("variable creation should fail if default value class matches defined variable type class")
        }

        coVerify(exactly = 1) { variableRepository.createVariable(any()) }
    }

    @Test
    fun `should fail variable creation if default value type don't match variable value type`() = runBlocking {
        val variableRequest = VariableRequest(
            id = VariableId.GST_BPS,
            resolvedValueType = VariableResolvedValueType.BIG_DECIMAL,
            defaultValue = StringValue(""),
            type = VariableType.SIGNAL,
            hierarchies = emptyList(),
            createdBy = ""
        )
        coEvery { variableRepository.getVariableById(VariableId.GST_BPS.name) } returns null

        try {
            variableManager.createVariable(variableRequest)
            fail("variable creation should fail if default value type don't match variable value type")
        } catch (ex: IllegalArgumentException) {
            assertEquals("Default value dataType and variable dataType should be matching!", ex.message)
        }
        coVerify(exactly = 0) { variableRepository.createVariable(any()) }
    }

    @Test
    fun `should create variable if default value satisfies allowed values constraint`() = runBlocking {
        val variableRequest = VariableRequest(
            id = VariableId.GST_BPS,
            resolvedValueType = VariableResolvedValueType.STRING,
            defaultValue = StringValue("a"),
            type = VariableType.SIGNAL,
            hierarchies = emptyList(),
            allowedValuesConstraint = ListValuesConstraint(
                value = listOf(
                    StringValue("a"),
                    StringValue("b")
                )
            ),
            createdBy = ""
        )
        val createdVariable = mockk<Variable>()
        coEvery { variableCache.invalidate(VariableRedisUtils.VARIABLE_ALL_KEY) } returns CompletableFuture.completedFuture("")
        coEvery { variableRepository.getVariableById(VariableId.GST_BPS.name) } returns null
        coEvery { variableRepository.createVariable(any()) } returns createdVariable

        try {
            val variable = variableManager.createVariable(variableRequest)
            assert(variable == createdVariable)
        } catch (ex: IllegalArgumentException) {
            fail("variable creation should fail if default value class matches defined variable type class")
        }

        coVerify(exactly = 1) { variableRepository.createVariable(any()) }
    }

    @Test
    fun `should fail variable creation if default value doesn't satisfy allowed values constraint`() = runBlocking {
        val variableRequest = VariableRequest(
            id = VariableId.GST_BPS,
            resolvedValueType = VariableResolvedValueType.STRING,
            defaultValue = StringValue("a"),
            type = VariableType.SIGNAL,
            hierarchies = emptyList(),
            allowedValuesConstraint = ListValuesConstraint(
                value = listOf(
                    StringValue("b"),
                    StringValue("c")
                )
            ),
            createdBy = ""
        )
        coEvery { variableRepository.getVariableById(VariableId.GST_BPS.name) } returns null

        try {
            variableManager.createVariable(variableRequest)
            fail("variable creation should fail if default value type don't match variable value type")
        } catch (ex: IllegalArgumentException) {
            assertTrue(ex.message!!.contains("is not in the allowed list:"))
        }
        coVerify(exactly = 0) { variableRepository.createVariable(any()) }
    }

    // todo: add variable resolver test


    /*
     *  Tests for VariableManager.resolveJitVendorBestPriceForCityGid
     */
    @Test
    fun `resolveJitVendorBestPriceForCityGid should throw IllegalArgumentException when product group ID is blank`() = runBlocking {
        try {
            variableManager.resolveJitVendorBestPriceForCityGid("", "BANGALORE")
            fail("Should have thrown IllegalArgumentException for blank product group ID")
        } catch (ex: IllegalArgumentException) {
            assertEquals("Product group ID and city cannot be empty", ex.message)
        }
    }

    @Test
    fun `resolveJitVendorBestPriceForCityGid should throw IllegalArgumentException when city is blank`() = runBlocking {
        try {
            variableManager.resolveJitVendorBestPriceForCityGid("PRODUCT_GROUP_1", "")
            fail("Should have thrown IllegalArgumentException for blank city")
        } catch (ex: IllegalArgumentException) {
            assertEquals("Product group ID and city cannot be empty", ex.message)
        }
    }

    @Test
    fun `resolveJitVendorBestPriceForCityGid should throw IllegalArgumentException when no warehouses found for city`() = runBlocking {
        val productGroupId = "PRODUCT_GROUP_1"
        val city = "NONEXISTENT_CITY"

        coEvery { pricingNetworkHelper.fetchAllWhsForGeoCity(city.lowercase()) } returns emptyList()

        try {
            variableManager.resolveJitVendorBestPriceForCityGid(productGroupId, city)
            fail("Should have thrown IllegalArgumentException for city with no warehouses")
        } catch (ex: IllegalArgumentException) {
            assertEquals("No Warehouses found for ${city.lowercase()}", ex.message)
        }

        coVerify { pricingNetworkHelper.fetchAllWhsForGeoCity(city.lowercase()) }
    }

    @Test
    fun `resolveJitVendorBestPriceForCityGid should return null when no JIT signals found for warehouses`() = runBlocking {
        val productGroupId = "PRODUCT_GROUP_1"
        val city = "BANGALORE"
        val warehouses = listOf("WH1", "WH2")

        coEvery { pricingNetworkHelper.fetchAllWhsForGeoCity(city.lowercase()) } returns warehouses
        coEvery {
            signalReadManager.getAllSignalsForLocations(
                catalogEntity = productGroupId,
                variableId = VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT,
                locationValues = warehouses
            )
        } returns emptyList()

        val result = variableManager.resolveJitVendorBestPriceForCityGid(productGroupId, city)

        assertNull("Result should be null when no JIT signals found", result)

        coVerify { pricingNetworkHelper.fetchAllWhsForGeoCity(city.lowercase()) }
        coVerify {
            signalReadManager.getAllSignalsForLocations(
                catalogEntity = productGroupId,
                variableId = VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT,
                locationValues = warehouses
            )
        }
    }

    @Test
    fun `resolveJitVendorBestPriceForCityGid should return minimum JIT price with metadata when signals found for warehouses`() = runBlocking {
        val productGroupId = "PRODUCT_GROUP_1"
        val city = "BANGALORE"
        val warehouses = listOf("WH1", "WH2")

        // Create signals with different prices
        val signal1 = Signal(
            catalogEntity = productGroupId,
            catalogEntityType = com.udaan.pricing.commons.CatalogEntityType.PRODUCT_GROUP_ID,
            variableId = VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT.name,
            signalData = BigDecimalValue(BigDecimal("100.0")),
            metadata = GenericMetadata(mapOf("vendorId" to "VENDOR1")),
            location = Location(LocationType.WAREHOUSE, "WH1"),
            state = SignalState.ACTIVE,
            createdBy = "TEST",
            updatedBy = "TEST",
            referenceId = "SIG1"
        )

        val signal2 = Signal(
            catalogEntity = productGroupId,
            catalogEntityType = com.udaan.pricing.commons.CatalogEntityType.PRODUCT_GROUP_ID,
            variableId = VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT.name,
            signalData = BigDecimalValue(BigDecimal("80.0")),
            metadata = GenericMetadata(mapOf("vendorId" to "VENDOR2")),
            location = Location(LocationType.WAREHOUSE, "WH2"),
            state = SignalState.ACTIVE,
            createdBy = "TEST",
            updatedBy = "TEST",
            referenceId = "SIG2"
        )

        val signals = listOf(signal1, signal2)

        coEvery { pricingNetworkHelper.fetchAllWhsForGeoCity(city.lowercase()) } returns warehouses
        coEvery {
            signalReadManager.getAllSignalsForLocations(
                catalogEntity = productGroupId,
                variableId = VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT,
                locationValues = warehouses
            )
        } returns signals

        val result = variableManager.resolveJitVendorBestPriceForCityGid(productGroupId, city)

        // Verify the result
        assertEquals(city.uppercase(), result?.city)
        assertEquals(productGroupId, result?.productGroupId)
        assertEquals(80.0, result?.jitVendorBestPrice)
        assertEquals(2, result?.refInputs?.size)

        // Verify the reference inputs
        val wh1Input = result?.refInputs?.find { it.warehouseId == "WH1" }
        assertEquals(100.0, wh1Input?.jitVendorBestPrice)
        assertEquals("VENDOR1", wh1Input?.vendorId)
        assertEquals("SIG1", wh1Input?.signalRefId)

        val wh2Input = result?.refInputs?.find { it.warehouseId == "WH2" }
        assertEquals(80.0, wh2Input?.jitVendorBestPrice)
        assertEquals("VENDOR2", wh2Input?.vendorId)
        assertEquals("SIG2", wh2Input?.signalRefId)

        coVerify { pricingNetworkHelper.fetchAllWhsForGeoCity(city.lowercase()) }
        coVerify {
            signalReadManager.getAllSignalsForLocations(
                catalogEntity = productGroupId,
                variableId = VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT,
                locationValues = warehouses
            )
        }
    }
}
