package com.udaan.pricing.core.strategyevaluator.fmcg

import com.udaan.pricing.core.strategyevaluator.impl.EvaluatorFactory
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils.equalsTo
import com.udaan.pricing.core.strategyevaluator.utils.MapComparatorUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class MrpCeilGuardrailEvaluatorTest {

    @Test
    fun `test mrp ceil when guardrail is hit`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createMrpCeilGuardrailEvaluatorConfig(
            previousOutput = LadderValue(
                value = listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 2,
                        ladderValue = BigDecimal(1200)
                    ),
                    Ladder(
                        minQuantity = 3,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(1400)
                    )
                )
            ),
            mrpInPaisa = BigDecimalValue(BigDecimal(1107)),
            cessBps = BigDecimalValue(BigDecimal(500)),
            gstBps = BigDecimalValue(BigDecimal(500)),
            mrpCeilGuardrailBps = BigDecimalValue(BigDecimal(500))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal("956.0454")
                )
            )
        )
        assertTrue(expectedOutput.value.equalsTo((result!!.output as LadderValue).value)) {
            "" + result.output + "is not matched with " + expectedOutput
        }

        MapComparatorUtils.compareMaps(
            result.metadata,
            mapOf(
                "MRP_CEIL_GUARDRAIL_INPUT" to "[(1,2,1200), (3,2147483647,1400)]",
                "MRP_WT_PAISA_SET" to "1107",
                "GST_BPS" to "500",
                "CESS_BPS" to "500",
                "MRP_CEIL_GUARDRAIL_MD_BPS" to "500",
                "CEIL_GUARDRAIL_PRICE" to "956.0454",
                "CEIL_GUARDRAIL_HIT" to "true",
                "PRICE_WITHOUT_CEIL_GUARDRAIL" to "[(1,2,1200), (3,2147483647,1400)]",
                "MRP_CEIL_GUARDRAIL_OUTPUT" to "[(1,2147483647,956.0454)]"
            )
        )

    }


    @Test
    fun `test mrp ceil when guardrail is not hit`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createMrpCeilGuardrailEvaluatorConfig(
            previousOutput = LadderValue(
                value = listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 2,
                        ladderValue = BigDecimal(900)
                    ),
                    Ladder(
                        minQuantity = 3,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(850)
                    )
                )
            ),
            mrpInPaisa = BigDecimalValue(BigDecimal(1107)),
            cessBps = BigDecimalValue(BigDecimal(500)),
            gstBps = BigDecimalValue(BigDecimal(500)),
            mrpCeilGuardrailBps = BigDecimalValue(BigDecimal(500))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = 2,
                    ladderValue = BigDecimal("900")
                ),
                Ladder(
                    minQuantity = 3,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal("850")
                )
            )
        )
        assertTrue(expectedOutput.value.equalsTo((result!!.output as LadderValue).value)) {
            "" + result.output + "is not matched with " + expectedOutput
        }

        MapComparatorUtils.compareMaps(
            result.metadata,
            mapOf(
                "MRP_CEIL_GUARDRAIL_INPUT" to "[(1,2,900), (3,2147483647,850)]",
                "MRP_WT_PAISA_SET" to "1107",
                "GST_BPS" to "500",
                "CESS_BPS" to "500",
                "MRP_CEIL_GUARDRAIL_MD_BPS" to "500",
                "CEIL_GUARDRAIL_PRICE" to "956.0454",
                "CEIL_GUARDRAIL_HIT" to "false",
                "PRICE_WITHOUT_CEIL_GUARDRAIL" to "[(1,2,900), (3,2147483647,850)]",
                "MRP_CEIL_GUARDRAIL_OUTPUT" to "[(1,2,900), (3,2147483647,850)]"
            )
        )

    }
}
