package com.udaan.pricing.core.strategyevaluator.common

import com.udaan.pricing.core.strategyevaluator.impl.EvaluatorFactory
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils.equalsTo
import com.udaan.pricing.core.strategyevaluator.utils.MapComparatorUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.variable.VariableId
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class VolumetricDiscountLadderEvaluatorTest {

    @Test
    fun `test volumetric discount with default ladder in previous output`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createVolumetricDiscountLadderEvaluatorConfig(
            volumetricDiscountBps = LadderValue(
                value = listOf(
                    Ladder(1, 10, BigDecimal(100)),
                    Ladder(11, 20, BigDecimal(150)),
                    Ladder(21, Int.MAX_VALUE.toLong(), BigDecimal(200))
                )
            ),
            previousOutput = LadderValue(
                value = listOf(Ladder(1, Int.MAX_VALUE.toLong(), BigDecimal(250)))
            )
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = LadderValue(
            value = listOf(
                Ladder(1, 10, BigDecimal(247.5)),
                Ladder(11, 20, BigDecimal(246.25)),
                Ladder(21, Int.MAX_VALUE.toLong(), BigDecimal(245))
            )
        )

        val expectedMetadata = mapOf(
            evaluatorConfig.strategy.name + "_INPUT" to "[(1,2147483647,250)]",
            VariableId.VOLUMETRIC_LADDER_DISCOUNT_BPS.name to "[(1,10,100), (11,20,150), (21,2147483647,200)]",
            "POST_ADJUSTMENT_LADDER_VALUES" to "[(1,10,247.5), (11,20,246.25), (21,2147483647,245)]",
            "POST_MERGE_VOLUME_DISCOUNT_VALUES" to "[(1,10,247.5), (11,20,246.25), (21,2147483647,245)]",
            evaluatorConfig.strategy.name + "_OUTPUT" to "[(1,10,247.5), (11,20,246.25), (21,2147483647,245)]"
        )

        assertTrue(
            expectedOutput.value.equalsTo((result!!.output as LadderValue).value),
            "" + result.output + " is not matched with " + expectedOutput
        )

        MapComparatorUtils.compareMaps(
            result.metadata,
            expectedMetadata
        )
    }

    @Test
    fun `test volumetric discount with no overlapping ladder in previous output`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createVolumetricDiscountLadderEvaluatorConfig(
            volumetricDiscountBps = LadderValue(
                value = listOf(
                    Ladder(1, 100, BigDecimal(0)),
                    Ladder(101, 200, BigDecimal(1000)),
                    Ladder(201, Int.MAX_VALUE.toLong(), BigDecimal(2000))
                )
            ),
            previousOutput = LadderValue(
                value = listOf(
                    Ladder(1, 10, BigDecimal(250)),
                    Ladder(11, 20, BigDecimal(240)),
                    Ladder(21, Int.MAX_VALUE.toLong(), BigDecimal(230))
                )
            )
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = LadderValue(
            value = listOf(
                Ladder(1, 10, BigDecimal(250)),
                Ladder(11, 20, BigDecimal(240)),
                Ladder(21, 100, BigDecimal(230)),
                Ladder(101, 200, BigDecimal(225)),
                Ladder(201, Int.MAX_VALUE.toLong(), BigDecimal(200))
            )
        )

        val expectedMetadata = mapOf(
            evaluatorConfig.strategy.name + "_INPUT" to "[(1,10,250), (11,20,240), (21,2147483647,230)]",
            VariableId.VOLUMETRIC_LADDER_DISCOUNT_BPS.name to "[(1,100,0), (101,200,1000), (201,2147483647,2000)]",
            "POST_ADJUSTMENT_LADDER_VALUES" to "[(1,100,250), (101,200,225), (201,2147483647,200)]",
            "POST_MERGE_VOLUME_DISCOUNT_VALUES" to
                    "[(1,10,250), (11,20,240), (21,100,230), (101,200,225), (201,2147483647,200)]",
            evaluatorConfig.strategy.name + "_OUTPUT" to
                    "[(1,10,250), (11,20,240), (21,100,230), (101,200,225), (201,2147483647,200)]"
        )

        assertTrue(
            expectedOutput.value.equalsTo((result!!.output as LadderValue).value),
            "" + result.output + " is not matched with " + expectedOutput
        )

        MapComparatorUtils.compareMaps(
            result.metadata,
            expectedMetadata
        )
    }

    @Test
    fun `test volumetric discount with previous output has no ladders`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createVolumetricDiscountLadderEvaluatorConfig(
            volumetricDiscountBps = LadderValue(
                value = listOf(
                    Ladder(1, 100, BigDecimal(100)),
                    Ladder(101, 200, BigDecimal(150)),
                    Ladder(201, Int.MAX_VALUE.toLong(), BigDecimal(200))
                )
            ),
            previousOutput = BigDecimalValue(
                value = BigDecimal(250)
            )
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = LadderValue(
            value = listOf(
                Ladder(1, 100, BigDecimal(247.5)),
                Ladder(101, 200, BigDecimal(246.25)),
                Ladder(201, Int.MAX_VALUE.toLong(), BigDecimal(245))
            )
        )

        val expectedMetadata = mapOf(
            evaluatorConfig.strategy.name + "_INPUT" to "250",
            VariableId.VOLUMETRIC_LADDER_DISCOUNT_BPS.name to "[(1,100,100), (101,200,150), (201,2147483647,200)]",
            "DEFAULT_LADDER_PASSED" to "true",
            "POST_ADJUSTMENT_LADDER_VALUES" to "[(1,100,247.5), (101,200,246.25), (201,2147483647,245)]",
            "POST_MERGE_VOLUME_DISCOUNT_VALUES" to "[(1,100,247.5), (101,200,246.25), (201,2147483647,245)]",
            evaluatorConfig.strategy.name + "_OUTPUT" to "[(1,100,247.5), (101,200,246.25), (201,2147483647,245)]"
        )

        assertTrue(
            expectedOutput.value.equalsTo((result!!.output as LadderValue).value),
            "" + result.output + " is not matched with " + expectedOutput
        )

        MapComparatorUtils.compareMaps(
            result.metadata,
            expectedMetadata
        )
    }

}
