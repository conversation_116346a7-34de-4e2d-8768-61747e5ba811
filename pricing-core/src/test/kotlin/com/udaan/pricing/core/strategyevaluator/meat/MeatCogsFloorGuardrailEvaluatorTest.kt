package com.udaan.pricing.core.strategyevaluator.meat

import com.udaan.pricing.core.strategyevaluator.impl.dynamic.meat.MeatCogsFloorGuardrailEvaluator
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal

class MeatCogsFloorGuardrailEvaluatorTest {

    @Test
    fun `test meat cogs guardrail with previous output as null`() {
        val meatCogsFloorGuardrailStrategyConfig = EvaluatorRequestContextUtils.createMeatCogsFloorGuardrailEvaluatorConfig(
            previousOutput = null,
            conversionRate = BigDecimalValue(BigDecimal(1)),
            meatCogsWotPaiseUnit = BigDecimalValue(BigDecimal(8000))
        )
        assertThrows<IllegalArgumentException> {
            MeatCogsFloorGuardrailEvaluator.evaluate(meatCogsFloorGuardrailStrategyConfig)
        }
    }

    @Test
    fun `test meat cogs guardrail with cogs as null`() {
        val meatCogsFloorGuardrailStrategyConfig = EvaluatorRequestContextUtils.createMeatCogsFloorGuardrailEvaluatorConfig(
            previousOutput = BigDecimalValue(BigDecimal(10000)),
            conversionRate = BigDecimalValue(BigDecimal(1)),
            meatCogsWotPaiseUnit = null
        )
        val result = MeatCogsFloorGuardrailEvaluator.evaluate(meatCogsFloorGuardrailStrategyConfig)

        assertTrue(result.output.toString() == BigDecimalValue(BigDecimal(10000)).toString())
        assertEquals(
            mapOf(
                "FLOOR_GUARDRAIL_WOT_PAISE_AT_ASSORTMENT" to "null",
                "FLOOR_GUARDRAIL_HIT" to "false"
            ), result.metadata
        )
    }

    @Test
    fun `test meat cogs guardrail with all valid values`() {
        val meatCogsFloorGuardrailStrategyConfig = EvaluatorRequestContextUtils.createMeatCogsFloorGuardrailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(1)),
            previousOutput = BigDecimalValue(BigDecimal(10000)),
            meatCogsWotPaiseUnit = BigDecimalValue(BigDecimal(8000))
        )
        val result = MeatCogsFloorGuardrailEvaluator.evaluate(meatCogsFloorGuardrailStrategyConfig)

        assertTrue(result.output.toString() == BigDecimalValue(BigDecimal(10000)).toString())
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_WOT_PAISE_AT_ASSORTMENT" to "8000",
            "FLOOR_GUARDRAIL_HIT" to "false"
        ), result.metadata)
    }

    @Test
    fun `test meat cogs guardrail with conversion rate greater than 1`() {
        val meatCogsFloorGuardrailStrategyConfig = EvaluatorRequestContextUtils.createMeatCogsFloorGuardrailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(10)),
            previousOutput = BigDecimalValue(BigDecimal(100000)),
            meatCogsWotPaiseUnit = BigDecimalValue(BigDecimal(8000))
        )
        val result = MeatCogsFloorGuardrailEvaluator.evaluate(meatCogsFloorGuardrailStrategyConfig)

        assertTrue(result.output.toString() == BigDecimalValue(BigDecimal(100000)).toString())
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_WOT_PAISE_AT_ASSORTMENT" to "80000",
            "FLOOR_GUARDRAIL_HIT" to "false"
        ), result.metadata)
    }

    @Test
    fun `test meat cogs guardrail with final price guard-railed to floor`() {
        val meatCogsFloorGuardrailStrategyConfig = EvaluatorRequestContextUtils.createMeatCogsFloorGuardrailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(1)),
            previousOutput = BigDecimalValue(BigDecimal(7900)),
            meatCogsWotPaiseUnit = BigDecimalValue(BigDecimal(8000))
        )
        val result = MeatCogsFloorGuardrailEvaluator.evaluate(meatCogsFloorGuardrailStrategyConfig)

        assertTrue(result.output.toString() == BigDecimalValue(BigDecimal(8000)).toString())
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_WOT_PAISE_AT_ASSORTMENT" to "8000",
            "FLOOR_GUARDRAIL_HIT" to "true",
            "GUARD_RAILED_TO_FLOOR_PRICE" to "8000"
        ), result.metadata)
    }

    @Test
    fun `test meat cogs guardrail with previous output as ladder and price in range`() {
        val meatCogsFloorGuardrailStrategyConfig = EvaluatorRequestContextUtils.createMeatCogsFloorGuardrailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(1)),
            previousOutput = LadderValue(listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(10000)
                )
            )),
            meatCogsWotPaiseUnit = BigDecimalValue(BigDecimal(8000))
        )
        val result = MeatCogsFloorGuardrailEvaluator.evaluate(meatCogsFloorGuardrailStrategyConfig)

        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(10000)
                )
            )
        )

        assertTrue(result.output.toString() == expectedOutput.toString())
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_WOT_PAISE_AT_ASSORTMENT" to "8000",
            "FLOOR_GUARDRAIL_HIT" to "false"
        ), result.metadata)
    }

    @Test
    fun `test meat cogs guardrail with previous output as ladder and price guard-railed to floor`() {
        val meatCogsFloorGuardrailStrategyConfig = EvaluatorRequestContextUtils.createMeatCogsFloorGuardrailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(1)),
            previousOutput = LadderValue(listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = 20,
                    ladderValue = BigDecimal(8100)
                ),
                Ladder(
                    minQuantity = 21,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(7800)
                )
            )),
            meatCogsWotPaiseUnit = BigDecimalValue(BigDecimal(8000))
        )
        val result = MeatCogsFloorGuardrailEvaluator.evaluate(meatCogsFloorGuardrailStrategyConfig)

        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = 20,
                    ladderValue = BigDecimal(8100)
                ),
                Ladder(
                    minQuantity = 21,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(8000)
                )
            )
        )
        assertTrue(result.output.toString() == expectedOutput.toString())
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_WOT_PAISE_AT_ASSORTMENT" to "8000",
            "2_SLAB_GUARD_RAILED_TO_FLOOR_PRICE" to "8000",
            "FLOOR_GUARDRAIL_HIT" to "true"
        ), result.metadata)
    }


}
