package com.udaan.pricing.core.strategyevaluator.fresh

import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.commons.QuantityType
import com.udaan.pricing.commons.StringValue
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fresh.FreshNinjacartCompMatchEvaluator
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils.equalsTo
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal

class FreshNinjacartCompMatchEvaluatorTest {

    /*
        The test Evaluator configuration used here assumes below defaults:
        gstBps = 0,
        cessBps = 0,
        freshCogsPriceWotPaisaUnit = null,
        ninjacartPrice = null,
        conversionRate = 1,
        ninjacartQtyType = GRAM,
        ninjacartQtyPerUnit = 1000,
        listingQtyType = GRAM,
        listingQtyPerUnit = 1000
     */

    @Test
    fun `test ninjacart comp match with previous output null`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            previousInput = null
        )
        assertThrows<IllegalArgumentException> {
            FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        }
    }

    @Test
    fun `test ninjacart comp match with ninjacart price as null`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("100")
                    )
                )
            )
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertEquals(
            LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("100")
                    )
                )
            ),
            result.output,
            "Output value mismatch"
        )
        assertEquals(emptyMap<String, String>(), result.metadata, "Metadata should be empty")
    }

    @Test
    fun `test ninjacart comp match with ninjacart qtyType null`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            freshCogsPriceWotPaisaUnit = BigDecimalValue(BigDecimal("80")),
            ninjacartPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("97")
                    )
                )
            ),
            previousInput = BigDecimalValue(BigDecimal("100")),
            ninjacartQtyType = null
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(100)
                    )
                )
            ),
            "Output ladder value mismatch"
        )

        assertEquals(
            mapOf(
                "DEFAULT_LADDER_CREATED" to "true",
                "NINJACART_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,97)]",
                "LISTING_QUANTITY_TYPE" to "GRAM",
                "COMP_QUANTITY_TYPE" to "null",
                "QUANTITY_TYPE_MATCHED" to "false"
            ), 
            result.metadata
        )
    }

    @Test
    fun `test ninjacart comp match with both ninjacart and listing qtyType null`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            freshCogsPriceWotPaisaUnit = BigDecimalValue(BigDecimal("80")),
            ninjacartPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("97")
                    )
                )
            ),
            previousInput = BigDecimalValue(BigDecimal("100")),
            ninjacartQtyType = null,
            listingQtyType = null
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(100)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "DEFAULT_LADDER_CREATED" to "true",
                "NINJACART_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,97)]",
                "LISTING_QUANTITY_TYPE" to "null",
                "COMP_QUANTITY_TYPE" to "null",
                "QUANTITY_TYPE_MATCHED" to "false"
            ),
            result.metadata,
            "Metadata mismatch"
        )
    }

    @Test
    fun `test ninjacart comp match with ninjacart qtyType not matching listing qtyType`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            freshCogsPriceWotPaisaUnit = BigDecimalValue(BigDecimal("80")),
            ninjacartPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("97")
                    )
                )
            ),
            previousInput = BigDecimalValue(BigDecimal("100")),
            ninjacartQtyType = StringValue(QuantityType.PIECE.name),
            listingQtyType = StringValue(QuantityType.GRAM.name)
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(100)
                    )
                )
            ),
            "Output ladder value mismatch"
        )
        assertEquals(
            mapOf(
                "DEFAULT_LADDER_CREATED" to "true",
                "NINJACART_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,97)]",
                "LISTING_QUANTITY_TYPE" to "GRAM",
                "COMP_QUANTITY_TYPE" to "PIECE",
                "QUANTITY_TYPE_MATCHED" to "false"
            ), result.metadata
        )
    }

    @Test
    fun `test ninjacart comp match with ninjacart qtyPerUnit null`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            freshCogsPriceWotPaisaUnit = BigDecimalValue(BigDecimal("80")),
            ninjacartPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("97")
                    )
                )
            ),
            previousInput = BigDecimalValue(BigDecimal("100")),
            ninjacartQtyPerUnit = null
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(100)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "DEFAULT_LADDER_CREATED" to "true",
                "NINJACART_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,97)]",
                "LISTING_QUANTITY_TYPE" to "GRAM",
                "COMP_QUANTITY_TYPE" to "GRAM",
                "QUANTITY_TYPE_MATCHED" to "true",
                "COMP_CONVERTED_TO_LISTING_PACK_SIZE" to "false"
            ), result.metadata
        )
    }

    @Test
    fun `test ninjacart comp match with ninjacart price but guardrail null`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            freshCogsPriceWotPaisaUnit = null,
            ninjacartPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("97")
                    )
                )
            ),
            previousInput = BigDecimalValue(BigDecimal("100"))
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(100)
                    )
                )
            ),
            "Output ladder value mismatch"
        )
        assertEquals(
            mapOf(
                "DEFAULT_LADDER_CREATED" to "true",
                "NINJACART_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,97)]",
                "LISTING_QUANTITY_TYPE" to "GRAM",
                "COMP_QUANTITY_TYPE" to "GRAM",
                "QUANTITY_TYPE_MATCHED" to "true",
                "COMP_CONVERTED_TO_LISTING_PACK_SIZE" to "true",
                "NINJACART_TRANSLATED_PACK_SIZE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,97)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "null",
                "NO_COMP_COMPARISON_AS_NO_GUARDRAIL" to "true"
            ), result.metadata
        )
    }

    @Test
    fun `test ninjacart comp match with ninjacart price and previous input as big-decimal`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            freshCogsPriceWotPaisaUnit = BigDecimalValue(BigDecimal("80")),
            ninjacartPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("97")
                    )
                )
            ),
            previousInput = BigDecimalValue(BigDecimal("100"))
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(97)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "DEFAULT_LADDER_CREATED" to "true",
                "NINJACART_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,97)]",
                "LISTING_QUANTITY_TYPE" to "GRAM",
                "COMP_QUANTITY_TYPE" to "GRAM",
                "QUANTITY_TYPE_MATCHED" to "true",
                "COMP_CONVERTED_TO_LISTING_PACK_SIZE" to "true",
                "NINJACART_TRANSLATED_PACK_SIZE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,97)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000"
            ), result.metadata
        )
    }

    @Test
    fun `test ninjacart comp match with ninjacart price and previous input as ladder`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            freshCogsPriceWotPaisaUnit = BigDecimalValue(BigDecimal("80")),
            ninjacartPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("98")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("100")
                    )
                )
            )
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(98)
                    )
                )
            ),
            "Output ladder value mismatch"
        )
        assertEquals(
            mapOf(
                "NINJACART_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,98)]",
                "LISTING_QUANTITY_TYPE" to "GRAM",
                "COMP_QUANTITY_TYPE" to "GRAM",
                "QUANTITY_TYPE_MATCHED" to "true",
                "COMP_CONVERTED_TO_LISTING_PACK_SIZE" to "true",
                "NINJACART_TRANSLATED_PACK_SIZE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,98)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000"
            ), result.metadata
        )
    }

    @Test
    fun `test ninjacart comp match with ninjacart price and conversion rate as 10`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal("10")),
            freshCogsPriceWotPaisaUnit = BigDecimalValue(BigDecimal("8.0")),
            ninjacartPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("9.8")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("100")
                    )
                )
            )
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(98)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "NINJACART_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,98)]",
                "LISTING_QUANTITY_TYPE" to "GRAM",
                "COMP_QUANTITY_TYPE" to "GRAM",
                "QUANTITY_TYPE_MATCHED" to "true",
                "COMP_CONVERTED_TO_LISTING_PACK_SIZE" to "true",
                "NINJACART_TRANSLATED_PACK_SIZE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,98)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000"
            ), result.metadata
        )
    }

    @Test
    fun `test ninjacart comp match with ninjacart price with ninjacart competitive but floor hit`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            freshCogsPriceWotPaisaUnit = BigDecimalValue(BigDecimal("80")),
            ninjacartPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("78")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("100")
                    )
                )
            )
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(80)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "NINJACART_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,78)]",
                "LISTING_QUANTITY_TYPE" to "GRAM",
                "COMP_QUANTITY_TYPE" to "GRAM",
                "QUANTITY_TYPE_MATCHED" to "true",
                "COMP_CONVERTED_TO_LISTING_PACK_SIZE" to "true",
                "NINJACART_TRANSLATED_PACK_SIZE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,78)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000",
                "COMP_PRICE_GUARD_RAILED_TO_FLOOR_FOR_LADDER_1" to "true"
            ),
            result.metadata
        )
    }

    @Test
    fun `test ninjacart comp match with ninjacart price competitive in one ladder and non-competitive in another`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            freshCogsPriceWotPaisaUnit = BigDecimalValue(BigDecimal("80")),
            ninjacartPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("98")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("97.5")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 2,
                        ladderValue = BigDecimal("100")
                    ),
                    Ladder(
                        minQuantity = 3,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("96")
                    )
                )
            )
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 2,
                        ladderValue = BigDecimal(98)
                    ),
                    Ladder(
                        minQuantity = 3,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(96)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "NINJACART_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,3,98), (4,2147483647,97.5)]",
                "LISTING_QUANTITY_TYPE" to "GRAM",
                "COMP_QUANTITY_TYPE" to "GRAM",
                "QUANTITY_TYPE_MATCHED" to "true",
                "COMP_CONVERTED_TO_LISTING_PACK_SIZE" to "true",
                "NINJACART_TRANSLATED_PACK_SIZE_WOT_PAISE_ASSORTMENT" to "[(1,3,98), (4,2147483647,97.5)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000"
            ), result.metadata
        )
    }

    @Test
    fun `test ninjacart comp match with ninjacart price competitive in all ladders`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            freshCogsPriceWotPaisaUnit = BigDecimalValue(BigDecimal("80")),
            ninjacartPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal("98")
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("97")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("96")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("100")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal("99")
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("98")
                    )
                )
            )
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal(98)
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal(97)
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(96)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "NINJACART_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,1,98), (2,3,97), (4,2147483647,96)]",
                "LISTING_QUANTITY_TYPE" to "GRAM",
                "COMP_QUANTITY_TYPE" to "GRAM",
                "QUANTITY_TYPE_MATCHED" to "true",
                "COMP_CONVERTED_TO_LISTING_PACK_SIZE" to "true",
                "NINJACART_TRANSLATED_PACK_SIZE_WOT_PAISE_ASSORTMENT" to "[(1,1,98), (2,3,97), (4,2147483647,96)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000"
            ), result.metadata
        )
    }

    @Test
    fun `test ninjacart comp match with ninjacart price having more than ladder threshold count ladders`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            freshCogsPriceWotPaisaUnit = BigDecimalValue(BigDecimal("1415")),
            ninjacartPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal("1435")
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 4,
                        ladderValue = BigDecimal("1430")
                    ),
                    Ladder(
                        minQuantity = 5,
                        maxQuantity = 9,
                        ladderValue = BigDecimal("1426")
                    ),
                    Ladder(
                        minQuantity = 10,
                        maxQuantity = 12,
                        ladderValue = BigDecimal("1421")
                    ),
                    Ladder(
                        minQuantity = 13,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("1420")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal("1474")
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 2,
                        ladderValue = BigDecimal("1471")
                    ),
                    Ladder(
                        minQuantity = 3,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("1467")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("1460")
                    )
                )
            )
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal(1435)
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 4,
                        ladderValue = BigDecimal(1430)
                    ),
                    Ladder(
                        minQuantity = 5,
                        maxQuantity = 9,
                        ladderValue = BigDecimal(1426)
                    ),
                    Ladder(
                        minQuantity = 10,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(1420)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "NINJACART_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,1,1435), (2,4,1430), (5,9,1426), (10,12,1421), (13,2147483647,1420)]",
                "LISTING_QUANTITY_TYPE" to "GRAM",
                "COMP_QUANTITY_TYPE" to "GRAM",
                "QUANTITY_TYPE_MATCHED" to "true",
                "COMP_CONVERTED_TO_LISTING_PACK_SIZE" to "true",
                "NINJACART_TRANSLATED_PACK_SIZE_WOT_PAISE_ASSORTMENT" to "[(1,1,1435), (2,4,1430), (5,9,1426), (10,12,1421), (13,2147483647,1420)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "1415.0000",
                "LADDERS_SIZE_CAPPED" to "true"
            ), result.metadata
        )
    }

    @Test
    fun `test ninjacart comp match with ninjacart price has more than 4 ladders with some hitting floor`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            freshCogsPriceWotPaisaUnit = BigDecimalValue(BigDecimal("80")),
            ninjacartPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal("83")
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("82")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal("80.5")
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = 8,
                        ladderValue = BigDecimal("80")
                    ),
                    Ladder(
                        minQuantity = 9,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("78")
                    ),
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("86")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal("84")
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("82")
                    )
                )
            )
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal(83)
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal(82)
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal(80.5)
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(80)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "NINJACART_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,1,83), (2,3,82), (4,5,80.5), (6,8,80), (9,2147483647,78)]",
                "LISTING_QUANTITY_TYPE" to "GRAM",
                "COMP_QUANTITY_TYPE" to "GRAM",
                "QUANTITY_TYPE_MATCHED" to "true",
                "COMP_CONVERTED_TO_LISTING_PACK_SIZE" to "true",
                "NINJACART_TRANSLATED_PACK_SIZE_WOT_PAISE_ASSORTMENT" to "[(1,1,83), (2,3,82), (4,5,80.5), (6,8,80), (9,2147483647,78)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000",
                "COMP_PRICE_GUARD_RAILED_TO_FLOOR_FOR_LADDER_5" to "true"
            ), result.metadata
        )
    }

    @Test
    fun `test ninjacart comp match with ninjacart price and input both lower than floor guardrail value`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            freshCogsPriceWotPaisaUnit = BigDecimalValue(BigDecimal("80")),
            ninjacartPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal("83")
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("82")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 8,
                        ladderValue = BigDecimal("80")
                    ),
                    Ladder(
                        minQuantity = 9,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("78")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("83")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal("81")
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("79")
                    )
                )
            )
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal(83)
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal(82)
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal(80)
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(79)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "NINJACART_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,1,83), (2,3,82), (4,8,80), (9,2147483647,78)]",
                "LISTING_QUANTITY_TYPE" to "GRAM",
                "COMP_QUANTITY_TYPE" to "GRAM",
                "QUANTITY_TYPE_MATCHED" to "true",
                "COMP_CONVERTED_TO_LISTING_PACK_SIZE" to "true",
                "NINJACART_TRANSLATED_PACK_SIZE_WOT_PAISE_ASSORTMENT" to "[(1,1,83), (2,3,82), (4,8,80), (9,2147483647,78)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000",
                "COMP_PRICE_GUARD_RAILED_TO_FLOOR_FOR_LADDER_4" to "true"
            ), result.metadata
        )
    }

    @Test
    fun `test ninjacart comp match with ninjacart qtyPerUnit different from listing qtyPerUnit`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            freshCogsPriceWotPaisaUnit = BigDecimalValue(BigDecimal("80")),
            ninjacartQtyPerUnit = BigDecimalValue(BigDecimal("2000")),
            listingQtyPerUnit = BigDecimalValue(BigDecimal("5000")),
            ninjacartPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal("39.2")
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("38.8")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("38.4")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("100")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal("99")
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("98")
                    )
                )
            )
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal(97)
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(96)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "NINJACART_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,1,39.2), (2,3,38.8), (4,2147483647,38.4)]",
                "LISTING_QUANTITY_TYPE" to "GRAM",
                "COMP_QUANTITY_TYPE" to "GRAM",
                "QUANTITY_TYPE_MATCHED" to "true",
                "COMP_CONVERTED_TO_LISTING_PACK_SIZE" to "true",
                "NINJACART_TRANSLATED_PACK_SIZE_WOT_PAISE_ASSORTMENT" to "[(1,1,97), (2,2147483647,96)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000"
            ),
            result.metadata,
            "Metadata mismatch"
        )
    }

    @Test
    fun `test ninjacart comp match with ninjacart with different qtyPerUnit hitting floor in some slabs`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFreshNinjacartCompMatchEvaluatorConfig(
            freshCogsPriceWotPaisaUnit = BigDecimalValue(BigDecimal("88")),
            ninjacartQtyPerUnit = BigDecimalValue(BigDecimal("2000")),
            listingQtyPerUnit = BigDecimalValue(BigDecimal("5000")),
            ninjacartPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("36")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("34.4")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("100")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("98")
                    )
                )
            )
        )
        val result = FreshNinjacartCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal(90)
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(88)
                    )
                )
            ),
            "Output ladder value mismatch"
        )
        assertEquals(
            mapOf(
                "NINJACART_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,3,36), (4,2147483647,34.4)]",
                "LISTING_QUANTITY_TYPE" to "GRAM",
                "COMP_QUANTITY_TYPE" to "GRAM",
                "QUANTITY_TYPE_MATCHED" to "true",
                "COMP_CONVERTED_TO_LISTING_PACK_SIZE" to "true",
                "NINJACART_TRANSLATED_PACK_SIZE_WOT_PAISE_ASSORTMENT" to "[(1,1,90), (2,2147483647,86)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "88.0000",
                "COMP_PRICE_GUARD_RAILED_TO_FLOOR_FOR_LADDER_2" to "true"
            ), result.metadata
        )
    }
}
