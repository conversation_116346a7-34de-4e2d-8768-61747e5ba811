package com.udaan.pricing.core.strategyevaluator.staples

import com.udaan.pricing.core.strategyevaluator.impl.dynamic.staples.VslMarkupPriceEvaluator
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.divideWithScale
import com.udaan.pricing.commons.BigDecimalValue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal

class VslMarkupPriceEvaluatorTest {

    @Test
    fun `test vsl price with null vsl price`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createVslPriceEvaluatorConfig(
            vslPriceInPaisa = null,
            vslMarkUpBps = BigDecimalValue(BigDecimal(0))
        )

        assertThrows<IllegalArgumentException> {
            VslMarkupPriceEvaluator.evaluate(evaluatorConfig)
        }
    }

    @Test
    fun `test vsl price with null markup`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createVslPriceEvaluatorConfig(
            vslPriceInPaisa = BigDecimalValue(BigDecimal(100)),
            vslMarkUpBps = null
        )
        val result = VslMarkupPriceEvaluator.evaluate(evaluatorConfig)

        assertEquals(
            (result.output as BigDecimalValue).value,
            BigDecimalValue(BigDecimal(100)).value.divideWithScale(BigDecimal(1))
        )
        assertEquals(emptyMap<String, String>(), result.metadata)
    }

    @Test
    fun `test vsl price with 0 markup`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createVslPriceEvaluatorConfig(
            vslPriceInPaisa = BigDecimalValue(BigDecimal(100)),
            vslMarkUpBps = BigDecimalValue(BigDecimal(0))
        )
        val result = VslMarkupPriceEvaluator.evaluate(evaluatorConfig)

        assertEquals(
            (result.output as BigDecimalValue).value,
            BigDecimalValue(BigDecimal(100)).value.divideWithScale(BigDecimal(1))
        )
        assertEquals(emptyMap<String, String>(), result.metadata)
    }

    @Test
    fun `test vsl price with -1 markup`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createVslPriceEvaluatorConfig(
            vslPriceInPaisa = BigDecimalValue(BigDecimal(100)),
            vslMarkUpBps = BigDecimalValue(BigDecimal(-100))
        )
        val result = VslMarkupPriceEvaluator.evaluate(evaluatorConfig)

        assertEquals(
            (result.output as BigDecimalValue).value,
            BigDecimalValue(BigDecimal(99)).value.divideWithScale(BigDecimal(1))
        )
        assertEquals(emptyMap<String, String>(), result.metadata)
    }


    @Test
    fun `test vsl price with +1 markup`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createVslPriceEvaluatorConfig(
            vslPriceInPaisa = BigDecimalValue(BigDecimal(100)),
            vslMarkUpBps = BigDecimalValue(BigDecimal(100))
        )
        val result = VslMarkupPriceEvaluator.evaluate(evaluatorConfig)

        assertEquals(
            (result.output as BigDecimalValue).value,
            BigDecimalValue(BigDecimal(101)).value.divideWithScale(BigDecimal(1))
        )
        assertEquals(emptyMap<String, String>(), result.metadata)
    }
}
