package com.udaan.pricing.core.strategyevaluator.fresh

import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fresh.FreshCogsFloorGuardrailEvaluator
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal

class FreshCogsFloorGuardrailEvaluatorTest {

    @Test
    fun `test fresh cogs guardrail with previous output as null`() {
        val freshCogsFloorGuardrailStrategyConfig = EvaluatorRequestContextUtils.createFreshCogsFloorGuardrailEvaluatorConfig(
            previousOutput = null,
            conversionRate = BigDecimalValue(BigDecimal(1)),
            freshCogsWotPaiseUnit = BigDecimalValue(BigDecimal(8000))
        )
        assertThrows<IllegalArgumentException> {
            FreshCogsFloorGuardrailEvaluator.evaluate(freshCogsFloorGuardrailStrategyConfig)
        }
    }

    @Test
    fun `test fresh cogs guardrail with cogs as null`() {
        val freshCogsFloorGuardrailStrategyConfig = EvaluatorRequestContextUtils.createFreshCogsFloorGuardrailEvaluatorConfig(
            previousOutput = BigDecimalValue(BigDecimal(10000)),
            conversionRate = BigDecimalValue(BigDecimal(1)),
            freshCogsWotPaiseUnit = null
        )
        val result = FreshCogsFloorGuardrailEvaluator.evaluate(freshCogsFloorGuardrailStrategyConfig)

        assertEquals(
            BigDecimalValue(BigDecimal(10000)).toString(),
            result.output.toString(),
            "Output value mismatch"
        )
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_WOT_PAISE_AT_ASSORTMENT" to "null",
            "FLOOR_GUARDRAIL_HIT" to "false"
        ), result.metadata)
    }

    @Test
    fun `test fresh cogs guardrail with all valid values`() {
        val freshCogsFloorGuardrailStrategyConfig = EvaluatorRequestContextUtils.createFreshCogsFloorGuardrailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(1)),
            previousOutput = BigDecimalValue(BigDecimal(10000)),
            freshCogsWotPaiseUnit = BigDecimalValue(BigDecimal(8000))
        )
        val result = FreshCogsFloorGuardrailEvaluator.evaluate(freshCogsFloorGuardrailStrategyConfig)

        assertEquals(
            BigDecimalValue(BigDecimal(10000)).toString(),
            result.output.toString(),
            "Output value mismatch"
        )
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_WOT_PAISE_AT_ASSORTMENT" to "8000",
            "FLOOR_GUARDRAIL_HIT" to "false"
        ), result.metadata)
    }

    @Test
    fun `test fresh cogs guardrail with conversion rate greater than 1`() {
        val freshCogsFloorGuardrailStrategyConfig = EvaluatorRequestContextUtils.createFreshCogsFloorGuardrailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(10)),
            previousOutput = BigDecimalValue(BigDecimal(100000)),
            freshCogsWotPaiseUnit = BigDecimalValue(BigDecimal(8000))
        )
        val result = FreshCogsFloorGuardrailEvaluator.evaluate(freshCogsFloorGuardrailStrategyConfig)

        assertEquals(
            BigDecimalValue(BigDecimal(100000)).toString(),
            result.output.toString(),
            "Output value mismatch"
        )
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_WOT_PAISE_AT_ASSORTMENT" to "80000",
            "FLOOR_GUARDRAIL_HIT" to "false"
        ), result.metadata)
    }

    @Test
    fun `test fresh cogs guardrail with final price guard-railed to floor`() {
        val freshCogsFloorGuardrailStrategyConfig = EvaluatorRequestContextUtils.createFreshCogsFloorGuardrailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(1)),
            previousOutput = BigDecimalValue(BigDecimal(7900)),
            freshCogsWotPaiseUnit = BigDecimalValue(BigDecimal(8000))
        )
        val result = FreshCogsFloorGuardrailEvaluator.evaluate(freshCogsFloorGuardrailStrategyConfig)

        assertEquals(
            BigDecimalValue(BigDecimal(8000)).toString(),
            result.output.toString(),
            "Output value mismatch"
        )
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_WOT_PAISE_AT_ASSORTMENT" to "8000",
            "FLOOR_GUARDRAIL_HIT" to "true",
            "GUARD_RAILED_TO_FLOOR_PRICE" to "8000"
        ), result.metadata)
    }

    @Test
    fun `test fresh cogs guardrail with previous output as ladder and price in range`() {
        val freshCogsFloorGuardrailStrategyConfig = EvaluatorRequestContextUtils.createFreshCogsFloorGuardrailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(1)),
            previousOutput = LadderValue(listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(10000)
                )
            )),
            freshCogsWotPaiseUnit = BigDecimalValue(BigDecimal(8000))
        )
        val result = FreshCogsFloorGuardrailEvaluator.evaluate(freshCogsFloorGuardrailStrategyConfig)

        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(10000)
                )
            )
        )

        assertEquals(
            expectedOutput.toString(),
            result.output.toString(),
            "Output value mismatch"
        )
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_WOT_PAISE_AT_ASSORTMENT" to "8000",
            "FLOOR_GUARDRAIL_HIT" to "false"
        ), result.metadata)
    }

    @Test
    fun `test fresh cogs guardrail with previous output as ladder and price guard-railed to floor`() {
        val freshCogsFloorGuardrailStrategyConfig = EvaluatorRequestContextUtils.createFreshCogsFloorGuardrailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(1)),
            previousOutput = LadderValue(listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = 20,
                    ladderValue = BigDecimal(8100)
                ),
                Ladder(
                    minQuantity = 21,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(7800)
                )
            )),
            freshCogsWotPaiseUnit = BigDecimalValue(BigDecimal(8000))
        )
        val result = FreshCogsFloorGuardrailEvaluator.evaluate(freshCogsFloorGuardrailStrategyConfig)

        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = 20,
                    ladderValue = BigDecimal(8100)
                ),
                Ladder(
                    minQuantity = 21,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(8000)
                )
            )
        )
        assertEquals(
            expectedOutput.toString(),
            result.output.toString(),
            "Output value mismatch"
        )
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_WOT_PAISE_AT_ASSORTMENT" to "8000",
            "2_SLAB_GUARD_RAILED_TO_FLOOR_PRICE" to "8000",
            "FLOOR_GUARDRAIL_HIT" to "true"
        ), result.metadata)
    }


}
