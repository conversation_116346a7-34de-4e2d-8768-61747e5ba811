package com.udaan.pricing.core.converters

import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.helpers.signals.rawsignalinputconverters.VolumetricSignalInputConverter
import com.udaan.pricing.signalcreation.VolumetricSignalInput
import com.udaan.pricing.signals.GenericMetadata
import com.udaan.pricing.signals.SignalState
import com.udaan.pricing.variable.VariableId
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.assertThrows
import java.lang.IllegalArgumentException
import java.math.BigDecimal

class VolumetricSignalInputConverterTest {

    @Test
    fun `convert should throw error due to invalid input`(): Unit = runBlocking {
        // Arrange: create a dummy input with mixed-case values and a non-empty ladders list
        val input = VolumetricSignalInput(
            catalogEntityId = "abc123",
            catalogEntityType = CatalogEntityType.PRODUCT_GROUP_ID,
            ladders = listOf(Ladder(1, 2, BigDecimal(12)), Ladder(2, 10, BigDecimal(15))),
            location = Location(LocationType.WAREHOUSE, "loc123"),
            updatedBy = "probe"
        )
        val converter = VolumetricSignalInputConverter()
        assertThrows<IllegalArgumentException> {
            runBlocking {
                converter.convert(input)
            }
        }
    }

    @Test
    fun `convert should return correct signal for valid input`() = runBlocking {
        // Arrange: create a dummy input with mixed-case values and a non-empty ladders list
        val input = VolumetricSignalInput(
            catalogEntityId = "abc123",
            catalogEntityType = CatalogEntityType.PRODUCT_GROUP_ID,
            ladders = listOf(Ladder(1, 2, BigDecimal(12)), Ladder(3, 10, BigDecimal(15))),
            location = Location(LocationType.WAREHOUSE, "loc123"),
            updatedBy = "probe"
        )
        val converter = VolumetricSignalInputConverter()
        val signals = converter.convert(input)

        assertNotNull(signals)
        assertEquals(1, signals.size)

        val signal = signals.first()
        assertEquals("ABC123", signal.catalogEntity)
        assertEquals("PRODUCT_GROUP_ID", signal.catalogEntityType.name)
        assertEquals(VariableId.VOLUMETRIC_LADDER_DISCOUNT_BPS.name, signal.variableId)
        assertEquals(LadderValue(input.ladders), signal.signalData)
        assertEquals(GenericMetadata(emptyMap()), signal.metadata)
        assertEquals("LOC123", signal.location.locationValue)
        assertEquals(SignalState.ACTIVE, signal.state)
        assertEquals("probe", signal.createdBy)
        assertEquals("probe", signal.updatedBy)
    }
}
