package com.udaan.pricing.core.strategyevaluator.staples

import com.udaan.pricing.core.strategyevaluator.impl.EvaluatorFactory
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils.equalsTo
import com.udaan.pricing.core.strategyevaluator.utils.MapComparatorUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal

class DiscountSlabsEvaluatorTest {

    @Test
    fun `test adjustment ladder with previous output of Ladder type`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createDiscountSlabsEvaluatorTestConfig(
            previousStrategyOutput = LadderValue(
                listOf(
                    Ladder(minQuantity = 1, maxQuantity = 2, ladderValue = BigDecimal(10))
                )
            ),
            conversionRate = BigDecimalValue(BigDecimal(1))
        )

        assertThrows<IllegalArgumentException> {
            EvaluatorFactory.evaluate(listOf(evaluatorConfig))
        }
    }

    @Test
    fun `test adjustment ladder with no discount slabs`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createDiscountSlabsEvaluatorTestConfig(
            previousStrategyOutput = BigDecimalValue(BigDecimal("100.0")),
            conversionRate = BigDecimalValue(BigDecimal(1))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal("100.0")
                )
            )
        )
        assertTrue(expectedOutput.value.equalsTo((result!!.output as LadderValue).value)) {
            "" + result.output + "is not matched with " + expectedOutput
        }

        MapComparatorUtils.compareMaps(
            result.metadata,
            mapOf(
                "DISCOUNT_SLABS_INPUT" to "100",
                "DISCOUNT_SLABS" to "null",
                "CONVERSION_RATE" to "1",
                "DEFAULT_LADDER_PASSED" to "true",
                "DISCOUNT_SLABS_OUTPUT" to "[(1,2147483647,100)]"
            )
        )
    }

    @Test
    fun `test adjustment ladder with vsl price but no ladders and conversion rate as 2`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createDiscountSlabsEvaluatorTestConfig(
            previousStrategyOutput = BigDecimalValue(BigDecimal("100.0")),
            conversionRate = BigDecimalValue(BigDecimal(2))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal("200.0")
                )
            )
        )
        assertTrue(expectedOutput.value.equalsTo((result!!.output as LadderValue).value)) {
            "" + result.output + "is not matched with " + expectedOutput
        }

        MapComparatorUtils.compareMaps(
            result.metadata,
            mapOf(
                "DISCOUNT_SLABS_INPUT" to "100",
                "DISCOUNT_SLABS" to "null",
                "CONVERSION_RATE" to "2",
                "DEFAULT_LADDER_PASSED" to "true",
                "DISCOUNT_SLABS_OUTPUT" to "[(1,2147483647,200)]"
            )
        )
    }

    @Test
    fun `test adjustment ladder will vsl price but with ladders`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createDiscountSlabsEvaluatorTestConfig(
            previousStrategyOutput = BigDecimalValue(BigDecimal("100.0")),
            conversionRate = BigDecimalValue(BigDecimal(1)),
            discountSlabs = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(100)
                    )
                )
            )
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal("99.0")
                )
            )
        )
        assertTrue(expectedOutput.value.equalsTo((result!!.output as LadderValue).value)) {
            "" + result.output + "is not matched with " + expectedOutput
        }

        MapComparatorUtils.compareMaps(
            result.metadata,
            mapOf(
                "DISCOUNT_SLABS_INPUT" to "100",
                "DISCOUNT_SLABS" to "[(1,2147483647,100)]",
                "CONVERSION_RATE" to "1",
                "POST_ADJUSTMENT_LADDER_VALUES" to "[(1,2147483647,99)]",
                "DISCOUNT_SLABS_OUTPUT" to "[(1,2147483647,99)]"
            )
        )
    }

    @Test
    fun `test adjustment ladder will vsl price but with multiple ladders`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createDiscountSlabsEvaluatorTestConfig(
            conversionRate = BigDecimalValue(BigDecimal(1)),
            previousStrategyOutput = BigDecimalValue(BigDecimal(100)),
            discountSlabs = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 21,
                        ladderValue = BigDecimal(-100)
                    ),
                    Ladder(
                        minQuantity = 22,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(-50)
                    )
                )
            )
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = 21,
                    ladderValue = BigDecimal("99.0000")
                ),
                Ladder(
                    minQuantity = 22,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal("99.5000")
                )
            )
        )
        assertTrue(expectedOutput.value.equalsTo((result!!.output as LadderValue).value)) {
            "" + result.output + "is not matched with " + expectedOutput
        }

        MapComparatorUtils.compareMaps(
            result.metadata,
            mapOf(
                "DISCOUNT_SLABS_INPUT" to "100",
                "DISCOUNT_SLABS" to "[(1,21,-100), (22,2147483647,-50)]",
                "CONVERSION_RATE" to "1",
                "POST_ADJUSTMENT_LADDER_VALUES" to "[(1,21,99), (22,2147483647,99.5)]",
                "DISCOUNT_SLABS_OUTPUT" to "[(1,21,99), (22,2147483647,99.5)]"
            )
        )
    }
}
