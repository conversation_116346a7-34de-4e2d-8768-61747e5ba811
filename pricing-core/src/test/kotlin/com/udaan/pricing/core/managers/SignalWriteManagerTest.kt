package com.udaan.pricing.core.managers

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.StringValue
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.dao.signals.SignalRepository
import com.udaan.pricing.core.events.EventController
import com.udaan.pricing.core.helpers.DataPlatformHelper
import com.udaan.pricing.core.helpers.signals.ResolvedValuesRedisHelper
import com.udaan.pricing.core.helpers.signals.deletesignalrequestconverters.DeleteSignalRequestConverterFactory
import com.udaan.pricing.core.helpers.signals.rawsignalinputconverters.RawSignalInputConverterFactory
import com.udaan.pricing.core.managers.signals.SignalAnomaliesManager
import com.udaan.pricing.core.managers.signals.SignalWriteManager
import com.udaan.pricing.core.managers.signals.VariableManager
import com.udaan.pricing.core.utils.signals.BigDecimalUtil.toBigDecimalWithScale
import com.udaan.pricing.signalcreation.AutomatedSourcingInput
import com.udaan.pricing.signalcreation.AutomatedSourcingInputType
import com.udaan.pricing.signalcreation.FpJitVendorPriceInput
import com.udaan.pricing.signals.GenericMetadata
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState
import com.udaan.pricing.variable.ListValuesConstraint
import com.udaan.pricing.variable.RangeValuesConstraint
import com.udaan.pricing.variable.Variable
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.variable.VariableResolvedValueType
import com.udaan.pricing.variable.VariableState
import com.udaan.pricing.variable.VariableType
import com.udaan.resources.cache.RedisCache2
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.Assert.fail
import org.junit.Test
import java.math.BigDecimal

class SignalWriteManagerTest {
    private val rawSignalInputConverterFactory: RawSignalInputConverterFactory = mockk()
    private val deleteSignalRequestConverterFactory: DeleteSignalRequestConverterFactory = mockk()
    private val signalRepository: SignalRepository = mockk()
    private val variableManager: VariableManager = mockk()
    private val objectMapper = ObjectMapper().registerKotlinModule()
    private val dataPlatformHelper: DataPlatformHelper = mockk()
    private val resolvedValuesRedisHelper: ResolvedValuesRedisHelper = mockk()
    private val signalCache: RedisCache2<Signal?> = mockk()
    private val signalAnomaliesManager: SignalAnomaliesManager = mockk()
    private val eventController: EventController = mockk()

    private val signalWriteManager = SignalWriteManager(
        rawSignalInputConverterFactory = rawSignalInputConverterFactory,
        deleteSignalRequestConverterFactory = deleteSignalRequestConverterFactory,
        signalRepository = signalRepository,
        variableManager = variableManager,
        objectMapper = objectMapper,
        dataPlatformHelper = dataPlatformHelper,
        signalCache = signalCache,
        signalAnomaliesManager = signalAnomaliesManager,
        eventController = eventController,
        resolvedValuesRedisHelper = resolvedValuesRedisHelper
    )

    @Test
    fun `validate signal creation if signal data doesn't match defined variable value subclass type`() = runBlocking {
        val automatedSourcingInput = AutomatedSourcingInput(
            groupId = "testGid",
            warehouseId = "testWhId",
            typeOfInput = AutomatedSourcingInputType.INVENTORY_FLAG,
            value = 2.0000,
            createdBy = ""
        )
        val existingSignal = Signal(
            catalogEntity = "testGid",
            signalData = BigDecimalValue(value = 2.0.toBigDecimalWithScale()),
            catalogEntityType = CatalogEntityType.PRODUCT_GROUP_ID,
            variableId = VariableId.GRANARY_INVENTORY_FLAG.name,
            metadata = GenericMetadata(emptyMap()),
            location = Location(LocationType.WAREHOUSE, ""),
            state = SignalState.ACTIVE,
            createdBy = "",
            updatedBy = ""
        )

        val newSignal = existingSignal.copy(signalData = BigDecimalValue(value = 10.0.toBigDecimalWithScale()))

        coEvery {
            rawSignalInputConverterFactory.getConverterImplAndConvert(automatedSourcingInput)
        } returns listOf(newSignal)

        coEvery {
            signalRepository.getSignalByIdAndPartitionKey(existingSignal.id, existingSignal.partitionKey)
        } returns existingSignal

        coEvery {
            variableManager.getVariable(VariableId.GRANARY_INVENTORY_FLAG.name)
        } returns Variable(
            id = VariableId.GRANARY_INVENTORY_FLAG,
            resolvedValueType = VariableResolvedValueType.STRING,
            defaultValue = null,
            freshnessDurationInMillis = null,
            type = VariableType.SIGNAL,
            listOf(Pair(CatalogEntityType.PRODUCT_GROUP_ID, LocationType.WAREHOUSE)),
            allowedValuesConstraint = ListValuesConstraint(
                listOf(
                    BigDecimalValue(BigDecimal(0)),
                    BigDecimalValue(BigDecimal(1))
                )
            ),
            state = VariableState.ACTIVE,
            createdBy = "",
            createdAt = System.currentTimeMillis()
        )

        try {
            signalWriteManager.createSignalFromRawInput(automatedSourcingInput)
            fail("Signal creation should fail, signal data doesn't match defined variable value subclass type")
        } catch (ex: IllegalArgumentException) {
            assert(ex.message!!.contains("Signal value should be of "))
        }
    }

    @Test
    fun `validate signal creation if big decimal value is not in ListData`() = runBlocking {
        val automatedSourcingInput = AutomatedSourcingInput(
            groupId = "testGid",
            warehouseId = "testWhId",
            typeOfInput = AutomatedSourcingInputType.INVENTORY_FLAG,
            value = 2.0000,
            createdBy = ""
        )
        val existingSignal = Signal(
            catalogEntity = "testGid",
            signalData = BigDecimalValue(value = 2.0.toBigDecimalWithScale()),
            catalogEntityType = CatalogEntityType.PRODUCT_GROUP_ID,
            variableId = VariableId.GRANARY_INVENTORY_FLAG.name,
            metadata = GenericMetadata(emptyMap()),
            location = Location(LocationType.WAREHOUSE, ""),
            state = SignalState.ACTIVE,
            createdBy = "",
            updatedBy = ""
        )

        val newSignal = existingSignal.copy(signalData = BigDecimalValue(value = 10.0.toBigDecimalWithScale()))

        coEvery {
            rawSignalInputConverterFactory.getConverterImplAndConvert(automatedSourcingInput)
        } returns listOf(newSignal)

        coEvery {
            signalRepository.getSignalByIdAndPartitionKey(existingSignal.id, existingSignal.partitionKey)
        } returns existingSignal

        coEvery {
            variableManager.getVariable(VariableId.GRANARY_INVENTORY_FLAG.name)
        } returns Variable(
            id = VariableId.GRANARY_INVENTORY_FLAG,
            resolvedValueType = VariableResolvedValueType.BIG_DECIMAL,
            defaultValue = null,
            freshnessDurationInMillis = null,
            type = VariableType.SIGNAL,
            listOf(Pair(CatalogEntityType.PRODUCT_GROUP_ID, LocationType.WAREHOUSE)),
            allowedValuesConstraint = ListValuesConstraint(
                listOf(
                    BigDecimalValue(BigDecimal(0)),
                    BigDecimalValue(BigDecimal(1))
                )
            ),
            state = VariableState.ACTIVE,
            createdBy = "",
            createdAt = System.currentTimeMillis()
        )

        coEvery {
            signalRepository.createOrUpdateSignal(newSignal)
        } returns newSignal

        try {
            signalWriteManager.createSignalFromRawInput(automatedSourcingInput)
            fail("Signal creation should fail, as given value should not be allowed.")
        } catch (ex: IllegalArgumentException) {
            assert(ex.message!!.contains("is not in the allowed list:"))
        }
    }

    @Test
    fun `validate signal creation if string value is not in ListData`(): Unit = runBlocking {
        val variable = VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT
        val existingSignal = Signal(
            catalogEntity = "testGid",
            signalData = StringValue("bns"),
            catalogEntityType = CatalogEntityType.PRODUCT_GROUP_ID,
            variableId = variable.name,
            metadata = GenericMetadata(emptyMap()),
            location = Location(LocationType.WAREHOUSE, ""),
            state = SignalState.ACTIVE,
            createdBy = "",
            updatedBy = ""
        )
        val gidLevelInput = FpJitVendorPriceInput(
            groupId = "testGid",
            warehouseId = "",
            unitPrice = 100.0,
            vendorId = ""
        )

        val newSignal = existingSignal.copy(signalData = StringValue("dtr"))

        coEvery {
            rawSignalInputConverterFactory.getConverterImplAndConvert(gidLevelInput)
        } returns listOf(newSignal)

        coEvery {
            variableManager.getVariable(variable.name)
        } returns Variable(
            id = variable,
            resolvedValueType = VariableResolvedValueType.STRING,
            defaultValue = null,
            freshnessDurationInMillis = null,
            type = VariableType.SIGNAL,
            listOf(Pair(CatalogEntityType.PRODUCT_GROUP_ID, LocationType.CITY)),
            allowedValuesConstraint = ListValuesConstraint(
                listOf(
                    StringValue("DTR"),
                    StringValue("BNS")
                )
            ),
            state = VariableState.ACTIVE,
            createdBy = "",
            createdAt = System.currentTimeMillis()
        )

        coEvery {
            signalRepository.getSignalByIdAndPartitionKey(existingSignal.id, existingSignal.partitionKey)
        } returns existingSignal

        try {
            signalWriteManager.createSignalFromRawInput(gidLevelInput)
            assert(false) {
                "Signal creation should fail, as given value should not be allowed."
            }
        } catch (ex: IllegalArgumentException) {
            assert(true)
        }
    }

    @Test
    fun `validate signal creation if big decimal value is not in RangeData`(): Unit = runBlocking {
        val variable = VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT
        val existingSignal = Signal(
            catalogEntity = "testGid",
            signalData = BigDecimalValue(1.1.toBigDecimalWithScale()),
            catalogEntityType = CatalogEntityType.PRODUCT_GROUP_ID,
            variableId = variable.name,
            metadata = GenericMetadata(emptyMap()),
            location = Location(LocationType.WAREHOUSE, ""),
            state = SignalState.ACTIVE,
            createdBy = "",
            updatedBy = ""
        )
        val gidLevelInput = FpJitVendorPriceInput(
            groupId = "testGid",
            warehouseId = "",
            unitPrice = 1.2,
            vendorId = ""
        )

        val newSignal = existingSignal.copy(signalData = BigDecimalValue(1.2.toBigDecimalWithScale()))
        coEvery {
            rawSignalInputConverterFactory.getConverterImplAndConvert(gidLevelInput)
        } returns listOf(newSignal)

        coEvery {
            variableManager.getVariable(variable.name)
        } returns Variable(
            id = variable,
            resolvedValueType = VariableResolvedValueType.BIG_DECIMAL,
            defaultValue = null,
            freshnessDurationInMillis = null,
            type = VariableType.SIGNAL,
            listOf(Pair(CatalogEntityType.PRODUCT_GROUP_ID, LocationType.CITY)),
            allowedValuesConstraint = RangeValuesConstraint(
                minValue = BigDecimalValue(10.0.toBigDecimalWithScale()),
                maxValue = BigDecimalValue(Int.MAX_VALUE.toBigDecimalWithScale())
            ),
            state = VariableState.ACTIVE,
            createdBy = "",
            createdAt = System.currentTimeMillis()
        )

        coEvery {
            signalRepository.getSignalByIdAndPartitionKey(existingSignal.id, existingSignal.partitionKey)
        } returns existingSignal

        try {
            signalWriteManager.createSignalFromRawInput(gidLevelInput)
            assert(false) {
                "Signal creation should fail, as given value should not be allowed."
            }
        } catch (e: java.lang.IllegalArgumentException) {
            assert(true)
        }
    }

    @Test
    fun `validate signal creation for same BigDecimal values having different scales`() = runBlocking {
        val automatedSourcingInput = AutomatedSourcingInput(
            groupId = "testGid",
            warehouseId = "testWhId",
            typeOfInput = AutomatedSourcingInputType.INVENTORY_FLAG,
            value = 2.1000,
            createdBy = ""
        )
        val existingSignal = Signal(
            catalogEntity = "testGid",
            signalData = BigDecimalValue(value = 2.1.toBigDecimalWithScale(2)),
            catalogEntityType = CatalogEntityType.PRODUCT_GROUP_ID,
            variableId = VariableId.GRANARY_INVENTORY_FLAG.name,
            metadata = GenericMetadata(emptyMap()),
            location = Location(LocationType.WAREHOUSE, ""),
            state = SignalState.ACTIVE,
            createdBy = "",
            updatedBy = ""
        )
        val mockVariable = Variable(
            id = VariableId.GRANARY_INVENTORY_FLAG,
            resolvedValueType = VariableResolvedValueType.BIG_DECIMAL,
            defaultValue = null,
            freshnessDurationInMillis = null,
            type = VariableType.SIGNAL,
            listOf(Pair(CatalogEntityType.PRODUCT_GROUP_ID, LocationType.WAREHOUSE)),
            allowedValuesConstraint = null,
            state = VariableState.ACTIVE,
            createdBy = "",
            createdAt = System.currentTimeMillis()
        )

        val newSignal = existingSignal.copy(signalData = BigDecimalValue(value = 2.1000.toBigDecimalWithScale(4)))

        coEvery {
            rawSignalInputConverterFactory.getConverterImplAndConvert(automatedSourcingInput)
        } returns listOf(newSignal)

        coEvery {
            signalRepository.getSignalByIdAndPartitionKey(existingSignal.id, existingSignal.partitionKey)
        } returns existingSignal

        coEvery {
            variableManager.getVariable(VariableId.GRANARY_INVENTORY_FLAG.name)
        } returns mockVariable

        coEvery {
            signalRepository.createOrUpdateSignal(newSignal)
        } returns newSignal

        val response = signalWriteManager.createSignalFromRawInput(automatedSourcingInput)

        assert(response.signalReferenceIds.isNotEmpty())
        assert(response.signalReferenceIds.contains(newSignal.referenceId))
    }
}
