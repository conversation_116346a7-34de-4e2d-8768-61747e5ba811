package com.udaan.pricing.core.strategyevaluator.meat

import com.udaan.pricing.core.strategyevaluator.impl.dynamic.meat.MeatManualPriceEvaluator
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.commons.BigDecimalValue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal

class MeatManualPriceEvaluatorTest {

    @Test
    fun `test meat manual price with null manual price input`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createMeatManualPriceEvaluatorConfig(
            meatManualPriceWotPaisaUnit = null,
            conversionRate = BigDecimalValue(BigDecimal("1"))
        )
        assertThrows<IllegalArgumentException> {
            MeatManualPriceEvaluator.evaluate(evaluatorConfig)
        }
    }

    @Test
    fun `test meat manual price with valid manual price input`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createMeatManualPriceEvaluatorConfig(
            meatManualPriceWotPaisaUnit = BigDecimalValue(BigDecimal("1000")),
            conversionRate = BigDecimalValue(BigDecimal("1"))
        )
        val result = MeatManualPriceEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            result.output == BigDecimalValue(BigDecimal("1000.0000"))
        )
        assertEquals(result.metadata, emptyMap<String, String>())
    }

    @Test
    fun `test meat manual price with conversion rate greater than 1`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createMeatManualPriceEvaluatorConfig(
            meatManualPriceWotPaisaUnit = BigDecimalValue(BigDecimal("1000")),
            conversionRate = BigDecimalValue(BigDecimal("10"))
        )
        val result = MeatManualPriceEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            result.output == BigDecimalValue(BigDecimal("10000.0000"))
        )
        assertEquals(result.metadata, emptyMap<String, String>())
    }
}
