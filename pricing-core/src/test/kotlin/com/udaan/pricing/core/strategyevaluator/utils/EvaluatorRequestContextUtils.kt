package com.udaan.pricing.core.strategyevaluator.utils

import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.GenericValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.commons.QuantityType
import com.udaan.pricing.commons.StringValue
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorConfigInput
import com.udaan.pricing.core.models.strategyevaluator.request.EvaluatorRequestContext
import com.udaan.pricing.core.models.strategyevaluator.response.EvaluatorOutput
import com.udaan.pricing.strategy.Strategy
import com.udaan.pricing.strategy.StrategyState
import com.udaan.pricing.strategy.StrategyType
import com.udaan.pricing.variable.VariableId
import java.math.BigDecimal

internal object EvaluatorRequestContextUtils {
    private fun getFmcgJumbotailStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.CONVERSION_RATE,
                VariableId.GST_BPS,
                VariableId.CESS_BPS
            ),
            conditionalFormulae = emptyList(),
            name = "FMCG_JUMBOTAIL_COMP",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.JUMBOTAIL_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                VariableId.CONVERSION_RATE,
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.FMCG_COGS_WOT_PAISA_SET,
                VariableId.JUMBOTAIL_COMP_MRP_WT_PAISA_UNIT,
                VariableId.MRP_WT_PAISA_SET
            )
        )
    }

    private fun getFmcgHyperpureStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.CONVERSION_RATE,
                VariableId.GST_BPS,
                VariableId.CESS_BPS
            ),
            conditionalFormulae = emptyList(),
            name = "FMCG_HYPERPURE_COMP",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.HYPERPURE_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                VariableId.CONVERSION_RATE,
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.FMCG_COGS_WOT_PAISA_SET
            )
        )
    }

    private fun getFmcgHyperpureCompDirectStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.CONVERSION_RATE,
                VariableId.GST_BPS,
                VariableId.CESS_BPS
            ),
            conditionalFormulae = emptyList(),
            name = "FMCG_HYPERPURE_COMP_DIRECT",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.HYPERPURE_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                VariableId.HYPERPURE_COMP_MARKDOWN_BPS,
                VariableId.CONVERSION_RATE,
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.FMCG_COGS_WOT_PAISA_SET
            )
        )
    }

    private fun getFmcgMetroStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.CONVERSION_RATE,
                VariableId.GST_BPS,
                VariableId.CESS_BPS
            ),
            conditionalFormulae = emptyList(),
            name = "FMCG_METRO_COMP",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.METRO_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                VariableId.CONVERSION_RATE,
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.FMCG_COGS_WOT_PAISA_SET,
                VariableId.METRO_COMP_MRP_WT_PAISA_UNIT,
                VariableId.MRP_WT_PAISA_SET
            )
        )
    }

    private fun getVslPriceStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.VSL_PRICE_WOT_PAISA_UNIT
            ),
            conditionalFormulae = emptyList(),
            name = "VSL_PRICE",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.VSL_PRICE_WOT_PAISA_UNIT,
                VariableId.VSL_MARKUP_BPS
            )
        )
    }

    private fun getJtLadderMatchStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.CONVERSION_RATE
            ),
            conditionalFormulae = emptyList(),
            name = "JT_LADDER_MATCH",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.STAPLES_LIP_WOT_PAISA_UNIT,
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.JUMBOTAIL_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                VariableId.CONVERSION_RATE,
                VariableId.VSL_SELECTED_CHANNEL,
                VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT
            )
        )
    }

    private fun getHyperpureLadderMatchStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.CONVERSION_RATE
            ),
            conditionalFormulae = emptyList(),
            name = "HYPERPURE_LADDER_MATCH",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.STAPLES_LIP_WOT_PAISA_UNIT,
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.HYPERPURE_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                VariableId.CONVERSION_RATE,
                VariableId.VSL_SELECTED_CHANNEL,
                VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT
            )
        )
    }

    private fun getMetroLadderMatchStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.CONVERSION_RATE
            ),
            conditionalFormulae = emptyList(),
            name = "METRO_LADDER_MATCH",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.STAPLES_LIP_WOT_PAISA_UNIT,
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.METRO_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                VariableId.CONVERSION_RATE,
                VariableId.VSL_SELECTED_CHANNEL,
                VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT
            )
        )
    }

    private fun getStaplesLipStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(),
            conditionalFormulae = emptyList(),
            name = "STAPLES_LIP_GUARD_RAIL",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.STAPLES_LIP_WOT_PAISA_UNIT,
                VariableId.CONVERSION_RATE,
                VariableId.LIP_FLOOR_GUARDRAIL_BPS,
                VariableId.LIP_CEIL_GUARDRAIL_BPS
            )
        )
    }

    private fun getDiscountSlabsStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = emptyList(),
            conditionalFormulae = emptyList(),
            name = "DISCOUNT_SLABS",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.DISCOUNT_SLABS,
                VariableId.CONVERSION_RATE
            )
        )
    }

    private fun getMrpPtrStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.MRP_WT_PAISA_SET,
                VariableId.PTR_BPS
            ),
            conditionalFormulae = emptyList(),
            name = "MRP_PTR_SCHEME",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.MRP_WT_PAISA_SET,
                VariableId.PTR_BPS,
                VariableId.GST_BPS,
                VariableId.CESS_BPS
            )
        )
    }

    private fun getBeveragesDtrBaseStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.BEVS_MOP_WT_PAISA_UNIT,
                VariableId.BEVS_MARGIN_BPS
            ),
            conditionalFormulae = emptyList(),
            name = "BEVS_DTR_BASE",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.BEVS_MOP_WT_PAISA_UNIT,
                VariableId.BEVS_MARGIN_BPS
            )
        )
    }

    private fun getBnsBaseStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.BUY_SELL_MARGIN_BPS
            ),
            conditionalFormulae = emptyList(),
            name = "BNS_BASE",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.FMCG_COGS_WOT_PAISA_SET,
                VariableId.BUY_SELL_MARGIN_BPS,
                VariableId.CONVERSION_RATE,
                VariableId.FMCG_LPP_WOT_RUPEES_UNIT
            )
        )
    }

    private fun getCohortAdjustmentStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.COHORT_ADJUSTMENT_BPS
            ),
            conditionalFormulae = emptyList(),
            name = "COHORT_ADJUSTMENT",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.COHORT_ADJUSTMENT_BPS
            )
        )
    }

    private fun getVolumetricDiscountLadderStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = emptyList(),
            conditionalFormulae = emptyList(),
            name = "VOLUMETRIC_LADDER_DISCOUNT",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.VOLUMETRIC_LADDER_DISCOUNT_BPS
            )
        )
    }

    private fun getFreshManualPriceStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.FRESH_MANUAL_PRICE_WOT_PAISA_UNIT,
                VariableId.CONVERSION_RATE
            ),
            conditionalFormulae = emptyList(),
            name = "FRESH_MANUAL_PRICE",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.FRESH_MANUAL_PRICE_WOT_PAISA_UNIT,
                VariableId.CONVERSION_RATE
            )
        )
    }

    private fun getMeatManualPriceStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.MEAT_MANUAL_PRICE_WOT_PAISA_UNIT,
                VariableId.CONVERSION_RATE
            ),
            conditionalFormulae = emptyList(),
            name = "MEAT_MANUAL_PRICE",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.MEAT_MANUAL_PRICE_WOT_PAISA_UNIT,
                VariableId.CONVERSION_RATE
            )
        )
    }

    private fun getFreshCogsFloorGuardrailStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.CONVERSION_RATE
            ),
            conditionalFormulae = emptyList(),
            name = "FRESH_COGS_FLOOR_GUARDRAIL",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.FRESH_COGS_WOT_PAISA_UNIT,
                VariableId.CONVERSION_RATE
            )
        )
    }

    private fun getMeatCogsFloorGuardrailStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.CONVERSION_RATE
            ),
            conditionalFormulae = emptyList(),
            name = "MEAT_COGS_FLOOR_GUARDRAIL",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.MEAT_COGS_WOT_PAISA_UNIT,
                VariableId.CONVERSION_RATE
            )
        )
    }

    private fun getMeatHyperpureCompMatchStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.CONVERSION_RATE
            ),
            conditionalFormulae = emptyList(),
            name = "MEAT_HYPERPURE_COMP_MATCH",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.MEAT_COGS_WOT_PAISA_UNIT,
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.CONVERSION_RATE,
                VariableId.HYPERPURE_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                VariableId.HYPERPURE_QUANTITY_TYPE,
                VariableId.HYPERPURE_QUANTITY_PER_UNIT,
                VariableId.QUANTITY_TYPE,
                VariableId.QUANTITY_PER_UNIT
            )
        )
    }

    private fun getFreshHyperpureCompMatchStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.CONVERSION_RATE
            ),
            conditionalFormulae = emptyList(),
            name = "FRESH_HYPERPURE_COMP_MATCH",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.FRESH_COGS_WOT_PAISA_UNIT,
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.CONVERSION_RATE,
                VariableId.HYPERPURE_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                VariableId.HYPERPURE_QUANTITY_TYPE,
                VariableId.HYPERPURE_QUANTITY_PER_UNIT,
                VariableId.QUANTITY_TYPE,
                VariableId.QUANTITY_PER_UNIT
            )
        )
    }

    private fun getFreshNinjacartCompMatchStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.CONVERSION_RATE
            ),
            conditionalFormulae = emptyList(),
            name = "FRESH_NINJACART_COMP_MATCH",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.FRESH_COGS_WOT_PAISA_UNIT,
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.CONVERSION_RATE,
                VariableId.NINJACART_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                VariableId.NINJACART_QUANTITY_TYPE,
                VariableId.NINJACART_QUANTITY_PER_UNIT,
                VariableId.QUANTITY_TYPE,
                VariableId.QUANTITY_PER_UNIT
            )
        )
    }

    private fun getPLBenchmarkListingCeilGuardrailStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.COHORT_ADJUSTMENT_BPS
            ),
            conditionalFormulae = emptyList(),
            name = "PL_BENCHMARK_LISTING_CEIL_GUARDRAIL",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.BENCHMARK_LISTING_PRICE_WOT_PAISA_SET,
                VariableId.COHORT_ADJUSTMENT_BPS
            )
        )
    }

    fun createFmcgJumbotailEvaluatorConfig(
        gstBps: BigDecimalValue? = null,
        cessBps: BigDecimalValue? = null,
        previousInput: GenericValue,
        fmcgCogsInPaisaAtAssortment: BigDecimalValue? = null,
        jtPrice: LadderValue? = null,
        conversionRate: BigDecimalValue? = null,
        jtMrpWithTaxInPaisaUnit: BigDecimalValue? = BigDecimalValue(BigDecimal(100)),
        udaanMrpWithTaxInPaisaAtAssortment: BigDecimalValue? = BigDecimalValue(BigDecimal(100))
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getFmcgJumbotailStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.GST_BPS,
                    value = gstBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate ?: BigDecimalValue(BigDecimal(1))
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CESS_BPS,
                    value = cessBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.JUMBOTAIL_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                    value = jtPrice
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.FMCG_COGS_WOT_PAISA_SET,
                    value = fmcgCogsInPaisaAtAssortment
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.JUMBOTAIL_COMP_MRP_WT_PAISA_UNIT,
                    value = jtMrpWithTaxInPaisaUnit
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.MRP_WT_PAISA_SET,
                    value = udaanMrpWithTaxInPaisaAtAssortment
                )
            ),
            previousOutput = EvaluatorOutput(output = previousInput, metadata = emptyMap())
        )
    }

    fun createFmcgHyperpureEvaluatorConfig(
        gstBps: BigDecimalValue? = null,
        cessBps: BigDecimalValue? = null,
        previousInput: GenericValue,
        fmcgCogsInPaisaAtAssortment: BigDecimalValue? = null,
        hyperpurePrice: LadderValue? = null,
        conversionRate: BigDecimalValue? = null
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getFmcgHyperpureStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.GST_BPS,
                    value = gstBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate ?: BigDecimalValue(BigDecimal(1))
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CESS_BPS,
                    value = cessBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.HYPERPURE_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                    value = hyperpurePrice
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.FMCG_COGS_WOT_PAISA_SET,
                    value = fmcgCogsInPaisaAtAssortment
                )
            ),
            previousOutput = EvaluatorOutput(output = previousInput, metadata = emptyMap())
        )
    }

    fun createFmcgHyperpureDirectCompEvaluatorConfig(
        gstBps: BigDecimalValue? = null,
        cessBps: BigDecimalValue? = null,
        previousInput: GenericValue,
        fmcgCogsInPaisaAtAssortment: BigDecimalValue? = null,
        hyperpurePrice: LadderValue? = null,
        conversionRate: BigDecimalValue? = null,
        hyperPureCompMarkdownBps: BigDecimalValue? = null
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getFmcgHyperpureCompDirectStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.GST_BPS,
                    value = gstBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate ?: BigDecimalValue(BigDecimal(1))
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CESS_BPS,
                    value = cessBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.HYPERPURE_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                    value = hyperpurePrice
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.HYPERPURE_COMP_MARKDOWN_BPS,
                    value = hyperPureCompMarkdownBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.FMCG_COGS_WOT_PAISA_SET,
                    value = fmcgCogsInPaisaAtAssortment
                )
            ),
            previousOutput = EvaluatorOutput(output = previousInput, metadata = emptyMap())
        )
    }

    fun createFmcgMetroEvaluatorConfig(
        gstBps: BigDecimalValue? = null,
        cessBps: BigDecimalValue? = null,
        previousInput: GenericValue,
        fmcgCogsInPaisaAtAssortment: BigDecimalValue? = null,
        metroPrice: LadderValue? = null,
        conversionRate: BigDecimalValue? = null,
        metroMrpWithTaxInPaisaUnit: BigDecimalValue? = BigDecimalValue(BigDecimal(100)),
        udaanMrpWithTaxInPaisaAtAssortment: BigDecimalValue? = BigDecimalValue(BigDecimal(100))
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getFmcgMetroStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.GST_BPS,
                    value = gstBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate ?: BigDecimalValue(BigDecimal(1))
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CESS_BPS,
                    value = cessBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.METRO_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                    value = metroPrice
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.FMCG_COGS_WOT_PAISA_SET,
                    value = fmcgCogsInPaisaAtAssortment
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.METRO_COMP_MRP_WT_PAISA_UNIT,
                    value = metroMrpWithTaxInPaisaUnit
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.MRP_WT_PAISA_SET,
                    value = udaanMrpWithTaxInPaisaAtAssortment
                )
            ),
            previousOutput = EvaluatorOutput(output = previousInput, metadata = emptyMap())
        )
    }

    fun createVslPriceEvaluatorConfig(
        vslPriceInPaisa: BigDecimalValue?,
        vslMarkUpBps: BigDecimalValue? = null
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getVslPriceStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.VSL_PRICE_WOT_PAISA_UNIT,
                    value = vslPriceInPaisa
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.VSL_MARKUP_BPS,
                    value = vslMarkUpBps
                )
            ),
            previousOutput = null
        )
    }

    fun createJtLadderMatchEvaluatorConfig(
        gstBps: BigDecimalValue? = null,
        cessBps: BigDecimalValue? = null,
        previousInput: GenericValue,
        lipPriceInPaisa: BigDecimalValue? = null,
        jtPrice: LadderValue? = null,
        conversionRate: BigDecimalValue? = null,
        vslSelectedChannel: StringValue? = null,
        fpjitBestVendorPriceWotInPaisaUnit: BigDecimalValue? = null
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getJtLadderMatchStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.GST_BPS,
                    value = gstBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate ?: BigDecimalValue(BigDecimal(1))
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CESS_BPS,
                    value = cessBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.JUMBOTAIL_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                    value = jtPrice
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.STAPLES_LIP_WOT_PAISA_UNIT,
                    value = lipPriceInPaisa
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.VSL_SELECTED_CHANNEL,
                    value = vslSelectedChannel
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT,
                    value = fpjitBestVendorPriceWotInPaisaUnit
                )
            ),
            previousOutput = EvaluatorOutput(output = previousInput, metadata = emptyMap())
        )
    }

    fun createHyperpureLadderMatchEvaluatorConfig(
        gstBps: BigDecimalValue? = null,
        cessBps: BigDecimalValue? = null,
        previousInput: GenericValue,
        lipPriceInPaisa: BigDecimalValue? = null,
        hyperpurePrice: LadderValue? = null,
        conversionRate: BigDecimalValue? = null,
        vslSelectedChannel: StringValue? = null,
        fpjitBestVendorPriceWotInPaisaUnit: BigDecimalValue? = null
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getHyperpureLadderMatchStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.GST_BPS,
                    value = gstBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate ?: BigDecimalValue(BigDecimal(1))
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CESS_BPS,
                    value = cessBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.HYPERPURE_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                    value = hyperpurePrice
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.STAPLES_LIP_WOT_PAISA_UNIT,
                    value = lipPriceInPaisa
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.VSL_SELECTED_CHANNEL,
                    value = vslSelectedChannel
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT,
                    value = fpjitBestVendorPriceWotInPaisaUnit
                )
            ),
            previousOutput = EvaluatorOutput(output = previousInput, metadata = emptyMap())
        )
    }

    fun createMetroLadderMatchEvaluatorConfig(
        gstBps: BigDecimalValue? = null,
        cessBps: BigDecimalValue? = null,
        previousInput: GenericValue,
        lipPriceInPaisa: BigDecimalValue? = null,
        metroPrice: LadderValue? = null,
        conversionRate: BigDecimalValue? = null,
        vslSelectedChannel: StringValue? = null,
        fpjitBestVendorPriceWotInPaisaUnit: BigDecimalValue? = null
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getMetroLadderMatchStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.GST_BPS,
                    value = gstBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate ?: BigDecimalValue(BigDecimal(1))
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CESS_BPS,
                    value = cessBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.METRO_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                    value = metroPrice
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.STAPLES_LIP_WOT_PAISA_UNIT,
                    value = lipPriceInPaisa
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.VSL_SELECTED_CHANNEL,
                    value = vslSelectedChannel
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT,
                    value = fpjitBestVendorPriceWotInPaisaUnit
                )
            ),
            previousOutput = EvaluatorOutput(output = previousInput, metadata = emptyMap())
        )
    }

    fun createLipGuardRailEvaluatorConfig(
        previousOutput: GenericValue,
        lipPriceInPaisa: BigDecimalValue? = null,
        conversionRate: BigDecimalValue? = null,
        ceilGuardrailBps: BigDecimalValue? = null,
        floorGuardRailBps: BigDecimalValue? = null
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getStaplesLipStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.STAPLES_LIP_WOT_PAISA_UNIT,
                    value = lipPriceInPaisa
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.LIP_CEIL_GUARDRAIL_BPS,
                    value = ceilGuardrailBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.LIP_FLOOR_GUARDRAIL_BPS,
                    value = floorGuardRailBps
                )
            ),
            previousOutput = EvaluatorOutput(output = previousOutput , metadata = emptyMap())
        )
    }

    fun createDiscountSlabsEvaluatorTestConfig(
        previousStrategyOutput: GenericValue,
        discountSlabs: LadderValue? = null,
        conversionRate: BigDecimalValue? = null
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getDiscountSlabsStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.DISCOUNT_SLABS,
                    value = discountSlabs
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate
                )
            ),
            previousOutput = EvaluatorOutput(output = previousStrategyOutput , metadata = emptyMap())
        )
    }

    private fun getSchemeMultiplierWithRetentionCapStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.SCHEME_MULTIPLIER_BPS,
                VariableId.CONVERSION_RATE,
                VariableId.SCHEME_RETENTION_CAP_BPS
            ),
            conditionalFormulae = emptyList(),
            name = "FMCG_SCHEME_MULTIPLIER_WITH_RETENTION_CAP",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.DTR_RETAIL_SCHEME,
                VariableId.GT_RETAIL_SCHEME,
                VariableId.GT_WS_SCHEME,
                VariableId.SOURCING_CHANNEL,
                VariableId.SCHEME_MULTIPLIER_BPS,
                VariableId.CONVERSION_RATE,
                VariableId.SCHEME_RETENTION_CAP_BPS
            )
        )
    }

    fun getSchemeMultiplierAndRetentionCapRequestConfig(
        schemeMultiplierBps: BigDecimalValue? = null,
        schemeRetentionCapBps: BigDecimalValue? = null,
        fmcgSourcingChannel: StringValue? = null,
        dtrRetailScheme: LadderValue? = null,
        gtRetailScheme: LadderValue? = null,
        gtWsScheme: LadderValue? = null
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getSchemeMultiplierWithRetentionCapStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = BigDecimalValue(BigDecimal(1))
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.SCHEME_MULTIPLIER_BPS,
                    value = schemeMultiplierBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.SCHEME_RETENTION_CAP_BPS,
                    value = schemeRetentionCapBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.SOURCING_CHANNEL,
                    value = fmcgSourcingChannel
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.DTR_RETAIL_SCHEME,
                    value = dtrRetailScheme
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.GT_RETAIL_SCHEME,
                    value = gtRetailScheme
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.GT_WS_SCHEME,
                    value = gtWsScheme
                )
            ),
            previousOutput = EvaluatorOutput(
                output = BigDecimalValue(BigDecimal(100))
            )
        )
    }

    fun List<Ladder>.equalsTo(ladders: List<Ladder>): Boolean {
        if (this.size != ladders.size) {
            return false
        }
        this.mapIndexed { index, item ->
            val ladderMatched = item.minQuantity == ladders[index].minQuantity &&
                    item.maxQuantity == ladders[index].maxQuantity
                    && item.ladderValue.compareTo(ladders[index].ladderValue) == 0
            if (ladderMatched.not()) {
                return false
            }
        }
        return true
    }


    fun createMrpPtrEvaluatorConfig(
        ptrBps: BigDecimalValue?,
        mrpInPaisa: BigDecimalValue?,
        gstBps: BigDecimalValue? = null,
        cessBps: BigDecimalValue? = null
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getMrpPtrStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.MRP_WT_PAISA_SET,
                    value = mrpInPaisa ?: BigDecimalValue(BigDecimal(10000))
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.PTR_BPS,
                    value = ptrBps ?: BigDecimalValue(BigDecimal(1000))
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.GST_BPS,
                    value = gstBps ?: BigDecimalValue(BigDecimal(500))
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CESS_BPS,
                    value = cessBps ?: BigDecimalValue(BigDecimal(0))
                )
            ),
            previousOutput = null
        )
    }

    private fun getMrpCeilGuardrailStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = listOf(
                VariableId.MRP_WT_PAISA_SET,
                VariableId.GST_BPS,
                VariableId.CESS_BPS
            ),
            conditionalFormulae = emptyList(),
            name = "MRP_CEIL_GUARDRAIL",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.MRP_WT_PAISA_SET,
                VariableId.GST_BPS,
                VariableId.CESS_BPS,
                VariableId.MRP_CEIL_GUARDRAIL_MD_BPS
            )
        )
    }

    fun createMrpCeilGuardrailEvaluatorConfig(
        previousOutput: GenericValue,
        mrpInPaisa: BigDecimalValue,
        gstBps: BigDecimalValue,
        cessBps: BigDecimalValue,
        mrpCeilGuardrailBps: BigDecimalValue? = null
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getMrpCeilGuardrailStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.MRP_WT_PAISA_SET,
                    value = mrpInPaisa
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.GST_BPS,
                    value = gstBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CESS_BPS,
                    value = cessBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.MRP_CEIL_GUARDRAIL_MD_BPS,
                    value = mrpCeilGuardrailBps
                )
            ),
            previousOutput = EvaluatorOutput(
                output = previousOutput,
                metadata = emptyMap()
            )
        )
    }

    private fun getCogsFloorGuardrailStrategy(): Strategy {
        return Strategy(
            mandatoryVariables = emptyList(),
            conditionalFormulae = emptyList(),
            name = "COGS_FLOOR_GUARDRAIL",
            metadata = emptyMap(),
            type = StrategyType.DYNAMIC,
            state = StrategyState.ACTIVE,
            usedVariables = listOf(
                VariableId.FMCG_COGS_WOT_PAISA_SET
            )
        )
    }

    fun createCogsFloorGuardrailEvaluatorConfig(
        previousOutput: GenericValue,
        cogs: BigDecimalValue? = null
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getCogsFloorGuardrailStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.FMCG_COGS_WOT_PAISA_SET,
                    value = cogs
                )
            ),
            previousOutput = EvaluatorOutput(
                output = previousOutput,
                metadata = emptyMap()
            )
        )
    }

    fun createBeveragesDtrBaseEvaluatorConfig(
        bevsMop: BigDecimalValue? = null,
        bevsMarginBps: BigDecimalValue? = null
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getBeveragesDtrBaseStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.BEVS_MOP_WT_PAISA_UNIT,
                    value = bevsMop
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.BEVS_MARGIN_BPS,
                    value = bevsMarginBps
                )
            ),
            previousOutput = null
        )
    }

    fun createBnsBaseEvaluatorConfig(
        fmcgCogsInPaise: BigDecimalValue? = null,
        bnsMarginBps: BigDecimalValue? = null,
        conversionRate: BigDecimalValue? = null,
        fmcgLppWotInRupeesAtUnit: BigDecimalValue? = null
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getBnsBaseStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.FMCG_COGS_WOT_PAISA_SET,
                    value = fmcgCogsInPaise
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.BUY_SELL_MARGIN_BPS,
                    value = bnsMarginBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.FMCG_LPP_WOT_RUPEES_UNIT,
                    value = fmcgLppWotInRupeesAtUnit
                )
            ),
            previousOutput = null
        )
    }

    fun createCohortAdjustmentEvaluatorConfig(
        previousOutput: GenericValue,
        cohortAdjustmentBps: BigDecimalValue? = null
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getCohortAdjustmentStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.COHORT_ADJUSTMENT_BPS,
                    value = cohortAdjustmentBps
                )
            ),
            previousOutput = EvaluatorOutput(
                output = previousOutput,
                metadata = emptyMap()
            )
        )
    }

    fun createVolumetricDiscountLadderEvaluatorConfig(
        previousOutput: GenericValue,
        volumetricDiscountBps: LadderValue? = null
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getVolumetricDiscountLadderStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.VOLUMETRIC_LADDER_DISCOUNT_BPS,
                    value = volumetricDiscountBps
                )
            ),
            previousOutput = EvaluatorOutput(
                output = previousOutput,
                metadata = emptyMap()
            )
        )
    }

    fun createFreshManualPriceEvaluatorConfig(
        freshManualPriceWotPaisaUnit: BigDecimalValue?,
        conversionRate: BigDecimalValue
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getFreshManualPriceStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.FRESH_MANUAL_PRICE_WOT_PAISA_UNIT,
                    value = freshManualPriceWotPaisaUnit
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate
                )
            ),
            previousOutput = null
        )
    }

    fun createMeatManualPriceEvaluatorConfig(
        meatManualPriceWotPaisaUnit: BigDecimalValue?,
        conversionRate: BigDecimalValue
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getMeatManualPriceStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.MEAT_MANUAL_PRICE_WOT_PAISA_UNIT,
                    value = meatManualPriceWotPaisaUnit
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate
                )
            ),
            previousOutput = null
        )
    }

    fun createFreshCogsFloorGuardrailEvaluatorConfig(
        previousOutput: GenericValue?,
        freshCogsWotPaiseUnit: BigDecimalValue?,
        conversionRate: BigDecimalValue?
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getFreshCogsFloorGuardrailStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.FRESH_COGS_WOT_PAISA_UNIT,
                    value = freshCogsWotPaiseUnit
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate
                )
            ),
            previousOutput = previousOutput?.let {
                EvaluatorOutput(output = previousOutput , metadata = emptyMap())
            }
        )
    }

    fun createMeatCogsFloorGuardrailEvaluatorConfig(
        previousOutput: GenericValue?,
        meatCogsWotPaiseUnit: BigDecimalValue?,
        conversionRate: BigDecimalValue?
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getMeatCogsFloorGuardrailStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.MEAT_COGS_WOT_PAISA_UNIT,
                    value = meatCogsWotPaiseUnit
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate
                )
            ),
            previousOutput = previousOutput?.let {
                EvaluatorOutput(output = previousOutput , metadata = emptyMap())
            }
        )
    }

    fun createMeatHyperpureCompMatchEvaluatorConfig(
        gstBps: BigDecimalValue? = BigDecimalValue(BigDecimal("0")),
        cessBps: BigDecimalValue? = BigDecimalValue(BigDecimal("0")),
        previousInput: GenericValue?,
        meatCogsPriceWotPaisaUnit: BigDecimalValue? = null,
        hyperpurePrice: LadderValue? = null,
        conversionRate: BigDecimalValue? = BigDecimalValue(BigDecimal(1)),
        hyperpureQtyType: StringValue? = StringValue(QuantityType.GRAM.name),
        hyperpureQtyPerUnit: BigDecimalValue? = BigDecimalValue(BigDecimal(1000)),
        listingQtyType: StringValue? = StringValue(QuantityType.GRAM.name),
        listingQtyPerUnit: BigDecimalValue? = BigDecimalValue(BigDecimal(1000))
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getMeatHyperpureCompMatchStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.GST_BPS,
                    value = gstBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate ?: BigDecimalValue(BigDecimal(1))
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CESS_BPS,
                    value = cessBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.HYPERPURE_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                    value = hyperpurePrice
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.MEAT_COGS_WOT_PAISA_UNIT,
                    value = meatCogsPriceWotPaisaUnit
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.HYPERPURE_QUANTITY_TYPE,
                    value = hyperpureQtyType
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.HYPERPURE_QUANTITY_PER_UNIT,
                    value = hyperpureQtyPerUnit
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.QUANTITY_TYPE,
                    value = listingQtyType
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.QUANTITY_PER_UNIT,
                    value = listingQtyPerUnit
                )
            ),
            previousOutput = previousInput?.let { EvaluatorOutput(output = previousInput, metadata = emptyMap()) }
        )
    }

    fun createFreshHyperpureCompMatchEvaluatorConfig(
        gstBps: BigDecimalValue? = BigDecimalValue(BigDecimal("0")),
        cessBps: BigDecimalValue? = BigDecimalValue(BigDecimal("0")),
        previousInput: GenericValue?,
        freshCogsPriceWotPaisaUnit: BigDecimalValue? = null,
        hyperpurePrice: LadderValue? = null,
        conversionRate: BigDecimalValue? = BigDecimalValue(BigDecimal(1)),
        hyperpureQtyType: StringValue? = StringValue(QuantityType.GRAM.name),
        hyperpureQtyPerUnit: BigDecimalValue? = BigDecimalValue(BigDecimal(1000)),
        listingQtyType: StringValue? = StringValue(QuantityType.GRAM.name),
        listingQtyPerUnit: BigDecimalValue? = BigDecimalValue(BigDecimal(1000))
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getFreshHyperpureCompMatchStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.GST_BPS,
                    value = gstBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate ?: BigDecimalValue(BigDecimal(1))
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CESS_BPS,
                    value = cessBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.HYPERPURE_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                    value = hyperpurePrice
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.FRESH_COGS_WOT_PAISA_UNIT,
                    value = freshCogsPriceWotPaisaUnit
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.HYPERPURE_QUANTITY_TYPE,
                    value = hyperpureQtyType
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.HYPERPURE_QUANTITY_PER_UNIT,
                    value = hyperpureQtyPerUnit
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.QUANTITY_TYPE,
                    value = listingQtyType
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.QUANTITY_PER_UNIT,
                    value = listingQtyPerUnit
                )
            ),
            previousOutput = previousInput?.let { EvaluatorOutput(output = previousInput, metadata = emptyMap()) }
        )
    }

    fun createFreshNinjacartCompMatchEvaluatorConfig(
        gstBps: BigDecimalValue? = BigDecimalValue(BigDecimal("0")),
        cessBps: BigDecimalValue? = BigDecimalValue(BigDecimal("0")),
        previousInput: GenericValue?,
        freshCogsPriceWotPaisaUnit: BigDecimalValue? = null,
        ninjacartPrice: LadderValue? = null,
        conversionRate: BigDecimalValue? = BigDecimalValue(BigDecimal(1)),
        ninjacartQtyType: StringValue? = StringValue(QuantityType.GRAM.name),
        ninjacartQtyPerUnit: BigDecimalValue? = BigDecimalValue(BigDecimal(1000)),
        listingQtyType: StringValue? = StringValue(QuantityType.GRAM.name),
        listingQtyPerUnit: BigDecimalValue? = BigDecimalValue(BigDecimal(1000))
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getFreshNinjacartCompMatchStrategy(),
            inputs = listOf(
                EvaluatorConfigInput(
                    variableId = VariableId.GST_BPS,
                    value = gstBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CONVERSION_RATE,
                    value = conversionRate ?: BigDecimalValue(BigDecimal(1))
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.CESS_BPS,
                    value = cessBps
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.NINJACART_COMP_LADDER_PRICE_WT_PAISA_UNIT,
                    value = ninjacartPrice
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.FRESH_COGS_WOT_PAISA_UNIT,
                    value = freshCogsPriceWotPaisaUnit
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.NINJACART_QUANTITY_TYPE,
                    value = ninjacartQtyType
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.NINJACART_QUANTITY_PER_UNIT,
                    value = ninjacartQtyPerUnit
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.QUANTITY_TYPE,
                    value = listingQtyType
                ),
                EvaluatorConfigInput(
                    variableId = VariableId.QUANTITY_PER_UNIT,
                    value = listingQtyPerUnit
                )
            ),
            previousOutput = previousInput?.let { EvaluatorOutput(output = previousInput, metadata = emptyMap()) }
        )
    }

    fun createPLBenchmarkListingCeilGuardrailEvaluatorConfig(
        previousOutput: GenericValue,
        benchmarkListingPrice: LadderValue?,
        cohortAdjustmentBps: BigDecimalValue
    ): EvaluatorRequestContext {
        return EvaluatorRequestContext(
            strategy = getPLBenchmarkListingCeilGuardrailStrategy(),
            inputs = listOfNotNull(
                benchmarkListingPrice?.let {
                    EvaluatorConfigInput(
                        variableId = VariableId.BENCHMARK_LISTING_PRICE_WOT_PAISA_SET,
                        value = it
                    )
                },
                EvaluatorConfigInput(
                    variableId = VariableId.COHORT_ADJUSTMENT_BPS,
                    value = cohortAdjustmentBps
                )
            ),
            previousOutput = EvaluatorOutput(
                output = previousOutput,
                metadata = emptyMap()
            )
        )
    }
}
