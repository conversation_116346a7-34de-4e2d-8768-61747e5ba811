package com.udaan.pricing.core.strategyevaluator.common

import com.udaan.pricing.core.strategyevaluator.impl.EvaluatorFactory
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils.equalsTo
import com.udaan.pricing.core.strategyevaluator.utils.MapComparatorUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.commons.StringValue
import com.udaan.pricing.variable.VariableId
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal

class CohortAdjustmentEvaluatorTest {

    @Test
    fun `test cohort adjustment strategy if cohort markup input is missing`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createCohortAdjustmentEvaluatorConfig(
            cohortAdjustmentBps = null,
            previousOutput = LadderValue(
                value = listOf(Ladder(1, Int.MAX_VALUE.toLong(), BigDecimal(250)))
            )
        )

        assertThrows<IllegalArgumentException> {
            EvaluatorFactory.evaluate(listOf(evaluatorConfig))
        }
    }

    @Test
    fun `test cohort adjustment strategy if previous input is not of BigDecimal or Ladder type`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createCohortAdjustmentEvaluatorConfig(
            cohortAdjustmentBps = BigDecimalValue(BigDecimal(0)),
            previousOutput = StringValue("ABC")
        )

        assertThrows<IllegalArgumentException> {
            EvaluatorFactory.evaluate(listOf(evaluatorConfig))
        }
    }

    @Test
    fun `test cohort adjustment strategy for bigDecimal value if all inputs are present and adjustment is 0`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createCohortAdjustmentEvaluatorConfig(
            cohortAdjustmentBps = BigDecimalValue(BigDecimal(0)),
            previousOutput = BigDecimalValue(BigDecimal("100.1"))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = BigDecimalValue(BigDecimal("100.1"))

        val expectedMetadata = mapOf(
            evaluatorConfig.strategy.name + "_INPUT" to "100.1",
            VariableId.COHORT_ADJUSTMENT_BPS.name to "0",
            evaluatorConfig.strategy.name + "_OUTPUT" to "100.1"
        )

        assertEquals(
            expectedOutput.value.toString(),
            (result!!.output as BigDecimalValue).toString(),
            "" + result.output + " is not matched with " + expectedOutput
        )

        MapComparatorUtils.compareMaps(
            result.metadata,
            expectedMetadata
        )
    }

    @Test
    fun `test cohort adjustment strategy for ladder value if all inputs are present and adjustment is 0`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createCohortAdjustmentEvaluatorConfig(
            cohortAdjustmentBps = BigDecimalValue(BigDecimal(0)),
            previousOutput = LadderValue(
                value = listOf(
                    Ladder(1, 5, BigDecimal(343)),
                    Ladder(6, 10, BigDecimal(292)),
                    Ladder(11, Int.MAX_VALUE.toLong(), BigDecimal(250))
                )
            )
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = LadderValue(
            value = listOf(
                Ladder(1, 5, BigDecimal("343")),
                Ladder(6, 10, BigDecimal("292")),
                Ladder(11, Int.MAX_VALUE.toLong(), BigDecimal("250"))
            )
        )

        val expectedMetadata = mapOf(
            evaluatorConfig.strategy.name + "_INPUT" to "[(1,5,343), (6,10,292), (11,2147483647,250)]",
            VariableId.COHORT_ADJUSTMENT_BPS.name to "0",
            evaluatorConfig.strategy.name + "_OUTPUT" to "[(1,5,343), (6,10,292), (11,2147483647,250)]"
        )

        assertTrue(
            expectedOutput.value.equalsTo((result!!.output as LadderValue).value),
            "" + result.output + " is not matched with " + expectedOutput
        )

        MapComparatorUtils.compareMaps(
            result.metadata,
            expectedMetadata
        )
    }

    @Test
    fun `test cohort adjustment strategy for bigDecimal value if all inputs are present and adjustment is +ve (markup)`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createCohortAdjustmentEvaluatorConfig(
            cohortAdjustmentBps = BigDecimalValue(BigDecimal(350)),
            previousOutput = BigDecimalValue(BigDecimal("100.1"))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = BigDecimalValue(BigDecimal("103.6035"))

        val expectedMetadata = mapOf(
            evaluatorConfig.strategy.name + "_INPUT" to "100.1",
            VariableId.COHORT_ADJUSTMENT_BPS.name to "350",
            evaluatorConfig.strategy.name + "_OUTPUT" to "103.6035"
        )

        assertEquals(
            expectedOutput.value.toString(),
            (result!!.output as BigDecimalValue).toString(),
            "" + result.output + " is not matched with " + expectedOutput
        )

        MapComparatorUtils.compareMaps(
            result.metadata,
            expectedMetadata
        )
    }

    @Test
    fun `test cohort adjustment strategy for ladder value if all inputs are present and adjustment is +ve (markup)`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createCohortAdjustmentEvaluatorConfig(
            cohortAdjustmentBps = BigDecimalValue(BigDecimal(350)),
            previousOutput = LadderValue(
                value = listOf(
                    Ladder(1, 5, BigDecimal(343)),
                    Ladder(6, 10, BigDecimal(292)),
                    Ladder(11, Int.MAX_VALUE.toLong(), BigDecimal(250))
                )
            )
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = LadderValue(
            value = listOf(
                Ladder(1, 5, BigDecimal("355.0050")),
                Ladder(6, 10, BigDecimal("302.2200")),
                Ladder(11, Int.MAX_VALUE.toLong(), BigDecimal("258.7500"))
            )
        )

        val expectedMetadata = mapOf(
            evaluatorConfig.strategy.name + "_INPUT" to "[(1,5,343), (6,10,292), (11,2147483647,250)]",
            VariableId.COHORT_ADJUSTMENT_BPS.name to "350",
            evaluatorConfig.strategy.name + "_OUTPUT" to "[(1,5,355.005), (6,10,302.22), (11,2147483647,258.75)]"
        )

        assertTrue(
            expectedOutput.value.equalsTo((result!!.output as LadderValue).value),
            "" + result.output + " is not matched with " + expectedOutput
        )

        MapComparatorUtils.compareMaps(
            result.metadata,
            expectedMetadata
        )
    }

    @Test
    fun `test cohort adjustment strategy for bigDecimal value if all inputs are present and adjustment is -ve (markdown)`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createCohortAdjustmentEvaluatorConfig(
            cohortAdjustmentBps = BigDecimalValue(BigDecimal(-350)),
            previousOutput = BigDecimalValue(BigDecimal("100.1"))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = BigDecimalValue(BigDecimal("96.5965"))

        val expectedMetadata = mapOf(
            evaluatorConfig.strategy.name + "_INPUT" to "100.1",
            VariableId.COHORT_ADJUSTMENT_BPS.name to "-350",
            evaluatorConfig.strategy.name + "_OUTPUT" to "96.5965"
        )

        assertEquals(
            expectedOutput.value.toString(),
            (result!!.output as BigDecimalValue).toString(),
            "" + result.output + " is not matched with " + expectedOutput
        )

        MapComparatorUtils.compareMaps(
            result.metadata,
            expectedMetadata
        )
    }

    @Test
    fun `test cohort adjustment strategy for ladder value if all inputs are present and adjustment is -ve (markdown)`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createCohortAdjustmentEvaluatorConfig(
            cohortAdjustmentBps = BigDecimalValue(BigDecimal(-350)),
            previousOutput = LadderValue(
                value = listOf(
                    Ladder(1, 5, BigDecimal(343)),
                    Ladder(6, 10, BigDecimal(292)),
                    Ladder(11, Int.MAX_VALUE.toLong(), BigDecimal(250))
                )
            )
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = LadderValue(
            value = listOf(
                Ladder(1, 5, BigDecimal("330.995")),
                Ladder(6, 10, BigDecimal("281.78")),
                Ladder(11, Int.MAX_VALUE.toLong(), BigDecimal("241.25"))
            )
        )

        val expectedMetadata = mapOf(
            evaluatorConfig.strategy.name + "_INPUT" to "[(1,5,343), (6,10,292), (11,2147483647,250)]",
            VariableId.COHORT_ADJUSTMENT_BPS.name to "-350",
            evaluatorConfig.strategy.name + "_OUTPUT" to "[(1,5,330.995), (6,10,281.78), (11,2147483647,241.25)]"
        )

        assertTrue(
            expectedOutput.value.equalsTo((result!!.output as LadderValue).value),
            "" + result.output + " is not matched with " + expectedOutput
        )

        MapComparatorUtils.compareMaps(
            result.metadata,
            expectedMetadata
        )
    }

}
