package com.udaan.pricing.core.managers

import com.udaan.instrumentation.TelemetryScope.Companion.runBlocking
import com.udaan.pricing.core.managers.signals.ValidationFlag
import com.udaan.pricing.core.managers.signals.ValidationHelper
import com.udaan.pricing.signalanomaly.Guardrail
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.core.helpers.ConfigHelper
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.Assert.assertEquals
import org.junit.Test
import java.math.BigDecimal

class ValidationHelperTests {

    companion object {
        private val configHelper: ConfigHelper = mockk()
        private val validationHelper = ValidationHelper()
    }

    @Test
    fun `given different percentDiff scenarios, when validating, then should return expected ValidationFlag`() = runBlocking {
        val testCases = listOf(
            // No guardrails defined
            TestCase(
                caseName = "No guardrail, no change",
                newValue = BigDecimal(100),
                existingValue = BigDecimal(120),
                guardrail = null,
                expectedFlag = ValidationFlag.Green
            ),
            // Below Yellow Guardrail
            TestCase(
                caseName = "Below Yellow Guardrail",
                newValue = BigDecimal(100),
                existingValue = BigDecimal(93),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Green
            ),
            TestCase(
                caseName = "Below Yellow Guardrail (negative diff)",
                newValue = BigDecimal(93),
                existingValue = BigDecimal(100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Green
            ),
            // Within Yellow Guardrail
            TestCase(
                caseName = "Within Yellow Guardrail",
                newValue = BigDecimal(115),
                existingValue = BigDecimal(100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Yellow
            ),
            TestCase(
                caseName = "Within Yellow Guardrail (negative diff)",
                newValue = BigDecimal(85),
                existingValue = BigDecimal(100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Yellow
            ),
            // Above Red Guardrail
            TestCase(
                caseName = "Above Red Guardrail",
                newValue = BigDecimal(130),
                existingValue = BigDecimal(100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Red
            ),
            TestCase(
                caseName = "Above Red Guardrail (negative diff)",
                newValue = BigDecimal(70),
                existingValue = BigDecimal(100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Red
            ),
            // Exact Guardrail Boundaries
            TestCase(
                caseName = "Exactly at Yellow Guardrail",
                newValue = BigDecimal(110),
                existingValue = BigDecimal(100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Yellow
            ),
            TestCase(
                caseName = "Exactly at Red Guardrail",
                newValue = BigDecimal(120),
                existingValue = BigDecimal(100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Red
            ),
            // Negative values
            TestCase(
                caseName = "Negative values, no guardrail",
                newValue = BigDecimal(-100),
                existingValue = BigDecimal(-120),
                guardrail = null,
                expectedFlag = ValidationFlag.Green
            ),
            TestCase(
                caseName = "Negative values, below Yellow Guardrail",
                newValue = BigDecimal(-100),
                existingValue = BigDecimal(-93),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Green
            ),
            TestCase(
                caseName = "Negative values, within Yellow Guardrail",
                newValue = BigDecimal(-115),
                existingValue = BigDecimal(-100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Yellow
            ),
            TestCase(
                caseName = "Negative values, above Red Guardrail",
                newValue = BigDecimal(-130),
                existingValue = BigDecimal(-100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Red
            )
        )

        for (testCase in testCases) {
            coEvery { configHelper.getGuardrailForVariable(inputType = "VAR1") } returns testCase.guardrail
            coEvery { configHelper.isAbsoluteDiffEnabled(inputType = "VAR1") } returns false

            if(testCase.guardrail == null) return@runBlocking

            val result = validationHelper.getValidationFlag(
                productGroupId = "PG1",
                locationValue = "LOC1",
                newValue = BigDecimalValue(value = testCase.newValue),
                existingValue = testCase.existingValue?.let { BigDecimalValue(value = it) },
                guardrail = testCase.guardrail
            )

            assertEquals(testCase.expectedFlag, result)
        }
    }

    @Test
    fun `given different absoluteDiff scenarios, when validating, then should return expected ValidationFlag`() = runBlocking {
        val testCases = listOf(
            // No guardrails defined
            TestCase(
                caseName = "No guardrail, no change",
                newValue = BigDecimal(100),
                existingValue = BigDecimal(100),
                guardrail = null,
                expectedFlag = ValidationFlag.Green
            ),
            // Below Yellow Guardrail
            TestCase(
                caseName = "Below Yellow Guardrail",
                newValue = BigDecimal(105),
                existingValue = BigDecimal(100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Green
            ),
            TestCase(
                caseName = "Below Yellow Guardrail (negative diff)",
                newValue = BigDecimal(95),
                existingValue = BigDecimal(100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Green
            ),
            // Within Yellow Guardrail
            TestCase(
                caseName = "Within Yellow Guardrail",
                newValue = BigDecimal(115),
                existingValue = BigDecimal(100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Yellow
            ),
            TestCase(
                caseName = "Within Yellow Guardrail (negative diff)",
                newValue = BigDecimal(85),
                existingValue = BigDecimal(100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Yellow
            ),
            // Above Red Guardrail
            TestCase(
                caseName = "Above Red Guardrail",
                newValue = BigDecimal(130),
                existingValue = BigDecimal(100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Red
            ),
            TestCase(
                caseName = "Above Red Guardrail (negative diff)",
                newValue = BigDecimal(70),
                existingValue = BigDecimal(100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Red
            ),
            // Exact Guardrail Boundaries
            TestCase(
                caseName = "Exactly at Yellow Guardrail",
                newValue = BigDecimal(110),
                existingValue = BigDecimal(100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Yellow
            ),
            TestCase(
                caseName = "Exactly at Red Guardrail",
                newValue = BigDecimal(120),
                existingValue = BigDecimal(100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Red
            ),
            // Negative values
            TestCase(
                caseName = "Negative values, no guardrail",
                newValue = BigDecimal(-100),
                existingValue = BigDecimal(-100),
                guardrail = null,
                expectedFlag = ValidationFlag.Green
            ),
            TestCase(
                caseName = "Negative values, below Yellow Guardrail",
                newValue = BigDecimal(-105),
                existingValue = BigDecimal(-100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Green
            ),
            TestCase(
                caseName = "Negative values, within Yellow Guardrail",
                newValue = BigDecimal(-115),
                existingValue = BigDecimal(-100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Yellow
            ),
            TestCase(
                caseName = "Negative values, above Red Guardrail",
                newValue = BigDecimal(-130),
                existingValue = BigDecimal(-100),
                guardrail = Guardrail(BigDecimal(10), BigDecimal(20), false),
                expectedFlag = ValidationFlag.Red
            )
        )

        for (testCase in testCases) {
            coEvery { configHelper.getGuardrailForVariable(inputType = "VAR1") } returns testCase.guardrail
            coEvery { configHelper.isAbsoluteDiffEnabled(inputType = "VAR1") } returns true

            if (testCase.guardrail == null) return@runBlocking

            val result = validationHelper.getValidationFlag(
                productGroupId = "PG1",
                locationValue = "LOC1",
                newValue = BigDecimalValue(value = testCase.newValue),
                existingValue = testCase.existingValue?.let { BigDecimalValue(value = it) },
                guardrail = testCase.guardrail
            )

            assertEquals(testCase.expectedFlag, result)
        }
    }

    @Test
    fun `given null existingValue scenarios, when validating, then should return expected ValidationFlag`() = runBlocking {
        val testCase = TestCase(
            caseName = "No guardrail, null existingValue",
            newValue = BigDecimal(100),
            existingValue = null,
            guardrail = null,
            expectedFlag = ValidationFlag.Green
        )

        coEvery { configHelper.getGuardrailForVariable(inputType = "VAR1") } returns testCase.guardrail
        coEvery { configHelper.isAbsoluteDiffEnabled(inputType = "VAR1") } returns true

        if (testCase.guardrail == null) return@runBlocking

        val result = validationHelper.getValidationFlag(
            productGroupId = "PG1",
            locationValue = "LOC1",
            newValue = BigDecimalValue(value = testCase.newValue),
            existingValue = testCase.existingValue?.let { BigDecimalValue(value = it) },
            guardrail = testCase.guardrail
        )

        assertEquals(testCase.expectedFlag,   result)
    }

    private data class TestCase(
        val caseName: String,
        val newValue: BigDecimal,
        val existingValue: BigDecimal?,
        val guardrail: Guardrail?,
        val expectedFlag: ValidationFlag,
    )
}
