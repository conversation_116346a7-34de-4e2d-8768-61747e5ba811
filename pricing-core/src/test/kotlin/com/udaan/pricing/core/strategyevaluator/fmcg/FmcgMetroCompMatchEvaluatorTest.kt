package com.udaan.pricing.core.strategyevaluator.fmcg

import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg.FmcgMetroCompMatchEvaluator
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils.equalsTo
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.math.BigDecimal

// @todo - tests are same as staples metro strategy tests but for minor differences (MRP chk, cogs guardrail, no slab count threshold)
// @todo - we should write paramatrized tests for both Metro strategies in one file. OR consolidate both Metro strategies.
class FmcgMetroCompMatchEvaluatorTest {

    private val fmcgMetroCompMatchEvaluator = FmcgMetroCompMatchEvaluator

    @Test
    fun `test metro ladder match with metro price as null`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgMetroEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            metroMrpWithTaxInPaisaUnit = null,
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("100")
                    )
                )
            )
        )
        val result = fmcgMetroCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            result.output == LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("100")
                    )
                )
            )
        )

        assertEquals(
            mapOf(
                "LISTING_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "COMP_MRP_WITH_TAX_PAISE_UNIT" to "null",
                "MRP_MATCHED" to "false"
            ), result.metadata)
    }

    @Test
    fun `test metro ladder match with metro price but guardrail null`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgMetroEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = null,
            metroPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("97")
                    )
                )
            ),
            previousInput = BigDecimalValue(BigDecimal("100"))
        )
        val result = fmcgMetroCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(100)
                    )
                )
            )
        )

        assertEquals(
            mapOf(
                "DEFAULT_LADDER_CREATED" to "true",
                "LISTING_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "MRP_MATCHED" to "true",
                "COMP_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "METRO_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,97)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "null",
                "NO_COMP_COMPARISON_AS_NO_GUARDRAIL" to "true"
            ), result.metadata
        )
    }

    @Test
    fun `test metro ladder match with metro price but MRP mismatched`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgMetroEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            metroMrpWithTaxInPaisaUnit = BigDecimalValue(BigDecimal("101")),
            udaanMrpWithTaxInPaisaAtAssortment = BigDecimalValue(BigDecimal("102")),
            metroPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("97")
                    )
                )
            ),
            previousInput = BigDecimalValue(BigDecimal("100"))
        )
        val result = fmcgMetroCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(100)
                    )
                )
            )
        )

        assertEquals(
            mapOf(
                "DEFAULT_LADDER_CREATED" to "true",
                "LISTING_MRP_WITH_TAX_PAISE_UNIT" to "102.0000",
                "COMP_MRP_WITH_TAX_PAISE_UNIT" to "101.0000",
                "MRP_MATCHED" to "false"
            ), result.metadata
        )
    }

    @Test
    fun `test metro ladder match with metro and udaan both having null MRP`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgMetroEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            metroMrpWithTaxInPaisaUnit = null,
            udaanMrpWithTaxInPaisaAtAssortment = null,
            metroPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("97")
                    )
                )
            ),
            previousInput = BigDecimalValue(BigDecimal("100"))
        )
        val result = fmcgMetroCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(100)
                    )
                )
            )
        )

        assertEquals(
            mapOf(
                "DEFAULT_LADDER_CREATED" to "true",
                "LISTING_MRP_WITH_TAX_PAISE_UNIT" to "null",
                "COMP_MRP_WITH_TAX_PAISE_UNIT" to "null",
                "MRP_MATCHED" to "false"
            ), result.metadata
        )
    }

    @Test
    fun `test metro ladder match with metro price and previous input as big-decimal`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgMetroEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            metroPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("97")
                    )
                )
            ),
            previousInput = BigDecimalValue(BigDecimal("100"))
        )
        val result = fmcgMetroCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(97)
                    )
                )
            )
        )

        assertEquals(
            mapOf(
                "DEFAULT_LADDER_CREATED" to "true",
                "LISTING_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "MRP_MATCHED" to "true",
                "COMP_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "METRO_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,97)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000"
            ), result.metadata
        )
    }

    @Test
    fun `test metro ladder match with metro price and previous input as ladder`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgMetroEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            metroPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("98")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("100")
                    )
                )
            )
        )
        val result = fmcgMetroCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(98)
                    )
                )
            )
        )

        assertEquals(
            mapOf(
                "LISTING_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "COMP_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "MRP_MATCHED" to "true",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "METRO_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,98)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000"
            ), result.metadata
        )
    }

    @Test
    fun `test metro ladder match with metro price having single slab and conversion rate as 10`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgMetroEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            conversionRate = BigDecimalValue(BigDecimal("10")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            metroMrpWithTaxInPaisaUnit = BigDecimalValue(BigDecimal("10")),
            metroPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("9.8")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("100")
                    )
                )
            )
        )
        val result = fmcgMetroCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(98)
                    )
                )
            )
        )

        assertEquals(
            mapOf(
                "LISTING_MRP_WITH_TAX_PAISE_UNIT" to "10.0000",
                "COMP_MRP_WITH_TAX_PAISE_UNIT" to "10.0000",
                "MRP_MATCHED" to "true",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "METRO_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,98)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000"
            ), result.metadata
        )
    }

    @Test
    fun `test metro ladder match with metro price with metro competitive but floor hit`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgMetroEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            metroPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("78")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("100")
                    )
                )
            )
        )
        val result = fmcgMetroCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(80)
                    )
                )
            )
        )

        assertEquals(
            mapOf(
                "LISTING_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "COMP_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "MRP_MATCHED" to "true",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "METRO_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,78)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000",
                "COMP_PRICE_GUARD_RAILED_TO_FLOOR_FOR_LADDER_1" to "true"
            ), result.metadata
        )
    }

    @Test
    fun `test metro ladder match with metro price competitive in one ladder and non-competitive in another`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgMetroEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            metroPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("98")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("97.5")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 2,
                        ladderValue = BigDecimal("100")
                    ),
                    Ladder(
                        minQuantity = 3,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("96")
                    )
                )
            )
        )
        val result = fmcgMetroCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 2,
                        ladderValue = BigDecimal(98)
                    ),
                    Ladder(
                        minQuantity = 3,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(96)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "LISTING_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "COMP_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "MRP_MATCHED" to "true",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "METRO_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,3,98), (4,2147483647,97.5)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000"
            ), result.metadata
        )
    }

    @Test
    fun `test metro ladder match with metro price competitive in all ladders`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgMetroEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            metroPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal("98")
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("97")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("96")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("100")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal("99")
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("98")
                    )
                )
            )
        )
        val result = fmcgMetroCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal(98)
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal(97)
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(96)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "LISTING_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "COMP_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "MRP_MATCHED" to "true",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "METRO_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,1,98), (2,3,97), (4,2147483647,96)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000"
            ), result.metadata
        )
    }

    @Test
    fun `test metro ladder match with metro price having more than ladder threshold count ladders`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgMetroEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            metroPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal("95")
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 4,
                        ladderValue = BigDecimal("90")
                    ),
                    Ladder(
                        minQuantity = 5,
                        maxQuantity = 9,
                        ladderValue = BigDecimal("86")
                    ),
                    Ladder(
                        minQuantity = 10,
                        maxQuantity = 12,
                        ladderValue = BigDecimal("81")
                    ),
                    Ladder(
                        minQuantity = 13,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("80")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal("99")
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 2,
                        ladderValue = BigDecimal("96")
                    ),
                    Ladder(
                        minQuantity = 3,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("91")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("90")
                    )
                )
            )
        )
        val result = fmcgMetroCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal(95)
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 4,
                        ladderValue = BigDecimal(90)
                    ),
                    Ladder(
                        minQuantity = 5,
                        maxQuantity = 9,
                        ladderValue = BigDecimal(86)
                    ),
                    Ladder(
                        minQuantity = 10,
                        maxQuantity = 12,
                        ladderValue = BigDecimal(81)
                    ),
                    Ladder(
                        minQuantity = 13,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(80)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "LISTING_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "COMP_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "MRP_MATCHED" to "true",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "METRO_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,1,95), (2,4,90), (5,9,86), (10,12,81), (13,2147483647,80)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000"
            ), result.metadata
        )
    }

    @Test
    fun `test metro ladder match with metro price having more than 4 ladders with some hitting floor`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgMetroEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            metroPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal("83")
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("82")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal("80.5")
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = 8,
                        ladderValue = BigDecimal("80")
                    ),
                    Ladder(
                        minQuantity = 9,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("78")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("86")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal("84")
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("82")
                    )
                )
            )
        )
        val result = fmcgMetroCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal(83)
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal(82)
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal(80.5)
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(80)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "LISTING_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "COMP_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "MRP_MATCHED" to "true",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "METRO_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,1,83), (2,3,82), (4,5,80.5), (6,8,80), (9,2147483647,78)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000",
                "COMP_PRICE_GUARD_RAILED_TO_FLOOR_FOR_LADDER_5" to "true"
            ), result.metadata
        )
    }

    @Test
    fun `test metro ladder match with metro price and input both lower than floor guardrail value`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgMetroEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            metroPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal("83")
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("82")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 8,
                        ladderValue = BigDecimal("80")
                    ),
                    Ladder(
                        minQuantity = 9,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("78")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("83")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal("81")
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("79")
                    )
                )
            )
        )
        val result = fmcgMetroCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal(83)
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal(82)
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal(80)
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(79)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "LISTING_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "COMP_MRP_WITH_TAX_PAISE_UNIT" to "100.0000",
                "MRP_MATCHED" to "true",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "METRO_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,1,83), (2,3,82), (4,8,80), (9,2147483647,78)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000",
                "COMP_PRICE_GUARD_RAILED_TO_FLOOR_FOR_LADDER_4" to "true"
            ), result.metadata
        )
    }

    @Test
    fun `test metro ladder match with metro having multiple slabs and conversion rate 10`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgMetroEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            metroMrpWithTaxInPaisaUnit = BigDecimalValue(BigDecimal("10")),
            conversionRate = BigDecimalValue(BigDecimal(10)),
            metroPrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 8,
                        ladderValue = BigDecimal("8.3")
                    ),
                    Ladder(
                        minQuantity = 9,
                        maxQuantity = 24,
                        ladderValue = BigDecimal("8.2")
                    ),
                    Ladder(
                        minQuantity = 25,
                        maxQuantity = 30,
                        ladderValue = BigDecimal("8.1")
                    ),
                    Ladder(
                        minQuantity = 31,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("7.9")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("86")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal("84")
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("79")
                    )
                )
            )
        )
        val result = fmcgMetroCompMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 2,
                        ladderValue = BigDecimal(82)
                    ),
                    Ladder(
                        minQuantity = 3,
                        maxQuantity = 3,
                        ladderValue = BigDecimal(81)
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal(80)
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(79)
                    )
                )
            )
        )
        assertEquals(
            mapOf(
                "LISTING_MRP_WITH_TAX_PAISE_UNIT" to "10.0000",
                "COMP_MRP_WITH_TAX_PAISE_UNIT" to "10.0000",
                "MRP_MATCHED" to "true",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "METRO_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2,82), (3,3,81), (4,2147483647,79)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000",
                "COMP_PRICE_GUARD_RAILED_TO_FLOOR_FOR_LADDER_3" to "true"
            ), result.metadata
        )
    }
}
