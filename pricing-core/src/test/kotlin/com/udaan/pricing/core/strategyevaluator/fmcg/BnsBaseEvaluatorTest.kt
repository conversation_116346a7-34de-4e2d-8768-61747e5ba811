package com.udaan.pricing.core.strategyevaluator.fmcg

import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg.BnsBaseEvaluator
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.commons.BigDecimalValue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import java.math.BigDecimal

class BnsBaseEvaluatorTest {

    private val bnsBaseEvaluator = BnsBaseEvaluator

    @Test
    fun `test bns base strategy if required inputs are present`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createBnsBaseEvaluatorConfig(
            fmcgCogsInPaise = BigDecimalValue(BigDecimal(1000)),
            bnsMarginBps = BigDecimalValue(BigDecimal(500)),
            fmcgLppWotInRupeesAtUnit = null,
            conversionRate = null
        )
        val result = bnsBaseEvaluator.evaluate(evaluatorConfig)

        assertEquals(
            BigDecimalValue(BigDecimal("1050")).toString(),
            result.output.toString(),
            "Output value mismatch"
        )

        assertEquals(
            emptyMap<String, String>(),
            result.metadata,
            "Metadata should be empty"
        )
    }

    @Test
    fun `test bns base strategy if COGS missing but LPP and conversion rate are present`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createBnsBaseEvaluatorConfig(
            fmcgCogsInPaise = null,
            bnsMarginBps = BigDecimalValue(BigDecimal(500)),
            conversionRate = BigDecimalValue(BigDecimal(2.0)),
            fmcgLppWotInRupeesAtUnit = BigDecimalValue(BigDecimal(4.9))
        )
        val result = bnsBaseEvaluator.evaluate(evaluatorConfig)

        assertEquals(
            BigDecimalValue(BigDecimal("1029")).toString(),
            result.output.toString(),
            "Output value mismatch"
        )

        assertEquals(
            mapOf(
                "FALLBACK_TO_LPP" to "true",
                "LPP_WOT_PAISE_ASSORTMENT" to "980.0000"
            ), result.metadata
        )
    }

    @Test
    fun `test bns base strategy if COGS and Conversion Rate missing`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createBnsBaseEvaluatorConfig(
            bnsMarginBps = BigDecimalValue(BigDecimal(500)),
            fmcgCogsInPaise = null,
            fmcgLppWotInRupeesAtUnit = BigDecimalValue(BigDecimal(4.9)),
            conversionRate = null
        )

        assertThrows(IllegalArgumentException::class.java) {
            bnsBaseEvaluator.evaluate(evaluatorConfig)
        }
    }


    @Test
    fun `test bns base strategy if COGS and LPP missing`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createBnsBaseEvaluatorConfig(
            bnsMarginBps = BigDecimalValue(BigDecimal(500)),
            fmcgCogsInPaise = null,
            fmcgLppWotInRupeesAtUnit = null,
            conversionRate = BigDecimalValue(BigDecimal(1.0))
        )

        assertThrows(IllegalArgumentException::class.java) {
            bnsBaseEvaluator.evaluate(evaluatorConfig)
        }
    }


    @Test
    fun `test bns base strategy if Bns margin missing`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createBnsBaseEvaluatorConfig(
            bnsMarginBps = null,
            fmcgCogsInPaise = BigDecimalValue(BigDecimal(1000)),
            conversionRate = BigDecimalValue(BigDecimal(2.0)),
            fmcgLppWotInRupeesAtUnit = BigDecimalValue(BigDecimal(4.9))
        )

        assertThrows(IllegalArgumentException::class.java) {
            bnsBaseEvaluator.evaluate(evaluatorConfig)
        }
    }
}
