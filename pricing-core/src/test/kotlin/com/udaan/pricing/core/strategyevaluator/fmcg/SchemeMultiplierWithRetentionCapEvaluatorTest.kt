package com.udaan.pricing.core.strategyevaluator.fmcg

import com.udaan.pricing.core.strategyevaluator.impl.EvaluatorFactory
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils.equalsTo
import com.udaan.pricing.core.strategyevaluator.utils.MapComparatorUtils
import com.udaan.pricing.core.utils.BigDecimalExtensionsUtil.multiplyWithScale
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.commons.StringValue
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.math.BigDecimal

// @todo - add tests covering multi slab schemes which needs conversion to PU level
// @todo - either add tests here or add for LadderUtil fun directly
class SchemeMultiplierWithRetentionCapEvaluatorTest {

    @Test
    fun `test when there is no scheme present at all`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.getSchemeMultiplierAndRetentionCapRequestConfig(
            schemeMultiplierBps = BigDecimalValue(BigDecimal(10000)),
            schemeRetentionCapBps = BigDecimalValue(BigDecimal(0)),
            fmcgSourcingChannel = StringValue("BNS")
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))
        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(100)
                )
            )
        )

        assertTrue(expectedOutput.value.equalsTo((result!!.output as LadderValue).value)) {
            "${result.output} is not matched with " + expectedOutput
        }

        MapComparatorUtils.compareMaps(
            result.metadata,
            mapOf(
                "FMCG_SCHEME_MULTIPLIER_WITH_RETENTION_CAP_INPUT" to "100",
                "CONVERSION_RATE" to "1",
                "SCHEME_MULTIPLIER_BPS" to "10000",
                "SCHEME_RETENTION_CAP_BPS" to "0",
                "SOURCING_CHANNEL" to "BNS",
                "DTR_RETAIL_SCHEME" to "null",
                "GT_RETAIL_SCHEME" to "null",
                "GT_WS_SCHEME" to "null",
                "SCHEME_POST_MULTIPLIER_AND_RETENTION_CAP" to "null",
                "FMCG_SCHEME_MULTIPLIER_WITH_RETENTION_CAP_OUTPUT" to "[(1,2147483647,100)]"
            )
        )
    }

    @Test
    fun `test apply dtr scheme with no scheme multiplier and no retention cap`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.getSchemeMultiplierAndRetentionCapRequestConfig(
            schemeMultiplierBps = BigDecimalValue(BigDecimal(10000)),
            schemeRetentionCapBps = BigDecimalValue(BigDecimal(0)),
            fmcgSourcingChannel = StringValue("DTR"),
            dtrRetailScheme = LadderValue(listOf(Ladder(1, 5, BigDecimal(100))))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))
        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(99)
                )
            )
        )
        assertTrue(expectedOutput.value.equalsTo((result!!.output as LadderValue).value)) {
            "" + result.output + "is not matched with " + expectedOutput
        }
    }

    @Test
    fun `test apply dtr scheme with scheme multiplier bps as 8000 and no retention cap`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.getSchemeMultiplierAndRetentionCapRequestConfig(
            schemeMultiplierBps = BigDecimalValue(BigDecimal(8000)),
            schemeRetentionCapBps = BigDecimalValue(BigDecimal(0)),
            fmcgSourcingChannel = StringValue("DTR"),
            dtrRetailScheme = LadderValue(listOf(Ladder(1, 5, BigDecimal(1500))))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))
        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(85.0000)
                )
            )
        )
        assertTrue(expectedOutput.value.equalsTo((result!!.output as LadderValue).value)) {
            "" + result.output + "is not matched with " + expectedOutput
        }

        MapComparatorUtils.compareMaps(
            result.metadata,
            mapOf(
                "FMCG_SCHEME_MULTIPLIER_WITH_RETENTION_CAP_INPUT" to "100",
                "CONVERSION_RATE" to "1",
                "SCHEME_MULTIPLIER_BPS" to "8000",
                "SCHEME_RETENTION_CAP_BPS" to "0",
                "SOURCING_CHANNEL" to "DTR",
                "DTR_RETAIL_SCHEME" to "[(1,5,1500)]",
                "GT_RETAIL_SCHEME" to "null",
                "GT_WS_SCHEME" to "null",
                "SELECTED_SCHEME_WITHOUT_MULTIPLIER" to "[(1,5,1500)]",
                "SELECTED_SCHEME_WITH_MULTIPLIER" to "[(1,5,1200)]",
                "RETAINED_SCHEME_CAP_HIT" to "true",
                "SCHEME_POST_MULTIPLIER_AND_RETENTION_CAP" to "[(1,2147483647,1500)]",
                "FMCG_SCHEME_MULTIPLIER_WITH_RETENTION_CAP_OUTPUT" to "[(1,2147483647,85)]"
            )
        )
    }

    @Test
    fun `test apply dtr scheme with scheme multiplier bps and capped retention scheme`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.getSchemeMultiplierAndRetentionCapRequestConfig(
            schemeMultiplierBps = BigDecimalValue(BigDecimal(8000)),
            schemeRetentionCapBps = BigDecimalValue(BigDecimal(100)),
            fmcgSourcingChannel = StringValue("DTR"),
            dtrRetailScheme = LadderValue(listOf(Ladder(1, 5, BigDecimal(1500))))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))
        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(86.0000)
                )
            )
        )
        assertTrue(expectedOutput.value.equalsTo((result!!.output as LadderValue).value)) {
            "" + result.output + "is not matched with " + expectedOutput
        }

        MapComparatorUtils.compareMaps(
            result.metadata,
            mapOf(
                "FMCG_SCHEME_MULTIPLIER_WITH_RETENTION_CAP_INPUT" to "100",
                "CONVERSION_RATE" to "1",
                "SCHEME_MULTIPLIER_BPS" to "8000",
                "SCHEME_RETENTION_CAP_BPS" to "100",
                "SOURCING_CHANNEL" to "DTR",
                "DTR_RETAIL_SCHEME" to "[(1,5,1500)]",
                "GT_RETAIL_SCHEME" to "null",
                "GT_WS_SCHEME" to "null",
                "SELECTED_SCHEME_WITHOUT_MULTIPLIER" to "[(1,5,1500)]",
                "SELECTED_SCHEME_WITH_MULTIPLIER" to "[(1,5,1200)]",
                "RETAINED_SCHEME_CAP_HIT" to "true",
                "SCHEME_POST_MULTIPLIER_AND_RETENTION_CAP" to "[(1,2147483647,1400)]",
                "FMCG_SCHEME_MULTIPLIER_WITH_RETENTION_CAP_OUTPUT" to "[(1,2147483647,86)]"
            )
        )
    }

    @Test
    fun `test apply gt retail scheme with no scheme multiplier and no retention cap`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.getSchemeMultiplierAndRetentionCapRequestConfig(
            schemeMultiplierBps = BigDecimalValue(BigDecimal(10000)),
            schemeRetentionCapBps = BigDecimalValue(BigDecimal(0)),
            fmcgSourcingChannel = StringValue("BNS"),
            gtRetailScheme = LadderValue(listOf(Ladder(1, 10, BigDecimal(50)))),
            gtWsScheme = LadderValue(listOf(Ladder(1, 10, BigDecimal(10))))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))
        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(99.9).multiplyWithScale(BigDecimal(1))
                )
            )
        )
        assertTrue(expectedOutput.value.equalsTo((result!!.output as LadderValue).value)) {
            "" + result.output + "is not matched with " + expectedOutput
        }

        MapComparatorUtils.compareMaps(
            result.metadata,
            mapOf(
                "FMCG_SCHEME_MULTIPLIER_WITH_RETENTION_CAP_INPUT" to "100",
                "CONVERSION_RATE" to "1",
                "SCHEME_MULTIPLIER_BPS" to "10000",
                "SCHEME_RETENTION_CAP_BPS" to "0",
                "SOURCING_CHANNEL" to "BNS",
                "DTR_RETAIL_SCHEME" to "null",
                "GT_RETAIL_SCHEME" to "[(1,10,50)]",
                "GT_WS_SCHEME" to "[(1,10,10)]",
                "GT_WS_GUARDRAIL_HIT" to "true",
                "SELECTED_SCHEME_WITHOUT_MULTIPLIER" to "[(1,10,10)]",
                "SELECTED_SCHEME_WITH_MULTIPLIER" to "[(1,10,10)]",
                "RETAINED_SCHEME_CAP_HIT" to "false",
                "SCHEME_POST_MULTIPLIER_AND_RETENTION_CAP" to "[(1,2147483647,10)]",
                "FMCG_SCHEME_MULTIPLIER_WITH_RETENTION_CAP_OUTPUT" to "[(1,2147483647,99.9)]"
            )
        )
    }

    @Test
    fun `test apply gt retail scheme with scheme multiplier bps as 4000 and retained scheme capped at 20 bps`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.getSchemeMultiplierAndRetentionCapRequestConfig(
            schemeMultiplierBps = BigDecimalValue(BigDecimal(4000)),
            schemeRetentionCapBps = BigDecimalValue(BigDecimal(20)),
            fmcgSourcingChannel = StringValue("BNS"),
            gtRetailScheme = LadderValue(listOf(Ladder(1, 10, BigDecimal(50)))),
            gtWsScheme = LadderValue(listOf(Ladder(1, 10, BigDecimal(10))))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))
        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(99.96).multiplyWithScale(BigDecimal(1))
                )
            )
        )
        assertTrue(expectedOutput.value.equalsTo((result!!.output as LadderValue).value)) {
            "" + result.output + "is not matched with " + expectedOutput
        }

        MapComparatorUtils.compareMaps(
            result.metadata,
            mapOf(
                "FMCG_SCHEME_MULTIPLIER_WITH_RETENTION_CAP_INPUT" to "100",
                "CONVERSION_RATE" to "1",
                "SCHEME_MULTIPLIER_BPS" to "4000",
                "SCHEME_RETENTION_CAP_BPS" to "20",
                "SOURCING_CHANNEL" to "BNS",
                "DTR_RETAIL_SCHEME" to "null",
                "GT_RETAIL_SCHEME" to "[(1,10,50)]",
                "GT_WS_SCHEME" to "[(1,10,10)]",
                "GT_WS_GUARDRAIL_HIT" to "true",
                "SELECTED_SCHEME_WITHOUT_MULTIPLIER" to "[(1,10,10)]",
                "SELECTED_SCHEME_WITH_MULTIPLIER" to "[(1,10,4)]",
                "RETAINED_SCHEME_CAP_HIT" to "false",
                "SCHEME_POST_MULTIPLIER_AND_RETENTION_CAP" to "[(1,2147483647,4)]",
                "FMCG_SCHEME_MULTIPLIER_WITH_RETENTION_CAP_OUTPUT" to "[(1,2147483647,99.96)]"
            )
        )
    }

    @Test
    fun `test apply gt retail scheme with capped at gt ws scheme and scheme multiplier as 4000 bps and no retention cap`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.getSchemeMultiplierAndRetentionCapRequestConfig(
            schemeMultiplierBps = BigDecimalValue(BigDecimal(4000)),
            schemeRetentionCapBps = BigDecimalValue(BigDecimal(0)),
            fmcgSourcingChannel = StringValue("BNS"),
            gtRetailScheme = LadderValue(listOf(Ladder(1, 10, BigDecimal(50)))),
            gtWsScheme = LadderValue(listOf(Ladder(1, 10, BigDecimal(60))))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))
        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(99.5).multiplyWithScale(BigDecimal(1))
                )
            )
        )
        assertTrue(expectedOutput.value.equalsTo((result!!.output as LadderValue).value)) {
            "" + result.output + "is not matched with " + expectedOutput
        }

        MapComparatorUtils.compareMaps(
            result.metadata,
            mapOf(
                "FMCG_SCHEME_MULTIPLIER_WITH_RETENTION_CAP_INPUT" to "100",
                "CONVERSION_RATE" to "1",
                "SCHEME_MULTIPLIER_BPS" to "4000",
                "SCHEME_RETENTION_CAP_BPS" to "0",
                "SOURCING_CHANNEL" to "BNS",
                "DTR_RETAIL_SCHEME" to "null",
                "GT_RETAIL_SCHEME" to "[(1,10,50)]",
                "GT_WS_SCHEME" to "[(1,10,60)]",
                "GT_WS_GUARDRAIL_HIT" to "false",
                "SELECTED_SCHEME_WITHOUT_MULTIPLIER" to "[(1,10,50)]",
                "SELECTED_SCHEME_WITH_MULTIPLIER" to "[(1,10,20)]",
                "RETAINED_SCHEME_CAP_HIT" to "true",
                "SCHEME_POST_MULTIPLIER_AND_RETENTION_CAP" to "[(1,2147483647,50)]",
                "FMCG_SCHEME_MULTIPLIER_WITH_RETENTION_CAP_OUTPUT" to "[(1,2147483647,99.5)]"
            )
        )
    }
}
