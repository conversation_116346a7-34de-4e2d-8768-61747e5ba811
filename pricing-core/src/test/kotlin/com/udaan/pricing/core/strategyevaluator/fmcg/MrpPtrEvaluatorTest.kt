package com.udaan.pricing.core.strategyevaluator.fmcg

import com.udaan.pricing.core.strategyevaluator.impl.EvaluatorFactory
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.core.strategyevaluator.utils.MapComparatorUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.variable.VariableId
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class MrpPtrEvaluatorTest {

    @Test
    fun `test mrp ptr`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createMrpPtrEvaluatorConfig(
            mrpInPaisa = BigDecimalValue(BigDecimal(10000)),
            ptrBps = BigDecimalValue(BigDecimal(1000))

        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))
        val expectedOutput = BigDecimalValue(BigDecimal("8658.0087"))

        val expectedMetadata = mapOf(
            evaluatorConfig.strategy.name + "_INPUT" to "",
            VariableId.MRP_WT_PAISA_SET.name to "10000",
            VariableId.PTR_BPS.name to "1000",
            VariableId.GST_BPS.name to "500",
            VariableId.CESS_BPS.name to "0",
            evaluatorConfig.strategy.name + "_OUTPUT" to "8658.0087"
        )

        assertTrue(expectedOutput.value.toString() == (result!!.output as BigDecimalValue).toString()) {
            "${result.output} is not matched with " + expectedOutput
        }

        MapComparatorUtils.compareMaps(
            result.metadata,
            expectedMetadata
        )
    }


    @Test
    fun `test mrp ptr with no tax`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createMrpPtrEvaluatorConfig(
            mrpInPaisa = BigDecimalValue(BigDecimal(10000)),
            ptrBps = BigDecimalValue(BigDecimal(1000)),
            gstBps = BigDecimalValue(BigDecimal(0))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))
        val expectedOutput = BigDecimalValue(BigDecimal("9090.9091"))

        val expectedMetadata = mapOf(
            evaluatorConfig.strategy.name + "_INPUT" to "",
            VariableId.MRP_WT_PAISA_SET.name to "10000",
            VariableId.PTR_BPS.name to "1000",
            VariableId.GST_BPS.name to "0",
            VariableId.CESS_BPS.name to "0",
            evaluatorConfig.strategy.name + "_OUTPUT" to "9090.9091"
        )

        assertTrue(expectedOutput.value.toString() == (result!!.output as BigDecimalValue).toString()) {
            "${result.output} is not matched with " + expectedOutput
        }

        MapComparatorUtils.compareMaps(
            result.metadata,
            expectedMetadata
        )
    }
}
