package com.udaan.pricing.core.strategyevaluator.fmcg

import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils.equalsTo
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.core.strategyevaluator.impl.dynamic.fmcg.FmcgHyperpureCompDirectMatchEvaluator
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import java.math.BigDecimal

class FmcgHyperpureDirectMatchEvaluatorTest {

    private val fmcgHyperpureDirectMatchEvaluator = FmcgHyperpureCompDirectMatchEvaluator

    @Test
    fun `test hyperpure ladder match with hyperpure price as null`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgHyperpureDirectCompEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            hyperPureCompMarkdownBps = BigDecimalValue(BigDecimal("0")),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("100")
                    )
                )
            )
        )
        val result = fmcgHyperpureDirectMatchEvaluator.evaluate(evaluatorConfig)
        assertEquals(
            LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("100")
                    )
                )
            ),
            result.output,
            "Output value mismatch"
        )
        assertEquals(
            emptyMap<String, String>(), result.metadata
        )
    }

    @Test
    fun `test hyperpure ladder match with hyperpure price but guardrail null`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgHyperpureDirectCompEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            hyperPureCompMarkdownBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = null,
            hyperpurePrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("97")
                    )
                )
            ),
            previousInput = BigDecimalValue(BigDecimal("100"))
        )
        val result = fmcgHyperpureDirectMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(100)
                    )
                )
            ),
            "Output ladder value mismatch"
        )
        assertEquals(
            mapOf(
                "DEFAULT_LADDER_CREATED" to "true",
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT_WITH_MARKDOWN" to "[(1,2147483647,97)]",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,97)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "null",
                "NO_COMP_COMPARISON_AS_NO_GUARDRAIL" to "true"
            ), result.metadata
        )
    }

    @Test
    fun `test hyperpure ladder match with hyperpure price and previous input as big-decimal`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgHyperpureDirectCompEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            hyperPureCompMarkdownBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            hyperpurePrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("97")
                    )
                )
            ),
            previousInput = BigDecimalValue(BigDecimal("100"))
        )
        val result = fmcgHyperpureDirectMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(97)
                    )
                )
            ),
            "Output ladder value mismatch"
        )
        assertEquals(
            mapOf(
                "DEFAULT_LADDER_CREATED" to "true",
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT_WITH_MARKDOWN" to "[(1,2147483647,97)]",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,97)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000"
            ), result.metadata
        )
    }

    @Test
    fun `test hyperpure ladder match with hyperpure price and previous input as ladder`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgHyperpureDirectCompEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            hyperPureCompMarkdownBps = BigDecimalValue(BigDecimal("0")),
            hyperpurePrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("98")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("100")
                    )
                )
            )
        )
        val result = fmcgHyperpureDirectMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(98)
                    )
                )
            ),
            "Output ladder value mismatch"
        )
        assertEquals(
            mapOf(
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT_WITH_MARKDOWN" to "[(1,2147483647,98)]",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,98)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000"
            ), result.metadata
        )
    }

    @Test
    fun `test hyperpure ladder match with hyperpure price having single slab and conversion rate as 10`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgHyperpureDirectCompEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            conversionRate = BigDecimalValue(BigDecimal("10")),
            hyperPureCompMarkdownBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            hyperpurePrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("9.8")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("100")
                    )
                )
            )
        )
        val result = fmcgHyperpureDirectMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(98)
                    )
                )
            ),
            "Output ladder value mismatch"
        )
        assertEquals(
            mapOf(
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT_WITH_MARKDOWN" to "[(1,2147483647,98)]",
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,98)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000"
            ), result.metadata
        )
    }

    @Test
    fun `test hyperpure ladder match with hyperpure price with hyperpure competitive but floor hit`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgHyperpureDirectCompEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            hyperPureCompMarkdownBps = BigDecimalValue(BigDecimal("0")),
            hyperpurePrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("78")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("100")
                    )
                )
            )
        )
        val result = fmcgHyperpureDirectMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(80)
                    )
                )
            ),
            "Output ladder value mismatch"
        )
        assertEquals(
            mapOf(
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT_WITH_MARKDOWN" to "[(1,2147483647,78)]",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2147483647,78)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000",
                "COMP_PRICE_GUARD_RAILED_TO_FLOOR_FOR_LADDER_1" to "true"
            ), result.metadata
        )
    }

    @Test
    fun `test hyperpure ladder match with hyperpure price competitive in all ladders`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgHyperpureDirectCompEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            hyperPureCompMarkdownBps = BigDecimalValue(BigDecimal("0")),
            hyperpurePrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal("98")
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("97")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("96")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("100")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal("99")
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("98")
                    )
                )
            )
        )
        val result = fmcgHyperpureDirectMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal(98)
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal(97)
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(96)
                    )
                )
            ),
            "Output ladder value mismatch"
        )
        assertEquals(
            mapOf(
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT_WITH_MARKDOWN" to "[(1,1,98), (2,3,97), (4,2147483647,96)]",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,1,98), (2,3,97), (4,2147483647,96)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000"
            ), result.metadata
        )
    }

    @Test
    fun `test hyperpure ladder match with hyperpure price having more than 4 ladders with some hitting floor`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgHyperpureDirectCompEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            hyperPureCompMarkdownBps = BigDecimalValue(BigDecimal("0")),
            hyperpurePrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal("83")
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("82")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal("80.5")
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = 8,
                        ladderValue = BigDecimal("80")
                    ),
                    Ladder(
                        minQuantity = 9,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("78")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("86")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal("84")
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("82")
                    )
                )
            )
        )
        val result = fmcgHyperpureDirectMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal(83)
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal(82)
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal(80.5)
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(80)
                    )
                )
            ),
            "Output ladder value mismatch"
        )
        assertEquals(
            mapOf(
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT_WITH_MARKDOWN" to "[(1,1,83), (2,3,82), (4,5,80.5), (6,8,80), (9,2147483647,78)]",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,1,83), (2,3,82), (4,5,80.5), (6,8,80), (9,2147483647,78)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000",
                "COMP_PRICE_GUARD_RAILED_TO_FLOOR_FOR_LADDER_4" to "true",
                "COMP_PRICE_GUARD_RAILED_TO_FLOOR_FOR_LADDER_5" to "true"
            ), result.metadata
        )
    }

    @Test
    fun `test hyperpure ladder match with hyperpure price and input both lower than floor guardrail value`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgHyperpureDirectCompEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            hyperPureCompMarkdownBps = BigDecimalValue(BigDecimal("0")),
            hyperpurePrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal("83")
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("82")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 8,
                        ladderValue = BigDecimal("80")
                    ),
                    Ladder(
                        minQuantity = 9,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("78")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("83")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal("81")
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("79")
                    )
                )
            )
        )
        val result = fmcgHyperpureDirectMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 1,
                        ladderValue = BigDecimal(83)
                    ),
                    Ladder(
                        minQuantity = 2,
                        maxQuantity = 3,
                        ladderValue = BigDecimal(82)
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(80)
                    )
                )
            ),
            "Output ladder value mismatch"
        )
        assertEquals(
            mapOf(
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT_WITH_MARKDOWN" to "[(1,1,83), (2,3,82), (4,8,80), (9,2147483647,78)]",
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,1,83), (2,3,82), (4,8,80), (9,2147483647,78)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000",
                "COMP_PRICE_GUARD_RAILED_TO_FLOOR_FOR_LADDER_3" to "true",
                "COMP_PRICE_GUARD_RAILED_TO_FLOOR_FOR_LADDER_4" to "true"
            ), result.metadata
        )
    }

    @Test
    fun `test hyperpure ladder match with hyperpure having multiple slabs and conversion rate 10`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createFmcgHyperpureDirectCompEvaluatorConfig(
            gstBps = BigDecimalValue(BigDecimal("0")),
            cessBps = BigDecimalValue(BigDecimal("0")),
            fmcgCogsInPaisaAtAssortment = BigDecimalValue(BigDecimal("80")),
            hyperPureCompMarkdownBps = BigDecimalValue(BigDecimal("100")),
            conversionRate = BigDecimalValue(BigDecimal(10)),
            hyperpurePrice = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 8,
                        ladderValue = BigDecimal("8.3")
                    ),
                    Ladder(
                        minQuantity = 9,
                        maxQuantity = 24,
                        ladderValue = BigDecimal("8.2")
                    ),
                    Ladder(
                        minQuantity = 25,
                        maxQuantity = 30,
                        ladderValue = BigDecimal("8.1")
                    ),
                    Ladder(
                        minQuantity = 31,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("7.9")
                    )
                )
            ),
            previousInput = LadderValue(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("86")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = 5,
                        ladderValue = BigDecimal("84")
                    ),
                    Ladder(
                        minQuantity = 6,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("79")
                    )
                )
            )
        )
        val result = fmcgHyperpureDirectMatchEvaluator.evaluate(evaluatorConfig)
        assertTrue(
            (result.output as LadderValue).value.equalsTo(
                listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 2,
                        ladderValue = BigDecimal("81.18")
                    ),
                    Ladder(
                        minQuantity = 3,
                        maxQuantity = 3,
                        ladderValue = BigDecimal("80.19")
                    ),
                    Ladder(
                        minQuantity = 4,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal("80")
                    )
                )
            ),
            "Output ladder value mismatch"
        )
        assertEquals(
            mapOf(
                "COMP_SLABS_ADJUSTED_FOR_ASSORTMENT" to "true",
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT_WITH_MARKDOWN" to "[(1,2,81.18), (3,3,80.19), (4,2147483647,78.21)]",
                "HP_LADDER_PRICE_WOT_PAISE_ASSORTMENT" to "[(1,2,82), (3,3,81), (4,2147483647,79)]",
                "COMP_FLOOR_GUARDRAIL_WOT_PAISE_ASSORTMENT" to "80.0000",
                "COMP_PRICE_GUARD_RAILED_TO_FLOOR_FOR_LADDER_3" to "true"
            ), result.metadata
        )
    }
}
