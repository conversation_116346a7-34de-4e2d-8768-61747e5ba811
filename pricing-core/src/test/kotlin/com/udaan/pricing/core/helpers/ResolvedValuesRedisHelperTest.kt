package com.udaan.pricing.core.helpers

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.udaan.pricing.core.cache.RedisHelper
import com.udaan.pricing.core.helpers.signals.ResolvedValuesRedisHelper
import com.udaan.pricing.variable.VariableId
import io.lettuce.core.KeyValue
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.Assert.*
import org.junit.Test

class ResolvedValuesRedisHelperTest {
    private val objectMapper = ObjectMapper().registerKotlinModule()
    private val redisHelper: RedisHelper = mockk()
    private val resolvedValuesRedisHelper = ResolvedValuesRedisHelper(redisHelper, objectMapper)

    @Test
    fun `getFieldValuesForKey() should return resolved values when all variables are found in cache`() = runBlocking {
        val variable1 = VariableId.GST_BPS
        val variable2 = VariableId.CESS_BPS
        val variables = listOf(variable1.name, variable2.name)
        val cacheKey = ""

        val mockKeyValue1 = KeyValue.fromNullable(variable1.name, "{\"success\":true,\"value\":{\"type\":\"BigDecimalValue\",\"value\":\"10.1\"},\"referenceSignalId\":null,\"resolverLogic\":null,\"exception\":null}")
        val mockKeyValue2 = KeyValue.fromNullable(variable2.name, "{\"success\":false,\"value\":null,\"referenceSignalId\":null,\"resolverLogic\":null, \"exception\": \"error\"}")

        coEvery {
            redisHelper.getRedisHashValues(cacheKey, variables)
        } returns listOf(mockKeyValue1, mockKeyValue2)

        val result = resolvedValuesRedisHelper.getFieldValuesForKey(cacheKey, variables)

        assertEquals(variables.size, result.size)
        assertNotNull(result[variable1])
        assertNotNull(result[variable2])
    }

    @Test
    fun `getFieldValuesForKey() should return empty map when not all variables are found in cache`() = runBlocking {
        val variable1 = VariableId.GST_BPS
        val variable2 = VariableId.CESS_BPS
        val variables = listOf(variable1.name, variable2.name)
        val cacheKey = ""

        val mockKeyValue1 = KeyValue.fromNullable(variable1.name, "{\"success\":true,\"value\":{\"type\":\"BigDecimalValue\",\"value\":\"10.1\"},\"referenceSignalId\":null,\"resolverLogic\":null,\"exception\":null}")
        val mockKeyValue2 = KeyValue.empty<String, String>(variable2.name)

        coEvery {
            redisHelper.getRedisHashValues(cacheKey, variables)
        } returns listOf(mockKeyValue1, mockKeyValue2)

        val result = resolvedValuesRedisHelper.getFieldValuesForKey(cacheKey, variables)

        assertTrue(result.isEmpty())
    }

    @Test
    fun `getFieldValuesForKey() should return empty map when the key itself isn't found in cache`() = runBlocking {
        val variable1 = VariableId.GST_BPS
        val variable2 = VariableId.CESS_BPS
        val variables = listOf(variable1.name, variable2.name)
        val cacheKey = ""

        coEvery {
            redisHelper.getRedisHashValues(cacheKey, variables)
        } returns null

        val result = resolvedValuesRedisHelper.getFieldValuesForKey(cacheKey, variables)

        assertTrue(result.isEmpty())
    }

}
