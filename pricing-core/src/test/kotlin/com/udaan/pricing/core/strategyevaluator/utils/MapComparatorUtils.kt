package com.udaan.pricing.core.strategyevaluator.utils

object MapComparatorUtils {
    fun compareMaps(map1: Map<String, String>, map2: Map<String, String>) {
        map1.forEach { (key, value1) ->
            val value2 = map2[key] // Check if the key exists in map2
            if (value2 == null || value1 != value2) {
                // Throw an exception for mismatched keys or values
                throw IllegalArgumentException("Mismatch found for key '$key': map1 has '$value1', map2 has '${value2 ?: "null"}'")
            }
        }

        // Check for extra keys in map2 that are not in map1
        map2.keys.forEach { key ->
            if (!map1.containsKey(key)) {
                throw IllegalArgumentException("Extra key found in map2: '$key'")
            }
        }
    }
}
