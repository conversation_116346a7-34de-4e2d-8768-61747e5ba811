package com.udaan.pricing.core.strategyevaluator.staples

import com.udaan.pricing.core.strategyevaluator.impl.dynamic.staples.LipGuardrailEvaluator
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class LipGuardrailTest {

    @Test
    fun `test staples lip guard rail test with previous output as null`() {
        val staplesGuardrailStrategyConfig = EvaluatorRequestContextUtils.createLipGuardRailEvaluatorConfig(
            previousOutput = BigDecimalValue(BigDecimal("100.0")),
            conversionRate = BigDecimalValue(BigDecimal(1))
        )
        val result = LipGuardrailEvaluator.evaluate(staplesGuardrailStrategyConfig)

        assertTrue(result.output.toString() == BigDecimalValue(BigDecimal("100")).toString())
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_PRICE" to "null",
            "CEIL_GUARDRAIL_PRICE" to "null",
            "FLOOR_GUARDRAIL_HIT" to "false",
            "CEIL_GUARDRAIL_HIT" to "false"
        ), result.metadata)
    }

    @Test
    fun `test staples lip guard rail test with previous output as not null`() {
        val staplesGuardrailStrategyConfig = EvaluatorRequestContextUtils.createLipGuardRailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(1)),
            previousOutput = BigDecimalValue(BigDecimal(100))
        )
        val result = LipGuardrailEvaluator.evaluate(staplesGuardrailStrategyConfig)

        assertTrue(result.output == BigDecimalValue(BigDecimal("100")))
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_PRICE" to "null",
            "CEIL_GUARDRAIL_PRICE" to "null",
            "FLOOR_GUARDRAIL_HIT" to "false",
            "CEIL_GUARDRAIL_HIT" to "false"
        ), result.metadata)
    }

    @Test
    fun `test staples lip guard rail test with floor and ceil as +-10 percent`() {
        val staplesGuardrailStrategyConfig = EvaluatorRequestContextUtils.createLipGuardRailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(1)),
            previousOutput = BigDecimalValue(BigDecimal(95)),
            lipPriceInPaisa = BigDecimalValue(BigDecimal(100)),
            floorGuardRailBps = BigDecimalValue(BigDecimal(-1000)),
            ceilGuardrailBps = BigDecimalValue(BigDecimal(1000))
        )
        val result = LipGuardrailEvaluator.evaluate(staplesGuardrailStrategyConfig)

        assertTrue(result.output.toString() == BigDecimalValue(BigDecimal("95")).toString())
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_PRICE" to "90",
            "CEIL_GUARDRAIL_PRICE" to "110",
            "FLOOR_GUARDRAIL_HIT" to "false",
            "CEIL_GUARDRAIL_HIT" to "false"
        ), result.metadata)
    }



    @Test
    fun `test staples lip guard rail test with final price guard-railed to floor`() {
        val staplesGuardrailStrategyConfig = EvaluatorRequestContextUtils.createLipGuardRailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(1)),
            previousOutput = BigDecimalValue(BigDecimal(89)),
            lipPriceInPaisa = BigDecimalValue(BigDecimal(100)),
            floorGuardRailBps = BigDecimalValue(BigDecimal(-1000)),
            ceilGuardrailBps = BigDecimalValue(BigDecimal(1000))
        )
        val result = LipGuardrailEvaluator.evaluate(staplesGuardrailStrategyConfig)

        assertTrue(result.output.toString() == BigDecimalValue(BigDecimal("90")).toString())
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_PRICE" to "90",
            "CEIL_GUARDRAIL_PRICE" to "110",
            "GUARD_RAILED_TO_FLOOR_PRICE" to "90",
            "FLOOR_GUARDRAIL_HIT" to "true",
            "CEIL_GUARDRAIL_HIT" to "false"
        ), result.metadata)
    }

    @Test
    fun `test staples lip guard rail test with final price guard-railed to ceil`() {
        val staplesGuardrailStrategyConfig = EvaluatorRequestContextUtils.createLipGuardRailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(1)),
            previousOutput = BigDecimalValue(BigDecimal(111)),
            lipPriceInPaisa = BigDecimalValue(BigDecimal(100)),
            floorGuardRailBps = BigDecimalValue(BigDecimal(-1000)),
            ceilGuardrailBps = BigDecimalValue(BigDecimal(1000))
        )
        val result = LipGuardrailEvaluator.evaluate(staplesGuardrailStrategyConfig)

        assertTrue(result.output.toString() == BigDecimalValue(BigDecimal("110")).toString())
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_PRICE" to "90",
            "CEIL_GUARDRAIL_PRICE" to "110",
            "GUARD_RAILED_TO_CEIL_PRICE" to "110",
            "FLOOR_GUARDRAIL_HIT" to "false",
            "CEIL_GUARDRAIL_HIT" to "true"
        ), result.metadata)
    }


    @Test
    fun `test staples lip guard rail test with previous output as ladder and price in range`() {
        val staplesGuardrailStrategyConfig = EvaluatorRequestContextUtils.createLipGuardRailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(1)),
            previousOutput = LadderValue(listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(100)
                )
            )),
            lipPriceInPaisa = BigDecimalValue(BigDecimal(100)),
            floorGuardRailBps = BigDecimalValue(BigDecimal(-1000)),
            ceilGuardrailBps = BigDecimalValue(BigDecimal(1000))
        )
        val result = LipGuardrailEvaluator.evaluate(staplesGuardrailStrategyConfig)

        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal("100")
                )
            )
        )

        assertTrue(result.output.toString() == expectedOutput.toString())
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_PRICE" to "90",
            "CEIL_GUARDRAIL_PRICE" to "110",
            "FLOOR_GUARDRAIL_HIT" to "false",
            "CEIL_GUARDRAIL_HIT" to "false"
        ), result.metadata)
    }

    @Test
    fun `test staples lip guard rail test with previous output as ladder and price guard-railed to ceil`() {
        val staplesGuardrailStrategyConfig = EvaluatorRequestContextUtils.createLipGuardRailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(1)),
            previousOutput = LadderValue(listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(111)
                )
            )),
            lipPriceInPaisa = BigDecimalValue(BigDecimal(100)),
            floorGuardRailBps = BigDecimalValue(BigDecimal(-1000)),
            ceilGuardrailBps = BigDecimalValue(BigDecimal(1000))
        )
        val result = LipGuardrailEvaluator.evaluate(staplesGuardrailStrategyConfig)

        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal("110.0000")
                )
            )
        )
        assertTrue(result.output.toString() == expectedOutput.toString())
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_PRICE" to "90",
            "CEIL_GUARDRAIL_PRICE" to "110",
            "0_LADDER_GUARD_RAILED_TO_CEIL_PRICE" to "110",
            "FLOOR_GUARDRAIL_HIT" to "false",
            "CEIL_GUARDRAIL_HIT" to "true"
        ), result.metadata)
    }

    @Test
    fun `test staples lip guard rail test with previous output as ladder and price guard-railed to floor`() {
        val staplesGuardrailStrategyConfig = EvaluatorRequestContextUtils.createLipGuardRailEvaluatorConfig(
            conversionRate = BigDecimalValue(BigDecimal(1)),
            previousOutput = LadderValue(listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(89)
                )
            )),
            lipPriceInPaisa = BigDecimalValue(BigDecimal(100)),
            floorGuardRailBps = BigDecimalValue(BigDecimal(-1000)),
            ceilGuardrailBps = BigDecimalValue(BigDecimal(1000))
        )
        val result = LipGuardrailEvaluator.evaluate(staplesGuardrailStrategyConfig)

        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal("90.0000")
                )
            )
        )
        assertTrue(result.output.toString() == expectedOutput.toString())
        assertEquals(mapOf(
            "FLOOR_GUARDRAIL_PRICE" to "90",
            "CEIL_GUARDRAIL_PRICE" to "110",
            "0_LADDER_GUARD_RAILED_TO_FLOOR_PRICE" to "90",
            "FLOOR_GUARDRAIL_HIT" to "true",
            "CEIL_GUARDRAIL_HIT" to "false"
        ), result.metadata)
    }

}
