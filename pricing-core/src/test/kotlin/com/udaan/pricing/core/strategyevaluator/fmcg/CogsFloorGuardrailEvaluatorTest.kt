package com.udaan.pricing.core.strategyevaluator.fmcg

import com.udaan.pricing.core.strategyevaluator.impl.EvaluatorFactory
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils.equalsTo
import com.udaan.pricing.core.strategyevaluator.utils.MapComparatorUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.assertTrue
import java.math.BigDecimal

class CogsFloorGuardrailEvaluatorTest {

    @Test
    fun `test mrp ceil when floor guardrail is hit`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createCogsFloorGuardrailEvaluatorConfig(
            previousOutput = LadderValue(
                value = listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 2,
                        ladderValue = BigDecimal(1400)
                    ),
                    Ladder(
                        minQuantity = 3,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(1200)
                    )
                )
            ),
            cogs = BigDecimalValue(BigDecimal(1300))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = 2,
                    ladderValue = BigDecimal("1400")
                ),
                Ladder(
                    minQuantity = 3,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal("1300")
                )
            )
        )
        assertTrue(expectedOutput.value.equalsTo((result!!.output as LadderValue).value),
            "${result.output} is not matched with $expectedOutput")

        MapComparatorUtils.compareMaps(
            result.metadata,
            mapOf(
                "COGS_FLOOR_GUARDRAIL_INPUT" to "[(1,2,1400), (3,2147483647,1200)]",
                "FMCG_COGS_WOT_PAISA_SET" to "1300",
                "FLOOR_GUARDRAIL_HIT" to "true",
                "PRICE_WITHOUT_FLOOR_GUARDRAIL" to "[(1,2,1400), (3,2147483647,1200)]",
                "COGS_FLOOR_GUARDRAIL_OUTPUT" to "[(1,2,1400), (3,2147483647,1300)]"
            )
        )

    }


    @Test
    fun `test mrp ceil when guardrail is not hit`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createCogsFloorGuardrailEvaluatorConfig(
            previousOutput = LadderValue(
                value = listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 2,
                        ladderValue = BigDecimal(1400)
                    ),
                    Ladder(
                        minQuantity = 3,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(1200)
                    )
                )
            ),
            cogs = BigDecimalValue(BigDecimal(1200))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = 2,
                    ladderValue = BigDecimal("1400")
                ),
                Ladder(
                    minQuantity = 3,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal("1200")
                )
            )
        )
        assertTrue(expectedOutput.value.equalsTo((result!!.output as LadderValue).value),
            "${result.output} is not matched with $expectedOutput")

        MapComparatorUtils.compareMaps(
            result.metadata,
            mapOf(
                "COGS_FLOOR_GUARDRAIL_INPUT" to "[(1,2,1400), (3,2147483647,1200)]",
                "FMCG_COGS_WOT_PAISA_SET" to "1200",
                "FLOOR_GUARDRAIL_HIT" to "false",
                "PRICE_WITHOUT_FLOOR_GUARDRAIL" to "[(1,2,1400), (3,2147483647,1200)]",
                "COGS_FLOOR_GUARDRAIL_OUTPUT" to "[(1,2,1400), (3,2147483647,1200)]"
            )
        )
    }

    @Test
    fun `test mrp ceil when cogs is absent`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createCogsFloorGuardrailEvaluatorConfig(
            previousOutput = LadderValue(
                value = listOf(
                    Ladder(
                        minQuantity = 1,
                        maxQuantity = 2,
                        ladderValue = BigDecimal(1400)
                    ),
                    Ladder(
                        minQuantity = 3,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = BigDecimal(1200)
                    )
                )
            ),
            cogs = null
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))

        val expectedOutput = LadderValue(
            listOf(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = 2,
                    ladderValue = BigDecimal("1400")
                ),
                Ladder(
                    minQuantity = 3,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal("1200")
                )
            )
        )
        assertTrue(expectedOutput.value.equalsTo((result!!.output as LadderValue).value),
            "${result.output} is not matched with $expectedOutput")

        MapComparatorUtils.compareMaps(
            result.metadata,
            mapOf(
                "COGS_FLOOR_GUARDRAIL_INPUT" to "[(1,2,1400), (3,2147483647,1200)]",
                "FMCG_COGS_WOT_PAISA_SET" to "null",
                "FLOOR_GUARDRAIL_HIT" to "false",
                "PRICE_WITHOUT_FLOOR_GUARDRAIL" to "[(1,2,1400), (3,2147483647,1200)]",
                "COGS_FLOOR_GUARDRAIL_OUTPUT" to "[(1,2,1400), (3,2147483647,1200)]"
            )
        )
    }
}
