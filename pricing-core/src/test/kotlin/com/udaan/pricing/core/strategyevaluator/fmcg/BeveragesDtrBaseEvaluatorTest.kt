package com.udaan.pricing.core.strategyevaluator.fmcg

import com.udaan.pricing.core.strategyevaluator.impl.EvaluatorFactory
import com.udaan.pricing.core.strategyevaluator.utils.EvaluatorRequestContextUtils
import com.udaan.pricing.core.strategyevaluator.utils.MapComparatorUtils
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.variable.VariableId
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal

class BeveragesDtrBaseEvaluatorTest {

    @Test
    fun `test bevs dtr strategy if all inputs are present`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createBeveragesDtrBaseEvaluatorConfig(
            bevsMop = BigDecimalValue(BigDecimal(10000)),
            bevsMarginBps = BigDecimalValue(BigDecimal(1000))
        )
        val result = EvaluatorFactory.evaluate(listOf(evaluatorConfig))
        val expectedOutput = BigDecimalValue(BigDecimal("11000"))

        val expectedMetadata = mapOf(
            evaluatorConfig.strategy.name + "_INPUT" to "",
            VariableId.BEVS_MOP_WT_PAISA_UNIT.name to "10000",
            VariableId.BEVS_MARGIN_BPS.name to "1000",
            evaluatorConfig.strategy.name + "_OUTPUT" to "11000"
        )

        assertEquals(
            expectedOutput.value.toString(),
            (result!!.output as BigDecimalValue).toString(),
            "${result.output} is not matched with $expectedOutput")

        MapComparatorUtils.compareMaps(
            result.metadata,
            expectedMetadata
        )
    }


    @Test
    fun `test bevs dtr strategy if one of the input is missing`() {
        val evaluatorConfig = EvaluatorRequestContextUtils.createBeveragesDtrBaseEvaluatorConfig(
            bevsMarginBps = BigDecimalValue(BigDecimal(1000))
        )

        assertThrows<IllegalArgumentException> {
            EvaluatorFactory.evaluate(listOf(evaluatorConfig))
        }
    }
}
