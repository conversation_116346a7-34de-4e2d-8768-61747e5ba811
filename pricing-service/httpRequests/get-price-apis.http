### POST.getPrice
POST {{host}}/v1/price/TLFCHNK4TX4R5C2843FJZBXREQ052F1/saleunit/SULS91RSBN8XCSXGB54D2HFQXDVL?
    fetchInactive=true&
    fetchRiderDetails=true
Content-Type: application/json

< ./requestBody/pacman-c-buyer-context.json

### POST.getPriceV2
POST {{host}}/v1/price/TLFCH7TSDY1EN50CPCFRT4SC4LJ9BLG/saleunit/SUL16TEJV49X8G9ZCTQN4WMEHBX3/v2?
    fetchInactive=true&
    fetchRiderDetails=true&
    fetchRiderAdditionalDetails=true&
    packagingUnit=&
    orderReadyForCheckout=false
Content-Type: application/json

< ./requestBody/horeca-d-buyer-context.json

### POST.getMultiPrice
POST {{host}}/v1/price/multi?
    fetchInactive=true&
    fetchRiderDetails=true
Content-Type: application/json

< ./requestBody/multi-get-price-body.json

### POST.getMultiPriceV2
POST {{host}}/v1/price/multi/v2?
    fetchInactive=true&
    fetchRiderDetails=true
Content-Type: application/json

< ./requestBody/multi-v2-get-price-body.json

### GET.getPriceForListing
GET {{host}}/v1/price/TLFCH0LWLV2QTYTD0JF22L5FN4BW7LT?
    fetchInactive=true&
    fetchRiderDetails=true


### POST.getPriceForListing
POST {{host}}/v1/price/TLFCH0LWLV2QTYTD0JF22L5FN4BW7LT?
    fetchInactive=true&
    fetchRiderDetails=true&
    fetchRiderAdditionalDetails=true
Content-Type: application/json

< ./requestBody/pacman-c-buyer-context.json

###
