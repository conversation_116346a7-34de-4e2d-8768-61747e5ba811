prometheus: udaan-support
alert-spec:
  groups:
    - name: ./pricing-service.rules
      rules:
        - alert: APILatencyDegradation
          expr: histogram_quantile(0.99,sum(rate(http_request_duration_seconds_bucket{service="pricing-service", namespace="prod", cluster="udaan-sin0"}[5m])) by (le)) > 0.5
          for: 5m
          labels:
            severity: error
            trigger_route: alerting-service
            service_slug: pricing-alerts
          annotations:
            summary: "pricing-service p99 latency ({{$value}})"
            description: "pricing-service p99 latency {{$value}} is above threshold (> 0.5s)"

        - record: prod_pricing_service:http_request_duration_seconds_count:sum_rate5m
          expr: sum (rate(http_request_duration_seconds_count{namespace="prod", service="pricing-service"}[5m]))

        - record: prod_pricing_service:error_http_request_duration_seconds_count:sum_rate5m
          expr: sum(rate(http_request_duration_seconds_count{namespace="prod", service="pricing-service", code=~"5.."}[5m]))

        - record: prod_pricing_service:http_request_duration_seconds_count:sum1m
          expr: sum(increase(http_request_duration_seconds_count{namespace="prod", service="pricing-service"}[1m]))

        - record: prod_pricing_service:error_http_request_duration_seconds_count:sum1m
          expr: sum(increase(http_request_duration_seconds_count{namespace="prod", service="pricing-service", code=~"5.."}[1m]))

        - record: prod_pricing_service:http_request_duration_seconds_count:sum5m
          expr: sum(increase(http_request_duration_seconds_count{namespace="prod", service="pricing-service"}[5m]))

        - record: prod_pricing_service:error_http_request_duration_seconds_count:sum5m
          expr: sum(increase(http_request_duration_seconds_count{namespace="prod", service="pricing-service", code=~"5.."}[5m]))

        - record: prod_pricing_service:http_request_duration_seconds_bucket_by_le_50:histogram5m
          expr: histogram_quantile(0.50, sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="pricing-service"}[5m])) by (le))

        - record: prod_pricing_service:http_request_duration_seconds_bucket_by_le_95:histogram5m
          expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="pricing-service"}[5m])) by (le))

        - record: prod_pricing_service:http_request_duration_seconds_bucket_by_le_99:histogram5m
          expr: histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="pricing-service"}[5m])) by (le))

        - record: prod_pricing_service:http_request_duration_seconds_count:sum_rate15m
          expr: sum(rate(http_request_duration_seconds_count{namespace="prod", service="pricing-service"}[15m]))

        - record: prod_pricing_service:http_request_duration_seconds_bucket:le0dot01_sum_rate15m
          expr: (sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="pricing-service", le="0.01"}[15m])))

        - record: prod_pricing_service:http_request_duration_seconds_bucket:le0dot05_sum_rate15m
          expr: (sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="pricing-service", le="0.05"}[15m])))

        - record: prod_pricing_service:http_request_duration_seconds_bucket:le0dot1_sum_rate15m
          expr: (sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="pricing-service", le="0.1"}[15m])))

        - record: prod_pricing_service:http_request_duration_seconds_bucket:le0dot5_sum_rate15m
          expr: (sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="pricing-service", le="0.5"}[15m])))

        - record: prod_pricing_service:http_request_duration_seconds_bucket:le1_sum_rate15m
          expr: (sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="pricing-service", le="1.0"}[15m])))

        - record: prod_pricing_service:http_request_duration_seconds_bucket:le2_sum_rate15m
          expr: (sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="pricing-service", le="2.0"}[15m])))

        - record: prod_pricing_service:http_request_duration_seconds_bucket:le5_sum_rate15m
          expr: (sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="pricing-service", le="5.0"}[15m])))

        - record: prod_pricing_service:http_request_duration_seconds_bucket:leInf_sum_rate15m
          expr: (sum(rate(http_request_duration_seconds_bucket{namespace="prod", service="pricing-service", le="+Inf"}[15m])))

        - record: prod_pricing_service:http_request_duration_seconds_sum:sum_rate5m
          expr: sum(rate(http_request_duration_seconds_sum{namespace="prod", service="pricing-service"}[5m]))

        - record: prod_pricing_service:http_request_duration_seconds_sum:sum_rate15m
          expr: sum(rate(http_request_duration_seconds_sum{namespace="prod", service="pricing-service"}[15m]))
