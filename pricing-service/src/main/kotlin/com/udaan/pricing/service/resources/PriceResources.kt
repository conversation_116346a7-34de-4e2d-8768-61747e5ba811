package com.udaan.pricing.service.resources

import com.google.inject.Inject
import com.udaan.common.server.HTTP_HEADER_UD_CLIENTIP
import com.udaan.common.server.HTTP_HEADER_UD_PRINCIPAL
import com.udaan.common.server.HTTP_HEADER_UD_USERAGENT
import com.udaan.common.utils.kotlin.logger
import com.udaan.error.trace.annotations.Severity
import com.udaan.error.trace.annotations.UDErrorMonitored
import com.udaan.error.trace.annotations.UDErrorMonitoredApi
import com.udaan.pricing.*
import com.udaan.pricing.core.controller.BasePriceController
import com.udaan.pricing.core.controller.PriceController
import com.udaan.pricing.core.helpers.BuyerTagHelper
import com.udaan.pricing.core.helpers.rider.RootRequestContext
import com.udaan.resources.with
import javax.ws.rs.*
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.MediaType

@Path("v1/price")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@UDErrorMonitored("1")
class PriceResources @Inject constructor(
    private val priceController: PriceController,
    private val basePriceController: BasePriceController,
    private val buyerTagHelper: BuyerTagHelper
) {
    companion object {
        private val log by logger()
    }

    @Path("/{listing-id}/saleunit/{sale-unit-id}")
    @POST
    @UDErrorMonitoredApi("1", Severity.HIGH,false)
    fun getPrice(
            @PathParam("listing-id") listingId: String,
            @PathParam("sale-unit-id") saleUnitId: String,
            @DefaultValue("false") @QueryParam("fetchInactive") fetchInactive: Boolean?,
            @DefaultValue("false") @QueryParam("fetchRiderDetails") fetchRiderDetails: Boolean?,
            transactionContext: ContextualPriceRequest,
            @HeaderParam(HTTP_HEADER_UD_PRINCIPAL) rootPrincipal: String?,
            @HeaderParam(HTTP_HEADER_UD_USERAGENT) rootUserAgent: String?,
            @HeaderParam(HTTP_HEADER_UD_CLIENTIP) rootClientIp: String?,
            @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        val rootRequestContext = RootRequestContext(
            rootPrincipal = rootPrincipal,
            rootUserAgent = rootUserAgent,
            rootClientIp = rootClientIp
        )

        priceController.getPriceListing(
            listingId = listingId,
            saleUnitId = saleUnitId,
            contextualPriceRequest = transactionContext,
            rootRequestContext = rootRequestContext,
            fetchInactive = fetchInactive ?: false,
            fetchRiderDetails = fetchRiderDetails ?: false
        ).firstOrNull()
    }

    @Path("/{listing-id}/saleunit/{sale-unit-id}/v2")
    @POST
    @UDErrorMonitoredApi("15", Severity.HIGH,false)
    fun getPriceV2(
        @PathParam("listing-id") listingId: String,
        @PathParam("sale-unit-id") saleUnitId: String,
        @DefaultValue("false") @QueryParam("fetchInactive") fetchInactive: Boolean?,
        @DefaultValue("false") @QueryParam("fetchRiderDetails") fetchRiderDetails: Boolean?,
        @DefaultValue("false") @QueryParam("fetchRiderAdditionalDetails") fetchRiderAdditionalDetails: Boolean?,
        @DefaultValue("") @QueryParam("packagingUnit") packagingType: String?,
        @DefaultValue("false") @QueryParam("orderReadyForCheckout") orderReadyForCheckout: Boolean,
        transactionContext: ContextualPriceRequest,
        @HeaderParam(HTTP_HEADER_UD_PRINCIPAL) rootPrincipal: String?,
        @HeaderParam(HTTP_HEADER_UD_USERAGENT) rootUserAgent: String?,
        @HeaderParam(HTTP_HEADER_UD_CLIENTIP) rootClientIp: String?,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        val rootRequestContext = RootRequestContext(
            rootPrincipal = rootPrincipal,
            rootUserAgent = rootUserAgent,
            rootClientIp = rootClientIp
        )

        priceController.getPriceListingV2(
            listingId,
            saleUnitId,
            transactionContext,
            rootRequestContext,
            fetchInactive?:false,
            fetchRiderDetails?:false,
            fetchRiderAdditionalDetails?:false,
            packagingType,
            orderReadyForCheckout = orderReadyForCheckout
        ).firstOrNull()
    }

    @POST
    @Path("/multi")
    @UDErrorMonitoredApi("2", Severity.HIGH,false)
    fun getMultiPrice(
            @DefaultValue("false") @QueryParam("fetchInactive") fetchInactive: Boolean?,
            @DefaultValue("false") @QueryParam("fetchRiderDetails") fetchRiderDetails: Boolean?,
            @HeaderParam(HTTP_HEADER_UD_PRINCIPAL) rootPrincipal: String?,
            @HeaderParam(HTTP_HEADER_UD_USERAGENT) rootUserAgent: String?,
            @HeaderParam(HTTP_HEADER_UD_CLIENTIP) rootClientIp: String?,
            priceMultiReq: PriceMultiReq,
            @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        val rootRequestContext = RootRequestContext(
            rootPrincipal = rootPrincipal,
            rootUserAgent = rootUserAgent,
            rootClientIp = rootClientIp
        )

        priceController.getPriceMulti(
            priceMultiReq = priceMultiReq,
            fetchInactive = fetchInactive ?: false,
            rootRequestContext = rootRequestContext,
            fetchRiderDetails = fetchRiderDetails ?: false
        )
    }

    @POST
    @Path("/multi/v2")
    @UDErrorMonitoredApi("2", Severity.HIGH,false)
    fun getMultiPriceV2(
        @DefaultValue("false") @QueryParam("fetchInactive") fetchInactive: Boolean?,
        @DefaultValue("false") @QueryParam("fetchRiderDetails") fetchRiderDetails: Boolean?,
        @DefaultValue("false") @QueryParam("orderReadyForCheckout") orderReadyForCheckout: Boolean,
        @HeaderParam(HTTP_HEADER_UD_PRINCIPAL) rootPrincipal: String?,
        @HeaderParam(HTTP_HEADER_UD_USERAGENT) rootUserAgent: String?,
        @HeaderParam(HTTP_HEADER_UD_CLIENTIP) rootClientIp: String?,
        priceMultiReq: PriceMultiReq,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        val rootRequestContext = RootRequestContext(
            rootPrincipal = rootPrincipal,
            rootUserAgent = rootUserAgent,
            rootClientIp = rootClientIp
        )

        priceController.getPriceMultiV2(
            priceMultiReq = priceMultiReq,
            fetchInactive = fetchInactive ?: false,
            fetchRiderDetails = fetchRiderDetails ?: false,
            rootRequestContext = rootRequestContext,
            orderReadyForCheckout = orderReadyForCheckout
        )
    }

    @Path("/{listing-id}")
    @GET
    @UDErrorMonitoredApi("3", Severity.HIGH,false)
    fun getPriceForListing(
            @PathParam("listing-id") listingId: String,
            @DefaultValue("false") @QueryParam("fetchInactive") fetchInactive: Boolean?,
            @DefaultValue("false") @QueryParam("fetchRiderDetails") fetchRiderDetails: Boolean?,
            @HeaderParam(HTTP_HEADER_UD_PRINCIPAL) rootPrincipal: String?,
            @HeaderParam(HTTP_HEADER_UD_USERAGENT) rootUserAgent: String?,
            @HeaderParam(HTTP_HEADER_UD_CLIENTIP) rootClientIp: String?,
            @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        val rootRequestContext = RootRequestContext(
            rootPrincipal = rootPrincipal,
            rootUserAgent = rootUserAgent,
            rootClientIp = rootClientIp
        )

        priceController.getPriceListing(
            listingId = listingId,
            saleUnitId = null,
            contextualPriceRequest = null,
            rootRequestContext = rootRequestContext,
            fetchInactive = fetchInactive ?: false,
            fetchRiderDetails = fetchRiderDetails ?: false
        )
    }

    @Path("/{listing-id}")
    @POST
    @UDErrorMonitoredApi("4", Severity.HIGH,false)
    fun getPriceForListing(
            @PathParam("listing-id") listingId: String,
            contextualPriceRequest: ContextualPriceRequest,
            @DefaultValue("false") @QueryParam("fetchInactive") fetchInactive: Boolean?,
            @DefaultValue("false") @QueryParam("fetchRiderDetails") fetchRiderDetails: Boolean?,
            @DefaultValue("false") @QueryParam("fetchRiderAdditionalDetails") fetchRiderAdditionalDetails: Boolean?,
            @HeaderParam(HTTP_HEADER_UD_PRINCIPAL) rootPrincipal: String?,
            @HeaderParam(HTTP_HEADER_UD_USERAGENT) rootUserAgent: String?,
            @HeaderParam(HTTP_HEADER_UD_CLIENTIP) rootClientIp: String?,
            @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        val rootRequestContext = RootRequestContext(
            rootPrincipal = rootPrincipal,
            rootUserAgent = rootUserAgent,
            rootClientIp = rootClientIp
        )

        priceController.getPriceListing(
            listingId = listingId,
            saleUnitId = null,
            contextualPriceRequest = contextualPriceRequest,
            rootRequestContext = rootRequestContext,
            fetchInactive = fetchInactive ?: false,
            fetchRiderDetails = fetchRiderDetails ?: false,
            fetchRiderAdditionalDetails = fetchRiderAdditionalDetails ?: false
        )
    }

    @Path("/")
    @POST
    @UDErrorMonitoredApi("5", Severity.HIGH,false)
    fun createPrice(
            priceRequest: PriceRequest,
            @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        log.info("Request createPrice with ${priceRequest.listingId} ${priceRequest.saleUnitId} price: ${priceRequest.price}")
        basePriceController.createOrUpdatePrice(priceRequest = priceRequest)
    }

    @POST
    @Path("/get-lot")
    @UDErrorMonitoredApi("8", Severity.LOW, false)
    fun getLotPricingBulk(
        getLotPriceMulti: List<GetLotPriceRequest>,
        @DefaultValue("false") @QueryParam("orderReadyForCheckout") orderReadyForCheckout: Boolean,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        priceController.getLotPricingBulk(getLotPriceMulti, orderReadyForCheckout)
    }

    @GET
    @Path("/base-price/listing/{listingId}/sales-unit/{salesUnitId}")
    @UDErrorMonitoredApi("9", Severity.LOW, false)
    fun getBasePriceForListingSalesUnit(
        @PathParam("listingId") listingId: String,
        @PathParam("salesUnitId") salesUnitId: String,
        @DefaultValue("false") @QueryParam("fetchInactive") fetchInactive: Boolean,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        basePriceController.getBasePriceForListingSalesUnitWithFilter(listingId, salesUnitId, fetchInactive)
    }

    @GET
    @Path("/listing-index-price/{listingId}")
    @UDErrorMonitoredApi("14", Severity.LOW, false)
    fun getIndexingPriceForListingId(
        @PathParam("listingId") listingId: String,
        @QueryParam("fetchInactive") fetchInactive: Boolean,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        priceController.getIndexingPriceForListingId(listingId, fetchInactive)
    }


    /**
     * Debug apis
     */
    @Path("/id/{id}")
    @GET
    @UDErrorMonitoredApi("10", Severity.HIGH, false)
    fun getPriceOnId(
            @PathParam("id") id: String,
            @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        basePriceController.getPriceOnId(id)
    }

    @Path("/meta-null")
    @GET
    @UDErrorMonitoredApi("12", Severity.LOW, false)
    fun getMetaNull(
            @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        priceController.metaNull()
    }

    @POST
    @Path("/get-lot-with-scheme")
    @UDErrorMonitoredApi("13", Severity.LOW, false)
    fun getLotPricesWithDesiredScheme(
        getLotPriceWithSchemeRequest: List<GetLotPriceWithSchemeRequest>,
        @DefaultValue("false") @QueryParam("orderReadyForCheckout") orderReadyForCheckout: Boolean,
        @Suspended asyncResponse: AsyncResponse
    )= asyncResponse.with {
        priceController.getLotWithDesiredScheme(getLotPriceWithSchemeRequest, orderReadyForCheckout)
    }

    @DELETE
    @Path("/delete-all-price/{listingId}/sales-unit/{salesUnitId}")
    @UDErrorMonitoredApi("14", Severity.LOW, false)
    fun deleteAllPricesForListingSalesUnitId(
        @PathParam("listingId") listingId: String,
        @PathParam("salesUnitId") salesUnitId: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        priceController.deleteAllPricesForListingSalesUnitId(
            listingId = listingId,
            salesUnitId = salesUnitId
        )
    }

    @Path("/buyer-cohort-and-location/{buyerNumber}/{listingId}/{salesUnitId}")
    @GET
    @UDErrorMonitoredApi("17", Severity.LOW, false)
    fun getBuyerCohortAndLocationForBuyerAndListingId(
        @PathParam("buyerNumber") buyerNumber: String,
        @PathParam("listingId") listingId: String,
        @PathParam("salesUnitId") salesUnitId: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        buyerTagHelper.getBuyerCohortAndLocationForBuyerAndListingId(buyerNumber, listingId, salesUnitId)
    }

}
