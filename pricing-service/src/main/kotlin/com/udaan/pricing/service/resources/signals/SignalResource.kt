package com.udaan.pricing.service.resources.signals

import com.google.inject.Inject
import com.udaan.pricing.core.managers.signals.SignalReadManager
import com.udaan.pricing.core.managers.signals.SignalWriteManager
import com.udaan.pricing.signaldeletion.DeleteSignalRequest
import com.udaan.pricing.signalcreation.RawSignalInput
import com.udaan.resources.with
import javax.ws.rs.Consumes
import javax.ws.rs.GET
import javax.ws.rs.POST
import javax.ws.rs.Path
import javax.ws.rs.PathParam
import javax.ws.rs.Produces
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.MediaType

@Path("/signal")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
class SignalResource @Inject constructor(
    private val signalWriteManager: SignalWriteManager,
    private val signalReadManager: SignalReadManager
) {
    @POST
    fun createSignalFromRawInput(
        rawSignalInput: RawSignalInput,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        signalWriteManager.createSignalFromRawInput(rawSignalInput)
    }

    @POST
    @Path("/delete")
    fun deleteSignal(
        deleteSignalRequest: DeleteSignalRequest,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        signalWriteManager.parseDeleteSignalRequestAndDelete(deleteSignalRequest)
    }

    @GET
    @Path("/{referenceId}")
    fun getSignalByReferenceId(
        @PathParam("referenceId") referenceId: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        signalReadManager.getSignalByReferenceId(referenceId)
    }
}
