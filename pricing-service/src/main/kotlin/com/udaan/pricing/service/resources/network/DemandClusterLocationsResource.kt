package com.udaan.pricing.service.resources.network

import com.google.inject.Inject
import com.udaan.error.trace.annotations.Severity
import com.udaan.error.trace.annotations.UDErrorMonitored
import com.udaan.error.trace.annotations.UDErrorMonitoredApi
import com.udaan.pricing.core.controller.network.DemandClusterLocationController
import com.udaan.pricing.network.DemandClusterLocationReq
import com.udaan.resources.with
import javax.ws.rs.*
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.MediaType

@UDErrorMonitored("2")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/demand-cluster-locations")
class DemandClusterLocationsResource @Inject constructor(
    private val demandClusterLocationController: DemandClusterLocationController
) {
    @POST
    @Path("/create-location")
    @UDErrorMonitoredApi("1", Severity.LOW, false)
    fun createDemandCluster(
        demandClusterLocationReq: DemandClusterLocationReq,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        demandClusterLocationController.createDemandClusterLocation(demandClusterLocationReq)
    }

    @GET
    @Path("/{locationName}/city")
    @UDErrorMonitoredApi("2", Severity.LOW, false)
    fun getCityForLocation(
        @PathParam("locationName") locationName: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        demandClusterLocationController.getCityForLocation(locationName)
    }

    @GET
    @Path("/{locationName}/warehouses")
    @UDErrorMonitoredApi("3", Severity.LOW, false)
    fun getWarehousesForLocation(
        @PathParam("locationName") locationName: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        demandClusterLocationController.getWarehousesForLocation(locationName)
    }

    @GET
    @Path("/city/{city}/locations")
    @UDErrorMonitoredApi("4", Severity.LOW, false)
    fun getLocationsForCity(
        @PathParam("city") city: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        demandClusterLocationController.getLocationsForCity(city)
    }

    @GET
    @Path("/warehouse/{warehouseId}/locations")
    @UDErrorMonitoredApi("5", Severity.LOW, false)
    fun getLocationsForWarehouse(
        @PathParam("warehouseId") warehouseId: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        demandClusterLocationController.getLocationsForWarehouse(warehouseId)
    }

    @GET
    @Path("/{locationName}")
    @UDErrorMonitoredApi("6", Severity.LOW, false)
    fun getLocationDetails(
        @PathParam("locationName") locationName: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        demandClusterLocationController.getLocationDetails(locationName)
    }

    @GET
    @Path("/all-location-names")
    @UDErrorMonitoredApi("7", Severity.LOW, false)
    fun getAllLocationNames(
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        demandClusterLocationController.getAllLocationNames()
    }
}
