package com.udaan.pricing.service.resources

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.error.trace.annotations.Severity
import com.udaan.error.trace.annotations.UDErrorMonitored
import com.udaan.error.trace.annotations.UDErrorMonitoredApi
import com.udaan.pricing.*
import com.udaan.pricing.core.controller.GeoPricingController
import com.udaan.pricing.core.dao.regionsMasterList
import com.udaan.resources.with
import javax.ws.rs.*
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.Context
import javax.ws.rs.core.MediaType
import javax.ws.rs.core.SecurityContext


@Path("v1/geo-pricing")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Singleton
@UDErrorMonitored("3")
class GeoPricingResource @Inject constructor(
    private val geoPricingController: GeoPricingController
) {
    companion object {
        val log by logger()
    }

    @Path("/create")
    @POST
    @UDErrorMonitoredApi("1", Severity.LOW,false)
    fun createGeoPricing(
            geoPricingReq: GeoPricingCreateReq,
            @Context context: SecurityContext?,
            @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        geoPricingController.createGeoPricing(geoPricingReq)
    }

    @Path("/update")
    @POST
    @UDErrorMonitoredApi("2", Severity.LOW,false)
    fun updateGeoPricing(
        geoPricingReq: GeoPricingUpdateReq,
        @Context context: SecurityContext?,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        geoPricingController.updateGeoPricing(geoPricingReq)
    }

    @Path("/upsert")
    @POST
    @UDErrorMonitoredApi("3", Severity.LOW,false)
    fun upsertGeoPricing(
        geoPricingReq: GeoPricingUpsertReq,
        @Context context: SecurityContext?,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        geoPricingController.upsertGeoPricing(geoPricingReq)
    }

    @GET
    @Path("/negative")
    @UDErrorMonitoredApi("5", Severity.LOW,false)
    fun getNegativeGeo(
        @QueryParam("listingId") listingId: String,
        @QueryParam("salesUnitId") salesUnitId: String,
        @QueryParam("orgId") orgId: String,
        @Suspended asyncResponse: AsyncResponse,
        @Context context: SecurityContext?
    ) = asyncResponse.with {
        geoPricingController.getNegativeGeoForListingAndSu(listingId,salesUnitId,orgId)
    }

    @Path("/listingAndSu")
    @GET
    @UDErrorMonitoredApi("6", Severity.LOW,false)
    fun getGeoByListingAndSU(
        @QueryParam("listingId") listingId: String,
        @QueryParam("salesUnitId") salesUnitId: String,
        @QueryParam("orgId") orgId: String,
        @Suspended asyncResponse: AsyncResponse,
        @Context context: SecurityContext?
    ) = asyncResponse.with {
        geoPricingController.getGeoPricingByListingAndSU(listingId,salesUnitId,orgId)
    }

    @Path("/seller/{sellerOrgId}/query")
    @GET
    @UDErrorMonitoredApi("7", Severity.LOW,false)
    fun queryGeoPricingForSeller(@Context context: SecurityContext?,
                                 @Suspended asyncResponse: AsyncResponse,
                                 @PathParam("sellerOrgId") sellerOrgId: String,
                                 @QueryParam("listingId") listingId: String?,
                                 @DefaultValue("true") @QueryParam("cascadeDown") cascadeDown: Boolean,
                                 @DefaultValue("false") @QueryParam("cascadeUp") cascadeUp: Boolean
    ) = asyncResponse.with {
        val res = geoPricingController.queryGeoPricing(
            QueryGeoPricingReq(
                orgId = sellerOrgId,
                listingId = listingId,
                cascadeDown = cascadeDown,
                cascadeUp = cascadeUp
            )
        )

        GeoPricingResponse(geoPricingList = res)
    }

    @Path("/seller/{sellerOrgId}")
    @GET
    @UDErrorMonitoredApi("8", Severity.LOW,false)
    fun getGeoPricingForSellerByIds(@Context context: SecurityContext?,
                                    @Suspended asyncResponse: AsyncResponse,
                                    @PathParam("sellerOrgId") sellerOrgId: String,
                                    @QueryParam("geoPriceIds") geoPriceIds: String
    ) = asyncResponse.with {
        val res = geoPricingController.getGeoPricingByIds(sellerOrgId, geoPriceIds)

        GeoPricingResponse(geoPricingList = res)
    }

    @Path("/all-regions")
    @GET
    @UDErrorMonitoredApi("9", Severity.LOW,false)
    fun getAllRegions(@Context context: SecurityContext?,
                      @Suspended asyncResponse: AsyncResponse) = asyncResponse.with {
        RegionResponse(
            regionList = regionsMasterList
        )
    }


}
