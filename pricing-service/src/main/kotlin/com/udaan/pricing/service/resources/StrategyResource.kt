package com.udaan.pricing.service.resources

import com.google.inject.Inject
import com.udaan.pricing.core.managers.StrategyManager
import com.udaan.pricing.strategy.CreateStrategyRequest
import com.udaan.pricing.strategy.GetStrategiesByStatesRequest
import com.udaan.pricing.strategy.StrategyState
import com.udaan.pricing.strategy.UpdateStrategyRequest
import com.udaan.resources.with
import javax.ws.rs.Consumes
import javax.ws.rs.GET
import javax.ws.rs.POST
import javax.ws.rs.PUT
import javax.ws.rs.Path
import javax.ws.rs.PathParam
import javax.ws.rs.Produces
import javax.ws.rs.QueryParam
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.MediaType

@Path("/strategies")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
class StrategyResource @Inject constructor(
    private val strategyManager: StrategyManager
) {

    @POST
    fun createStrategy(
        createStrategyRequest: CreateStrategyRequest,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        strategyManager.createStrategy(createStrategyRequest)
    }

    @PUT
    @Path("/{strategyId}")
    fun updateStrategy(
        updateStrategyRequest: UpdateStrategyRequest,
        @PathParam("strategyId") strategyId: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        strategyManager.updateStrategy(updateStrategyRequest)
    }

    @POST
    @Path("/for-states")
    fun getStrategiesForStates(
        strategiesByStatesRequest: GetStrategiesByStatesRequest,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        strategyManager.getStrategiesForStates(strategiesByStatesRequest)
    }

    @GET
    @Path("/{strategyId}")
    fun getById(
        @PathParam("strategyId") id:String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        strategyManager.getStrategyById(id)
    }

    @GET
    fun getMultipleStrategiesByIds(
        @QueryParam("ids") ids: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        strategyManager.getMultipleStrategiesByIds(ids)
    }

    @POST
    @Path("/{strategyId}/active")
    fun activateStrategy(
        @PathParam("strategyId") strategyId: String,
        @QueryParam("updatedBy") updatedBy: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        strategyManager.updateStateForStrategyId(strategyId, StrategyState.ACTIVE, updatedBy)
    }
}
