package com.udaan.pricing.service.resources

import com.google.inject.Inject
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.controller.automation.TradingRepriceController
import com.udaan.resources.with
import javax.ws.rs.Consumes
import javax.ws.rs.PUT
import javax.ws.rs.Path
import javax.ws.rs.PathParam
import javax.ws.rs.Produces
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.MediaType

@Path("v1/trading-price")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
class TradingPortfolioRepriceResource @Inject constructor(
    private val tradingRepriceController: TradingRepriceController
) {

    @PUT
    @Path("/catalogEntity/{catalogEntity}/locationType/{locationType}/locationValue/{locationValue}")
    fun repriceTradingPortfolioItem(
        @PathParam("locationValue") locationValue: String,
        @PathParam("locationType") locationType: String,
        @PathParam("catalogEntity") catalogEntity: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        tradingRepriceController.repriceItems(
            locationValue = locationValue,
            locationType = LocationType.valueOf(locationType),
            catalogEntity = catalogEntity,
            referenceId = ""
        )
    }
}
