package com.udaan.pricing.service.resources

import com.google.inject.Inject
import com.udaan.error.trace.annotations.Severity
import com.udaan.error.trace.annotations.UDErrorMonitored
import com.udaan.error.trace.annotations.UDErrorMonitoredApi
import com.udaan.pricing.UserCohortCreateReq
import com.udaan.pricing.UserCohortType
import com.udaan.pricing.core.controller.UserCohortController
import com.udaan.resources.with
import javax.ws.rs.Consumes
import javax.ws.rs.DELETE
import javax.ws.rs.GET
import javax.ws.rs.POST
import javax.ws.rs.Path
import javax.ws.rs.Produces
import javax.ws.rs.QueryParam
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.MediaType

@Path("v1/user-cohort")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@UDErrorMonitored("5")
class UserCohortResource @Inject constructor(
    private val userCohortController: UserCohortController
) {

    @Path("/")
    @POST
    @UDErrorMonitoredApi("1", Severity.LOW,false)
    fun createUserCohort(
            userCohortCreateReq: UserCohortCreateReq,
            @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        userCohortController.createCohort(userCohortCreateReq)
    }

    @Path("/")
    @DELETE
    @UDErrorMonitoredApi("2", Severity.LOW,false)
    fun deleteUserCohort(
            @QueryParam("sellerOrgId") sellerOrgId: String?,
            @QueryParam("userCohortType") userCohortType: UserCohortType,
            @QueryParam("orgId") orgId: String,
            @QueryParam("cohortName") cohortName: String,
            @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        userCohortController.deleteCohort(orgId, cohortName,sellerOrgId,userCohortType)
    }

    @Path("/getAll")
    @GET
    @UDErrorMonitoredApi("3", Severity.LOW,false)
    fun getAllCohort(
            @QueryParam("sellerOrgId") sellerOrgId: String,
            @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        userCohortController.getAllCohort(sellerOrgId)
    }

}
