package com.udaan.pricing.service

import com.codahale.metrics.health.HealthCheck
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.google.inject.Injector
import com.udaan.common.auth.AuthHelper
import com.udaan.common.auth.authentication.AzureOAuthAuthenticatorConfig
import com.udaan.common.auth.roles.ConsoleUsersRolesManagerConfigV2
import com.udaan.common.server.DropwizardApplication
import com.udaan.common.server.UdaanServer
import com.udaan.common.server.register
import com.udaan.common.utils.kotlin.logger
import com.udaan.error.trace.module.registerErrorCodeFramework
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.core.dao.*
import com.udaan.pricing.service.resources.*
import com.udaan.pricing.core.dao.ExceptionContractsRepository
import com.udaan.pricing.core.dao.automation.PortfolioItemRepository
import com.udaan.pricing.core.dao.automation.PortfolioPlanRepository
import com.udaan.pricing.core.dao.automation.PortfolioRepository
import com.udaan.pricing.core.dao.automation.StrategyRepository
import com.udaan.pricing.core.dao.automation.TradingPortfolioItemRepository
import com.udaan.pricing.core.dao.network.DemandClusterLocationsRepository
import com.udaan.pricing.core.dao.network.DemandClusterRepository
import com.udaan.pricing.core.dao.signals.AsyncJobRepository
import com.udaan.pricing.core.dao.signals.SignalAnomaliesRepository
import com.udaan.pricing.core.dao.signals.SignalRepository
import com.udaan.pricing.core.dao.signals.VariableRepository
import com.udaan.pricing.service.resources.network.DemandClusterLocationsResource
import com.udaan.pricing.service.resources.network.DemandClusterResource
import com.udaan.pricing.service.resources.network.TerritoryResource
import com.udaan.pricing.service.resources.signals.SignalAnomalyResource
import com.udaan.pricing.service.resources.signals.SignalResource
import com.udaan.pricing.service.resources.signals.VariableResource
import com.udaan.resources.lifecycle.LifeCycleObjectRepo
import io.dropwizard.forms.MultiPartBundle
import io.dropwizard.jersey.setup.JerseyEnvironment
import io.dropwizard.setup.Environment
import org.glassfish.jersey.media.multipart.MultiPartFeature

class PricingService : DropwizardApplication<PricingServiceConfiguration>() {

    override fun getGuiceModules(configuration: PricingServiceConfiguration, environment: Environment) = listOf(
        PricingServiceModule()
    )

    override fun getDropwizardBundles() = listOf(MultiPartBundle())

    override fun getJacksonModules() = listOf(JavaTimeModule())

    override fun runAdditional(configuration: PricingServiceConfiguration, environment: Environment) {
        environment.register(LifeCycleObjectRepo.global())

        environment.jersey().registerErrorCodeFramework()

        AuthHelper.enableAuthV2(
            environment,
            listOf(AzureOAuthAuthenticatorConfig(isUdaanOnly = true)),
            consoleAppConfig = ConsoleUsersRolesManagerConfigV2(
                appName = "PRICING",
                refreshTimeInMinutes = 60
            )
        )

        environment.healthChecks().register("pricing-service", PricingServiceHC())

        registerResources(environment.jersey(), injector)

        initialiseCosmosRepositories(injector)
    }

    private fun registerResources(jerseyEnvironment: JerseyEnvironment, injector: Injector) {
        jerseyEnvironment.apply {
            register(injector.getInstance(PriceResources::class.java))
            register(injector.getInstance(MultiPartFeature::class.java))
            register(injector.getInstance(GeoPricingResource::class.java))
            register(injector.getInstance(GeoBasePriceResource::class.java))
            register(injector.getInstance(UserCohortResource::class.java))
            register(injector.getInstance(SellingPriceLimitsResource::class.java))
            register(injector.getInstance(MaxPromotionResources::class.java))
            register(injector.getInstance(PricingConsoleResource::class.java))
            register(injector.getInstance(ManualPriceResource::class.java))
            register(injector.getInstance(TradingPortfolioRepriceResource::class.java))
            register(injector.getInstance(TradingPortfolioItemResource::class.java))
            register(injector.getInstance(PortfolioItemResource::class.java))
            register(injector.getInstance(PortfolioResource::class.java))
            register(injector.getInstance(SignalAnomalyResource::class.java))
            register(injector.getInstance(SignalResource::class.java))
            register(injector.getInstance(VariableResource::class.java))
            register(injector.getInstance(DemandClusterLocationsResource::class.java))
            register(injector.getInstance(DemandClusterResource::class.java))
            register(injector.getInstance(TerritoryResource::class.java))
        }
    }

    /**
     * This function is being added for reducing cold start time for pods. Currently, cosmos client initialization takes
     * quite some time (3s - 4s) during initial requests, so we are initialising it while starting our service.
     * There are 4 cosmos repositories used in getPrice requests.
     * The `initialise()` func. is a dummy function in each of these four repositories which are being called here
     * for initialization.
     * We will be able to remove this once lazy initialization is removed for cosmos utils library.
     */
    private fun initialiseCosmosRepositories(injector: Injector) {
        TelemetryScope.runBlocking {
            val basePriceRepository = injector.getInstance(BasePriceRepository::class.java)
            val geoLocationBasePriceRepository = injector.getInstance(GeoLocationBasePriceRepository::class.java)
            val geoPriceRepository = injector.getInstance(GeoPriceRepository::class.java)
            val maxPromotionRepository = injector.getInstance(MaxPromotionRepository::class.java)
            val contractRepository = injector.getInstance(ContractRepository::class.java)
            val exceptionContractsRepository = injector.getInstance(ExceptionContractsRepository::class.java)
            val manualPriceRepository = injector.getInstance(ManualPriceRepository::class.java)
            val portfolioItemRepository = injector.getInstance(PortfolioItemRepository::class.java)
            val portfolioPlanRepository = injector.getInstance(PortfolioPlanRepository::class.java)
            val portfolioRepository = injector.getInstance(PortfolioRepository::class.java)
            val strategyRepository = injector.getInstance(StrategyRepository::class.java)
            val tradingPortfolioItemRepository = injector.getInstance(TradingPortfolioItemRepository::class.java)
            val signalRepository = injector.getInstance(SignalRepository::class.java)
            val signalAnomaliesRepository = injector.getInstance(SignalAnomaliesRepository::class.java)
            val variableRepository = injector.getInstance(VariableRepository::class.java)
            val asyncJobRepository = injector.getInstance(AsyncJobRepository::class.java)

            basePriceRepository.initialise()
            geoLocationBasePriceRepository.initialise()
            geoPriceRepository.initialise()
            maxPromotionRepository.initialise()
            contractRepository.intialise()
            exceptionContractsRepository.intialise()
            manualPriceRepository.initialise()
            portfolioRepository.initialise()
            portfolioPlanRepository.initialise()
            portfolioItemRepository.initialise()
            strategyRepository.initialise()
            tradingPortfolioItemRepository.initialise()
            signalRepository.initialise()
            signalAnomaliesRepository.initialise()
            variableRepository.initialise()
            asyncJobRepository.initialise()
            DemandClusterRepository.initialise()
            DemandClusterLocationsRepository.initialise()
        }
    }

    companion object {
        private val log by logger()

        @Throws(Exception::class)
        @JvmStatic
        fun main(args: Array<String>) {
            log.info("Starting Pricing Service")
            UdaanServer.startService(PricingService(), args)
        }
    }
}

class PricingServiceHC : HealthCheck() {

    @Throws(Exception::class)
    override fun check(): Result {
        return Result.healthy()
    }
}
