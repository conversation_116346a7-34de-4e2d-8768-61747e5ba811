package com.udaan.pricing.service.resources

import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.error.trace.annotations.Severity
import com.udaan.error.trace.annotations.UDErrorMonitored
import com.udaan.error.trace.annotations.UDErrorMonitoredApi
import com.udaan.pricing.MaxPromotionReq
import com.udaan.pricing.core.controller.MaxPromotionController
import com.udaan.resources.with
import javax.ws.rs.*
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.MediaType


@Path("v1/max-promotion")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@UDErrorMonitored("2")
class MaxPromotionResources @Inject constructor(
    private val maxPromotionController: MaxPromotionController
) {

    companion object {
        private val log by logger()
    }

    @Path("/{listingId}")
    @GET
    @UDErrorMonitoredApi("1", Severity.LOW,false)
    fun getMaxPromotion(
        @PathParam("listingId") listingId: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        maxPromotionController.get(listingId)
    }

    @POST
    @UDErrorMonitoredApi("2", Severity.LOW,false)
    fun upsertMaxPromotion(
         maxPromotionReq: MaxPromotionReq,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        maxPromotionController.createOrUpdate(maxPromotionReq)
    }


    @Path("/{listingId}")
    @DELETE
    @UDErrorMonitoredApi("2", Severity.LOW,false)
    fun deleteMaxPromotion(
        @PathParam("listingId") listingId: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        maxPromotionController.delete(listingId)
    }
}