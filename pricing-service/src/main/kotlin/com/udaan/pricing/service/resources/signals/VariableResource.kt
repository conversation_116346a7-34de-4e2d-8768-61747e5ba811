package com.udaan.pricing.service.resources.signals

import com.google.inject.Inject
import com.udaan.pricing.core.managers.signals.VariableManager
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.variable.requestreponse.GetResolvedValuesRequest
import com.udaan.pricing.variable.requestreponse.SourcingModel
import com.udaan.resources.with
import javax.ws.rs.Consumes
import javax.ws.rs.Path
import javax.ws.rs.Produces
import javax.ws.rs.GET
import javax.ws.rs.POST
import javax.ws.rs.PathParam
import javax.ws.rs.QueryParam
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.MediaType

@Path("/variable")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
class VariableResource @Inject constructor(
    private val variableManager: VariableManager
) {

    @GET
    @Path("/{variableId}")
    fun getVariable(
        @PathParam("variableId") variableId: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        variableManager.getVariable(variableId)
    }

    @GET
    @Path("/all")
    fun getAllVariables(
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        variableManager.getAllVariables()
    }

    @GET
    @Path("/values/listing/{listingId}/{salesUnitId}/location/{locationType}/{locationValue}")
    fun resolveValuesForListingSalesUnit(
        @PathParam("listingId") listingId: String,
        @PathParam("salesUnitId") salesUnitId: String,
        @PathParam("locationType") locationType: LocationType,
        @PathParam("locationValue") locationValue: String,
        @QueryParam("variableIds") variableIds: Set<VariableId>,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        variableManager.resolveValuesForListingSalesUnitCached(
            listingId = listingId,
            salesUnitId = salesUnitId,
            location = Location(
                locationType = locationType,
                locationValue = locationValue
            ),
            variableIds = variableIds,
            inputTerritoryMap = null
        )
    }

    @POST
    @Path("/v2/values/listing/{listingId}/{salesUnitId}/location/{locationType}/{locationValue}")
    fun resolveValuesForListingSalesUnit(
        @PathParam("listingId") listingId: String,
        @PathParam("salesUnitId") salesUnitId: String,
        @PathParam("locationType") locationType: LocationType,
        @PathParam("locationValue") locationValue: String,
        getResolvedValuesRequest: GetResolvedValuesRequest,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        variableManager.resolveValuesForListingSalesUnitCached(
            listingId = listingId,
            salesUnitId = salesUnitId,
            location = Location(
                locationType = locationType,
                locationValue = locationValue
            ),
            variableIds = getResolvedValuesRequest.variableIds.toSet(),
            inputTerritoryMap = getResolvedValuesRequest.inputTerritoryMap
        )
    }

    @GET
    @Path("/values/group/{productGroupId}/location/{locationType}/{locationValue}")
    fun resolveValuesForProductGroup(
        @PathParam("productGroupId") productGroupId: String,
        @PathParam("locationType") locationType: LocationType,
        @PathParam("locationValue") locationValue: String,
        @QueryParam("variableIds") variableIds: Set<VariableId>,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        variableManager.resolveValuesForProductGroupCached(
            productGroupId = productGroupId,
            location = Location(
                locationType = locationType,
                locationValue = locationValue
            ),
            variableIds = variableIds,
            inputTerritoryMap = null
        )
    }

    @POST
    @Path("/v2/values/group/{productGroupId}/location/{locationType}/{locationValue}")
    fun resolveValuesForProductGroup(
        @PathParam("productGroupId") productGroupId: String,
        @PathParam("locationType") locationType: LocationType,
        @PathParam("locationValue") locationValue: String,
        getResolvedValuesRequest: GetResolvedValuesRequest,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        variableManager.resolveValuesForProductGroupCached(
            productGroupId = productGroupId,
            location = Location(
                locationType = locationType,
                locationValue = locationValue
            ),
            variableIds = getResolvedValuesRequest.variableIds.toSet(),
            inputTerritoryMap = getResolvedValuesRequest.inputTerritoryMap
        )
    }

    @GET
    @Path("/bmt-values/group/{productGroupId}/location/{locationType}/{locationValue}")
    fun resolveBmtValuesForProductGroup(
        @PathParam("productGroupId") productGroupId: String,
        @PathParam("locationType") locationType: LocationType,
        @PathParam("locationValue") locationValue: String,
        @QueryParam("sourcingModel") sourcingModel: SourcingModel,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        variableManager.resolveBmtValuesForProductGroup(
            productGroupId = productGroupId,
            location = Location(
                locationType = locationType,
                locationValue = locationValue
            ),
            sourcingModel = sourcingModel
        )
    }

    @GET
    @Path("/jit/group/{productGroupId}/city/{city}")
    fun getJitVendorBestPriceForCityGid(
        @PathParam("productGroupId") productGroupId: String,
        @PathParam("city") city: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        variableManager.resolveJitVendorBestPriceForCityGid(
            productGroupId = productGroupId,
            city = city
        )
    }
}
