package com.udaan.pricing.service.resources

import com.google.inject.Inject
import com.udaan.pricing.core.managers.TradingPortfolioItemManager
import com.udaan.pricing.portfolioitem.CreateTradingPortfolioItemRequest
import com.udaan.pricing.portfolioitem.DeletePortfolioItemRequest
import com.udaan.resources.with
import javax.ws.rs.Consumes
import javax.ws.rs.GET
import javax.ws.rs.POST
import javax.ws.rs.Path
import javax.ws.rs.PathParam
import javax.ws.rs.Produces
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.MediaType

@Path("/trading-portfolio-items")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
class TradingPortfolioItemResource @Inject constructor(
    private val tradingPortfolioItemManager: TradingPortfolioItemManager
) {

    @GET
    @Path("/{catalogEntity}/{locationValue}")
    fun getPortfolioTaggingForEntityAndLocation(
        @PathParam("catalogEntity") catalogEntity: String,
        @PathParam("locationValue") locationValue: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        tradingPortfolioItemManager.getPortfolioTaggingForEntityAndLocation(catalogEntity, locationValue)
    }

    @POST
    fun createPortfolioItemTagging(
        createTradingPortfolioItemRequest: CreateTradingPortfolioItemRequest,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        tradingPortfolioItemManager.createPortfolioItemTagging(createTradingPortfolioItemRequest)
    }

    @POST
    @Path("/delete")
    fun deletePortfolioItemTagging(
        deletePortfolioItemRequest: DeletePortfolioItemRequest,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        tradingPortfolioItemManager.deletePortfolioItemTagging(deletePortfolioItemRequest)
    }
}
