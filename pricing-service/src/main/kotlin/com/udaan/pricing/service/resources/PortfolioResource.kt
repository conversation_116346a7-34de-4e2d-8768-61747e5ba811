package com.udaan.pricing.service.resources

import com.google.inject.Inject
import com.udaan.pricing.core.managers.PortfolioManager
import com.udaan.pricing.portfolio.PortfolioRequest
import com.udaan.resources.with
import javax.ws.rs.Consumes
import javax.ws.rs.GET
import javax.ws.rs.POST
import javax.ws.rs.Path
import javax.ws.rs.PathParam
import javax.ws.rs.Produces
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.MediaType

@Path("/portfolios")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
class PortfolioResource @Inject constructor(
    private val portfolioManager: PortfolioManager
) {

    @POST
    fun createPortfolio(
        portfolioRequest: PortfolioRequest,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        portfolioManager.createPortfolio(portfolioRequest = portfolioRequest)
    }

    @GET
    @Path("/all")
    fun getAllPortfolios(
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        portfolioManager.getAllActivePortfolios()
    }

    @GET
    @Path("/{portfolioId}")
    fun getPortfolioById(
        @PathParam("portfolioId") portfolioId: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        portfolioManager.getPortfolioById(portfolioId)
    }
}
