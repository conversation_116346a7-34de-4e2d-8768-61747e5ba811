package com.udaan.pricing.service.resources

import com.google.inject.Inject
import com.udaan.error.trace.annotations.Severity
import com.udaan.error.trace.annotations.UDErrorMonitoredApi
import com.udaan.pricing.core.controller.PricingConsoleController
import com.udaan.pricing.core.controller.contract.ContractController
import com.udaan.pricing.core.managers.PortfolioItemManager
import com.udaan.pricing.core.managers.PortfolioManager
import com.udaan.pricing.core.managers.PortfolioPlanManager
import com.udaan.pricing.core.managers.StrategyManager
import com.udaan.pricing.core.helpers.PricingSignalsHelper
import com.udaan.pricing.core.managers.signals.AsyncJobController
import com.udaan.pricing.core.managers.signals.SignalAnomaliesManager
import com.udaan.pricing.core.managers.signals.VariableManager
import com.udaan.pricing.core.utils.signals.CosmosQueryConstants
import com.udaan.pricing.job.AsyncJobType
import com.udaan.pricing.portfolio.PortfolioRequest
import com.udaan.pricing.portfolioitem.CreatePortfolioItemRequest
import com.udaan.pricing.portfolioplan.PortfolioPlanRequestFromConsole
import com.udaan.pricing.portfolioplan.toPortfolioPlanRequest
import com.udaan.pricing.signalanomaly.SignalAnomalyReviewRequest
import com.udaan.pricing.variable.VariableType
import com.udaan.resources.with
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.glassfish.jersey.media.multipart.FormDataBodyPart
import org.glassfish.jersey.media.multipart.FormDataContentDisposition
import org.glassfish.jersey.media.multipart.FormDataParam
import java.io.InputStream
import javax.annotation.security.PermitAll
import javax.ws.rs.BadRequestException
import javax.ws.rs.Consumes
import javax.ws.rs.DELETE
import javax.ws.rs.GET
import javax.ws.rs.POST
import javax.ws.rs.PUT
import javax.ws.rs.Path
import javax.ws.rs.PathParam
import javax.ws.rs.Produces
import javax.ws.rs.QueryParam
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.Context
import javax.ws.rs.core.HttpHeaders
import javax.ws.rs.core.MediaType
import javax.ws.rs.core.Response
import javax.ws.rs.core.SecurityContext

@Suppress("LongParameterList")
@Path("v1/console")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
class PricingConsoleResource @Inject constructor(
    private val pricingConsoleController: PricingConsoleController,
    private val contractController: ContractController,
    private val portfolioManager: PortfolioManager,
    private val portfolioPlanManager: PortfolioPlanManager,
    private val strategyManager: StrategyManager,
    private val portfolioItemManager: PortfolioItemManager,
    private val pricingSignalsHelper: PricingSignalsHelper,
    private val asyncJobController: AsyncJobController,
    private val signalAnomaliesManager: SignalAnomaliesManager,
    private val variableManager: VariableManager
) {

    @Path("/final-price-visibility/{listingId}/{buyerNumber}")
    @GET
    @UDErrorMonitoredApi("1", Severity.LOW, false)
    fun getFinalPriceVisibility(
        @PathParam("listingId") listingId: String,
        @PathParam("buyerNumber") buyerNumber: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        pricingConsoleController.getPriceListingVisibilityForBuyer(listingId, buyerNumber)
    }

    @Path("/listing-details/{listingId}")
    @GET
    @UDErrorMonitoredApi("1", Severity.LOW, false)
    fun getListingDetailsForPricingConsole(
        @PathParam("listingId") listingId: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        pricingConsoleController.getListingDetailsForPricingConsole(listingId)
    }

    @Path("/all-possible-buyer-cohorts-and-locations/{listingId}")
    @GET
    @UDErrorMonitoredApi("2", Severity.LOW, false)
    fun getAllPossibleBuyerCohortsAndLocations(
        @PathParam("listingId") listingId: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        pricingConsoleController.getAllPossibleBuyerCohortsAndLocations(listingId)
    }

    @Path("/ssc-price-visibility/{listingId}/{buyerCohort}/{locationId}/{locationType}")
    @GET
    @UDErrorMonitoredApi("15", Severity.LOW, false)
    fun getSSCPriceVisibility(
        @PathParam("listingId") listingId: String,
        @PathParam("buyerCohort") buyerCohort: String,
        @PathParam("locationId") locationId: String,
        @PathParam("locationType") locationType: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        pricingConsoleController.getSSCPriceVisibility(listingId, buyerCohort, locationId, locationType)
    }


    @POST
    @Path("/upload")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    // todo: @omraj-tec - Figure out proper authz roles for pricing jobs
    @PermitAll
    fun createJob(
        @Context context: SecurityContext,
        @FormDataParam("job_type") jobType: String,
        @FormDataParam("file") fileInputStream: InputStream?,
        @FormDataParam("file") contentDispositionHeader: FormDataContentDisposition?,
        @FormDataParam("additionalInfo") jsonPart: FormDataBodyPart?,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {

        jsonPart?.mediaType = MediaType.APPLICATION_JSON_TYPE
        val additionalInfo = jsonPart?.getValueAs(Map::class.java) as Map<String, String>?
        if (fileInputStream == null) {
            throw BadRequestException("Input stream cannot be null for file upload.")
        }
        pricingConsoleController.uploadFileAndCreateJob(
            jobType = AsyncJobType.valueOf(jobType),
            additionalInfo = additionalInfo ?: emptyMap(),
            requestedBy = context.userPrincipal.name,
            fileInputStream = fileInputStream,
            fileLength = contentDispositionHeader?.size ?: 0
        )
    }

    @Path("/contract/buyer/{buyerNumber}/{catalogEntityId}")
    @DELETE
    fun deleteBuyerContract(
        @PathParam("buyerNumber") buyerNumber: String,
        @PathParam("catalogEntityId") catalogEntityId: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        /**
         * TODO: replace lastRefreshedBy with actual user email
         */
        contractController.deleteContract(buyerNumber, catalogEntityId, "console")
    }

    @Path("/contract/buyer/{buyerNumber}")
    @GET
    fun getContractByBuyerNumber(
        @PathParam("buyerNumber") buyerNumber: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        pricingConsoleController.getContractVisibilitySummary(buyerNumber)
    }

    @Path("/contract/exception/buyer/{buyerNumber}")
    @GET
    fun getExceptionContractsByBuyerNumber(
        @PathParam("buyerNumber") buyerNumber: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        pricingConsoleController.getExceptionContractVisibilityForBuyer(buyerNumber)
    }

    @Path("/contract/exception/buyer/{buyerOrgId}/{catalogEntityId}/approve")
    @POST
    fun approveExceptionContract(
        @PathParam("buyerOrgId") buyerOrgId: String,
        @PathParam("catalogEntityId") catalogEntityId: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        /**
         * TODO: replace lastRefreshedBy with actual user email
         */
        contractController.approveExceptionContract(buyerOrgId, catalogEntityId, "console")
    }

    @Path("/contract/exception/buyer/{buyerOrgId}/{catalogEntityId}/reject")
    @POST
    fun rejectExceptionContracts(
        @PathParam("buyerOrgId") buyerOrgId: String,
        @PathParam("catalogEntityId") catalogEntityId: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        contractController.rejectExceptionContract(buyerOrgId, catalogEntityId)
    }



    @POST
    @Path("/portfolios")
    fun createPortfolio(
        portfolioRequest: PortfolioRequest,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        portfolioManager.createPortfolio(portfolioRequest = portfolioRequest)
    }

    @GET
    @Path("/portfolios/all")
    fun getAllPortfolios(
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        portfolioManager.getAllActivePortfolios()
    }

    @GET
    @Path("/all-cohort-variables")
    fun getAllCohortVariableIds(
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        pricingSignalsHelper.getAllVariables().filter { it.type == VariableType.COHORT }.map { it.id.name }
    }

    @GET
    @Path("/portfolio-plans/{portfolioId}")
    fun getAllPortfolioPlan(
        @PathParam("portfolioId") portfolioId: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        portfolioPlanManager.getAllPortfolioPlans(portfolioId)
    }

    @GET
    @Path("/strategies")
    fun getMultipleStrategiesByIds(
        @QueryParam("ids") ids: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        strategyManager.getMultipleStrategiesByIds(ids)
    }

    @POST
    @Path("/portfolio-plans")
    fun createPortfolioPlan(
        portfolioPlanRequestFromConsole: PortfolioPlanRequestFromConsole,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        val portfolioPlanRequest = portfolioPlanRequestFromConsole.toPortfolioPlanRequest()
        portfolioPlanManager.createOrUpdatePortfolioPlan(portfolioPlanRequest = portfolioPlanRequest)
    }

    @GET
    @Path("/strategies/all")
    fun getAllActiveStrategies(
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        strategyManager.getAllActiveStrategies()
    }

    @GET
    @Path("/portfolio-items/portfolio-tagging-count")
    fun getCountOfPortfolioItemToPortfolioTaggedCount(
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        portfolioItemManager.getPortfolioItemToPortfolioTaggingCount()
    }


    // TODO:- Get updatedByID from security context
    @DELETE
    @Path("/portfolio-plan/{portfolioId}/{buyerCohort}/{updatedById}")
    fun deletePortfolioPlan(
        @PathParam("portfolioId") portfolioId: String,
        @PathParam("buyerCohort") buyerCohort: String,
        @PathParam("updatedById") updatedById: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        portfolioPlanManager.deletePortfolioPlan(portfolioId, buyerCohort, updatedById)
    }

    @DELETE
    @Path("/portfolio/{portfolioId}/{updatedById}")
    fun deletePortfolio(
        @PathParam("portfolioId") portfolioId: String,
        @PathParam("updatedById") updatedById: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        portfolioManager.deletePortfolio(portfolioId, updatedById)
    }

    @POST
    @Path("/portfolio-items")
    fun createPortfolioItemTagging(
        createPortfolioItemRequest: CreatePortfolioItemRequest,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        portfolioItemManager.createPortfolioItemTagging(createPortfolioItemRequest)
    }

    @GET
    @Path("/async-job/type/{jobType}")
    fun getAsyncJob(
        @PathParam("jobType") asyncJobType: AsyncJobType,
        @QueryParam("offSet") offSet: Int?,
        @QueryParam("limit") limit: Int?,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        asyncJobController.getAsyncJobs(
            asyncJobType,
            offSet = offSet ?: CosmosQueryConstants.DEFAULT_OFFSET,
            limit = limit ?: CosmosQueryConstants.DEFAULT_LIMIT
        )
    }

    @GET
    @Path("/async-job/{jobId}/file")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    fun getAsyncJob(
        @PathParam("jobId") jobId: String,
        @QueryParam("fileType") fileType: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        withContext(Dispatchers.IO) {
            val inputStream = asyncJobController.getJobFile(jobId, fileType)
            Response.ok(inputStream)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=${jobId}_${fileType.lowercase()}.xlsx")
                .build()
        }
    }


    @GET
    @Path("/signal-anomaly/pending-review")
    fun getSignalsPendingForReview(
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        signalAnomaliesManager.getSignalsPendingForReview()
    }


    @PUT
    @Path("/signal-anomaly/{referenceId}/act")
    fun reviewAnomalousSignal(
        @PathParam("referenceId") referenceId: String,
        signalAnomalyReviewRequest: SignalAnomalyReviewRequest,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        signalAnomaliesManager.reviewAnomalousSignal(
            referenceId,
            signalAnomalyReviewRequest.action,
            signalAnomalyReviewRequest.updatedBy
        )
    }

    @GET
    @Path("/variable/all")
    fun getAllVariables(
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        variableManager.getAllVariables()
    }

}
