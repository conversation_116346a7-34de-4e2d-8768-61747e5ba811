package com.udaan.pricing.service.resources

import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.core.managers.PortfolioItemManager
import com.udaan.pricing.portfolioitem.CreatePortfolioItemRequest
import com.udaan.pricing.portfolioitem.DeletePortfolioItemRequest
import com.udaan.resources.with
import javax.ws.rs.Consumes
import javax.ws.rs.GET
import javax.ws.rs.POST
import javax.ws.rs.Path
import javax.ws.rs.PathParam
import javax.ws.rs.Produces
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.MediaType

@Path("/portfolio-items")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
class PortfolioItemResource @Inject constructor(
    private val portfolioItemManager: PortfolioItemManager
) {
    companion object {
        private val logger by logger()
    }

    @GET
    @Path("/{catalogEntity}/{locationValue}")
    fun getPortfolioTaggingForEntityAndLocation(
        @PathParam("catalogEntity") catalogEntity: String,
        @PathParam("locationValue") locationValue: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        portfolioItemManager.getPortfolioTaggingForEntityAndLocation(catalogEntity, locationValue)
    }

    @POST
    @Path("/delete")
    fun deletePortfolioItemTagging(
        deletePortfolioItemRequest: DeletePortfolioItemRequest,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        portfolioItemManager.deletePortfolioItemTagging(deletePortfolioItemRequest)
    }
}
