package com.udaan.pricing.service.resources.signals

import com.google.inject.Inject
import com.udaan.pricing.core.helpers.ConfigHelper
import com.udaan.pricing.core.managers.signals.SignalAnomaliesManager
import com.udaan.pricing.signalanomaly.SignalAnomalyReviewRequest
import com.udaan.resources.with
import javax.ws.rs.Consumes
import javax.ws.rs.GET
import javax.ws.rs.PUT
import javax.ws.rs.Path
import javax.ws.rs.PathParam
import javax.ws.rs.Produces
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.MediaType

@Path("/signal-anomaly")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
class SignalAnomalyResource @Inject constructor(
    private val signalAnomaliesManager: SignalAnomaliesManager,
    private val configHelper: ConfigHelper
) {
    @GET
    @Path("/pending-review")
    fun getSignalsPendingForReview(
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        signalAnomaliesManager.getSignalsPendingForReview()
    }

    @PUT
    @Path("/{referenceId}/act")
    fun reviewAnomalousSignal(
        @PathParam("referenceId") referenceId: String,
        signalAnomalyReviewRequest: SignalAnomalyReviewRequest,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        signalAnomaliesManager.reviewAnomalousSignal(
            referenceId,
            signalAnomalyReviewRequest.action,
            signalAnomalyReviewRequest.updatedBy
        )
    }

    @GET
    @Path("/variable/yellow-guardrails")
    fun getYellowGuardRails(
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        configHelper.getAllYellowGuardrails()
    }
}
