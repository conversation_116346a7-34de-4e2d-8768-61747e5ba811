package com.udaan.pricing.service.resources

import com.google.inject.Inject
import com.udaan.error.trace.annotations.Severity
import com.udaan.error.trace.annotations.UDErrorMonitoredApi
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.controller.ManualPriceController
import com.udaan.pricing.core.helpers.BuyerCohortDetails
import com.udaan.pricing.manualprice.ManualPriceDeleteRequest
import com.udaan.pricing.manualprice.ManualPriceUpdateRequest
import com.udaan.pricing.manualprice.ManualPriceUpdateResponse
import com.udaan.resources.with
import javax.ws.rs.Consumes
import javax.ws.rs.GET
import javax.ws.rs.POST
import javax.ws.rs.Path
import javax.ws.rs.Produces
import javax.ws.rs.QueryParam
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.MediaType

@Path("v1/manual-price")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
class ManualPriceResource @Inject constructor(
    private val manualPriceController: ManualPriceController
) {

    @POST
    @Path("/update")
    @UDErrorMonitoredApi("1", Severity.LOW,false)
    fun createOrUpdateManualPrice(
        manualPriceUpdateRequest: ManualPriceUpdateRequest,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        val updatedManualPrice = manualPriceController.createOrUpdateManualPrice(
            manualPriceUpdateRequest = manualPriceUpdateRequest
        )

        ManualPriceUpdateResponse(updatedManualPrice.referenceId)
    }

    @POST
    @Path("/delete")
    @UDErrorMonitoredApi("2", Severity.LOW,false)
    fun deleteManualPrice(
        manualPriceDeleteRequest: ManualPriceDeleteRequest,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        val deletedManualPrice = manualPriceController.deleteManualPrice(manualPriceDeleteRequest)

        deletedManualPrice?.referenceId
    }

    @GET
    @Path("/get")
    @UDErrorMonitoredApi("3", Severity.LOW,false)
    fun getValidManualPrice(
        @QueryParam("catalogEntity") catalogEntity: String,
        @QueryParam("locationType") locationType: LocationType,
        @QueryParam("locationValue") locationValue: String,
        @QueryParam("buyerCohort") buyerCohort: String? = null,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        manualPriceController.getValidManualPrice(
            catalogEntitiesSortedByPriority = listOf(catalogEntity),
            locationsSortedByPriority = listOf(
                Pair(locationType, locationValue)
            ),
            allBuyerCohorts = buyerCohort?.let {
                listOf(
                    BuyerCohortDetails(
                        buyerCohort = it,
                        cohortPriority = 1
                    )
                )
            } ?: emptyList()
        )
    }
}
