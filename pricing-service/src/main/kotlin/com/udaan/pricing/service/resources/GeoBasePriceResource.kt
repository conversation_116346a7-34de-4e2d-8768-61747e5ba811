package com.udaan.pricing.service.resources

import com.google.inject.Inject
import com.udaan.error.trace.annotations.Severity
import com.udaan.error.trace.annotations.UDErrorMonitored
import com.udaan.error.trace.annotations.UDErrorMonitoredApi
import com.udaan.pricing.DeleteGeoBasePriceRequest
import com.udaan.pricing.UpdateEmptyActiveGeoBasePriceRequest
import com.udaan.pricing.GeoLocationBasePriceStateChangeRequest
import com.udaan.pricing.GeoLocationType
import com.udaan.pricing.UpsertGeoLocationBasePriceRequest
import com.udaan.pricing.convert
import com.udaan.pricing.core.controller.GeoLocationBasePriceController
import com.udaan.resources.with
import javax.ws.rs.GET
import javax.ws.rs.POST
import javax.ws.rs.PUT
import javax.ws.rs.Path
import javax.ws.rs.PathParam
import javax.ws.rs.Produces
import javax.ws.rs.QueryParam
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.Context
import javax.ws.rs.core.MediaType
import javax.ws.rs.core.SecurityContext

@Path("v1/geo-base-price")
@UDErrorMonitored("4")
class GeoBasePriceResource @Inject constructor(
    private val geoLocationBasePriceController: GeoLocationBasePriceController
) {

    @Path("/upsert")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @UDErrorMonitoredApi("1", Severity.LOW,false)
    fun upsertGeoPrice(
            req: UpsertGeoLocationBasePriceRequest,
            @Context context: SecurityContext?,
            @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        geoLocationBasePriceController.upsertEntry(req)
    }

    @Path("/{id}/change-state")
    @PUT
    @Produces(MediaType.APPLICATION_JSON)
    @UDErrorMonitoredApi("2", Severity.LOW,false)
    fun changeGeoListingState(
            @PathParam("id") id: String,
            req: GeoLocationBasePriceStateChangeRequest,
            @Context context: SecurityContext?,
            @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        geoLocationBasePriceController.changeGeoListingState(id, req)
    }

    @Path("/all")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @UDErrorMonitoredApi("3", Severity.LOW,false)
    fun getAllActiveGeoBasePrices(
            @QueryParam("listingId") listingId: String,
            @QueryParam("salesUnitId") salesUnitId: String,
            @Context context: SecurityContext?,
            @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        geoLocationBasePriceController.getAllActiveGeoBasePrices(
                listingId = listingId,
                salesUnitId = salesUnitId
        ).convert()
    }


    /**
     * This is not simple delete in this case we will be serving empty response , special use case for not falling back to base prices
     * Needed this for WH level pricing to not fall back to default city price.
     */
    @PUT
    @Produces(MediaType.APPLICATION_JSON)
    @UDErrorMonitoredApi("4", Severity.LOW,false)
    @Path("/update-empty-active-geo-price")
    fun updateEmptyActiveGeoBasePrice(
        updateEmptyActiveGeoBasePriceRequest: UpdateEmptyActiveGeoBasePriceRequest,
        @Context context: SecurityContext?,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        geoLocationBasePriceController.updateEmptyActiveGeoBasePrice(updateEmptyActiveGeoBasePriceRequest)
    }

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @UDErrorMonitoredApi("5", Severity.LOW, false)
    @Path("/dc-with-active-price-food/{listingId}")
    fun getDCsHavingActivePriceForFoodListing(
        @PathParam("listingId") listingId: String,
        @Context context: SecurityContext?,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        geoLocationBasePriceController.getDCsHavingActivePriceForFoodListing(listingId)
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/delete")
    fun deleteGeoBasePricesForListingSalesUnitId(
        deleteGeoBasePriceRequest: DeleteGeoBasePriceRequest,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        geoLocationBasePriceController.deleteGeoLocationBasePriceForListingSalesUnitId(
            listingId = deleteGeoBasePriceRequest.listingId,
            salesUnitId = deleteGeoBasePriceRequest.saleUnitId,
            locationTypeId = deleteGeoBasePriceRequest.locationTypeId,
            locationType = GeoLocationType.valueOf(deleteGeoBasePriceRequest.locationType)
        )
    }
}
