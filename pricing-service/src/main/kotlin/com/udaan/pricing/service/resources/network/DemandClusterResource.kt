package com.udaan.pricing.service.resources.network

import com.google.inject.Inject
import com.udaan.error.trace.annotations.Severity
import com.udaan.error.trace.annotations.UDErrorMonitored
import com.udaan.error.trace.annotations.UDErrorMonitoredApi
import com.udaan.pricing.core.controller.network.DemandClusterController
import com.udaan.pricing.network.DemandClusterReq
import com.udaan.resources.with
import javax.ws.rs.Consumes
import javax.ws.rs.GET
import javax.ws.rs.POST
import javax.ws.rs.Path
import javax.ws.rs.PathParam
import javax.ws.rs.Produces
import javax.ws.rs.QueryParam
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.MediaType

@UDErrorMonitored("1")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/demand-cluster")
class DemandClusterResource @Inject constructor(
    private val demandClusterController: DemandClusterController
) {
    @POST
    @Path("/create-demand-cluster")
    @UDErrorMonitoredApi("1", Severity.LOW, false)
    fun createDemandCluster(
        demandClusterReq: DemandClusterReq,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        demandClusterController.createDemandCluster(demandClusterReq)
    }

    @GET
    @Path("/anchor-city")
    @UDErrorMonitoredApi("2", Severity.LOW, false)
    fun getAnchorCityForLocation(
        @QueryParam("locationName") locationName: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        demandClusterController.getAnchorCityForLocation(locationName)
    }

    @GET
    @Path("/anchor-city/{anchorCity}/locations")
    @UDErrorMonitoredApi("3", Severity.LOW, false)
    fun getLocationsForAnchorCity(
        @PathParam("anchorCity") anchorCity: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        demandClusterController.getLocationsForAnchorCity(anchorCity)
    }

    @GET
    @Path("/anchor-city/{anchorCity}/warehouses")
    @UDErrorMonitoredApi("4", Severity.LOW, false)
    fun getWarehousesForAnchorCity(
        @PathParam("anchorCity") anchorCity: String,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        demandClusterController.getWarehousesForAnchorCity(anchorCity)
    }
}