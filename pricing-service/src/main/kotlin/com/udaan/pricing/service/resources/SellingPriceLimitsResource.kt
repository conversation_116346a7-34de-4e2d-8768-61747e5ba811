package com.udaan.pricing.service.resources

import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.error.trace.annotations.Severity
import com.udaan.error.trace.annotations.UDErrorMonitored
import com.udaan.error.trace.annotations.UDErrorMonitoredApi
import com.udaan.pricing.SellingPriceLimitsCheckRequest
import com.udaan.pricing.core.controller.SellingPriceLimitsController
import com.udaan.resources.with
import javax.ws.rs.Consumes
import javax.ws.rs.POST
import javax.ws.rs.Path
import javax.ws.rs.Produces
import javax.ws.rs.container.AsyncResponse
import javax.ws.rs.container.Suspended
import javax.ws.rs.core.Context
import javax.ws.rs.core.MediaType
import javax.ws.rs.core.SecurityContext

@Path("v1/sellingPriceLimits")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@UDErrorMonitored("5")
class SellingPriceLimitsResource @Inject constructor(
    private val sellingPriceLimitsController: SellingPriceLimitsController
) {

    @Path("/checkIntentPriceForPriceLimits")
    @POST
    @UDErrorMonitoredApi("1", Severity.LOW,false)
    fun checkIntentPriceForPriceLimits(
        sellingPriceLimitsCheckRequest: SellingPriceLimitsCheckRequest,
        @Context context: SecurityContext?,
        @Suspended asyncResponse: AsyncResponse
    ) = asyncResponse.with {
        sellingPriceLimitsController.checkIntentPriceForPriceLimits(
            sellingPriceLimitsCheckRequest.productId,
            sellingPriceLimitsCheckRequest.price
        )
    }
}
