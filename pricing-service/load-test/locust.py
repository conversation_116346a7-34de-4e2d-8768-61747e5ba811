from locust import HttpUser, TaskSet, task, events, between
import json


class PricingTasks(HttpUser):
    # it determines for how long a simulated user will wait between executing tasks
    wait_time = between(1, 2)

    # loading the data for the test
    # define your init data load or things here
    def on_start(self):
        print("Add something here")

    # @task: represents the tasks to be performed
    # testing the listings read api without price
    # follow https://www.notion.so/udaantech/Locust-Load-testing-********************************
    @task
    def put_a_test_name(self):
        print("define things like me")

    @task
    def test(self):
        headers = {
        'Content-Type': 'application/json'
        }
        payload = {
        }
        url = "/v1/price/TLFIH7Q1N18FKHMCNQZGJMQTNB9HKXB?fetchInactive=false"
        self.client.post(url,
                     data=json.dumps(payload),
                     headers=headers
                     )

    @task
    def testWithMultiSU(self):
        headers = {
            'Content-Type': 'application/json'
        }
        payload = {
        }
        url = "/v1/price/TLIGDZ5KW9J98FZQRS4WMBDJ11GLJGS1?fetchInactive=false"
        self.client.post(url,
                         data=json.dumps(payload),
                         headers=headers
                         )