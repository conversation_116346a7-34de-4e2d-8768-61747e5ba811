apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: pricing-default-portfolio-tagger
  namespace: VAR_KUBE_ENV
  labels:
    app: pricing-default-portfolio-tagger
    config: udaan
spec:
  schedule: "45 9 * * 1-6" # everyday 3PM IST from Monday to Saturday (skipping Sunday)
  concurrencyPolicy: Forbid
  startingDeadlineSeconds: 200
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            config: udaan
        spec:
          containers:
            - name: pricing-default-portfolio-tagger
              image: udaan.azurecr.io/pricing-jobs:latest
              resources:
                limits:
                  memory: 5072Mi
                  cpu: 800m
                  ephemeral-storage: 1Gi
                requests:
                  memory: 5048Mi
                  cpu: 500m
                  ephemeral-storage: 512Mi
              args:
                - java
                - -cp
                - /jars/*
                - -Dcom.sun.management.jmxremote
                - -Dcom.sun.management.jmxremote.port=9010
                - -Dcom.sun.management.jmxremote.authenticate=false
                - -Dcom.sun.management.jmxremote.ssl=false
                - -XX:InitialRAMPercentage=70.0
                - -XX:MaxRAMPercentage=70.0
                - -XX:MaxMetaspaceSize=214m
                - com.udaan.pricing.jobs.automation.DefaultPortfolioTaggerJobRunner
          restartPolicy: Never
