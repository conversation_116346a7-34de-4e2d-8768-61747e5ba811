apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: pricing-signals-fmcg-channel-sync-cronjob-v2
  namespace: VAR_KUBE_ENV
  labels:
    app: pricing-signals-fmcg-channel-sync-cronjob-v2
    config: udaan
spec:
  schedule: "30 3 * * *" # 9 AM Daily
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            config: udaan
        spec:
          containers:
            - name: pricing-signals-fmcg-channel-sync-cronjob-v2
              image: udaan.azurecr.io/pricing-jobs:latest
              resources:
                limits:
                  memory: 3072Mi
                  cpu: 2000m
                requests:
                  memory: 3072Mi
                  cpu: 1000m
              args:
                - java
                - -cp
                - /jars/*
                - com.udaan.pricing.jobs.signals.probesyncer.FmcgSourcingChannelSyncJob
          restartPolicy: Never
