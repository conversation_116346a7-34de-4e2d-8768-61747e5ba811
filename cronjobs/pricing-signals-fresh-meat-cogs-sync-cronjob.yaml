apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: pricing-signals-fresh-meat-cogs-sync-cronjob
  namespace: VAR_KUBE_ENV
  labels:
    app: pricing-signals-fresh-meat-cogs-sync-cronjob
    config: udaan
spec:
  schedule: "30 12 * * *" # 6 PM Daily (source probe is manually update everyday 5pm post collecting required data from various sources)
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            config: udaan
        spec:
          containers:
            - name: pricing-signals-fresh-meat-cogs-sync-cronjob
              image: udaan.azurecr.io/pricing-jobs:latest
              resources:
                limits:
                  memory: 2048Mi
                  cpu: 2000m
                requests:
                  memory: 1024Mi
                  cpu: 1000m
              args:
                - java
                - -cp
                - /jars/*
                - com.udaan.pricing.jobs.signals.probesyncer.FreshMeatCogsSyncJob
          restartPolicy: Never
