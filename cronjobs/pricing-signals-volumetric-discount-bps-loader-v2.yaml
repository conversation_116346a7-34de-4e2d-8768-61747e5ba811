apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: pricing-signals-volumetric-discount-bps-loader-v2
  namespace: VAR_KUBE_ENV
  labels:
    app: pricing-signals-volumetric-discount-bps-loader-v2
    config: udaan
spec:
  schedule: "0 0/12 * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            config: udaan
        spec:
          containers:
            - name: pricing-signals-volumetric-discount-bps-loader-v2
              image: udaan.azurecr.io/pricing-jobs:latest
              resources:
                limits:
                  memory: 3072Mi
                  cpu: 2000m
                requests:
                  memory: 3072Mi
                  cpu: 1000m
              args:
                - java
                - -cp
                - /jars/*
                - com.udaan.pricing.jobs.signals.probesyncer.VolumetricDiscountBpsLoaderJob
          restartPolicy: Never
