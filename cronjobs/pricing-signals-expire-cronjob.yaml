apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: pricing-signals-expire-cronjob
  namespace: VAR_KUBE_ENV
  labels:
    app: pricing-signals-expire-cronjob
    config: udaan
spec:
  schedule: "0 0/1 * * *" # for every 1 hour.
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            config: udaan
        spec:
          containers:
            - name: pricing-signals-expire-cronjob
              image: udaan.azurecr.io/pricing-jobs:latest
              resources:
                limits:
                  memory: 3072Mi
                  cpu: 2000m
                requests:
                  memory: 3072Mi
                  cpu: 1000m
              args:
                - java
                - -cp
                - /jars/*
                - com.udaan.pricing.jobs.signals.ExpiredSignalRepriceJob
          restartPolicy: Never
