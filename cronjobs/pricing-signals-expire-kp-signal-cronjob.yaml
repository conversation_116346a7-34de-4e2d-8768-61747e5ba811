apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: pricing-signals-expire-kp-signal-cronjob
  namespace: VAR_KUBE_ENV
  labels:
    app: pricing-signals-expire-kp-signal-cronjob
    config: udaan
spec:
  schedule: "30 9 * * *" # daily at 3:00 PM
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            config: udaan
        spec:
          containers:
            - name: pricing-signals-expire-kp-signal-cronjob
              image: udaan.azurecr.io/pricing-jobs:latest
              resources:
                limits:
                  memory: 3072Mi
                  cpu: 2000m
                requests:
                  memory: 3072Mi
                  cpu: 1000m
              args:
                - java
                - -cp
                - /jars/*
                - com.udaan.pricing.jobs.signals.ExpireKpSignalJob
          restartPolicy: Never
