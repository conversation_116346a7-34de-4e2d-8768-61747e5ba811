apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: pricing-listing-ssc-price-sync-job
  namespace: VAR_KUBE_ENV
  labels:
    app: pricing-listing-ssc-price-sync-job
    config: udaan
spec:
  schedule: "30 20 * * *"  #Everyday 02:00 AM.
  concurrencyPolicy: Allow
  startingDeadlineSeconds: 200 #(CurrentTime in ms - last 100th) job time should be less than startingDeadlineSeconds https://github.com/kubernetes/kubernetes/blob/270b66fb94747ed8eb296b2626e81b1fa648c306/pkg/controller/cronjob/utils.go#L94
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            config: udaan
        spec:
          containers:
            - name: pricing-listing-ssc-price-sync-job
              image: udaan.azurecr.io/pricing-jobs:latest
              resources:
                limits:
                  memory: 3Gi
                  cpu: 500m
                  ephemeral-storage: 1Gi
                requests:
                  memory: 1Gi
                  cpu: 400m
                  ephemeral-storage: 512Mi
              args:
                - java
                - -cp
                - /jars/*
                - com.udaan.pricing.jobs.ListingsSscPriceSyncJob
          restartPolicy: Never
