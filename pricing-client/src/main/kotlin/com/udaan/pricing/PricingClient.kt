package com.udaan.pricing

import com.udaan.common.client.UdaanClientConfig
import com.udaan.common.client.UdaanServiceClient
import com.udaan.common.client.UdaanServiceRequest
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.network.DemandClusterLocationRes
import com.udaan.pricing.network.DemandClusterLocations
import com.udaan.pricing.network.GetTerritoriesRequest
import com.udaan.pricing.network.TerritoryType
import com.udaan.pricing.network.WarehouseDetails
import com.udaan.pricing.portfolioitem.CreateTradingPortfolioItemRequest
import com.udaan.pricing.portfolioitem.TradingPortfolioItem
import com.udaan.pricing.signalcreation.CreateSignalResponse
import com.udaan.pricing.signalcreation.RawSignalInput
import com.udaan.pricing.signaldeletion.DeleteSignalRequest
import com.udaan.pricing.signaldeletion.DeleteSignalResponse
import com.udaan.pricing.variable.requestreponse.BmtDataResponse
import com.udaan.pricing.variable.requestreponse.GetResolvedValuesRequest
import com.udaan.pricing.variable.requestreponse.GetResolvedValuesResponse
import com.udaan.pricing.variable.requestreponse.JitVendorBestPriceCityGidResponse
import com.udaan.pricing.variable.requestreponse.SourcingModel

const val basePath = "/v1/price"

class PricingClient(config: UdaanClientConfig) : UdaanServiceClient(config) {

    fun getPriceForListing(listingId: String) = getResource<List<PriceForListing>>("$basePath/${listingId}")

    fun addPrice(priceRequest: PriceRequest) = postResourceWithBody<Unit>(
        "$basePath/",
        priceRequest
    )

    fun getContextualPrice(
        listingId: String,
        saleUnitId: String,
        transactionContext: TransactionContext? = null,
        buyerContext: BuyerContext? = null,
        fetchInactive: Boolean = false,
        fetchRiderDetails: Boolean = false
    ) = postResourceWithBody<PriceForListing>(
        "$basePath/${listingId}/saleunit/$saleUnitId?fetchInactive=$fetchInactive&fetchRiderDetails=$fetchRiderDetails",
        ContextualPriceRequest(transactionContext, buyerContext)
    )


    fun getContextualPrice(
        listingId: String,
        transactionContext: TransactionContext? = null,
        buyerContext: BuyerContext? = null,
        fetchInactive: Boolean = false,
        fetchRiderDetails: Boolean = false
    ) = postResourceWithBody<List<PriceForListing>>(
        "$basePath/${listingId}?fetchInactive=$fetchInactive&fetchRiderDetails=$fetchRiderDetails",
        ContextualPriceRequest(transactionContext, buyerContext)
    )

    fun getContextualPriceWithPromotions(
        listingId: String,
        buyerContext: BuyerContext,
        transactionContext: TransactionContext? = null,
        fetchInactive: Boolean = false,
        fetchRiderDetails: Boolean = false,
        fetchRiderAdditionalDetails: Boolean = true
    ) = postResourceWithBody<List<PriceForListing>>(
        "$basePath/${listingId}?fetchInactive=$fetchInactive&fetchRiderDetails=$fetchRiderDetails&fetchRiderAdditionalDetails=$fetchRiderAdditionalDetails",
        ContextualPriceRequest(
            transactionContext = transactionContext,
            buyerContext = buyerContext
        )
    )

    fun getContextualPriceWithCluster(
        listingId: String,
        transactionContext: TransactionContext? = null,
        buyerContext: BuyerContext? = null,
        fetchInactive: Boolean = false,
        fetchRiderDetails: Boolean = false,
        cluster: String? = null
    ) = postResourceWithBody<List<PriceForListing>>(
        "$basePath/${listingId}?fetchInactive=$fetchInactive&fetchRiderDetails=$fetchRiderDetails",
        ContextualPriceRequest(transactionContext = transactionContext, buyerContext = buyerContext, cluster = cluster)
    )

    fun getPriceMulti(
        listingInfo: List<ListingInfo>,
        transactionContext: TransactionContext? = null,
        buyerContext: BuyerContext? = null,
        fetchInactive: Boolean = false,
        fetchRiderDetails: Boolean = false
    ) = postResourceWithBody<List<PriceForListing>>(
        "$basePath/multi?fetchInactive=$fetchInactive&fetchRiderDetails=$fetchRiderDetails",
        PriceMultiReq(listingInfo, ContextualPriceRequest(transactionContext, buyerContext))
    )

    fun getPriceMultiV2(
        listingInfo: List<ListingInfo>,
        transactionContext: TransactionContext? = null,
        buyerContext: BuyerContext? = null,
        fetchInactive: Boolean = false,
        fetchRiderDetails: Boolean = false,
        orderReadyForCheckout: Boolean = false
    ) = postResourceWithBody<List<PriceForListing>>(
        "$basePath/multi/v2?fetchInactive=$fetchInactive&fetchRiderDetails=$fetchRiderDetails&orderReadyForCheckout=$orderReadyForCheckout",
        PriceMultiReq(listingInfo, ContextualPriceRequest(transactionContext, buyerContext))
    )

    fun getContextualPriceWithRefId(
        listingId: String,
        saleUnitId: String,
        transactionContext: TransactionContext? = null,
        buyerContext: BuyerContext? = null,
        refId: String,
        fetchInactive: Boolean = false,
        fetchRiderDetails: Boolean = false
    ) = postResourceWithBody<PriceForListing>(
        "$basePath/${listingId}/saleunit/$saleUnitId?fetchInactive=$fetchInactive&fetchRiderDetails=$fetchRiderDetails",
        ContextualPriceRequest(transactionContext, buyerContext, refId)
    )

    fun getContextualPriceWithRefIdV2(
        listingId: String,
        saleUnitId: String,
        transactionContext: TransactionContext? = null,
        buyerContext: BuyerContext? = null,
        refId: String,
        fetchInactive: Boolean = false,
        fetchRiderDetails: Boolean = false,
        fetchRiderAdditionalDetails: Boolean = false,
        packagingType: String = ""
    ) = postResourceWithBody<PriceForListing>(
        "$basePath/${listingId}/saleunit/$saleUnitId" +
                "?fetchInactive=$fetchInactive" + "&fetchRiderDetails=$fetchRiderDetails" +
                "&fetchRiderAdditionalDetails=$fetchRiderAdditionalDetails" + "&packagingType=$packagingType",
        ContextualPriceRequest(transactionContext, buyerContext, refId)
    )

    fun getMetaNull() = getResource<List<RawInfoListing>>("$basePath/meta-null")

    fun sync(listingId: String) = postResource<Unit>("$basePath/sync/$listingId")
    
    fun getBasePriceForListingSalesUnit(
        listingId: String,
        salesUnitId: String,
        fetchInactive: Boolean = false
    ): UdaanServiceRequest<BasePrice> {
        val queryParams = mutableListOf<Pair<String, String>>()
        queryParams.add(Pair("fetchInactive", fetchInactive.toString()))
        return getResource(
            path = "$basePath/base-price/listing/$listingId/sales-unit/$salesUnitId",
            queryParams = *queryParams.toTypedArray()
        )
    }

    fun getIndexingPriceForListingId(
        listingId: String,
        fetchInactive: Boolean
    ) = getResource<List<PriceForListing>>(
        path = "$basePath/listing-index-price/$listingId",
        queryParams = *arrayOf(
            Pair("fetchInactive", fetchInactive.toString())
        )
    )

    fun deleteAllPricesForListingSalesUnitId(
        listingId: String,
        salesUnitId: String
    ) = deleteResource<PriceDeleteResponseDto>(
        path = "$basePath/delete-all-price/$listingId/sales-unit/$salesUnitId"
    )

    fun getTradingPortfolioTaggingForEntityAndLocation(
        catalogEntity: String,
        locationValue: String
    ): UdaanServiceRequest<TradingPortfolioItem> {
        return getResource<TradingPortfolioItem>(
            path = "/trading-portfolio-items/$catalogEntity/$locationValue"
        )
    }

    fun createTradingPortfolioItem(
        createTradingPortfolioItemRequest: CreateTradingPortfolioItemRequest
    ): UdaanServiceRequest<TradingPortfolioItem> {
        return postResourceWithBody(
            path = "/trading-portfolio-items/",
            requestBody = createTradingPortfolioItemRequest
        )
    }


    /*------- signals v2 apis start here -------- */
    fun pushRawInputForSignalCreation(rawSignalInput: RawSignalInput) = postResourceWithBody<CreateSignalResponse>(
        path = "/signal", requestBody = rawSignalInput
    )

    fun pushInputForSignalDeletion(
        deleteSignalRequest: DeleteSignalRequest
    ) = postResourceWithBody<DeleteSignalResponse>(
        path = "/signal/delete", requestBody = deleteSignalRequest
    )

    fun resolveValuesForProductGroupV2(
        productGroupId: String,
        locationType: LocationType,
        locationValue: String,
        getResolvedValuesRequest: GetResolvedValuesRequest
    ) = postResourceWithBody<GetResolvedValuesResponse>(
        path = "/variable/v2/values/group/$productGroupId/" +
                "location/${locationType.name}/${locationValue.uppercase()}",
        requestBody = getResolvedValuesRequest
    )

    fun resolveBmtValuesForProductGroup(
        productGroupId: String,
        locationType: LocationType,
        locationValue: String,
        sourcingModel: SourcingModel
    ) = getResourceV2<BmtDataResponse>(
        path = "/variable/bmt-values/group/$productGroupId/location/$locationType/$locationValue",
        queryParams = arrayOf(
            Pair("sourcingModel", sourcingModel.name)
        )
    )

    fun getJitVendorBestPriceForCityGid(
        productGroupId: String,
        city: String,
    ) = getResourceV2<JitVendorBestPriceCityGidResponse>(
        path = "/variable/jit/group/$productGroupId/city/$city"
    )
    /*------- signals v2 apis end here -------- */

    /* ------- network apis start here -------- */
    fun getAnchorCityForLocation(locationName: String) =
        getResource<String>(
            path = "/demand-cluster/anchor-city",
            queryParams = *arrayOf("locationName" to locationName)
        )

    fun getLocationsForAnchorCity(anchorCity : String) =
        getResource<List<DemandClusterLocations>>("/demand-cluster/anchor-city/${anchorCity}/locations")

    fun getWarehousesForAnchorCity(anchorCity : String) =
        getResource<List<WarehouseDetails>>("/demand-cluster/anchor-city/${anchorCity}/warehouses")

    fun getCityForLocation(locationName : String) =
        getResource<String>("/demand-cluster-locations/${locationName}/city")

    fun getWarehousesForLocation(locationName : String) =
        getResource<List<WarehouseDetails>>("/demand-cluster-locations/${locationName}/warehouses")

    fun getLocationsForCity(city : String) =
        getResource<List<DemandClusterLocations>>("/demand-cluster-locations/city/${city}/locations")

    fun getLocationsForWarehouse(warehouseId : String) =
        getResource<List<DemandClusterLocations>>("/demand-cluster-locations/warehouse/${warehouseId}/locations")

    fun getLocationDetails(locationName : String) =
        getResource<DemandClusterLocationRes>("/demand-cluster-locations/${locationName}")

    fun getAllLocationNames() = getResource<List<String>>(
        path = "/demand-cluster-locations/all-location-names"
    )

    fun getTerritoriesForLocation(getTerritoryRequest: GetTerritoriesRequest) =
        postResourceWithBody<Map<TerritoryType, String?>>(
            path = "/v1/territories/location",
            requestBody = getTerritoryRequest
        )
    /*------- network apis end here -------- */
}
