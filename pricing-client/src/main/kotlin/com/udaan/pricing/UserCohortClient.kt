package com.udaan.pricing

import com.udaan.common.client.UdaanClientConfig
import com.udaan.common.client.UdaanServiceClient

const val userCohortBasePath = "/v1/user-cohort"

@Deprecated("UserCohortClient is deprecated. Please reach out to pricing team if there is a need to use it")
class UserCohortClient(config: UdaanClientConfig) : UdaanServiceClient(config) {

    fun createUserCohort(userCohortCreateReq: UserCohortCreateReq) = postResourceWithBody<UserCohort>("$userCohortBasePath/",userCohortCreateReq)
    fun deleteUserCohort(sellerOrgId: String?,userCohortType: UserCohortType,
                         orgId: String,cohortName:String) = deleteResource<UserCohort>("$userCohortBasePath/",
                        "orgId" to orgId, "cohortName" to cohortName,"sellerOrgId" to sellerOrgId,"userCohortType" to userCohortType.name)
    fun getAllCohort()=getResource<List<String>>("$userCohortBasePath/getAll")

}
