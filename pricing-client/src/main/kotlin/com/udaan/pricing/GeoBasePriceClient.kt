package com.udaan.pricing

import com.udaan.common.client.UdaanClientConfig
import com.udaan.common.client.UdaanServiceClient

class GeoBasePriceClient(config: UdaanClientConfig) : UdaanServiceClient(config) {
    companion object {
        private const val BASE_PATH = "/v1/geo-base-price"
    }

    fun upsertGeoPrice(req: UpsertGeoLocationBasePriceRequest) = postResourceWithBody<GeoLocationBasePriceResponse>(
            "$BASE_PATH/upsert",
            req
    )

    fun deleteGeoBasePrice(req: DeleteGeoBasePriceRequest) = postResourceWithBody<PriceDeleteResponseDto>(
        "$BASE_PATH/delete",
        req
    )

    fun getGeoBasePriceAll(listingId: String, salesUnitId: String?) = getResource<List<GeoLocationBasePriceResponse>>(
            "$BASE_PATH/all",
            Pair("listingId", listingId),
            Pair("salesUnitId", salesUnitId)
    )

    fun changeGeoListingState(id: String, req: GeoLocationBasePriceStateChangeRequest)=
            putResourceWithBody<GeoLocationBasePriceResponse>("$BASE_PATH/${id}/change-state", req)

    /**
     * This is for special cases where we don't want to fallback WH locations to base city
     * Please don't use this client to deactivate the geo base price, for that use changeGeoListingState
     */
    fun updateEmptyActiveGeoBasePrice(
        updateEmptyActiveGeoBasePriceRequest: UpdateEmptyActiveGeoBasePriceRequest
    ) = putResourceWithBody<Unit>(
        "$BASE_PATH/update-empty-active-geo-price",
        updateEmptyActiveGeoBasePriceRequest
    )

    fun getDCsHavingActivePriceForFoodListing(
        listingId: String
    ) = getResource<DCsHavingActivePriceResponse>(
        "$BASE_PATH/dc-with-active-price-food/$listingId"
    )

}
