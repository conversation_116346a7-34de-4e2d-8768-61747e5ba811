package com.udaan.pricing

import com.udaan.common.client.UdaanClientConfig
import com.udaan.common.client.UdaanServiceClient

class LotPricingClient(config: UdaanClientConfig) : UdaanServiceClient(config) {
    fun getLotPriceMulti(
        getLotPriceMulti: List<GetLotPriceRequest>,
        orderReadyForCheckout: Boolean = false
    ) =
        postResourceWithBody<List<PriceForListing?>>(
            "$basePath/get-lot?orderReadyForCheckout=$orderReadyForCheckout",
            getLotPriceMulti
        )

    fun getLotPriceMultiWithScheme(
        getLotPriceWithSchemeRequest: List<GetLotPriceWithSchemeRequest>,
        orderReadyForCheckout: Boolean = false
    ) = postResourceWithBody<List<PriceForListing?>>(
        "$basePath/get-lot-with-scheme?orderReadyForCheckout=$orderReadyForCheckout",
        getLotPriceWithSchemeRequest
    )

}