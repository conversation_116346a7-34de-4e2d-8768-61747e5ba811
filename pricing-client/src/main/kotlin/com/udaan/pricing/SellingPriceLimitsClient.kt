package com.udaan.pricing

import com.udaan.common.client.UdaanClientConfig
import com.udaan.common.client.UdaanServiceClient
import java.math.BigDecimal

const val sellingPriceLimitsBasePath = "/v1/sellingPriceLimits"

@Deprecated("This client is deprecated")
class SellingPriceLimitsClient(config: UdaanClientConfig) : UdaanServiceClient(config) {

    fun checkIntentPriceForPriceLimits(productId: String, price: BigDecimal) =
        postResourceWithBody<Unit>(
            "$sellingPriceLimitsBasePath/checkIntentPriceForPriceLimits",
            requestBody = SellingPriceLimitsCheckRequest(productId = productId, price = price)
        )
}
