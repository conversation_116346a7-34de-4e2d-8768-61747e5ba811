package com.udaan.pricing

import com.udaan.common.client.UdaanClientConfig
import com.udaan.common.client.UdaanServiceClient

class GeoPricingClient(config: UdaanClientConfig): UdaanServiceClient(config) {

    fun getAllRegions() = getResource<RegionResponse>("/v1/geo-pricing/all-regions")

    fun queryGeoPricingForSeller(sellerOrgId: String, listingId: String? = null, cascadeDown: Boolean = true, casecadeUp: Boolean = false) = getResource<GeoPricingResponse>(
        "/v1/geo-pricing/seller/$sellerOrgId/query", "listingId" to listingId, "cascadeDown" to cascadeDown.toString(), "cascadeUp" to casecadeUp.toString())

    fun createGeoPricing(geoPricingReq: GeoPricingCreateReq) = postResourceWithBody<GeoPricing>(
        "/v1/geo-pricing/create",
        requestBody = geoPricingReq
    )

    fun getNegativeGeo(sellerOrgId: String, listingId: String, salesUnitId: String , orgId: String ) = getResource<List<GeoPricing>>(
            "/v1/geo-pricing/negative", "listingId" to listingId, "salesUnitId" to salesUnitId, "orgId" to orgId)


    fun updateGeoPricing(geoPricingReq: GeoPricingUpdateReq) = postResourceWithBody<GeoPricing>(
        "/v1/geo-pricing/update",
        requestBody = geoPricingReq
    )

    fun upsertGeoPricing(geoPricingReq: GeoPricingUpsertReq) = postResourceWithBody<GeoPricing>(
        "/v1/geo-pricing/upsert",
        requestBody = geoPricingReq
    )

    fun getGeoPriceByIds(sellerOrgId: String, geoPriceIds: List<String>) = getResource<GeoPricingResponse>(
        path = "/v1/geo-pricing/seller/$sellerOrgId",
        queryParams = *listOf("geoPriceIds" to geoPriceIds.joinToString(",")).toTypedArray()
    )

    fun getGeoByListingIdAndSU(listingId: String, salesUnitId: String , orgId: String ) = getResource<List<GeoPricing>>(
            "/v1/geo-pricing/listingAndSu", "listingId" to listingId, "salesUnitId" to salesUnitId, "orgId" to orgId)

}
