package com.udaan.pricing

fun List<PriceForListing>.minPriceInPaisa()
    = this
    .map {
        it.prices
    }
    .flatten()
    .map {
        it.priceInPaisa.defaultPrice
    }.min()

fun List<PriceForListing>.minPricePerKgInPaisa()
    = this
    .map {
        it.prices
    }
    .flatten()
    .mapNotNull {
        it.pricePerKgInPaisa?.defaultPrice
    }.min()

fun List<PriceForListing>.maxPriceInPaisa()
    = this
    .map {
        it.prices
    }
    .flatten()
    .map {
        it.priceInPaisa.defaultPrice
    }.max()

fun List<PriceForListing>.maxPricePerKgInPaisa()
    = this
    .map {
        it.prices
    }
    .flatten()
    .mapNotNull {
        it.pricePerKgInPaisa?.defaultPrice
    }.max()

fun List<PriceForListing>.minPriceInPaisaPerSaleUnit()
    = this
    .associateBy {
        it.saleUnitId
    }
    .map { (su, price) ->
        su to price.prices.map { it.priceInPaisa.defaultPrice }.min()
    }
    .toMap()


fun List<PriceForListing>.maxPriceInPaisaPerSaleUnit()
    = this
    .associateBy {
        it.saleUnitId
    }
    .map { (su, price) ->
        su to price.prices.map { it.priceInPaisa.defaultPrice }.max()
    }
    .toMap()

fun PriceForListing?.minPriceInPaisa() = this?.prices?.map { it.priceInPaisa.defaultPrice }?.min() ?: 0

fun PriceForListing?.maxPriceInPaisa() = this?.prices?.map { it.priceInPaisa.defaultPrice }?.max() ?: 0

fun PriceForListing?.minPricePerKgInPaisa() = this?.prices?.map { it.pricePerKgInPaisa?.defaultPrice ?: it.priceInPaisa.defaultPrice }?.min() ?: 0

fun PriceForListing?.maxPricePerKgInPaisa() = this?.prices?.map { it.pricePerKgInPaisa?.defaultPrice ?: it.priceInPaisa.defaultPrice }?.max() ?: 0
