package com.udaan.pricing

import com.udaan.common.client.UdaanClientConfig
import com.udaan.common.client.UdaanServiceClient

class MaxPromotionClient(config: UdaanClientConfig): UdaanServiceClient(config){

    fun upsertMaxPromotion(maxPromotionReq: MaxPromotionReq)  = postResourceWithBody<MaxPromotion>("/v1/max-promotion",maxPromotionReq)
    fun getMaxPromotion(listingId:String) = getResource<List<MaxPromotion>>("/v1/max-promotion/$listingId")
    fun deleteMaxPromotion(listingId:String) = deleteResource<Unit>("/v1/max-promotion/$listingId")
}