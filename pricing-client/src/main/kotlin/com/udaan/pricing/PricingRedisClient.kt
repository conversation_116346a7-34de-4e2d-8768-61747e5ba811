package com.udaan.pricing

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.udaan.common.client.UdaanClientConfig
import com.udaan.common.client.UdaanServiceClient
import com.udaan.pricing.portfolioitem.TradingPortfolioItem
import com.udaan.resources.ResourceBuilder
import com.udaan.resources.cache.RedisCache2
import com.udaan.resources.cache.RedisCacheConfig
import java.util.concurrent.*
import javax.ws.rs.NotFoundException

class PricingRedisClient(config: UdaanClientConfig) : UdaanServiceClient(config) {

    private val objectMapper = jacksonObjectMapper().also {
        it.disable(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES)
        it.registerModule(JavaTimeModule())
        it.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
        it.configure(SerializationFeature.WRITE_DATE_TIMESTAMPS_AS_NANOSECONDS, false)
        it.configure(DeserializationFeature.READ_DATE_TIMESTAMPS_AS_NANOSECONDS, false)
    }

    private val pricingClient = PricingClient(config)
    private val redisClient = ResourceBuilder.redisClient("pricing-engine-onprem").buildLettuce6Client()

    private val tradingPortfolioItemCache = tradingPortfolioItemCache()

    private fun tradingPortfolioItemCache(): RedisCache2<TradingPortfolioItem?> {
        return RedisCache2.create(
            name = "pricing-automation-trading-portfolio-item",
            lettuceClient = null,
            localCacheConfig = null,
            redisCacheConfig = RedisCacheConfig(
                expireAfterWriteMs = TimeUnit.MINUTES.toMillis(30),
                refreshAfterWriteMs = TimeUnit.MINUTES.toMillis(15),
                storeNull = true
            ),
            objectMapper = objectMapper,
            javaType = objectMapper.typeFactory.constructType(TradingPortfolioItem::class.java),
            lettuce6Client = redisClient
        )
    }


    fun getTradingPortfolioTaggingByEntityAndLocation(
        catalogEntity: String,
        locationValue: String
    ): CompletableFuture<TradingPortfolioItem?> {
        return tradingPortfolioItemCache.get("$catalogEntity:$locationValue".uppercase()) {
            pricingClient.getTradingPortfolioTaggingForEntityAndLocation(
                catalogEntity = catalogEntity,
                locationValue = locationValue
            ).execute().thenApply {
                it ?: throw NotFoundException("Trading Portfolio tagging" +
                        " not found for $catalogEntity and $locationValue")
            }
        }
    }

}

