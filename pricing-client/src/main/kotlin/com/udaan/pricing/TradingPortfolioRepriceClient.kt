package com.udaan.pricing

import com.udaan.common.client.UdaanClientConfig
import com.udaan.common.client.UdaanServiceClient

class TradingPortfolioRepriceClient(config: UdaanClientConfig) : UdaanServiceClient(config) {
    companion object {
        private const val BASE_PATH = "/v1/trading-price"
    }

    /*
     *  TODO("Deprecate and remove the client.")
     */
    @Deprecated("Would be removed once pricing-automation service is deprecated.")
    fun repriceTradingPortfolioItem(
        locationValue: String, locationType: String, catalogEntity: String
    ) = putResource<Unit>(
        "$BASE_PATH/catalogEntity/${catalogEntity}/locationType/${locationType}/locationValue/${locationValue}"
    )
}
