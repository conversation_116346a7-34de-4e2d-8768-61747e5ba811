{
    // === Java Configuration ===
    "java.jdt.ls.vmargs": "-Xmx2G -XX:+UseG1GC",
    "java.compile.nullAnalysis.mode": "automatic",
    "java.maven.downloadSources": true,
    
    // === Java Project Settings ===
    "java.project.sourcePaths": [
        "src/main/kotlin",
        "src/main/java"
    ],

    // === Java Test Config ===
    "java.test.config": {
        "default": {
            "workingDirectory": "${workspaceFolder}",
            "vmargs": [
                "-ea"
            ],
            "testRoots": [
                "**/src/test/kotlin",
                "**/src/test/java"
            ],
            "disableTestClasspathFlag": true
        }
    },

    "java.test.defaultConfig": "default",

    // === Language-Specific Settings ===
    "[kotlin]": {
        "editor.defaultFormatter": "fwcd.kotlin",
        "editor.tabSize": 4,
        "editor.insertSpaces": true
    },
    "[java]": {
        "editor.insertSpaces": true,
        "editor.tabSize": 4 
    },
    
    // === Files and Explorer ===
    "files.watcherExclude": {
        "**/target/**": true,
        "**/node_modules/**": true
    },
    "search.exclude": {
        "**/target": true
    },
    "files.exclude": {
        "target": true,
        ".idea": true
    },
    "java.configuration.updateBuildConfiguration": "interactive",
    "java.trace.server": "verbose"
}
