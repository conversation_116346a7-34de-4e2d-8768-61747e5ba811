// To run tasks - Open Command Palette (Cmd + Shift + P)
// Select "Tasks: Run Task" →  Select task name ("compile-service")

{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "compile-service",
            "type": "shell",
            "command": "mvn",
            "args": [
                "compile",
                "-pl",
                "pricing-service",
                "-am"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "options": {
                "env": {
                    "JAVA_HOME": "${env:JAVA_HOME}"
                }
            }
        },
        {
            "label": "compile-jobs",
            "type": "shell",
            "command": "mvn",
            "args": [
                "compile",
                "-pl",
                "pricing-jobs",
                "-am"
            ],
            "group": "build",
            "options": {
                "env": {
                    "JAVA_HOME": "${env:JAVA_HOME}"
                }
            }
        },
        {
            "label": "compile-events",
            "type": "shell",
            "command": "mvn",
            "args": [
                "compile",
                "-pl",
                "pricing-events",
                "-am"
            ],
            "group": "build",
            "options": {
                "env": {
                    "JAVA_HOME": "${env:JAVA_HOME}"
                }
            }
        }
    ]
}
