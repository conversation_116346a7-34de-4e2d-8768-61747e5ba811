{"version": "0.2.0", "configurations": [{"type": "java", "name": "Service: Pricing", "request": "launch", "mainClass": "com.udaan.pricing.service.PricingService", "projectName": "pricing-service", "preLaunchTask": "compile-service", "vmArgs": "-Dudaan.env=prod"}, {"type": "java", "name": "Service: Pricing Event Consumer", "request": "launch", "mainClass": "com.udaan.pricing.events.PricingEventConsumerApplication", "projectName": "pricing-events", "preLaunchTask": "compile-events", "vmArgs": "-Dudaan.env=prod"}, {"type": "java", "name": "Job: ListingsSscPriceSync", "request": "launch", "mainClass": "com.udaan.pricing.jobs.ListingsSscPriceSyncJob", "projectName": "pricing-jobs", "preLaunchTask": "compile-jobs", "vmArgs": "-Dudaan.env=prod"}, {"type": "java", "name": "Job: RenameCluster", "request": "launch", "mainClass": "com.udaan.pricing.jobs.adhocJobs.RenameClusterJobRunner", "projectName": "pricing-jobs", "preLaunchTask": "compile-jobs", "vmArgs": "-Dudaan.env=prod"}]}