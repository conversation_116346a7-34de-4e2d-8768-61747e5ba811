package com.udaan.pricing.jobs.contract

import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.core.helpers.CatalogHelper
import com.udaan.pricing.core.models.contracts.Contract
import com.udaan.pricing.core.svcinterfaces.UserSvcInterface
import com.udaan.proto.models.ModelV1
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.Instant
import java.time.ZoneId

class ContractAlertsHelper @Inject constructor(
    private val userServiceInterface: UserSvcInterface,
    private val catalogHelper: CatalogHelper
) {

    private val log by logger()

    // formatter for dd/MM/yyyy HH:mm:ss
    private val PREFERRED_FORMAT = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")

    suspend fun getBuyerDetails(contracts: List<Contract>): Map<String, ModelV1.OrgAccount?> {
        return contracts.groupBy { it.buyerOrgId }.map {
            it.key to userServiceInterface.getBuyer(it.key.trim())
        }.toMap()
    }


    suspend fun getCatalogDetails(contracts: List<Contract>): Map<String, String?> {
        return contracts.groupBy { it.catalogEntityId }.map {
            it.key to catalogHelper.getCatalogTitle(
                catalogEntity = CatalogEntityType.valueOf(it.value.first().contractCatalogEntity.name),
                catalogEntityId = it.key
            )
        }.toMap()
    }

    fun convertEpochToReadableFormat(epoch: Long?, zone: ZoneId = ZoneId.systemDefault()): String? {
        return epoch?.let {
            try {
                val dt = LocalDateTime.ofInstant(Instant.ofEpochMilli(epoch), zone)
                dt.format(PREFERRED_FORMAT)
            } catch (e: Exception) {
                log.error("Unable to convert $epoch to date time format with error", e)
                null
            }
        }
    }
}