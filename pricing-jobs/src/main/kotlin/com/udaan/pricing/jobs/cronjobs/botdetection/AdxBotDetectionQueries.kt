package com.udaan.pricing.jobs.cronjobs.botdetection

import org.slf4j.Marker
import org.slf4j.MarkerFactory
import kotlin.reflect.KClass

internal data class BotDetectionAdxQuerySyncJob(
    val jobName: String,
    val query: String,
    val dpOutputTable: String,
    val dataKlass: KClass<*>
) {
    fun withContext(): Marker = MarkerFactory.getMarker(jobName)
}

internal val jobs = arrayOf(
//    BotDetectionAdxQuerySyncJob(
//        jobName = "adxClientIpUserId",
//        dpOutputTable = "default.bot_detect_ip_user_id",
//        dataKlass = BotAdxClientIpToUserData::class,
//        query = """
//            let probe_start_timestamp = datetime_local_to_utc(startofday(now(), -1), 'Asia/Kolkata');
//            let probe_end_timestamp = datetime_local_to_utc(startofday(now()), 'Asia/Kolkata');
//            let temp  = view() { HttpRequests
//            | where Timestamp >= probe_start_timestamp and Timestamp < probe_end_timestamp
//            | where Container == 'preorderflow-gateway-service' and Host has "udaan.com"
//            | extend rootLoc = tostring(Properties['rootLoc']), userId = tostring(Properties['rootPrincipal']), appId = tostring(Properties['appId']), reqDate = format_datetime(datetime_utc_to_local(Timestamp, 'Asia/Kolkata'), 'yyyy-MM-dd')
//            | where isnotempty(userId)
//            | summarize req = count() by reqDate, ClientIp, userId
//            };
//            temp
//            | summarize a = count() by ClientIp
//            | join kind=inner temp on ClientIp
//            | project req_date = reqDate, client_ip = ClientIp, user_id = userId, user_count = a, requests = req
//        """.trimIndent()
//    ),
    BotDetectionAdxQuerySyncJob(
        jobName = "adxClientIpUserAgent",
        dpOutputTable = "default.bot_detect_ip_user_agent",
        dataKlass = BotAdxClientIpUserAgent::class,
        query = """
            let probe_start_timestamp = datetime_local_to_utc(startofday(now(), -1), 'Asia/Kolkata');
            let probe_end_timestamp = datetime_local_to_utc(startofday(now()), 'Asia/Kolkata');
            HttpRequests
            | where Timestamp >= probe_start_timestamp and Timestamp < probe_end_timestamp
            | where Container in ('preorderflow-gateway-service', 'pagebuilder-gateway-service', 'api-server') and Host has "udaan.com"
            | extend userId = tostring(Properties['rootPrincipal']), agent = tostring(split(UserAgent, '/')[0]), req_date = format_datetime(datetime_utc_to_local(Timestamp, 'Asia/Kolkata'), 'yyyy-MM-dd')
            | extend java_python = (tostring(UserAgent) has 'java' or tostring(UserAgent) has 'python')
            | extend not_android_udaan_mozilla = (tostring(UserAgent) !has 'android' and tostring(UserAgent) !has 'udaan' and tostring(UserAgent) !has 'mozilla')
            | where isnotempty(userId)
            | summarize requests = count() by req_date, client_ip = ClientIp, user_id = userId, agent, java_python, not_android_udaan_mozilla
        """.trimIndent()
    )

)


data class BotAdxClientIpToUserData(
    val req_date: String,
    val client_ip: String,
    val user_id: String,
    val user_count: Int,
    val requests: Long
)

data class BotAdxClientIpUserAgent(
    val req_date: String,
    val client_ip: String,
    val user_id: String,
    val agent: String,
    val java_python: Boolean,
    val not_android_udaan_mozilla: Boolean,
    val requests: Long
)
