package com.udaan.pricing.jobs.helpers

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.udaan.common.server.awaitWithTimeout
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.pricing.core.models.contracts.ContractCatalogEntity
import com.udaan.pricing.core.svcinterfaces.DpServiceInterface
import com.udaan.pricing.core.svcinterfaces.FpCatalogSvcInterface
import com.udaan.pricing.jobs.models.contracts.ContractQuoteTaggingResponse
import com.udaan.resources.RedisLettuce6Client

class ContractQuoteTaggingHelper @Inject constructor(
    private val dpServiceInterface: DpServiceInterface,
    private val fpCatalogSvcInterface: FpCatalogSvcInterface,
    private val redisClient: RedisLettuce6Client,
    private val objectMapper: ObjectMapper
) {

    private val contractQuoteTaggingProbeId = "4q4zbm"
    private val redisKeyTimeOutConfigInSeconds = 172800 // 2 days
    private val preFixKey = "contract_quote_tagging_"

    private val log by logger()

    suspend fun setContractQuoteTaggingInRedis() {
        val contractQuoteTaggingList = dpServiceInterface.getProbeData<ContractQuoteTaggingResponse>(
            probeId = contractQuoteTaggingProbeId
        )
        contractQuoteTaggingList.parallelMap {
            redisClient.asyncCommands.setex(
                "$preFixKey${it.contractCatalogEntityId}_${it.city}".lowercase(),
                redisKeyTimeOutConfigInSeconds.toLong(),
                objectMapper.writeValueAsString(it)
            )
            log.info("Set contract quote tagging for ${it.contractCatalogEntityId} and ${it.city} in redis.")
        }
    }

    private suspend fun fetchContractQuoteTaggingFromRedis(
        contractCatalogEntityId: String,
        city: String
    ): ContractQuoteTaggingResponse? {
        val value =
            redisClient.asyncCommands.get(
                "$preFixKey${contractCatalogEntityId}_${city}".lowercase()
            ).toCompletableFuture().awaitWithTimeout(timeoutMillis = 500, timeoutResponse = null)
        return value?.let { objectMapper.readValue(it, ContractQuoteTaggingResponse::class.java) }
    }

    suspend fun getContractQuoteTagging(
        contractCatalogEntity: String,
        contractCatalogEntityId: String,
        city: String
    ): ContractQuoteTaggingResponse? {
        log.info("Getting contract quote tagging for $contractCatalogEntityId and $city and $contractCatalogEntity")
        val contractQuoteTaggingForCatalogId = fetchContractQuoteTaggingFromRedis(contractCatalogEntityId, city)
        if (contractQuoteTaggingForCatalogId == null
            && ContractCatalogEntity.LISTING_ID.name.equals(contractCatalogEntity, ignoreCase = true)
        ) {
            /**
             * if listing level tagging is not available pick GID level tagging.
             */
            fpCatalogSvcInterface.fetchGroupIdFromListingId(contractCatalogEntityId)?.let { groupId ->
                return fetchContractQuoteTaggingFromRedis(groupId, city)
            }
        }
        return contractQuoteTaggingForCatalogId
    }
}
