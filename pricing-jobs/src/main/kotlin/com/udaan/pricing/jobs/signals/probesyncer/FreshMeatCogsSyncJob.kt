package com.udaan.pricing.jobs.signals.probesyncer

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.google.inject.Guice
import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.JobScope
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.core.managers.signals.SignalWriteManager
import com.udaan.pricing.core.svcinterfaces.DpServiceInterface
import com.udaan.pricing.core.utils.signals.BigDecimalUtil.roundToDefaultScale
import com.udaan.pricing.signalcreation.GenericLidLevelInput
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.jobs.utils.PushGatewayUtil
import java.math.BigDecimal
import kotlin.system.exitProcess

class FreshMeatCogsSyncJob @Inject constructor(
    dpServiceInterface: DpServiceInterface,
    signalWriteManager: SignalWriteManager
) : DPInputProbeSyncer(
    dpServiceInterface,
    signalWriteManager
) {

    companion object {
        private val logger by logger()
        private val injector = Guice.createInjector(PricingCoreModule())
        private val pushGatewayUtil = PushGatewayUtil("FreshMeatCogsSyncJob", logger)

        @JvmStatic
        fun main(args: Array<String>) {
            val freshMeatCogsSyncJob = injector.getInstance(FreshMeatCogsSyncJob::class.java)
            try {
                logger.info("********************** Job STARTED **********************")
                JobScope.runBlocking {
                    pushGatewayUtil.pushJobStarted()
                    pushGatewayUtil.recordStats {
                        freshMeatCogsSyncJob.process()
                    }
                    pushGatewayUtil.pushJobEnded()
                }
                logger.info("********************** Job Completed **********************")
            } catch (e: Throwable) {
                e.printStackTrace()
                logger.error("********************** Job FAILED **********************", e)
                exitProcess(1)
            } finally {
                logger.error("********************** Process End **********************")
                exitProcess(0)
            }
        }
    }

    override val probeId = "fj4c4n"
    override val chunkSize = 25
    override val delayBetweenChunksInMillis = 1000L

    suspend fun process() {
        val freshMeatCogsDPData = getDataFromDataPlatform<FreshMeatCogsDPData>().filter {
            it.cogs != null
        }

        val convertedLidLevelInputs = freshMeatCogsDPData.mapNotNull {
            try {
                val variable = when (it.category.uppercase()) {
                    "FRESH" -> VariableId.FRESH_COGS_WOT_PAISA_UNIT
                    "MEAT" -> VariableId.MEAT_COGS_WOT_PAISA_UNIT
                    else -> {
                        throw IllegalArgumentException("Category ${it.category} is not fresh or meat")
                    }
                }
                GenericLidLevelInput(
                    listingId = it.listingId,
                    variableId = variable,
                    data = BigDecimalValue(
                        value = it.cogs!!.multiply(BigDecimal(100)).roundToDefaultScale()
                    ),
                    metadata = emptyMap(),
                    location = Location(
                        locationType = LocationType.CENTRAL,
                        locationValue = "CENTRAL"
                    ),
                    updatedBy = "DATA_PLATFORM_PROBE"
                )
            } catch (iae: IllegalArgumentException) {
                logger.error("$iae, ignoring entry")
                null
            } catch (ex: Exception) {
                logger.error("Error while converting FreshMeatCogsDPData to GenericLidLevelInput", ex)
                null
            }
        }
        processAndSaveRawInputs(convertedLidLevelInputs)
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class FreshMeatCogsDPData(
        @JsonProperty("listing_id")
        val listingId: String,

        @JsonProperty("cogs")
        val cogs: BigDecimal?,

        @JsonProperty("Category")
        val category: String
    )
}
