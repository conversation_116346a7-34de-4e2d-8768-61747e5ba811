package com.udaan.pricing.jobs.keda.handlers

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.catalog.model.VerticalCategory
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.planning.ChannelType
import com.udaan.planning.SelectionClient
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.commons.StringValue
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.commons.validate
import com.udaan.pricing.core.constants.AsyncJobConstants
import com.udaan.pricing.core.constants.AuthConstants
import com.udaan.pricing.core.helpers.BlobStorageHelper
import com.udaan.pricing.core.helpers.CatalogHelper
import com.udaan.pricing.core.helpers.PricingNetworkHelper
import com.udaan.pricing.core.managers.signals.AsyncJobController
import com.udaan.pricing.core.managers.signals.SignalWriteManager
import com.udaan.pricing.core.managers.signals.VariableManager
import com.udaan.pricing.core.svcinterfaces.FpCatalogSvcInterface
import com.udaan.pricing.core.svcinterfaces.VerticalSvcInterface
import com.udaan.pricing.core.utils.AuthorizationUtils
import com.udaan.pricing.core.utils.signals.BigDecimalUtil.roundToDefaultScale
import com.udaan.pricing.core.utils.signals.BigDecimalUtil.roundValues
import com.udaan.pricing.job.AsyncJob
import com.udaan.pricing.job.AsyncJobStatus
import com.udaan.pricing.jobs.keda.fileutils.FileUtils
import com.udaan.pricing.jobs.models.ParsedDataInfo
import com.udaan.pricing.jobs.models.ProcessedDataInfo
import com.udaan.pricing.signals.GenericMetadata
import com.udaan.pricing.signals.Signal
import com.udaan.pricing.signals.SignalState
import com.udaan.pricing.variable.Variable
import com.udaan.pricing.variable.VariableResolvedValueType
import java.io.File
import java.math.BigDecimal
import javax.ws.rs.BadRequestException

@Singleton
class ManualSignalInputJobHandler @Inject constructor(
    private val asyncJobController: AsyncJobController,
    private val blobStorageHelper: BlobStorageHelper,
    private val fileUtils: FileUtils,
    private val authorizationUtils: AuthorizationUtils,
    private val variableManager: VariableManager,
    private val fpCatalogSvcInterface: FpCatalogSvcInterface,
    private val verticalSvcInterface: VerticalSvcInterface,
    private val catalogHelper: CatalogHelper,
    private val signalWriteManager: SignalWriteManager,
    private val pricingNetworkHelper: PricingNetworkHelper,
    private val selectionClient: SelectionClient
) : AsyncJobHandler() {

    companion object {
        private val logger by logger()
    }

    /**
     * Converts a row to BuyerContractFileInputRow and processes each row to create contract.
     */
    override suspend fun processJob(asyncJob: AsyncJob) {
        try {
            asyncJobController.updateAsyncJobStatusAndMetadata(
                jobId = asyncJob.id,
                status = AsyncJobStatus.IN_PROGRESS,
                updatedBy = "SYSTEM"
            )

            val file = blobStorageHelper.getFileFromBlobStorage(
                containerReference = asyncJob.type.toString().lowercase().replace("_", "-"),
                directoryReference = asyncJob.type.toString().lowercase().replace("_", "-"),
                fileName = asyncJob.id + "_input.xlsx"
            )

            val variableId = asyncJob.jobData.attributesMap["VARIABLE_ID"]
                ?: throw IllegalStateException("Variable ID missing in job ${asyncJob.id}")

            val variable = variableManager.getVariable(variableId)

            val manualSignalsParsedDataInfoList = when (variable.resolvedValueType) {
                VariableResolvedValueType.BIG_DECIMAL -> parseAndConvertBigDecimalInputsToSignal(
                    job = asyncJob,
                    file = file,
                    variable = variable
                )
                VariableResolvedValueType.STRING -> parseAndConvertStringInputsToSignal(
                    job = asyncJob,
                    file = file,
                    variable = variable
                )
                VariableResolvedValueType.LADDER -> parseAndConvertLadderInputsToSignal(
                    job = asyncJob,
                    file = file,
                    variable = variable
                )
            }


            val processedManualSignalInputData = manualSignalsParsedDataInfoList.parallelMap { manualSignalParsedDataInfo ->
                // Process each row and convert to request
                manualSignalParsedDataInfo.dataRequestDTO?.let {
                    try {
                        validateSignalUpdateAccess(it)
                        val createdSignal = signalWriteManager.applyValidationsAndSaveSignal(it)
                        ProcessedDataInfo(
                            row = manualSignalParsedDataInfo.row,
                            parsingExceptionMessage = manualSignalParsedDataInfo.parsingExceptionMessage,
                            processingExceptionMessage = null,
                            processingOutput = ManualSignalFileOutputResponse(
                                output = createdSignal.referenceId
                            )
                        )
                    } catch (e: Exception) {
                        logger.error("Error while creating signal for $it with error {}", e.message)
                        ProcessedDataInfo(
                            row = manualSignalParsedDataInfo.row,
                            parsingExceptionMessage = manualSignalParsedDataInfo.parsingExceptionMessage,
                            processingExceptionMessage = e.message,
                            processingOutput =  ManualSignalFileOutputResponse(
                                output = "N/A"
                            )
                        )
                    }
                } ?: ProcessedDataInfo(
                    row = manualSignalParsedDataInfo.row,
                    parsingExceptionMessage = manualSignalParsedDataInfo.parsingExceptionMessage,
                    processingExceptionMessage = null,
                    processingOutput = ManualSignalFileOutputResponse(
                        output = "N/A"
                    )
                )
            }

            val successfullyProcessedRowCount = processedManualSignalInputData.count {
                it.parsingExceptionMessage == null && it.processingExceptionMessage == null
            }

            val outputFile = fileUtils.updateExcelWithProcessedData(
                jobId = asyncJob.id,
                file = file,
                processedDataInfoList = processedManualSignalInputData
            )

            blobStorageHelper.uploadFileToBlobStorage(
                containerReference = asyncJob.type.toString().lowercase().replace("_", "-"),
                directoryReference = asyncJob.type.toString().lowercase().replace("_", "-"),
                file = outputFile
            )

            asyncJobController.updateAsyncJobStatusAndMetadata(
                jobId = asyncJob.id,
                status = AsyncJobStatus.COMPLETED,
                outputFilePath = outputFile.name,
                attributesMap = mapOf(
                    AsyncJobConstants.TOTAL to manualSignalsParsedDataInfoList.size.toString(),
                    AsyncJobConstants.SUCCESSFUL to successfullyProcessedRowCount.toString(),
                    AsyncJobConstants.FAILED to (manualSignalsParsedDataInfoList.size - successfullyProcessedRowCount).toString()
                ),
                updatedBy = "SYSTEM"
            )
        } catch (e: Exception) {
            logger.error("Error while processing job with error {}", e.message)
            asyncJobController.updateAsyncJobStatusAndMetadata(
                jobId = asyncJob.id,
                status = AsyncJobStatus.FAILED,
                remarks = "Error while processing job with error " + e.message,
                outputFilePath = null,
                updatedBy = "SYSTEM"
            )
        }
    }

    private suspend fun parseAndConvertBigDecimalInputsToSignal(
        job: AsyncJob,
        file: File,
        variable: Variable
    ): List<ParsedDataInfo<Signal>> {
        val bigDecimalInputsParsedDataInfoList = fileUtils.readExcelFile(
            jobId = job.id,
            file = file,
            sheetNum = 0,
            headerNum = 0,
            dataRowNum = 1,
            dataClass = ManualBigDecimalSignalFileInputRow::class
        )

        // Identify duplicate row identifiers
        val duplicateIdentifiers = bigDecimalInputsParsedDataInfoList
            .filter { it.parsingExceptionMessage == null && it.dataRequestDTO != null }
            .groupBy { parsedDataInfo ->
                RowIdentifier(
                    catalogEntity = parsedDataInfo.dataRequestDTO!!.catalogEntity.uppercase(),
                    catalogEntityType = parsedDataInfo.dataRequestDTO.catalogEntityType,
                    locationType = parsedDataInfo.dataRequestDTO.locationType,
                    locationValue = parsedDataInfo.dataRequestDTO.locationValue
                )
            }
            .filter { it.value.size > 1 }
            .keys

        return bigDecimalInputsParsedDataInfoList.map { parsedDataInfo ->
            if (parsedDataInfo.parsingExceptionMessage != null) {
                ParsedDataInfo(
                    row = parsedDataInfo.row,
                    parsingExceptionMessage = parsedDataInfo.parsingExceptionMessage,
                    dataRequestDTO = null
                )
            } else {
                val manualBigDecimalSignalFileInputRow = parsedDataInfo.dataRequestDTO!!
                val rowIdentifier = RowIdentifier(
                    catalogEntity = manualBigDecimalSignalFileInputRow.catalogEntity.uppercase(),
                    catalogEntityType = manualBigDecimalSignalFileInputRow.catalogEntityType,
                    locationType = manualBigDecimalSignalFileInputRow.locationType,
                    locationValue = manualBigDecimalSignalFileInputRow.locationValue
                )

                if (duplicateIdentifiers.contains(rowIdentifier)) {
                    // Mark all duplicate rows as parsing errors
                    ParsedDataInfo(
                        row = parsedDataInfo.row,
                        parsingExceptionMessage = "Duplicate row found with same catalog entity, catalog entity type, location type, and location value",
                        dataRequestDTO = null
                    )
                } else {
                    // Process only distinct rows
                    val signal = Signal(
                        catalogEntity = manualBigDecimalSignalFileInputRow.catalogEntity.uppercase(),
                        catalogEntityType = manualBigDecimalSignalFileInputRow.catalogEntityType,
                        variableId = variable.id.name,
                        signalData = BigDecimalValue(
                            value = manualBigDecimalSignalFileInputRow.inputValue.roundToDefaultScale()
                        ),
                        metadata = GenericMetadata(
                            metadataMap = mapOf(
                                "CREATION_JOB_ID_REF" to job.id
                            )
                        ),
                        location = Location(
                            locationType = manualBigDecimalSignalFileInputRow.locationType,
                            locationValue = manualBigDecimalSignalFileInputRow.locationValue.replace("BBCT", "Bhubaneswar", ignoreCase = true)
                        ),
                        state = SignalState.ACTIVE,
                        createdBy = job.createdBy,
                        updatedBy = job.createdBy
                    )

                    ParsedDataInfo(
                        row = parsedDataInfo.row,
                        parsingExceptionMessage = null,
                        dataRequestDTO = signal
                    )
                }
            }
        }
    }

    private suspend fun parseAndConvertStringInputsToSignal(
        job: AsyncJob,
        file: File,
        variable: Variable
    ): List<ParsedDataInfo<Signal>> {
        val stringInputsParsedDataInfoList = fileUtils.readExcelFile(
            jobId = job.id,
            file = file,
            sheetNum = 0,
            headerNum = 0,
            dataRowNum = 1,
            dataClass = ManualStringSignalFileInputRow::class
        )

        // Identify duplicate row identifiers
        val duplicateIdentifiers = stringInputsParsedDataInfoList
            .filter { it.parsingExceptionMessage == null && it.dataRequestDTO != null }
            .groupBy { parsedDataInfo ->
                RowIdentifier(
                    catalogEntity = parsedDataInfo.dataRequestDTO!!.catalogEntity.uppercase(),
                    catalogEntityType = parsedDataInfo.dataRequestDTO.catalogEntityType,
                    locationType = parsedDataInfo.dataRequestDTO.locationType,
                    locationValue = parsedDataInfo.dataRequestDTO.locationValue
                )
            }
            .filter { it.value.size > 1 }
            .keys

        return stringInputsParsedDataInfoList.map { parsedDataInfo ->
            if (parsedDataInfo.parsingExceptionMessage != null) {
                ParsedDataInfo(
                    row = parsedDataInfo.row,
                    parsingExceptionMessage = parsedDataInfo.parsingExceptionMessage,
                    dataRequestDTO = null
                )
            } else {
                val manualStringSignalFileInputRow = parsedDataInfo.dataRequestDTO!!
                val rowIdentifier = RowIdentifier(
                    catalogEntity = manualStringSignalFileInputRow.catalogEntity.uppercase(),
                    catalogEntityType = manualStringSignalFileInputRow.catalogEntityType,
                    locationType = manualStringSignalFileInputRow.locationType,
                    locationValue = manualStringSignalFileInputRow.locationValue
                )

                if (duplicateIdentifiers.contains(rowIdentifier)) {
                    // Mark all duplicate rows as parsing errors
                    ParsedDataInfo(
                        row = parsedDataInfo.row,
                        parsingExceptionMessage = "Duplicate row found with same catalog entity, catalog entity type, location type, and location value",
                        dataRequestDTO = null
                    )
                } else {
                    // Process only distinct rows
                    val signal = Signal(
                        catalogEntity = manualStringSignalFileInputRow.catalogEntity.uppercase(),
                        catalogEntityType = manualStringSignalFileInputRow.catalogEntityType,
                        variableId = variable.id.name,
                        signalData = StringValue(
                            value = manualStringSignalFileInputRow.inputValue
                        ),
                        metadata = GenericMetadata(
                            metadataMap = mapOf(
                                "CREATION_JOB_ID_REF" to job.id
                            )
                        ),
                        location = Location(
                            locationType = manualStringSignalFileInputRow.locationType,
                            locationValue = manualStringSignalFileInputRow.locationValue.replace("BBCT", "Bhubaneswar", ignoreCase = true)
                        ),
                        state = SignalState.ACTIVE,
                        createdBy = job.createdBy,
                        updatedBy = job.createdBy
                    )

                    ParsedDataInfo(
                        row = parsedDataInfo.row,
                        parsingExceptionMessage = null,
                        dataRequestDTO = signal
                    )
                }
            }
        }
    }

    private suspend fun parseAndConvertLadderInputsToSignal(
        job: AsyncJob,
        file: File,
        variable: Variable
    ): List<ParsedDataInfo<Signal>> {
        val ladderInputsParsedDataInfoList = fileUtils.readExcelFile(
            jobId = job.id,
            file = file,
            sheetNum = 0,
            headerNum = 0,
            dataRowNum = 1,
            dataClass = ManualLadderSignalFileInputRow::class
        )

        // Identify duplicate row identifiers
        val duplicateIdentifiers = ladderInputsParsedDataInfoList
            .filter { it.parsingExceptionMessage == null && it.dataRequestDTO != null }
            .groupBy { parsedDataInfo ->
                RowIdentifier(
                    catalogEntity = parsedDataInfo.dataRequestDTO!!.catalogEntity.uppercase(),
                    catalogEntityType = parsedDataInfo.dataRequestDTO.catalogEntityType,
                    locationType = parsedDataInfo.dataRequestDTO.locationType,
                    locationValue = parsedDataInfo.dataRequestDTO.locationValue
                )
            }
            .filter { it.value.size > 1 }
            .keys

        return ladderInputsParsedDataInfoList.map { parsedDataInfo ->
            if (parsedDataInfo.parsingExceptionMessage != null) {
                ParsedDataInfo(
                    row = parsedDataInfo.row,
                    parsingExceptionMessage = parsedDataInfo.parsingExceptionMessage,
                    dataRequestDTO = null
                )
            } else {
                val manualLadderSignalFileInputRow = parsedDataInfo.dataRequestDTO!!
                val rowIdentifier = RowIdentifier(
                    catalogEntity = manualLadderSignalFileInputRow.catalogEntity.uppercase(),
                    catalogEntityType = manualLadderSignalFileInputRow.catalogEntityType,
                    locationType = manualLadderSignalFileInputRow.locationType,
                    locationValue = manualLadderSignalFileInputRow.locationValue
                )

                if (duplicateIdentifiers.contains(rowIdentifier)) {
                    // Mark all duplicate rows as parsing errors
                    ParsedDataInfo(
                        row = parsedDataInfo.row,
                        parsingExceptionMessage = "Duplicate row found with same catalog entity, catalog entity type, location type, and location value",
                        dataRequestDTO = null
                    )
                } else {
                    // Process only distinct rows
                    val laddersFromParsedData = getInputLaddersFromParsedLadderInputRow(manualLadderSignalFileInputRow)

                    val signal = Signal(
                        catalogEntity = manualLadderSignalFileInputRow.catalogEntity.uppercase(),
                        catalogEntityType = manualLadderSignalFileInputRow.catalogEntityType,
                        variableId = variable.id.name,
                        signalData = LadderValue(
                            value = laddersFromParsedData.roundValues()
                        ),
                        metadata = GenericMetadata(
                            metadataMap = mapOf(
                                "CREATION_JOB_ID_REF" to job.id
                            )
                        ),
                        location = Location(
                            locationType = manualLadderSignalFileInputRow.locationType,
                            locationValue = manualLadderSignalFileInputRow.locationValue.replace("BBCT", "Bhubaneswar", ignoreCase = true)
                        ),
                        state = SignalState.ACTIVE,
                        createdBy = job.createdBy,
                        updatedBy = job.createdBy
                    )

                    ParsedDataInfo(
                        row = parsedDataInfo.row,
                        parsingExceptionMessage = null,
                        dataRequestDTO = signal
                    )
                }
            }
        }
    }

    private fun getInputLaddersFromParsedLadderInputRow(
        manualLadderSignalFileInputRow: ManualLadderSignalFileInputRow
    ): List<Ladder> {
        val firstLadderSlab = Ladder(
            minQuantity = manualLadderSignalFileInputRow.ladder1MinQuantity,
            maxQuantity = Int.MAX_VALUE.toLong(),
            ladderValue = manualLadderSignalFileInputRow.ladder1Value
        )

        val otherLadderSlabs = listOfNotNull(
            if (manualLadderSignalFileInputRow.ladder2MinQuantity != null && manualLadderSignalFileInputRow.ladder2Value != null) {
                Ladder(
                    minQuantity = manualLadderSignalFileInputRow.ladder2MinQuantity,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = manualLadderSignalFileInputRow.ladder2Value
                )
            } else null,
            if (manualLadderSignalFileInputRow.ladder3MinQuantity != null && manualLadderSignalFileInputRow.ladder3Value != null) {
                Ladder(
                    minQuantity = manualLadderSignalFileInputRow.ladder3MinQuantity,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = manualLadderSignalFileInputRow.ladder3Value
                )
            } else null,
            if (manualLadderSignalFileInputRow.ladder4MinQuantity != null && manualLadderSignalFileInputRow.ladder4Value != null) {
                Ladder(
                    minQuantity = manualLadderSignalFileInputRow.ladder4MinQuantity,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = manualLadderSignalFileInputRow.ladder4Value
                )
            } else null,
            if (manualLadderSignalFileInputRow.ladder5MinQuantity != null && manualLadderSignalFileInputRow.ladder5Value != null) {
                Ladder(
                    minQuantity = manualLadderSignalFileInputRow.ladder5MinQuantity,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = manualLadderSignalFileInputRow.ladder5Value
                )
            } else null
        )

        val inputLadderSlabs = listOf(firstLadderSlab) + otherLadderSlabs

        val correctedLadderSlabs = inputLadderSlabs.mapIndexed { i, ladder ->
            if (i < inputLadderSlabs.size - 1) {
                ladder.copy(maxQuantity = inputLadderSlabs[i+1].minQuantity - 1)
            } else {
                ladder
            }
        }

        correctedLadderSlabs.validate()

        return correctedLadderSlabs
    }

    private suspend fun validateSignalUpdateAccess(
        signal: Signal
    ) {
        val userRoles = authorizationUtils.fetchUserRoles(signal.createdBy)
        val verticalCategory = when (signal.catalogEntityType) {
            CatalogEntityType.LISTING_ID -> verticalSvcInterface.getVerticalCategoryForLid(signal.catalogEntity)
            CatalogEntityType.PRODUCT_ID -> catalogHelper.getCategoryForProduct(signal.catalogEntity)
            CatalogEntityType.PRODUCT_GROUP_ID -> catalogHelper.getCategoryForProductGroupId(signal.catalogEntity)
            CatalogEntityType.VERTICAL -> catalogHelper.getCategoryForVertical(signal.catalogEntity)
        }

        when (verticalCategory) {
            VerticalCategory.FMCG -> {
                val userHasAllFmcgUploadAccess = userRoles.any {
                    it.roleName == AuthConstants.AccessRole.MANUAL_SIGNAL_INPUT_UPLOAD_ACCESS &&
                            it.attributes[AuthConstants.AuthAttribute.CATEGORY]?.contains(verticalCategory.name) == true &&
                            it.attributes[AuthConstants.AuthAttribute.SOURCING_MODEL]?.contains("ALL") == true
                }

                if (!userHasAllFmcgUploadAccess) {
                    // Cluster and central locationValues not allowed for BnS role
                    if (signal.location.locationType in listOf(LocationType.CLUSTER, LocationType.CENTRAL)) {
                        throw BadRequestException("User not authorised to upload Cluster or State level for FMCG")
                    }

                    if (signal.catalogEntityType == CatalogEntityType.VERTICAL) {
                        throw BadRequestException("User not authorised to upload vertical level inputs for FMCG")
                    }

                    val gid = when (signal.catalogEntityType) {
                        CatalogEntityType.LISTING_ID -> fpCatalogSvcInterface.fetchGroupIdFromListingId(signal.catalogEntity)
                        CatalogEntityType.PRODUCT_ID -> fpCatalogSvcInterface.getProductGroupDetailsForProductId(signal.catalogEntity)
                        CatalogEntityType.PRODUCT_GROUP_ID -> signal.catalogEntity
                        else -> null
                    } ?: throw BadRequestException("Unable to determine GID for access validation")

                    // check if BNS selection
                    val isBnsSelection = isBnsSelection(gid, signal.location.locationValue, signal.location.locationType)

                    if (isBnsSelection) {
                        val userHasBnSFmcgUploadAccess = userRoles.any {
                            it.roleName == AuthConstants.AccessRole.MANUAL_SIGNAL_INPUT_UPLOAD_ACCESS &&
                                    it.attributes[AuthConstants.AuthAttribute.CATEGORY]?.contains(verticalCategory.name) == true &&
                                    it.attributes[AuthConstants.AuthAttribute.SOURCING_MODEL]?.contains("BNS") == true
                        }

                        if (!userHasBnSFmcgUploadAccess) {
                            throw BadRequestException("User not authorised to upload manual inputs for BnS FMCG listing.")
                        }
                    } else {
                        throw BadRequestException("User not authorised to upload manual inputs for non BnS FMCG listing.")
                    }
                }
            }
            VerticalCategory.STAPLES,
            VerticalCategory.FRESH,
            VerticalCategory.MEAT,
            VerticalCategory.OTHER -> {
                val userHasUploadAccess = userRoles.any {
                    it.roleName == AuthConstants.AccessRole.MANUAL_SIGNAL_INPUT_UPLOAD_ACCESS &&
                            it.attributes[AuthConstants.AuthAttribute.CATEGORY]?.contains(verticalCategory.name) == true
                }

                if (!userHasUploadAccess) {
                    throw BadRequestException("User ${signal.createdBy} doesn't have upload access for $verticalCategory")
                }
            }
        }
    }

    /**
     *  This fun decides if a GID belongs to Buy & Sell sourcing channel or not.
     *  This fetches anchor WH (assuming the locationValue passed here is present in pricing N/w) and uses it alonw
     *  with GID to hit planning svc API for same.
     *
     *  @return - true if GID is Buy & Sell else false
     */
    private suspend fun isBnsSelection(gid: String, locationValue: String, locationType: LocationType): Boolean {
        val anchorWh = pricingNetworkHelper.getAnchorWhDetailsFromLoc(locationType, locationValue)

        return anchorWh?.let {
            val channel = selectionClient.getMinifiedRecPidMetadataForGidWh(
                groupId = gid,
                warehouseId = it.whId.uppercase()
            ).executeAwaitOrNull()?.primaryChannelType
            channel == ChannelType.BnS
        } ?: false
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class ManualBigDecimalSignalFileInputRow(
        @JsonProperty("Catalog Entity") val catalogEntity: String,
        @JsonProperty("Catalog Entity Type") val catalogEntityType: CatalogEntityType,
        @JsonProperty("Location Type") val locationType: LocationType,
        @JsonProperty("Location Value") val locationValue: String,
        @JsonProperty("Value") val inputValue: BigDecimal
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class ManualStringSignalFileInputRow(
        @JsonProperty("Catalog Entity") val catalogEntity: String,
        @JsonProperty("Catalog Entity Type") val catalogEntityType: CatalogEntityType,
        @JsonProperty("Location Type") val locationType: LocationType,
        @JsonProperty("Location Value") val locationValue: String,
        @JsonProperty("Value") val inputValue: String
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class ManualLadderSignalFileInputRow(
        @JsonProperty("Catalog Entity") val catalogEntity: String,
        @JsonProperty("Catalog Entity Type") val catalogEntityType: CatalogEntityType,
        @JsonProperty("Location Type") val locationType: LocationType,
        @JsonProperty("Location Value") val locationValue: String,
        @JsonProperty("Ladder1 Min Qty") val ladder1MinQuantity: Long,
        @JsonProperty("Ladder1 Value") val ladder1Value: BigDecimal,
        @JsonProperty("Ladder2 Min Qty") val ladder2MinQuantity: Long?,
        @JsonProperty("Ladder2 Value") val ladder2Value: BigDecimal?,
        @JsonProperty("Ladder3 Min Qty") val ladder3MinQuantity: Long?,
        @JsonProperty("Ladder3 Value") val ladder3Value: BigDecimal?,
        @JsonProperty("Ladder4 Min Qty") val ladder4MinQuantity: Long?,
        @JsonProperty("Ladder4 Value") val ladder4Value: BigDecimal?,
        @JsonProperty("Ladder5 Min Qty") val ladder5MinQuantity: Long?,
        @JsonProperty("Ladder5 Value") val ladder5Value: BigDecimal?
    )

    data class RowIdentifier(
        val catalogEntity: String,
        val catalogEntityType: CatalogEntityType,
        val locationType: LocationType,
        val locationValue: String
    )

    data class ManualSignalFileOutputResponse(
        val output: String
    )
}
