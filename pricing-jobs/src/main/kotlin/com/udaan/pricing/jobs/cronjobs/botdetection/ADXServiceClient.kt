package com.udaan.pricing.jobs.cronjobs.botdetection

import com.microsoft.azure.kusto.data.Client
import com.microsoft.azure.kusto.data.ClientFactory
import com.microsoft.azure.kusto.data.KustoResultColumn
import com.microsoft.azure.kusto.data.KustoResultSetTable
import com.microsoft.azure.kusto.data.auth.ConnectionStringBuilder
import java.sql.Timestamp
import java.time.Instant
import kotlin.reflect.KClass
import kotlin.reflect.KParameter
import kotlin.reflect.jvm.javaType

data class ADXConfig(
    val resourceUri: String,
    val tenantId: String,
    val appId: String,
    val appKey: String
) {
    companion object {
        fun fromMap(configMap: Map<String, Any>): ADXConfig {
            val resourceUri: String by configMap
            val tenantId: String by configMap
            val appId: String by configMap
            val appKey: String by configMap

            return ADXConfig(resourceUri, tenantId, appId, appKey)

        }
    }
}

class ADXResult(val resultSetTable: KustoResultSetTable) {
    val columnNames: Map<String, KustoResultColumn> = resultSetTable.columns.associate {
        it.columnName to it
    }

    fun iterator(): Iterator<List<Any>> {
        return object : Iterator<List<Any>> {
            var first = resultSetTable.first()

            override fun hasNext(): Boolean {
                if (first) {
                    first = false
                    return true
                }
                return resultSetTable.next()
            }

            override fun next(): List<Any> {
                return resultSetTable.currentRow
            }
        }
    }

    inline fun <reified T : Any> to(): Sequence<T> = to(T::class)

    fun <T : Any> to(klass: KClass<T>): Sequence<T> {
        val defaultConstructor = klass.constructors.first()
        val paramsColumnIndexPairs = defaultConstructor.parameters.map {
            it to columnNames[it.name]
        }
        return iterator().asSequence().map { row: List<Any> ->
            val params = toObjectParams(paramsColumnIndexPairs)
            defaultConstructor.call(*params.toTypedArray())
        }
    }

    private fun toObjectParams(paramsColumnIndexPairs: List<Pair<KParameter, KustoResultColumn?>>): List<Any?> {
        return paramsColumnIndexPairs.map { (arg: KParameter, column) ->
            val index = column?.ordinal ?: return@map null
            val columnType = column?.columnType ?: return@map null

            when (arg.type.javaType) {
                java.util.Date::class.java -> {
                    java.util.Date.from(Instant.ofEpochMilli(resultSetTable.getDate(index).time))
                }
                java.time.LocalDate::class.java -> {
                    val dateString = resultSetTable.getString(index)
                    java.time.LocalDate.parse(dateString) // format: "year-month-date"
                }
                java.sql.Date::class.java -> {
                    resultSetTable.getDate(index)
                }
                Timestamp::class.java -> {
                    resultSetTable.getTimestamp(index)
                }
                else -> {
                    when (columnType) {
                        "datetime" -> {
                            resultSetTable.getTimestamp(index).time
                        }
                        else -> {
                            resultSetTable.getObject(index)
                        }
                    }
                }
            }
        }
    }
}


class ADXServiceClient internal constructor(private val kustoClient: Client, private val databaseName: String) {
    fun query(query: String): ADXResult {
        val adxResult = kustoClient.execute(databaseName, query)
        return ADXResult(adxResult.primaryResults)
    }
}

class ADXServiceClientFactory(private val adxConfig: ADXConfig): AutoCloseable {
    private val kustoClient: Client by lazy {
        val csb = ConnectionStringBuilder.createWithAadApplicationCredentials(
            adxConfig.resourceUri,
            adxConfig.appId,
            adxConfig.appKey,
            adxConfig.tenantId
        )

        ClientFactory.createClient(csb)
    }

    fun createInstanceForDB(databaseName: String): ADXServiceClient {
        return ADXServiceClient(kustoClient, databaseName)
    }

    override fun close() {
        //do nothing
    }
}
