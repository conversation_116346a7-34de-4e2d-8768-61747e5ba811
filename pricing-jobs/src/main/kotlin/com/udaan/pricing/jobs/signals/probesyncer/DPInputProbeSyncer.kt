package com.udaan.pricing.jobs.signals.probesyncer

import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.databind.MappingIterator
import com.fasterxml.jackson.dataformat.csv.CsvMapper
import com.fasterxml.jackson.dataformat.csv.CsvSchema
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope.Companion.async
import com.udaan.pricing.core.managers.signals.SignalWriteManager
import com.udaan.pricing.core.svcinterfaces.DpServiceInterface
import com.udaan.pricing.signalcreation.RawSignalInput
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import java.io.InputStream

abstract class DPInputProbeSyncer(
    protected val dpServiceInterface: DpServiceInterface,
    private val signalWriteManager: SignalWriteManager
) {
    companion object {
        protected val logger by logger()
        protected val csvMapper: CsvMapper = CsvMapper().apply {
            registerKotlinModule()
            this.disable(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY)
        }

        protected val schema: CsvSchema = CsvSchema.emptySchema().withHeader().withArrayElementSeparator(";")
    }

    abstract val probeId: String
    abstract val chunkSize: Int
    abstract val delayBetweenChunksInMillis: Long

    protected suspend fun processAndSaveRawInputs(rawInputs: List<RawSignalInput>) {
        rawInputs.chunked(chunkSize).forEachIndexed { index, dataChunk ->
            delay(delayBetweenChunksInMillis)
            logger.info("Processing chunk no. {}", index + 1)
            dataChunk.map {
                async {
                    try {
                        withTimeout(60000) {
                            val createdSignal = signalWriteManager.createSignalFromRawInput(it)
                            logger.info("Created signal {}", createdSignal)
                            true
                        }
                    } catch (e: Exception) {
                        logger.error("Got exception while processing probe data {}: {}", it, e.message)
                        false
                    }
                }
            }.awaitAll()
        }
    }

    protected suspend inline fun <reified R> getDataFromDataPlatform(): List<R> {
        val inputStream = dpServiceInterface.getProbe(probeId)
        return withContext(Dispatchers.IO) {
            inputStream.readCSV<R>().readAll()
        }
    }

    protected inline fun <reified R> InputStream.readCSV(): MappingIterator<R> {
        return csvMapper.readerFor(R::class.java)
            .with(schema)
            .readValues<R>(this)
    }
}
