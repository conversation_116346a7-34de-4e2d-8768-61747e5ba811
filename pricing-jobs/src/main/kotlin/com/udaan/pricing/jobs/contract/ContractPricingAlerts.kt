package com.udaan.pricing.jobs.contract

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.google.inject.Guice
import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.JobScope
import com.udaan.instrumentation.TelemetryScope.Companion.async
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.core.controller.contract.ContractController
import com.udaan.pricing.core.models.contracts.Contract
import com.udaan.pricing.core.svcinterfaces.DpServiceInterface
import com.udaan.pricing.core.utils.EMailSender
import com.udaan.pricing.jobs.models.contracts.ExpiryAlertSummary
import com.udaan.pricing.jobs.models.contracts.QuoteAlertSummary
import com.udaan.pricing.jobs.models.contracts.VolumeAlertSummary
import com.udaan.pricing.jobs.helpers.ContractQuoteGidLidMappingHelper
import com.udaan.pricing.jobs.helpers.ContractQuoteTaggingHelper
import com.udaan.pricing.jobs.keda.fileutils.FileUtils
import java.util.concurrent.TimeUnit
import kotlin.system.exitProcess


@Suppress("TooGenericExceptionCaught", "LongParameterList")
class ContractPricingAlerts @Inject constructor(
    private val contractController: ContractController,
    private val contractQuoteAlerts: ContractQuoteAlerts,
    private val contractVolumeAlerts: ContractVolumeAlerts,
    private val fileUtils: FileUtils,
    private val contractExpiryAlerts: ContractExpiryAlerts,
    private val contractQuoteTaggingHelper: ContractQuoteTaggingHelper,
    private val contactQuoteGidLidMappingHelper: ContractQuoteGidLidMappingHelper,
    private val contractAlertsHelper: ContractAlertsHelper,
    private val dpServiceInterface: DpServiceInterface
) {

    companion object {

        private val logger by logger()
        private val injector = Guice.createInjector(PricingCoreModule())
        const val activeContractListProbeId = "hpcscm"
        const val buyerToKamMappingProbeId = "fo52mq"

        @JvmStatic
        fun main(args: Array<String>) {
            val contractPricingAlerts = injector.getInstance(ContractPricingAlerts::class.java)
            try {
                logger.info("********************** Job STARTED **********************")
                JobScope.runBlocking {
                    contractPricingAlerts.process()
                }
                logger.info("********************** Job Completed **********************")
            } catch (e: Exception) {
                logger.error("********************** Job FAILED **********************", e)
                exitProcess(1)
            } finally {
                logger.error("********************** Process End **********************")
                exitProcess(0)
            }
        }
    }

    /**
     *  TODO: this has multiple probe dependencies, reduce it further
     *  1. activeContractListProbeId - to fetch active contracts
     *  2. buyerToKamMappingProbeId - to fetch buyer to kam email mapping.
     *  3. contractQuoteGidLidMappingProbeId - to fetch contract quote gid lid mapping, in case of FMCG.
     *  4. contractQuoteTaggingProbeId - - to fetch contract quote tagging, to execute list of strategies
     *  5. volumeOrderedByBuyerProbeId - to fetch day level volume of a buyer and gid.
     */
    suspend fun process() {
        val activeContracts = fetchActiveContracts()
        val setContractQuoteTaggingInRedis = async {
            setContractQuoteTaggingInRedis()
        }
        val setContractQuoteGidLidMappingInRedis = async {
            setContractQuoteGidLidMappingInRedis()
        }
        val buyerToKamMapping = async {
            fetchBuyerToKamMapping()
        }
        val buyerOrgDetailsMap = async {
            contractAlertsHelper.getBuyerDetails(activeContracts)
        }
        val catalogDetailsMap = async {
            contractAlertsHelper.getCatalogDetails(activeContracts)
        }
        val quoteAlertSummary = async {
            setContractQuoteTaggingInRedis.await()
            setContractQuoteGidLidMappingInRedis.await()
            contractQuoteAlerts.process(
                activeContracts = activeContracts,
                buyerOrgDetailsMap = buyerOrgDetailsMap.await(),
                catalogTitleDetailsMap = catalogDetailsMap.await()
            )
        }
        val volumeAlertSummary = async {
            contractVolumeAlerts.process(
                contracts = activeContracts,
                buyerOrgDetailsMap = buyerOrgDetailsMap.await(),
                catalogTitleDetailsMap = catalogDetailsMap.await()
            )
        }
        val expiryAlertSummary = async {
            contractExpiryAlerts.process(
                activeContracts = activeContracts,
                quoteAlertSummary = quoteAlertSummary.await(),
                buyerOrgDetailsMap = buyerOrgDetailsMap.await(),
                catalogTitleDetailsMap = catalogDetailsMap.await()
            )
        }
        val kamLevelAlerts = async {
            generateBuyerLevelAlerts(
                volumeAlertSummary.await(),
                expiryAlertSummary.await(),
                buyerToKamMapping.await()
            )
        }
        val centralPricingAlerts = async {
            generateCentralPricingAlerts(
                volumeAlertSummary.await(),
                expiryAlertSummary.await(),
                quoteAlertSummary.await(),
                buyerToKamMapping.await()
            )
        }
        kamLevelAlerts.await()
        centralPricingAlerts.await()
    }

    private suspend fun generateCentralPricingAlerts(
        volumeAlertSummary: VolumeAlertSummary,
        expiryAlertSummary: ExpiryAlertSummary,
        quoteAlertSummary: QuoteAlertSummary,
        buyerKamMapping: List<BuyerKamMapping>
    ) {

        val volumeAlertSummaryFile = fileUtils.generateFile(
            volumeAlertSummary.volumeAlertSummaryForBuyerList,
            "volume_alerts_summary.xlsx"
        )
        val volumeDetailsAlertFile = fileUtils.generateFile(
            volumeAlertSummary.volumeDataToRaiseAlert,
            "volume_detailed_alert_data.xlsx"
        )
        val expiryAlertSummaryFile = fileUtils.generateFile(
            expiryAlertSummary.expiryAlertSummaryForBuyerList,
            "expiry_alerts_summary.xlsx"
        )
        val expiryAlertDetailedFile = fileUtils.generateFile(
            expiryAlertSummary.expiryDataToRaiseAlert, "expiry_alerts_detailed.xlsx"
        )
        val quoteAlertSummaryFile = fileUtils.generateFile(
            quoteAlertSummary.quoteAlertSummaryForBuyerList,
            "quote_alerts_summary.xlsx"
        )
        val quoteAlertDetailedFile = fileUtils.generateFile(
            quoteAlertSummary.quoteDataToRaiseAlert,
            "quote_alerts_detailed.xlsx"
        )
        val centralPocMailIds = buyerKamMapping.map {
            setOf(
                it.pricingExecutiveMailId.trim(),
                it.pricingManagerMailId.trim(),
                it.centralPocManager1.trim(),
                it.centralPocManager2.trim(),
                it.centralPocManager3.trim(),
                it.centralPocManager4.trim(),
                it.centralPocManager5.trim(),
                it.centralPocManager6.trim(),
                it.centralPocManager7.trim()
            )
        }.flatten().toSet().filter { it.isNotBlank() }
        val centralPocCCMailIds = buyerKamMapping.map {
            setOf(
                it.pricingExecutiveMailId.trim(),
                it.pricingManagerMailId.trim()
            )
        }.flatten().toSet().filter { it.isNotBlank() }
        EMailSender.sendMail(
            tos = centralPocMailIds.toList(),
            subject = "Contract Pricing Alerts Summary",
            body = generateBulkEmailBody(
                expiryAlerts = expiryAlertSummary.expiryAlertSummaryForBuyerList,
                volumeAlerts = volumeAlertSummary.volumeAlertSummaryForBuyerList,
                quoteAlerts = quoteAlertSummary.quoteAlertSummaryForBuyerList
            ),
            cc = centralPocCCMailIds.toList() + "<EMAIL>",
            bcc = emptyList(),
            attachments = listOfNotNull(
                volumeAlertSummaryFile,
                volumeDetailsAlertFile, expiryAlertSummaryFile,
                expiryAlertDetailedFile, quoteAlertSummaryFile, quoteAlertDetailedFile
            )
        )
    }

    private suspend fun generateBuyerLevelAlerts(
        volumeAlertSummary: VolumeAlertSummary?,
        expiryAlertSummary: ExpiryAlertSummary?,
        buyerKamMapping: List<BuyerKamMapping>
    ) {
        buyerKamMapping.groupBy { it.buyerOrgId }.forEach { (buyerOrgId, buyerKamMappingList) ->
            val buyerVolumeAlertSummary = volumeAlertSummary?.volumeAlertSummaryForBuyerList?.find {
                it.buyerOrgId == buyerOrgId
            }
            val buyerExpiryAlertSummary = expiryAlertSummary?.expiryAlertSummaryForBuyerList?.find {
                it.buyerOrgId == buyerOrgId
            }
            if (buyerVolumeAlertSummary != null || buyerExpiryAlertSummary != null) {
                logger.info("Buyer: $buyerOrgId")
                logger.info("Volume Alert Summary: $buyerVolumeAlertSummary")
                logger.info("Expiry Alert Summary: $buyerExpiryAlertSummary")
                val buyerKam = buyerKamMappingList.firstOrNull { it.buyerOrgId == buyerOrgId }
                logger.info("Buyer KAM: $buyerKam")
                val buyerName = buyerVolumeAlertSummary?.buyerName ?: buyerExpiryAlertSummary?.buyerName
                val expiryDataList = expiryAlertSummary?.expiryDataToRaiseAlert?.filter {
                    it.buyerOrgId == buyerOrgId
                }.let {
                    fileUtils.generateFile(it, "expiry_alerts_${buyerOrgId.lowercase()}.xlsx")
                }
                val volumeDataFile = volumeAlertSummary?.volumeDataToRaiseAlert?.filter {
                    it.buyerOrgId == buyerOrgId
                }.let {
                    fileUtils.generateFile(it, "volume_alerts_${buyerOrgId.lowercase()}.xlsx")
                }
                // Send email
                buyerKam?.let {
                    EMailSender.sendMail(
                        tos = listOf(buyerKam.kamEmailId.trim()),
                        subject = "Contract volume and expiry alerts for $buyerName",
                        body = generateEmailBody(
                            expiryAlert = buyerExpiryAlertSummary,
                            volumeAlert = buyerVolumeAlertSummary
                        ),
                        cc = listOf(buyerKam.kamManagerEmailId.trim()),
                        bcc = listOf(buyerKam.kamManagerEmailId.trim()),
                        attachments = listOfNotNull(volumeDataFile, expiryDataList)
                    )
                }
            }
        }
    }


    /**
     * Fetches active contracts from the probe.
     * Why not fetch from repository directly ?
     * 1. This gives an additional load on database, and the query is fan out query to all partitions.
     */
    private suspend fun fetchActiveContracts(): List<Contract> {
        val activeContractList = dpServiceInterface.getProbeData<ActiveContract>(activeContractListProbeId)
        logger.info("Active Contract List: $activeContractList")
        return activeContractList.mapNotNull {
            contractController.getContractByBuyerOrgAndCatalogEntity(
                buyerOrgId = it.buyerOrgId,
                catalogEntityId = it.catalogEntityId
            )
        }.filter { it.duration.endTime > System.currentTimeMillis() - TimeUnit.DAYS.toMillis(11) }
    }


    @JsonIgnoreProperties(ignoreUnknown = true)
    data class ActiveContract(
        @JsonProperty("buyerOrgId") val buyerOrgId: String,
        @JsonProperty("catalogEntityId") val catalogEntityId: String,
        @JsonProperty("contractCatalogEntity") val contractCatalogEntity: String
    )


    private suspend fun fetchBuyerToKamMapping(): List<BuyerKamMapping> {
        val buyerKamMappingList = dpServiceInterface.getProbeData<BuyerKamMapping>(buyerToKamMappingProbeId)
        logger.info("Buyer-KAM mapping list: $buyerKamMappingList")
        return buyerKamMappingList
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class BuyerKamMapping(
        @JsonProperty("LE_Org") val buyerOrgId: String,
        @JsonProperty("LE_KAM") val kamEmailId: String,
        @JsonProperty("LE_KAM_Manager") val kamManagerEmailId: String,
        @JsonProperty("Pricing_Exec") val pricingExecutiveMailId: String,
        @JsonProperty("Pricing_Manager") val pricingManagerMailId: String,
        @JsonProperty("Central_1") val centralPocManager1: String,
        @JsonProperty("Central_2") val centralPocManager2: String,
        @JsonProperty("Central_3") val centralPocManager3: String,
        @JsonProperty("Central_4") val centralPocManager4: String,
        @JsonProperty("Central_5") val centralPocManager5: String,
        @JsonProperty("Central_6") val centralPocManager6: String,
        @JsonProperty("Central_7") val centralPocManager7: String
    )

    private suspend fun setContractQuoteTaggingInRedis() {
        contractQuoteTaggingHelper.setContractQuoteTaggingInRedis()
    }

    private suspend fun setContractQuoteGidLidMappingInRedis() {
        contactQuoteGidLidMappingHelper.setContractQuoteGidLidMappingsInRedis()
    }
}
