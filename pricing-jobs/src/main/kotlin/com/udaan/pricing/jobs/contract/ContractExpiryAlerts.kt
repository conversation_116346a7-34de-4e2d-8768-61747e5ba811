package com.udaan.pricing.jobs.contract

import com.google.inject.Inject
import com.udaan.common.utils.getCurrentMillis
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.pricing.core.models.contracts.Contract
import com.udaan.pricing.core.models.contracts.ContractLadderMrpMarkdownBps
import com.udaan.pricing.core.models.contracts.ContractLadderPrice
import com.udaan.pricing.core.models.contracts.ContractType
import com.udaan.pricing.jobs.models.contracts.ExpiryAlertOutputFileRow
import com.udaan.pricing.jobs.models.contracts.ExpiryAlertSummary
import com.udaan.pricing.jobs.models.contracts.ExpiryAlertSummaryForBuyer
import com.udaan.pricing.jobs.models.contracts.QuoteAlertOutputFileRow
import com.udaan.pricing.jobs.models.contracts.QuoteAlertSummary
import com.udaan.proto.models.ModelV1
import java.lang.Math.abs
import java.util.concurrent.*

class ContractExpiryAlerts @Inject constructor(
    private val contractAlertsHelper: ContractAlertsHelper
) {

    private val logger by logger()
    private val expiryAlertsThresholdInDays = 5

    suspend fun process(
        activeContracts: List<Contract>,
        quoteAlertSummary: QuoteAlertSummary,
        buyerOrgDetailsMap: Map<String, ModelV1.OrgAccount?>,
        catalogTitleDetailsMap: Map<String, String?>
    ): ExpiryAlertSummary {
        val expiryDataToRaiseAlert = expiryContracts(
            activeContracts = activeContracts,
            quoteAlertSummary = quoteAlertSummary,
            buyerOrgDetailsMap = buyerOrgDetailsMap,
            catalogTitleDetailsMap = catalogTitleDetailsMap
        )
        return ExpiryAlertSummary(
            expiryAlertSummaryForBuyerList = getExpiryAlertsSummary(
                expiryDataToRaiseAlert,
                activeContracts,
                buyerOrgDetailsMap
            ),
            expiryDataToRaiseAlert = expiryDataToRaiseAlert
        )
    }

    private suspend fun getExpiryAlertsSummary(
        data: List<ExpiryAlertOutputFileRow>,
        contracts: List<Contract>,
        buyerOrgDetailsMap: Map<String, ModelV1.OrgAccount?>
    ): List<ExpiryAlertSummaryForBuyer> {
        return contracts.groupBy { it.buyerOrgId }.entries.parallelMap { groupByBuyerOrgId ->
            val contractsExpiresIn5Days = data.filter { it.buyerOrgId == groupByBuyerOrgId.key }
            val buyerOrgName = buyerOrgDetailsMap[groupByBuyerOrgId.key.trim()]?.displayName ?: ""
            ExpiryAlertSummaryForBuyer(
                buyerOrgId = groupByBuyerOrgId.key,
                buyerName = buyerOrgName,
                skusLiveOnContract = groupByBuyerOrgId.value.size - contractsExpiresIn5Days.size,
                skusWithExpiryLessThan5Days = contractsExpiresIn5Days.size
            )
        }.sortedByDescending { it.skusLiveOnContract }
    }

    private fun expiryContracts(
        activeContracts: List<Contract>,
        quoteAlertSummary: QuoteAlertSummary,
        buyerOrgDetailsMap: Map<String, ModelV1.OrgAccount?>,
        catalogTitleDetailsMap: Map<String, String?>
    ): List<ExpiryAlertOutputFileRow> {
        return activeContracts.mapNotNull { contract ->
            val endTime = if (contract.duration.lockInTime > 0) {
                contract.duration.lockInTime
            } else {
                contract.duration.endTime
            }
            val quoteDetails = quoteAlertSummary.quoteDataToRaiseAlert.firstOrNull {
                it.buyerOrgId == contract.buyerOrgId && it.contractCatalogEntityId == contract.catalogEntityId
            }
            if (endTime - TimeUnit.DAYS.toMillis(expiryAlertsThresholdInDays.toLong()) < System.currentTimeMillis()
            ) {
                logger.info("Contract ${contract.id} is about to expire.")
                contract.toContractExpiryAlertOutputFileRow(
                    endTime, quoteDetails,
                    buyerOrgDetailsMap[contract.buyerOrgId]?.displayName ?: "",
                    catalogTitleDetailsMap[contract.catalogEntityId] ?: ""
                )
            } else {
                null
            }
        }
    }

    private fun Contract.toContractExpiryAlertOutputFileRow(
        endTime: Long,
        quoteDetails: QuoteAlertOutputFileRow?,
        buyerName: String,
        catalogTitle: String
    ): ExpiryAlertOutputFileRow {
        val aboutToExpireInDays = (endTime - getCurrentMillis()) / TimeUnit.DAYS.toMillis(1)
        val remarks = if (this.type == ContractType.LOCK_IN) {
            if (aboutToExpireInDays < 0) {
                "Contract lock-in is expired ${abs(aboutToExpireInDays)} days ago."
            } else {
                "Contract lock-in is about to expire in $aboutToExpireInDays days."
            }
        } else {
            if (aboutToExpireInDays < 0) {
                "Contract is expired ${abs(aboutToExpireInDays)} days ago."
            } else {
                "Contract is about to expire in $aboutToExpireInDays days."
            }
        }
        return ExpiryAlertOutputFileRow(
            buyerOrgId = buyerOrgId,
            city = metadata.city ?: "",
            buyerName = buyerName,
            contractCatalogEntityId = catalogEntityId,
            contractCatalogEntity = contractCatalogEntity.name,
            catalogTitle = catalogTitle,
            startTime = duration.startTime.toString(),
            endTime = duration.endTime.toString(),
            endTimeInDate = contractAlertsHelper.convertEpochToReadableFormat(duration.endTime) ?: "",
            currentTime = getCurrentMillis().toString(),
            contractPriceInPaisa = if (price is ContractLadderPrice) {
                (price as ContractLadderPrice).value.firstOrNull()?.ladderValue
            } else {
                null
            },
            contractMrpMarkdownInBps = if (price is ContractLadderMrpMarkdownBps) {
                (price as ContractLadderMrpMarkdownBps).value.firstOrNull()?.ladderValue
            } else {
                null
            },
            quotePriceInPaisa = quoteDetails?.quotePriceInPaisa,
            quotePriceInMrpMarkDownBps = quoteDetails?.mrpMarkDownBps,
            cogsUnitPriceInPaisa = quoteDetails?.cogsUnitPriceInPaisa,
            volumeCommitted = metadata.volumeCommitted,
            volumeOrdered = metadata.volumeOrdered,
            remarks = remarks
        )
    }
}
