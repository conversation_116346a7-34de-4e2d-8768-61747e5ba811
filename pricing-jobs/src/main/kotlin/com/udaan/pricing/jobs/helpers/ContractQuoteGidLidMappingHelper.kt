package com.udaan.pricing.jobs.helpers

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.udaan.common.server.awaitWithTimeout
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.pricing.core.svcinterfaces.DpServiceInterface
import com.udaan.pricing.jobs.models.contracts.ContractQuoteGidLidMappingResponse
import com.udaan.resources.RedisLettuce6Client

class ContractQuoteGidLidMappingHelper @Inject constructor(
    private val dpServiceInterface: DpServiceInterface,
    private val redisClient: RedisLettuce6Client,
    private val objectMapper: ObjectMapper
) {

    private val contractQuoteGidLidMappingProbeId = "fanpii"
    private val redisKeyTimeOutConfigInSeconds = 172800 // 2 days
    private val preFixKey = "contract_quote_gid_lid_mapping_"

    private val log by logger()

    suspend fun setContractQuoteGidLidMappingsInRedis() {
        val contractQuoteGidLidMappingList = dpServiceInterface.getProbeData<ContractQuoteGidLidMappingResponse>(
            probeId = contractQuoteGidLidMappingProbeId
        )
        contractQuoteGidLidMappingList.parallelMap {
            redisClient.asyncCommands.setex(
                "$preFixKey${it.groupId}".lowercase(),
                redisKeyTimeOutConfigInSeconds.toLong(),
                objectMapper.writeValueAsString(it)
            )
            log.info("Set contract quote gid-lid mapping for ${it.groupId} and ${it.listingId} in redis.")
        }
    }

    suspend fun fetchContractQuoteGidLidMappingFromRedis(
        groupId: String
    ): ContractQuoteGidLidMappingResponse? {
        val value =
            redisClient.asyncCommands.get(
                "$preFixKey${groupId}".lowercase()
            ).toCompletableFuture().awaitWithTimeout(timeoutMillis = 500, timeoutResponse = null)
        return value?.let { objectMapper.readValue(it, ContractQuoteGidLidMappingResponse::class.java) }
    }
}
