package com.udaan.pricing.jobs.network

import com.google.inject.Guice
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.JobScope
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.core.cache.network.DemandClusterCacheRepo
import com.udaan.pricing.core.cache.network.DemandClusterLocationCacheRepo
import com.udaan.pricing.core.dao.network.DemandClusterLocationsRepository
import com.udaan.pricing.core.dao.network.DemandClusterRepository
import com.udaan.pricing.network.DemandClusterLocations
import com.udaan.pricing.network.WarehouseDetails
import kotlin.system.exitProcess

@Singleton
class AddNewWhJob @Inject constructor(
    private val demandClusterCacheRepo: DemandClusterCacheRepo,
    private val demandClusterLocationCacheRepo: DemandClusterLocationCacheRepo
) {
    companion object {
        private val logger by logger()
    }

    enum class WhType {
        MFC, ANCHOR
    }

    suspend fun process() {
        /*
            Fill in the details below and run. DC will get updated and DCL will get created.

            NOTE:
            1. Below API can be used to get WH details using whId:
            https://svc-hack.prod.udaan.io/sc-network-service/facility-cluster/facility/org-unit/ORUGJYVNNQQBSQ73F4P3D03DYPB8B/detailed

            2. Make sure to point local config yml file to prod versions

            3. This is just to add Wh as DCL for an existing DC. It wont work for a case where DC is missing or we need to update an existing DCL.

            4. A good practise is to set shouldPersist=false and first check if DCL and DC getting updated properly.
         */
        val anchorCity = "patna"
        val whName = "Phulwari FMCG WH"
        val whId = "ORUGJYVNNQQBSQ73F4P3D03DYPB8B".lowercase()
        val whType = WhType.MFC.name
        val geoCity = "patna"                       // usually anchor city, can put other if seems required
        val userEmail = "<EMAIL>"

        val shouldPersist = true           // set this to true in order to create entries

        // Getting DC and deriving anchor WH from this
        val dc = DemandClusterRepository.getClusterForAnchorCity(anchorCity)
        logger.info("Demand cluster associated: $dc")

        val anchorWhDetails = dc?.fulfilmentCenters?.firstOrNull { it.type == WhType.ANCHOR.name }
        logger.info("anchor Wh details: $anchorWhDetails")

        // checking if wh already exists as DCL
        val existingDcl = DemandClusterLocationsRepository.getLocationForName(whId)
        if(existingDcl != null) throw Exception("Wh already exists as DCL.")

        // creating DCL and updating DC if all is well
        if(dc != null && anchorWhDetails != null) {
            val whDetails = WarehouseDetails(
                whId = whId,
                name= whName,
                city = "",              // can leave blank or put city/town name basis WH name, this is not used anywhere so no impact
                type = whType,
                category = "FOOD"       // for now going with hardcoded as not used anywhere
            )

            val updatedDcFfcenters = dc.fulfilmentCenters as MutableList<WarehouseDetails>
            updatedDcFfcenters.add(whDetails)
            val updatedDc = dc.copy(
                fulfilmentCenters = updatedDcFfcenters,
                updatedBy = userEmail,
                updatedAt = System.currentTimeMillis()
            )
            logger.info("updated Dc : $updatedDc")

            val dcl = DemandClusterLocations(
                name = whId,
                type = LocationType.WAREHOUSE,
                city = geoCity,                                             // associated city with location and not anchor city
                demandClusterId = dc.id,                                    // demand cluster ID, if not exists already, create one
                fulfilmentCenters = listOf(whDetails, anchorWhDetails),     // list of WHs associated, needed only for Staples
                createdBy = userEmail,
                updatedBy = null
            )
            logger.info("DCL to persist: $dcl")

            if(shouldPersist) {
                DemandClusterLocationsRepository.createOrUpdate(dcl).also {
                    demandClusterLocationCacheRepo.invalidateDemandClusterLocationCache(it)
                }
                DemandClusterRepository.createOrUpdate(updatedDc).also {
                    demandClusterCacheRepo.invalidateDemandClusterCache(it)
                }
            }
        }
    }
}

class AddNewWhJobRunner {
    companion object {
        private val injector = Guice.createInjector(PricingCoreModule())

        @JvmStatic
        fun main(args: Array<String>) {
            System.setProperty("udaan.env", "prod")
            val addNewWhJob = injector.getInstance(AddNewWhJob::class.java)
            JobScope.runBlocking {
                try {
                    addNewWhJob.process()
                    exitProcess(0)
                } catch (e: Exception) {
                    e.printStackTrace()
                    exitProcess(1)
                }
            }
        }
    }
}
