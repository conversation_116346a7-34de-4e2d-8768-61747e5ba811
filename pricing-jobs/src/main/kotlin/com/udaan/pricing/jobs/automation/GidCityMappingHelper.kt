package com.udaan.pricing.jobs.automation

import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.core.helpers.ConfigHelper
import com.udaan.pricing.core.helpers.DataPlatformHelper

class GidCityMappingHelper @Inject constructor(
    private val dataPlatformHelper: DataPlatformHelper,
    private val configSvcHelper: ConfigHelper
) {

    companion object {
        private const val probeId = "jqwiiq"
        private val logger by logger()
    }

    private suspend fun getSugarGidsToTag(cities: Collection<String>): Collection<SellerCityGroupIdMapping> {
        val sugarGidsToMigrate = configSvcHelper.getSugarGidsExcludedInAutoTaggingProbe()
        return sugarGidsToMigrate.map { gid ->
            cities.map { sellerOrgCity ->
                SellerCityGroupIdMapping(
                    productGroupId = gid, sellerOrgCity = sellerOrgCity
                )
            }
        }.flatten()
    }

    suspend fun getDataForGroupIdTagging(): Collection<SellerCityGroupIdMapping> {
        val probeData = dataPlatformHelper.getDataFromDataPlatform<SellerCityGroupIdMapping>(probeId)
        val citiesFroProbe = probeData.map { it.sellerOrgCity }.distinct()
        logger.info("Cities from probe $citiesFroProbe")
        return probeData + getSugarGidsToTag(citiesFroProbe)
    }
}