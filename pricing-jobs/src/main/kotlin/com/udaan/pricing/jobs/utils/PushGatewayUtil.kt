package com.udaan.pricing.jobs.utils

import com.udaan.common.client.UdaanClientConfig
import com.udaan.common.server.UdaanServerConfig
import com.udaan.instrumentation.PushGatewayManager
import org.slf4j.Logger

class PushGatewayUtil (private val jobName: String, private val logger: Logger) {
    companion object {
        private const val METRIC_PUSH_FREQUENCY = 5000L
    }

    private val pushGatewayClientConfig: UdaanClientConfig = UdaanServerConfig["prometheus-pushgateway"]!!
    private val pushGatewayManager = PushGatewayManager(pushGatewayClientConfig.baseUrl, jobName = jobName)

    fun pushJobStarted() {
        pushGatewayManager.incrementByValueCounterToPushGateway(1.0, "${jobName}-ENTRY").also {
            logger.info("Incremented ENTRY Counter for job $jobName")
        }
    }

    fun pushJobEnded() {
        pushGatewayManager.incrementByValueCounterToPushGateway(1.0, "${jobName}-EXIT").also {
            logger.info("Incremented EXIT Counter for job $jobName")
        }
    }

    suspend fun <A> recordStats(block: suspend () -> A) {
        pushGatewayManager.recordDefaultMetrics(jobName, METRIC_PUSH_FREQUENCY, block)
    }
}
