package com.udaan.pricing.jobs.keda.handlers

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.instrumentation.TelemetryScope.Companion.async
import com.udaan.pricing.contracts.ContractQuoteRequest
import com.udaan.pricing.contracts.ContractQuoteResponse
import com.udaan.pricing.core.constants.AsyncJobConstants
import com.udaan.pricing.core.controller.contract.ContractQuoteController
import com.udaan.pricing.core.helpers.BlobStorageHelper
import com.udaan.pricing.core.managers.signals.AsyncJobController
import com.udaan.pricing.job.AsyncJob
import com.udaan.pricing.job.AsyncJobStatus
import com.udaan.pricing.jobs.helpers.ContractQuoteGidLidMappingHelper
import com.udaan.pricing.jobs.helpers.ContractQuoteTaggingHelper
import com.udaan.pricing.jobs.keda.fileutils.FileUtils
import com.udaan.pricing.jobs.models.ProcessedDataInfo
import kotlinx.coroutines.awaitAll

@Singleton
class BuyerContractQuoteGenerationHandler @Inject constructor(
    private val asyncJobController: AsyncJobController,
    private val blobStorageHelper: BlobStorageHelper,
    private val contractQuoteController: ContractQuoteController,
    private val contractQuoteTaggingHelper: ContractQuoteTaggingHelper,
    private val contactQuoteGidLidMappingHelper: ContractQuoteGidLidMappingHelper,
    private val fileUtils: FileUtils
) : AsyncJobHandler() {

    companion object {
        private val logger by logger()
    }

    override suspend fun processJob(asyncJob: AsyncJob) {
        try {
            val autoCreateContract = asyncJob.jobData.attributesMap["autoCreateContract"]?.toBoolean() ?: false
            asyncJobController.updateAsyncJobStatusAndMetadata(
                jobId = asyncJob.id,
                status = AsyncJobStatus.IN_PROGRESS,
                updatedBy = "SYSTEM"
            )
            setQuoteTaggingAndGidLidMappings()

            val file = blobStorageHelper.getFileFromBlobStorage(
                containerReference = asyncJob.type.toString().lowercase().replace("_", "-"),
                directoryReference = asyncJob.type.toString().lowercase().replace("_", "-"),
                fileName = asyncJob.id + "_input.xlsx"
            )

            val buyerContractQuoteFileParsedData = fileUtils.readExcelFile(
                jobId = asyncJob.id,
                file = file,
                sheetNum = 0,
                headerNum = 0,
                dataRowNum = 1,
                dataClass = BuyerContractQuoteFileInputRow::class
            )

            logger.info("Generating quotes with autoCreateContract=$autoCreateContract")
            val processedBuyerContractQuoteData = buyerContractQuoteFileParsedData.parallelMap { buyerContractQuoteParsedDataInfo ->
                // Process each row and convert to request

                buyerContractQuoteParsedDataInfo.dataRequestDTO?.let {
                    val contractQuoteRequest = it.toContractQuoteRequest(
                        requestedBy = asyncJob.createdBy,
                        autoCreateContract = autoCreateContract,
                        referenceLid = contactQuoteGidLidMappingHelper.fetchContractQuoteGidLidMappingFromRedis(
                            it.contractCatalogEntityId
                        )?.listingId
                    )
                    val contractQuoteTaggingForCatalogId = contractQuoteTaggingHelper.getContractQuoteTagging(
                        contractCatalogEntity = it.contractCatalogEntity,
                        contractCatalogEntityId = it.contractCatalogEntityId,
                        city = it.city
                    )
                    logger.info("Contract quote request is $contractQuoteRequest")

                    try {
                        contractQuoteTaggingForCatalogId ?: error(
                            "No contract quote tagging found for ${it.contractCatalogEntityId} and ${it.city}"
                        )
                        val contractQuoteResponse = contractQuoteController.generateQuote(
                            contractQuoteRequest,
                            contractQuoteTaggingForCatalogId.strategies.map { strategy -> strategy.trim() }
                        )

                        contractQuoteResponse.contractCreationRemarks?.let { remarks ->
                            logger.info("Contract creation remarks: {}", remarks)
                        }

                        ProcessedDataInfo(
                            row = buyerContractQuoteParsedDataInfo.row,
                            parsingExceptionMessage = buyerContractQuoteParsedDataInfo.parsingExceptionMessage,
                            processingExceptionMessage = null,
                            processingOutput = convertToBuyerContractQuoteProcessResponse(
                                contractQuoteResponse = contractQuoteResponse
                            )
                        )
                    } catch (e: Exception) {
                        logger.error("Error while generating quote for $contractQuoteRequest with error {}", e.message)
                        ProcessedDataInfo(
                            row = buyerContractQuoteParsedDataInfo.row,
                            parsingExceptionMessage = buyerContractQuoteParsedDataInfo.parsingExceptionMessage,
                            processingExceptionMessage = e.message,
                            processingOutput = convertToBuyerContractQuoteProcessResponse(
                                contractQuoteResponse = null
                            )
                        )
                    }
                } ?: ProcessedDataInfo(
                    row = buyerContractQuoteParsedDataInfo.row,
                    parsingExceptionMessage = buyerContractQuoteParsedDataInfo.parsingExceptionMessage,
                    processingExceptionMessage = null,
                    processingOutput = null
                )
            }

            val successfullyProcessedRowCount = processedBuyerContractQuoteData.count {
                it.parsingExceptionMessage == null && it.processingExceptionMessage == null
            }

            val outputFile = fileUtils.updateExcelWithProcessedData(
                jobId = asyncJob.id,
                file = file,
                processedDataInfoList = processedBuyerContractQuoteData
            )

            blobStorageHelper.uploadFileToBlobStorage(
                containerReference = asyncJob.type.toString().lowercase().replace("_", "-"),
                directoryReference = asyncJob.type.toString().lowercase().replace("_", "-"),
                file = outputFile
            )

            val remarks = "$successfullyProcessedRowCount rows are success, " +
                    "${buyerContractQuoteFileParsedData.size - successfullyProcessedRowCount} rows failed"

            asyncJobController.updateAsyncJobStatusAndMetadata(
                jobId = asyncJob.id,
                status = AsyncJobStatus.COMPLETED,
                remarks = remarks,
                outputFilePath = outputFile.name,
                attributesMap = mapOf(
                    AsyncJobConstants.TOTAL to buyerContractQuoteFileParsedData.size.toString(),
                    AsyncJobConstants.SUCCESSFUL to successfullyProcessedRowCount.toString(),
                    AsyncJobConstants.FAILED to (buyerContractQuoteFileParsedData.size - successfullyProcessedRowCount).toString()
                ),
                updatedBy = "SYSTEM"
            )
        } catch (ex: Exception) {
            ex.printStackTrace()
            logger.error("Error while generating quote with error {}", ex.message)
            asyncJobController.updateAsyncJobStatusAndMetadata(
                jobId = asyncJob.id,
                status = AsyncJobStatus.FAILED,
                remarks = "Error while processing job with error " + ex.message,
                outputFilePath = null,
                updatedBy = "SYSTEM"
            )
        }
    }

    /**
     * Before every quote generation we try to set the quote tagging and gid-lid mappings in redis.
     * This is done to ensure that the latest data is used for quote generation.
     */
    private suspend fun setQuoteTaggingAndGidLidMappings() {
        try {
            listOf(
                async {
                    contractQuoteTaggingHelper.setContractQuoteTaggingInRedis()
                },
                async {
                    contactQuoteGidLidMappingHelper.setContractQuoteGidLidMappingsInRedis()
                }
            ).awaitAll()
        } catch (e: Exception) {
            logger.error("Error while setting quote tagging and gid-lid mappings in redis: ${e.message}")
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class BuyerContractQuoteFileInputRow(
        val buyerOrgId: String,
        val city: String,
        val contractCatalogEntityId: String,
        val contractCatalogEntity: String,
        val volumeCommitted: Long,
        val targetUnitPriceInPaisa: Long?,
        val lockInDays: Long = 0,
        val expiryInDays: Long = 0
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class BuyerContractQuoteProcessResponse(
        val quotePriceInPaisa: Long?,
        val quotePriceInPaisaWithTax: Long?,
        val mrpMarkDownBps: Long?,
        val priceValidity: String?,
        val customerSavingsInBps: Long?,
        val bestCompQuoteInPaisa: Long?,
        val bestCompQuoteProvider: String?,
        val marginInBps: Long?,
        val cogsUnitPriceInPaisa: Long?,
        val referenceListingId: String?,
        val contractCreationRemarks: String?
    )

    private fun BuyerContractQuoteFileInputRow.toContractQuoteRequest(
        requestedBy: String,
        autoCreateContract: Boolean,
        referenceLid: String? = null
    ): ContractQuoteRequest {
        return ContractQuoteRequest(
            buyerOrgId = buyerOrgId,
            city = city,
            contractCatalogEntityId = contractCatalogEntityId,
            contractCatalogEntity = contractCatalogEntity,
            volumeCommitted = volumeCommitted,
            targetUnitPrice = targetUnitPriceInPaisa,
            referenceListingId = referenceLid,
            requestedBy = requestedBy,
            autoCreateContract = autoCreateContract,
            lockInDays = lockInDays,
            expiryInDays = expiryInDays
        )
    }

    private fun convertToBuyerContractQuoteProcessResponse(
        contractQuoteResponse: ContractQuoteResponse?
    ): BuyerContractQuoteProcessResponse {
        return BuyerContractQuoteProcessResponse(
            quotePriceInPaisa = contractQuoteResponse?.quotePriceInPaisa,
            customerSavingsInBps = contractQuoteResponse?.customerSavingsInBps,
            bestCompQuoteInPaisa = contractQuoteResponse?.bestCompetitorQuote?.priceInPaisa,
            bestCompQuoteProvider = contractQuoteResponse?.bestCompetitorQuote?.competitor,
            mrpMarkDownBps = contractQuoteResponse?.mrpMarkDownBps,
            cogsUnitPriceInPaisa = contractQuoteResponse?.cogsUnitPriceInPaisa,
            marginInBps = contractQuoteResponse?.marginInBps,
            priceValidity = contractQuoteResponse?.priceValidity,
            quotePriceInPaisaWithTax = contractQuoteResponse?.quotePriceInPaisaWithTax,
            referenceListingId = contractQuoteResponse?.referenceListingId,
            contractCreationRemarks = contractQuoteResponse?.contractCreationRemarks
        )
    }
}
