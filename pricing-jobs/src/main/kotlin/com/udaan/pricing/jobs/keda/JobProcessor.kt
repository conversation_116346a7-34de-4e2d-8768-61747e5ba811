package com.udaan.pricing.jobs.keda

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Guice
import com.google.inject.Inject
import com.google.inject.Singleton
import com.microsoft.azure.storage.queue.CloudQueue
import com.microsoft.azure.storage.queue.CloudQueueClient
import com.microsoft.azure.storage.queue.CloudQueueMessage
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.JobScope
import com.udaan.pricing.job.AsyncJobEvent
import com.udaan.pricing.jobs.PricingJobModule
import com.udaan.pricing.jobs.keda.handlers.AsyncJobHandlerFactory
import com.udaan.pricing.jobs.keda.models.QueueConstants.queueTiming
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.system.exitProcess

@Singleton
class JobProcessor @Inject constructor(
    private val asyncJobHandlerFactory: AsyncJobHandlerFactory,
    private val cloudQueueClient: CloudQueueClient,
    private val objectMapper: ObjectMapper
) {

    companion object {
        private val logger by logger()

        @JvmStatic
        fun main(args: Array<String>) {
            try {
                logger.info("********************** JobProcessor STARTED **********************")
                JobScope.runBlocking {
                    val queueName = args[0]
                    /**
                     * TODO: check if still environment needs to be passed.
                     */
                    val environment = args[1]
                    if (environment.lowercase() == "keda") {
                        System.setProperty("udaan.env", "prod")
                    } else {
                        System.setProperty("udaan.env", environment)
                    }

                    val injector = Guice.createInjector(PricingJobModule())
                    val jobProcessor = injector.getInstance(JobProcessor::class.java)
                    jobProcessor.process(queueName, environment)
                }
                logger.info("********************** JobProcessor Completed **********************")
            } catch (e: Throwable) {
                logger.error("********************** JobProcessor FAILED ********************** {}", e)
                exitProcess(1)
            } finally {
                logger.info("********************** Process End **********************")
                exitProcess(0)
            }
        }

    }

    private suspend fun process(queueName: String, environment: String) {
        logger.info("q name $queueName, environment name $environment")
        val q = cloudQueueClient.getQueueReference(queueName)
        val messages = getMessages(q, queueName)
        messages.forEach {
            val jobEvent = objectMapper.readValue(it.messageContentAsString, AsyncJobEvent::class.java)
            logger.info("Read the message $jobEvent")
            asyncJobHandlerFactory.processJob(jobEvent)
            logger.info("Processed the message $jobEvent")

            q.deleteMessage(it, null, null)
        }
    }

    private suspend fun getMessages(q: CloudQueue, queueName: String): Iterable<CloudQueueMessage> {
        return withContext(Dispatchers.IO) {
            q.retrieveMessages(
                queueTiming[queueName]?.queueLength
                    ?: error("no queueLength defined for $queueName"),
                queueTiming[queueName]?.visibilityTimeout
                    ?: error("no visibility timeout defined for $queueName"),
                null, null
            )
        }
    }
}
