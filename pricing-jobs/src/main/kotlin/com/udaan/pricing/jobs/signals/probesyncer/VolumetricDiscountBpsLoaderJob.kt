package com.udaan.pricing.jobs.signals.probesyncer

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.google.inject.Guice
import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.JobScope
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.core.managers.signals.SignalWriteManager
import com.udaan.pricing.core.svcinterfaces.DpServiceInterface
import com.udaan.pricing.signalcreation.VolumetricSignalInput
import com.udaan.pricing.jobs.utils.PushGatewayUtil
import java.math.BigDecimal
import kotlin.system.exitProcess

class VolumetricDiscountBpsLoaderJob @Inject constructor(
    dpServiceInterface: DpServiceInterface,
    signalWriteManager: SignalWriteManager
) : DPInputProbeSyncer(
    dpServiceInterface, signalWriteManager
) {

    companion object {
        private val logger by logger()
        private val injector = Guice.createInjector(PricingCoreModule())
        private val pushGatewayUtil = PushGatewayUtil("VolumetricDiscountBpsSyncJob", logger)

        @JvmStatic
        fun main(args: Array<String>) {
            val volumetricDiscountBpsLoaderJob = injector.getInstance(VolumetricDiscountBpsLoaderJob::class.java)
            try {
                logger.info("********************** Job STARTED **********************")
                JobScope.runBlocking {
                    pushGatewayUtil.pushJobStarted()
                    pushGatewayUtil.recordStats {
                        volumetricDiscountBpsLoaderJob.process()
                    }
                    pushGatewayUtil.pushJobEnded()
                }
                logger.info("********************** Job Completed **********************")
            } catch (e: Throwable) {
                e.printStackTrace()
                logger.error("********************** Job FAILED **********************", e)
                exitProcess(1)
            } finally {
                logger.error("********************** Process End **********************")
                exitProcess(0)
            }
        }
    }

    /**
     * TODO: update probeId
     */
    override val probeId = "mwhz7b"
    override val chunkSize = 25
    override val delayBetweenChunksInMillis = 1000L

    suspend fun process() {
        if (probeId.isBlank()) {
            logger.error("ProbeId is not set. Exiting the job.")
            return
        }
        val volumetricDiscountBpsData = getDataFromDataPlatform<VolumetricDiscountBps>().filter {
            it.firstLadderMinQuantity > 0 && it.catalogId.isNotBlank() && it.catalogEntity.isNotBlank()
        }

        val convertedLidLevelInputs = volumetricDiscountBpsData.map {
            VolumetricSignalInput(
                catalogEntityId = it.catalogId,
                catalogEntityType = CatalogEntityType.valueOf(it.catalogEntity),
                ladders = it.toListOfLadders(),
                location = Location(
                    locationType = LocationType.CITY,
                    locationValue = it.city,
                ), updatedBy = "DATA_PLATFORM_PROBE"
            )
        }

        processAndSaveRawInputs(convertedLidLevelInputs)
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class VolumetricDiscountBps(
        @JsonProperty("catalogId") val catalogId: String,
        @JsonProperty("catalogEntity") val catalogEntity: String,
        @JsonProperty("city") val city: String,
        @JsonProperty("firstLadderMinQuantity") val firstLadderMinQuantity: Long,
        @JsonProperty("firstLadderDiscountBps") val firstLadderDiscountBps: Long,
        @JsonProperty("secondLadderMinQuantity") val secondLadderMinQuantity: Long?,
        @JsonProperty("secondLadderDiscountBps") val secondLadderDiscountBps: Long?,
        @JsonProperty("thirdLadderMinQuantity") val thirdLadderMinQuantity: Long?,
        @JsonProperty("thirdLadderDiscountBps") val thirdLadderDiscountBps: Long?,
        @JsonProperty("fourthLadderMinQuantity") val fourthLadderMinQuantity: Long?,
        @JsonProperty("fourthLadderDiscountBps") val fourthLadderDiscountBps: Long?
    )

    private fun VolumetricDiscountBps.toListOfLadders(): List<Ladder> {
        val ladders = mutableListOf<Ladder>()
        ladders.add(
            Ladder(
                minQuantity = firstLadderMinQuantity,
                maxQuantity = secondLadderMinQuantity?.let {
                    if (it > 0) it - 1 else Int.MAX_VALUE.toLong()
                } ?: Int.MAX_VALUE.toLong(),
                ladderValue = BigDecimal(firstLadderDiscountBps)
            )
        )
        if (secondLadderMinQuantity != null && secondLadderDiscountBps != null) {
            ladders.add(
                Ladder(
                    minQuantity = secondLadderMinQuantity,
                    maxQuantity = thirdLadderMinQuantity?.let {
                        if (it > 0) it - 1 else Int.MAX_VALUE.toLong()
                    } ?: Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(secondLadderDiscountBps)
                )
            )
        }
        if (thirdLadderMinQuantity != null && thirdLadderDiscountBps != null) {
            ladders.add(
                Ladder(
                    minQuantity = thirdLadderMinQuantity,
                    maxQuantity = fourthLadderMinQuantity?.let {
                        if (it > 0) it - 1 else Int.MAX_VALUE.toLong()
                    } ?: Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(thirdLadderDiscountBps)
                )
            )
        }
        if (fourthLadderMinQuantity != null && fourthLadderDiscountBps != null) {
            ladders.add(
                Ladder(
                    minQuantity = fourthLadderMinQuantity,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = BigDecimal(fourthLadderDiscountBps)
                )
            )
        }
        addFirstLadderIfNotAvailable(ladders)
        return ladders.sortedBy { it.minQuantity }
    }

    private fun addFirstLadderIfNotAvailable(ladders: MutableList<Ladder>) {
        ladders.sortBy { it.minQuantity }
        if (ladders.isNotEmpty() && ladders.first().minQuantity > 1) {
            ladders.add(
                Ladder(
                    minQuantity = 1,
                    maxQuantity = (ladders[0].minQuantity - 1).coerceAtLeast(1),
                    ladderValue = BigDecimal(0)
                )
            )
        }
    }
}