package com.udaan.pricing.jobs.models.contracts

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

data class QuoteAlertSummary(
    val quoteAlertSummaryForBuyerList: List<QuoteAlertSummaryForBuyer>,
    val quoteDataToRaiseAlert: List<QuoteAlertOutputFileRow>
)

data class QuoteAlertSummaryForBuyer(
    val buyerOrgId: String,
    val buyerName: String,
    val skusLiveOnContract: Int,
    val skusWith5PercentBelowQuotePrice: Int,
    val skusWith5PercentAboveQuotePrice: Int
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class QuoteAlertOutputFileRow(
    val buyerOrgId: String,
    val buyerName: String,
    val city: String,
    val contractCatalogEntityId: String,
    val contractCatalogEntity: String,
    val catalogTitle: String,
    val volumeCommitted: Long,
    val targetUnitPriceInPaisa: Long?,
    val quotePriceInPaisa: Long?,
    val quotePriceInPaisaWithTax: Long?,
    val mrpMarkDownBps: Long?,
    val priceValidity: String?,
    val customerSavingsInBps: Long?,
    val bestCompQuoteInPaisa: Long?,
    val bestCompQuoteProvider: String?,
    val marginInBps: Long?,
    val cogsUnitPriceInPaisa: Long?,
    val contractPrice: Long?,
    val contractMrpMarkDownBps: Long?,
    val quotePriceDiffInBps: Long?,
    val quoteMrpMarkDownDiffInBps: Long?,
    val remarks: String
)
