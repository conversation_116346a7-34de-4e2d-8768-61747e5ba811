package com.udaan.pricing.jobs.keda.handlers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.pricing.core.managers.signals.AsyncJobController
import com.udaan.pricing.job.AsyncJobEvent
import com.udaan.pricing.job.AsyncJobType

@Singleton
class AsyncJobHandlerFactory @Inject constructor(
    private val buyerContractJobHandler: BuyerContractJobHandler,
    private val buyerContractQuoteGenerationHandler: BuyerContractQuoteGenerationHandler,
    private val manualPriceJobHandler: ManualPriceJobHandler,
    private val asyncJobController: AsyncJobController,
    private val manualSignalInputJobHandler: ManualSignalInputJobHandler
) {

    /**
     * Process the job based on the type of the job.
     */
    suspend fun processJob(asyncJobEvent: AsyncJobEvent) {
        val asyncJob = asyncJobController.getAsyncJob(asyncJobEvent.jobId)
            ?: error("No job exists with id ${asyncJobEvent.jobId}")
        getHandler(asyncJob.type).processJob(asyncJob)
    }

    /**
     * Get the handler based on the type of the job.
     */
    private fun getHandler(type: AsyncJobType): AsyncJobHandler {
        return when (type) {
            AsyncJobType.BUYER_CONTRACTS -> buyerContractJobHandler
            AsyncJobType.BUYER_CONTRACT_QUOTES -> buyerContractQuoteGenerationHandler
            AsyncJobType.MANUAL_PRICE -> manualPriceJobHandler
            AsyncJobType.MANUAL_SIGNAL_INPUT -> manualSignalInputJobHandler
            else -> error("No handler found for type $type")
        }
    }
}
