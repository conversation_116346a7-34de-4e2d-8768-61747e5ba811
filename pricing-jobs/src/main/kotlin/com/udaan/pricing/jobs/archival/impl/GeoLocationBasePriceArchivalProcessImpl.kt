package com.udaan.pricing.jobs.archival.impl

import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.GeoLocationBasePrice
import com.udaan.pricing.core.controller.cache.GeoBasePriceCacheController
import com.udaan.pricing.core.dao.GeoLocationBasePriceRepository
import com.udaan.pricing.core.utils.SlackChannelEnum
import com.udaan.pricing.core.utils.SlackNotifier
import com.udaan.pricing.jobs.archival.abstraction.ArchivalProcess
import com.udaan.pricing.jobs.archival.models.ArchiveCountOverTime
import java.util.*

class GeoLocationBasePriceArchivalProcessImpl @Inject constructor(
    private val geoLocationBasePriceRepository: GeoLocationBasePriceRepository,
    private val geoBasePriceCacheController: GeoBasePriceCacheController
) : ArchivalProcess<GeoLocationBasePrice> {

    private val probeId = "iulunt"

    private val slackNotifier = SlackNotifier(SlackChannelEnum.RETAIL_DB_ARCHIVAL_ALERTS.slackUrl)

    private val log by logger()

    override fun getTimeStepperInSeconds(): Long {
        /** one hour in epoch **/
        return 3600
    }

    override fun getTtl(): Long {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.YEAR, -1);
        return calendar.timeInMillis.div(1000)
    }

    override fun fetchProbeId(): String {
        return probeId
    }

    override suspend fun fetchDataCountFromSource(fromTs: Long, toTs: Long): Long {
        val count = geoLocationBasePriceRepository.fetchDataCountByTimeRange(fromTimeStamp = fromTs, toTimeStamp = toTs)
        log.info("Fetched count from source $count with $fromTs and $toTs")
        return count
    }

    override suspend fun fetchDataFromSource(fromTs: Long, toTs: Long): Collection<GeoLocationBasePrice> {
        val data = geoLocationBasePriceRepository.fetchDataByTimeRange(fromTimeStamp = fromTs, toTimeStamp = toTs)
        log.info("Fetched data from source ${data.size} with $fromTs and $toTs")
        return data
    }

    override suspend fun deleteItems(items: Collection<GeoLocationBasePrice>) {
        geoLocationBasePriceRepository.deleteItems(items).also {
            geoBasePriceCacheController.invalidateCachedGeoBasePriceForListings(
                items.map { it.listingId }.distinct()
            )
        }
    }

    override suspend fun onSourceAndArchivalCountMismatch(
        sourceCount: Long,
        archivalCount: Long,
        item: ArchiveCountOverTime
    ) {
        if (sourceCount != 0L && (sourceCount != archivalCount)) {
            slackNotifier.sendMessage(
                notifyTo = "<@satya.e>",
                message = "geo-base-price collection has mismatch between  sourceCount: $sourceCount, " +
                        "archivalCount: $archivalCount for $item"
            )
        }
    }
}