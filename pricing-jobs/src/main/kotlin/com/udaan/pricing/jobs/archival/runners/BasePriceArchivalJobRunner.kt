package com.udaan.pricing.jobs.archival.runners

import com.google.inject.Guice
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.JobScope
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.core.dao.GeoLocationBasePriceRepository
import com.udaan.pricing.core.dao.GeoPriceRepository
import com.udaan.pricing.jobs.archival.impl.BasePriceArchivalProcessImpl
import com.udaan.pricing.jobs.archival.impl.GeoLocationBasePriceArchivalProcessImpl
import com.udaan.pricing.jobs.archival.impl.GeoPriceArchivalProcessImpl
import kotlin.system.exitProcess

class BasePriceArchivalJobRunner {
    companion object {
        private val log by logger()
        private val injector = Guice.createInjector(PricingCoreModule())
        private val basePriceArchivalProcessImpl = injector.getInstance(BasePriceArchivalProcessImpl::class.java)

        @JvmStatic
        fun main(args: Array<String>) {
            JobScope.runBlocking {
                try {
                    log.info("Job STARTED")
                    basePriceArchivalProcessImpl.process()
                    log.info("Job SUCCESSFUL")
                } catch (e: Throwable) {
                    log.error("Job FAILED", e)
                    exitProcess(1)
                } finally {
                    exitProcess(0)
                }
            }
        }
    }
}