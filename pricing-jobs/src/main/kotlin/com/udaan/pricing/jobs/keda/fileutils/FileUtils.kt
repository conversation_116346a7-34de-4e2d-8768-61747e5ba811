package com.udaan.pricing.jobs.keda.fileutils

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.jobs.extensions.isRowEmpty
import com.udaan.pricing.jobs.extensions.value
import com.udaan.pricing.jobs.models.ParsedDataInfo
import com.udaan.pricing.jobs.models.ProcessedDataInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import java.io.File
import java.io.FileOutputStream
import kotlin.reflect.KClass

class FileUtils @Inject constructor(
    private val objectMapper: ObjectMapper
) {
    companion object {
        private val logger by logger()
    }

    /**
     * Converts a row to a map of column name to cell index
     */
    private fun createHeaderMap(
        columnNames: Collection<String>,
        headerRow: Row
    ): Map<String, Int> {
        val result = mutableMapOf<String, Int>()
        for (cell in headerRow.cellIterator()) {
            if (columnNames.contains(cell.stringCellValue)) {
                result[cell.stringCellValue] = cell.columnIndex
            }
        }
        return result
    }

    /**
     * Converts a row to a map of column name to cell value
     */
    private fun Row.getRowData(
        headerMap: Map<String, Int>
    ): Map<String, Any?> {
        return headerMap.map { header ->
            header.key to headerMap[header.key]?.let {
                getCell(it, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL)
            }?.value()
        }.toMap()
    }

    /**
     * Reads an Excel file and converts each row to a data object and returns a list of data objects
     */
    suspend fun <T : Any> readExcelFile(
        jobId: String,
        file: File,
        sheetNum: Int,
        headerNum: Int,
        dataRowNum: Int,
        dataClass: KClass<T>
    ): List<ParsedDataInfo<T>> {
        val workbook = withContext(Dispatchers.IO) { XSSFWorkbook(file.inputStream()) }
        val sheet = workbook.getSheetAt(sheetNum)
        val headerRow = sheet.getRow(headerNum)
        val headers = (0 until headerRow.physicalNumberOfCells).map { headerRow.getCell(it).stringCellValue }
        val headerMap = createHeaderMap(columnNames = headers, headerRow = headerRow)
        val allParsedDataInfo = mutableListOf<ParsedDataInfo<T>>()
        for (rowIndex in dataRowNum..sheet.lastRowNum) {
            val row = sheet.getRow(rowIndex)
            val parsedRowDataInfo = try {
                if (row == null || row.isRowEmpty()) {
                    continue
                }
                val rowData = row.getRowData(headerMap)

                val dataInstance: T = objectMapper.convertValue(rowData, dataClass.java)
                ParsedDataInfo(
                    row = row.rowNum,
                    parsingExceptionMessage = null,
                    dataRequestDTO = dataInstance
                )
            } catch (e: Exception) {
                logger.error(
                    "JobId: {}, Row {}, Exception while parsing data: {}",
                    jobId,
                    row.rowNum,
                    e.message
                )

                ParsedDataInfo<T>(
                    row = row.rowNum,
                    parsingExceptionMessage = e.message,
                    dataRequestDTO = null
                )
            }

            allParsedDataInfo.add(parsedRowDataInfo)
        }
        workbook.close()
        return allParsedDataInfo
    }

    /**
     * Generates an Excel file from a list of data objects
     */
    suspend inline fun <reified T : Any> generateFile(
        response: List<T>?,
        fileName: String
    ): File? {
        if (response.isNullOrEmpty()) {
            return null
        }
        // Create a new workbook and a sheet
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("Sheet1")

        // Write header row if the response is not empty
        if (response.isNotEmpty()) {
            val headerRow = sheet.createRow(0)
            val fields = T::class.java.declaredFields
            fields.forEachIndexed { index, field ->
                headerRow.createCell(index).setCellValue(field.name)
            }

            // Write data rows
            response.forEachIndexed { rowIndex, item ->
                val row = sheet.createRow(rowIndex + 1)
                fields.forEachIndexed { colIndex, field ->
                    field.isAccessible = true
                    val value = field.get(item)?.toString() ?: ""
                    row.createCell(colIndex).setCellValue(value)
                }
            }
        }

        // Write the workbook to a file
        val outputFile = File(fileName)
        withContext(Dispatchers.IO) {
            FileOutputStream(outputFile).use { fos ->
                workbook.write(fos)
            }
        }
        workbook.close()
        return outputFile
    }

    /**
     * Updates an existing Excel file with processed data information
     * This function takes a list of ProcessedDataInfo objects and updates the corresponding rows
     * in the Excel file with the processing results, without modifying existing input columns
     */
    suspend inline fun <reified T : Any> updateExcelWithProcessedData(
        jobId: String,
        file: File,
        processedDataInfoList: List<ProcessedDataInfo<out T>>
    ): File {
        val resultColumnNames = listOf(
            "Parse Exception Message",
            "Process Exception Message"
        ) + T::class.java.declaredFields.map { it.name }

        // Open the existing workbook
        val outputWb = withContext(Dispatchers.IO) {
            XSSFWorkbook(file.inputStream())
        }
        val sheet = outputWb.getSheetAt(0)

        // Get excel header row
        val headerRow = sheet.getRow(0) ?: throw IllegalStateException("Header row not found in the Excel file")

        // Determine where to start adding result columns
        val startColIndex = headerRow.physicalNumberOfCells

        // Add result column headers if they don't exist
        resultColumnNames.forEachIndexed { index, columnName ->
            var cell = headerRow.getCell(startColIndex + index, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK)
            if (cell == null) {
                cell = headerRow.createCell(startColIndex + index)
                cell.setCellValue(columnName)
            }
        }

        // Update rows with processed data - only adding result columns, not modifying input columns
        processedDataInfoList.forEach { processedData ->
            val rowIndex = processedData.row
            val row = sheet.getRow(rowIndex) ?: throw IllegalStateException("Row $rowIndex not found in the Excel file")

            // Add parse exception message column
            val parseExceptionCell = row.getCell(startColIndex, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)
            parseExceptionCell.setCellValue(processedData.parsingExceptionMessage ?: "N/A")

            // Add processing exception message column
            val processingExceptionCell = row.getCell(startColIndex + 1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)
            processingExceptionCell.setCellValue(processedData.processingExceptionMessage ?: "N/A")

            T::class.java.declaredFields.forEachIndexed { colIndex, field ->
                field.isAccessible = true
                val value = field.get(processedData.processingOutput)?.toString() ?: ""
                row.createCell(startColIndex + 2 + colIndex).setCellValue(value)
            }
        }

        // Write the workbook to a file
        val outputFile = File(jobId + "_output.xlsx")
        withContext(Dispatchers.IO) {
            FileOutputStream(outputFile).use { fos ->
                outputWb.write(fos)
            }
        }
        file.delete()
        outputWb.close()

        return outputFile
    }
}
