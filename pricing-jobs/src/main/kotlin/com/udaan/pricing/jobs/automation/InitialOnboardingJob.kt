package com.udaan.pricing.jobs.automation

import com.google.inject.Guice
import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.JobScope
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.core.managers.PortfolioItemManager
import com.udaan.pricing.core.managers.PortfolioManager
import com.udaan.pricing.core.managers.PortfolioPlanManager
import com.udaan.pricing.core.managers.StrategyManager
import com.udaan.pricing.portfolioitem.CatalogEntityLevel
import com.udaan.pricing.portfolioitem.CreatePortfolioItemRequest
import com.udaan.pricing.portfolioitem.DeletePortfolioItemRequest
import com.udaan.pricing.portfolioitem.PortfolioItem
import com.udaan.pricing.portfolioplan.CohortInput
import com.udaan.pricing.portfolioplan.PortfolioPlanRequest
import com.udaan.pricing.strategy.CreateStrategyRequest
import com.udaan.pricing.strategy.Strategy
import com.udaan.pricing.strategy.StrategyState
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.variable.VariableId
import java.math.BigDecimal
import kotlin.system.exitProcess

class InitialOnboardingJob @Inject constructor(
    private val strategyManager: StrategyManager,
    private val portfolioManager: PortfolioManager,
    private val portfolioPlanManager: PortfolioPlanManager,
    private val portfolioItemManager: PortfolioItemManager
) {
    companion object {
        private val logger by logger()
    }

    /**
     * This creates and then activates the strategy.
     */
    private suspend fun createAndActivateStrategy(createStrategyRequests: List<CreateStrategyRequest>, updatedBy: String): List<Strategy> {
        return createStrategyRequests.mapNotNull { createStrategyRequest ->
            strategyManager.createStrategy(createStrategyRequest)?.let {
                strategyManager.updateStateForStrategyId(
                    strategyId = it.id,
                    newState = StrategyState.ACTIVE,
                    updatedBy = updatedBy
                )
            }
        }
    }

    /**
     * This creates all plans for respective cohorts for given portfolioId and list of strategies.
     * The map provides cohort to cohortInput value.
     *
     * This fun is highly limiting in the sense that it applies same list of strategies to all plans and
     * assumes only one cohort variables is required for all policies.
     * But again, this is to start with velocity and not to support complex flows.
     */
    private suspend fun createPlansForPortfolioWithCohortInput(
        cohortWiseInput: Map<String, BigDecimal>,
        cohortWiseSecondInput: Map<String, BigDecimal>,
        portfolioId: String,
        strategies: List<String>,
        createdBy: String
    ) {
        cohortWiseInput.forEach { (cohort, cohortInput) ->
            portfolioPlanManager.createOrUpdatePortfolioPlan(
                PortfolioPlanRequest(
                    portfolioId = portfolioId,
                    strategies = strategies,
                    buyerCohort = cohort,
                    cohortInputs = listOf(
                        CohortInput(
                            variableId = VariableId.VSL_MARKUP_BPS,
                            value = BigDecimalValue(
                                value = cohortInput
                            )
                        ),
                        CohortInput(
                            variableId = VariableId.DISCOUNT_SLABS_MULTIPLIER,
                            value = BigDecimalValue(
                                value = cohortWiseSecondInput[cohort]!!
                            )
                        )
                    ),
                    createdBy = createdBy
                )
            )
        }
    }

    /**
     * This fun creates items basis passed params.
     */
    private suspend fun createPortfolioItems(
        entities: List<String>,
        entityLevel: CatalogEntityLevel,
        locationValues: List<String>,
        locationType: LocationType,
        portfolioId: String,
        createdBy: String
    ) : List<PortfolioItem>{
        return entities.map { entity ->
            locationValues.map { locationValue ->
                portfolioItemManager.createPortfolioItemTagging(
                    CreatePortfolioItemRequest(
                        catalogEntity = entity,
                        entityLevel = entityLevel,
                        location = Location(
                            locationType = locationType,
                            locationValue = locationValue
                        ),
                        portfolioId = portfolioId,
                        createdBy = createdBy,
                        jobIdReference = "localScript"
                    )
                )
            }
        }.flatten()
    }

    private suspend fun deletePortfolioItems(
        entities: List<String>,
        entityLevel: CatalogEntityLevel,
        locationValue: String,
        locationType: LocationType,
        deletedBy: String,
        reason: String
    ): List<PortfolioItem?> {
        return entities.map { catalogEntity ->
            portfolioItemManager.deletePortfolioItemTagging(
                DeletePortfolioItemRequest(
                    catalogEntity = catalogEntity,
                    entityLevel = entityLevel,
                    location = Location(
                        locationType = locationType,
                        locationValue = locationValue
                    ),
                    deletedBy = deletedBy,
                    deletionReason = reason,
                )
            )
        }
    }

    suspend fun process() {
        // create strategies passing req params
        /*val createdBy = "<EMAIL>"
        val createStrategyRequests = listOf(
            CreateStrategyRequest(
                name = "TRADING_KP_MATCH_V7",
                type = StrategyType.FORMULA,
                conditionalFormulae = emptyList(),
                usedVariables = listOf(
                    VariableId.OFFLINE_COMPETITION_WT_PAISA_UNIT,
                    VariableId.STAPLES_LIP_WOT_PAISA_UNIT,
                    VariableId.GST_BPS,
                    VariableId.CESS_BPS,
                    VariableId.BMT_GID_BPS,
                    VariableId.BMT_MULTIPLIER,
                    VariableId.CONVERSION_RATE,
                    VariableId.KP_ABS_MARKUP_WOT_PAISA_UNIT,
                    VariableId.STAPLES_LPP_WOT_RUPEES_UNIT
                ),
                mandatoryVariables = listOf(
                    VariableId.GST_BPS,
                    VariableId.CESS_BPS,
                    VariableId.BMT_GID_BPS,
                    VariableId.BMT_MULTIPLIER,
                    VariableId.CONVERSION_RATE
                ),
                createdBy = createdBy
            ),
            CreateStrategyRequest(
                name = "TRADING_MANUAL_V5",
                type = StrategyType.FORMULA,
                conditionalFormulae = emptyList(),
                usedVariables = listOf(
                    VariableId.GRANARY_INVENTORY_FLAG,
                    VariableId.TRADING_PRICE_WOT_PAISA_UNIT,
                    VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT,
                    VariableId.STAPLES_LPP_WOT_RUPEES_UNIT,
                    VariableId.BMT_GID_BPS,
                    VariableId.CONVERSION_RATE
                ),
                mandatoryVariables = listOf(
                    VariableId.BMT_GID_BPS,
                    VariableId.CONVERSION_RATE
                ),
                createdBy = createdBy
            ),
            CreateStrategyRequest(
                name = "TRADING_BEAST_MODE_V5",
                type = StrategyType.FORMULA,
                conditionalFormulae = emptyList(),
                usedVariables = listOf(
                    VariableId.STAPLES_LIP_WOT_PAISA_UNIT,
                    VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT,
                    VariableId.STAPLES_LPP_WOT_RUPEES_UNIT,
                    VariableId.BMT_GID_BPS,
                    VariableId.BMT_MULTIPLIER,
                    VariableId.CONVERSION_RATE
                ),
                mandatoryVariables = listOf(
                    VariableId.BMT_GID_BPS,
                    VariableId.BMT_MULTIPLIER,
                    VariableId.CONVERSION_RATE
                ),
                createdBy = createdBy
            ),
            CreateStrategyRequest(
                name = "TRADING_DEFAULT_V7",
                type = StrategyType.FORMULA,
                conditionalFormulae = emptyList(),
                usedVariables = listOf(
                    VariableId.GRANARY_INVENTORY_FLAG,
                    VariableId.STAPLES_LIP_WOT_PAISA_UNIT,
                    VariableId.FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT,
                    VariableId.STAPLES_LPP_WOT_RUPEES_UNIT,
                    VariableId.BMT_GID_BPS,
                    VariableId.BMT_MULTIPLIER,
                    VariableId.CONVERSION_RATE
                ),
                mandatoryVariables = listOf(
                    VariableId.BMT_GID_BPS,
                    VariableId.BMT_MULTIPLIER,
                    VariableId.CONVERSION_RATE
                ),
                createdBy = createdBy
            )
        )
        createAndActivateStrategy(createStrategyRequests, createdBy)*/

        // create portfolio
        /*portfolioManager.createPortfolio(
            PortfolioRequest(
                name = "STAPLES_RANGE_BASE_WITHOUT_VSL_MARKUP",
                category = PortfolioCategory.STAPLES,
                geoPricingEnabled = true,
                tradingEnabled = false,
                createdBy = "<EMAIL>"
            )
        )*/

        // create plans for a portfolio
        /*val vslMarkupBpsToCohortMap = mapOf(
                "PACMAN_A" to BigDecimal(0),
                "PACMAN_B" to BigDecimal(0),
                "PACMAN_C" to BigDecimal(0),
                "PACMAN_D" to BigDecimal(0),
                "HORECA_A" to BigDecimal(0),
                "HORECA_B" to BigDecimal(0),
                "HORECA_C" to BigDecimal(0),
                "HORECA_D" to BigDecimal(0),
                "DEFAULT_COHORT" to BigDecimal(0)
        )
        val discSlabsMultiplierToCohortMap = mapOf(
            "PACMAN_A" to BigDecimal(1),
            "PACMAN_B" to BigDecimal(1),
            "PACMAN_C" to BigDecimal(1),
            "PACMAN_D" to BigDecimal(1),
            "HORECA_A" to BigDecimal(1),
            "HORECA_B" to BigDecimal(1),
            "HORECA_C" to BigDecimal(1),
            "HORECA_D" to BigDecimal(1),
            "DEFAULT_COHORT" to BigDecimal(1)
        )
        val portfolioId = "PFCXAC9YZYWL4GEVNAL94L"
        val strategies = listOf( "STGI7C33PC6P466T4GUFLBL","STG81VXW3CRT8XBYTBSQE5E","STGY27KIZMDJJK1C82L813N","STG11IATIDEP4TZ12WEPCOY")
        val createdBy = "<EMAIL>"
        createPlansForPortfolioWithCohortInput(vslMarkupBpsToCohortMap, discSlabsMultiplierToCohortMap, portfolioId, strategies, createdBy)*/

        // create portfolio items for a portfolio
        val entities = listOf("GRIVKRLNZXFVZZIOM614")
        val entityLevel = CatalogEntityLevel.PRODUCT_GROUP_ID
        val locationValues = listOf("ORU1Z7Q3GMD2TQ81GSH6QKTG2RPXC","ORU15X4N921FFDKZF51WHPJ0K9EPB","ORUBX4DJYEL3M8XQFTGC79ED9CX91","ORUG7LFRB57BX8QHFE3ZMNPQ7FVY9","ORU7R0JYZ4YNJDN84ZCQGLE9CBF92","ORUFKV8BWV9DP8HWZ7W6KXQ7ZN45L","ORUFQ3K5GP6CM8TKZRPGNZHPV66XC","ORULLG1HN5SN588H4VG4BF9X0PG2F","ORU4LCKHK3TZMDC849VR55ZM3QGZB","ORUE5BD1ZXMPVCHN4DNS66N4WMMY7","ORU46HY5P31QX86TFWD9XP9NZEJTM","ORU4H7PF9KERFDY4G3BP3R9V2CTFR","ORUKZPR1M0WVQ8C6F2GXFXW9MWG26","ORUMS0Q88F65BCN642TQCC2TC7FVF","ORUXXW1V72XVCQ5SGF7FZVTP9R1XR","ORUZ07NWL1XZ2D724CS4EHNLTBX5Z","ORU1VF52JN972CLV4SMMMHMXEQ408","ORU112TEHHYJMDC6GF59LCBTHMZ8M","ORU7JNJK3WW458L6FK8Z9XGV01SV8")
        val locationType = LocationType.WAREHOUSE
        val portfolioId = "PFJKA16XEEC2ARMXRW3S9S"
        val createdBy = "<EMAIL>"
        createPortfolioItems(entities, entityLevel, locationValues, locationType, portfolioId, createdBy)

        // delete portfolio items
        /*val entities = listOf("GRIV2J2S2KSYWQI86HUC","GRIVZU1EZ0FMPJVWVPPQ")
        val entityLevel = CatalogEntityLevel.PRODUCT_GROUP_ID
        val locationValue = "ORU112TEHHYJMDC6GF59LCBTHMZ8M"
        val locationType = LocationType.WAREHOUSE
        val deletedBy = "<EMAIL>"
        val reason = "movingToPortfolio: PFCXAC9YZYWL4GEVNAL94L"
        deletePortfolioItems(entities, entityLevel, locationValue, locationType, deletedBy, reason)*/
    }
}

fun main(args: Array<String>) {
    JobScope.runBlocking {
        System.setProperty("udaan.env", "prod")
        try{
            val injector = Guice.createInjector(PricingCoreModule())
            val tempJob = injector.getInstance(InitialOnboardingJob::class.java)
            tempJob.process()
            exitProcess(0)
        }catch (ex: Exception) {
            println("exception $ex")
            exitProcess(1)
        }
    }
}
