package com.udaan.pricing.jobs.models.contracts

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

data class VolumeAlertSummary(
    val volumeAlertSummaryForBuyerList: List<VolumeAlertSummaryForBuyer>,
    val volumeDataToRaiseAlert: List<VolumeAlertOutputFileRow>
)

data class VolumeAlertSummaryForBuyer(
    val buyerOrgId: String,
    val buyerName: String,
    val skusLiveOnContract: Long,
    val skusWith25PercentVolumeAchievement: Long,
    val skusWith50PercentVolumeAchievement: Long
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class VolumeAlertOutputFileRow(
    val buyerOrgId: String,
    val buyerName: String,
    val city: String,
    val contractCatalogEntityId: String,
    val catalogTitle: String,
    val contractCatalogEntity: String,
    val volumeCommitted: Long,
    val expectedVolumeTillNow: Long,
    val volumeOrderedTillNow: Long,
    val volumeOrderedRefreshedTill: String,
    val volumeOrderedRefreshedTillInDate: String
)
