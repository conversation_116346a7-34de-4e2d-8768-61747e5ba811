package com.udaan.pricing.jobs.network

import com.google.inject.Guice
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.JobScope
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.core.cache.network.DemandClusterCacheRepo
import com.udaan.pricing.core.cache.network.DemandClusterLocationCacheRepo
import com.udaan.pricing.core.dao.network.DemandClusterLocationsRepository
import com.udaan.pricing.core.dao.network.DemandClusterRepository
import com.udaan.pricing.jobs.utils.XlsxReader
import com.udaan.pricing.network.DemandCluster
import com.udaan.pricing.network.DemandClusterLocations
import com.udaan.pricing.network.WarehouseDetails
import kotlin.system.exitProcess

@Singleton
class NetworkMigrationJob @Inject constructor(
    private val demandClusterCacheRepo: DemandClusterCacheRepo,
    private val demandClusterLocationCacheRepo: DemandClusterLocationCacheRepo
) {
    companion object {
        private val logger by logger()

        private const val WH_SHEET = 0
        private const val DC_SHEET = 1
        private const val LOC_SHEET = 2

        // col count mapping for each sheet
        private const val WH_COLS = 5
        private const val DC_COLS = 2
        private const val LOC_COLS = 5

        //consts for inputLocationChain sheet
        private const val ILC_SHEET = 0
        private const val ILC_COLS = 4
    }

    enum class IlcType {
        VERTICAL, GID
    }

    private fun mapWhDataFromExcel(source: List<List<String>>): Pair<Map<String, List<WarehouseDetails>>, List<WarehouseDetails>> {
        val whs = mutableListOf<WarehouseDetails>()
        val whsForDc = mutableMapOf<String, List<WarehouseDetails>>()
        var count = 2
        try {
            source.forEach {
                if (it[0].isBlank() || it[1].isBlank()
                    || it[2].isBlank() || it[4].isBlank() || it[5].isBlank()
                ) {
                    throw IllegalArgumentException("Invalid field value in WhDetails sheet at row $count")
                }

                logger.info(it[0] + ", " + it[1] + ", " + it[2] + ", " + it[3] + ", " + it[4] + ", " + it[5])
                val whId = it[0].lowercase()
                val name = it[1]
                val city = it[2]
                val category = if (it[3].isNotBlank()) it[3] else ""
                val type = it[4]
                val anchorCity = it[5].lowercase()

                val wh = WarehouseDetails(
                    whId = whId,
                    name = name,
                    city = city,
                    type = type,
                    category = category
                )
                whs.add(wh)

                //we need to create list of all suppliers of an agent, map it in
                if (whsForDc[anchorCity] == null) {
                    val dcWhs = mutableListOf<WarehouseDetails>()
                    dcWhs.add(wh)
                    whsForDc[anchorCity] = dcWhs
                } else {
                    val dcWhs = whsForDc[anchorCity] as MutableList<WarehouseDetails>
                    dcWhs.add(wh)
                    whsForDc[anchorCity] = dcWhs
                }
                count += 1
            }
        } catch (ex: Exception) {
            logger.error("Exception in row $count for WHs sheet", ex)
            throw ex
        }
        return Pair(whsForDc, whs)
    }

    private fun mapDcDataFromExcel(
        source: List<List<String>>,
        whs: Map<String, List<WarehouseDetails>>
    ): List<DemandCluster> {
        val dcs = mutableListOf<DemandCluster>()
        var count = 2
        try {
            source.forEach {
                if (it[0].isBlank() || it[1].isBlank()
                    || it[2].isBlank()
                ) {
                    throw IllegalArgumentException("Invalid field value in DemandCluster sheet at row $count")
                }

                logger.info(it[0] + ", " + it[1] + ", " + it[2])
                val demandClusterName = it[0].lowercase()
                val anchorCityName = it[1].lowercase()
                val createdBy = it[2]

                val dc = DemandCluster(
                    demandClusterName = demandClusterName,
                    anchorCityName = anchorCityName,
                    fulfilmentCenters = whs[anchorCityName]
                        ?: throw Exception("No Whs found for value in DemandCluster sheet at row $count"),
                    createdBy = createdBy,
                    updatedBy = null
                )
                dcs.add(dc)
                count += 1
            }
        } catch (ex: Exception) {
            logger.error("Exception in row $count for DemandCluster sheet", ex)
            throw ex
        }
        return dcs
    }

    private fun mapLocationDataFromExcel(
        source: List<List<String>>,
        whs: List<WarehouseDetails>,
        dcs: Map<String, DemandCluster>
    ): List<DemandClusterLocations> {
        val locations = mutableListOf<DemandClusterLocations>()
        var count = 2
        try {
            source.forEach {
                if (it[0].isBlank() || it[1].isBlank()
                    || it[2].isBlank() || it[3].isBlank()
                    || it[4].isBlank() || it[5].isBlank()
                ) {
                    throw IllegalArgumentException("Invalid field value in locations sheet at row $count")
                }

                logger.info(it[0] + ", " + it[1] + ", " + it[2] + ", " + it[3] + ", " + it[4] + ", " + it[5])
                val name = it[0].lowercase()
                val type =
                    LocationType.values().firstOrNull { locType -> locType.name.equals(it[1], ignoreCase = true) }
                        ?: throw Exception("Type value is invalid at row $count in Locations sheet")
                val city = it[2].lowercase()
                val whIdsStr = it[3]
                val createdBy = it[4]
                val anchorCity = it[5].lowercase()

                //derived values
                val dc = dcs[anchorCity]
                val whIds = whIdsStr.split(',').map { str -> str.trim() }.map { str -> str.lowercase() }
                val whIdMap = whs.groupBy { wh -> wh.whId }.mapValues { whh -> whh.value.first() }
                val whApplicable = whIds.map { whId ->
                    whIdMap[whId]!!
                }

                val location = DemandClusterLocations(
                    name = name,
                    type = type,
                    city = city,
                    demandClusterId = dc?.id
                        ?: throw Exception("No valid DC found for location at row $count in locations sheet"),
                    fulfilmentCenters = whApplicable,
                    createdBy = createdBy,
                    updatedBy = null
                )
                locations.add(location)
                count += 1
            }
        } catch (ex: Exception) {
            logger.error("Exception in row $count for locations sheet", ex)
            throw ex
        }
        return locations
    }

    /**
     *  This allows adding DemandCluster and DemandClusterLocation to pricing network system.
     *  For this to work we need data in certain format in excel file and file added in jobs -> resources -> networkConfig.xlsx
     *  Sample link - https://udaandotcom-my.sharepoint.com/:x:/g/personal/nirvantosh_m_udaan_com/EcwMpefUeDJPjPy21P1Sc5MBPHySxFaJzb8fJFqrYvWKQw?e=HPQyhV
     *
     *  NOTE: this is only to be used when DC and DCL both are getting created first time. This doesn't fetch existing DC if there, hence chances of duplication.
     */
    private suspend fun onboardNetworkConfig() {
        var whsForAnchorcity = mutableMapOf<String, List<WarehouseDetails>>()
        var whs = mutableListOf<WarehouseDetails>()
        val dcData = mutableListOf<DemandCluster>()
        val locationData = mutableListOf<DemandClusterLocations>()

        // reading excel and passing relevant sheet data to respective funs
        val sheetColMap = mapOf(
            WH_SHEET to listOf(0..WH_COLS).flatten(),
            DC_SHEET to listOf(0..DC_COLS).flatten(),
            LOC_SHEET to listOf(0..LOC_COLS).flatten()
        )
        var count = 0
        val source = this::class.java.classLoader.getResourceAsStream("networkConfig.xlsx")
            ?: throw IllegalArgumentException("Cannot find File")

        XlsxReader.readMultipleSheets(
            inputStream = source,
            startRow = 1,
            sheetColMap = sheetColMap
        ).forEach { sheet ->
            when (count) {
                // mapping WHs
                WH_SHEET -> {
                    val allWhData = mapWhDataFromExcel(sheet)
                    logger.info("allWhData: $allWhData")
                    whsForAnchorcity = allWhData.first as MutableMap<String, List<WarehouseDetails>>
                    logger.info("whsForAnchorCity: $whsForAnchorcity")
                    whs = allWhData.second as MutableList<WarehouseDetails>
                }
                // mapping DC
                DC_SHEET -> {
                    dcData.addAll(mapDcDataFromExcel(sheet, whsForAnchorcity))
                    logger.info("dcData: $dcData")
                }
                // mapping locations
                LOC_SHEET -> {
                    val anchorCityMappedDc = dcData.groupBy { it.anchorCityName }.mapValues { dcc -> dcc.value.first() }
                    logger.info("anchorCityMappedDc: $anchorCityMappedDc")
                    locationData.addAll(mapLocationDataFromExcel(sheet, whs, anchorCityMappedDc))
                    logger.info("locationData: $locationData")
                }
                else -> logger.info("Extra Sheet Found")
            }
            count += 1
        }
        if (dcData.isNotEmpty() && locationData.isNotEmpty()) {
            dcData.forEach {
                DemandClusterRepository.createOrUpdate(it).also {
                    demandClusterCacheRepo.invalidateDemandClusterCache(it)
                }
            }
            locationData.forEach {
                DemandClusterLocationsRepository.createOrUpdate(it).also {
                    demandClusterLocationCacheRepo.invalidateDemandClusterLocationCache(it)
                }
            }
        }
    }

    suspend fun process() {
        // onboards demandCluster and demandClusterLocations provided in excel format
        // do read below methods comments first
        //onboardNetworkConfig()

        // onboard inputLocationChain data provided in excel format
        // do read below methods comments first

        // creating a demand cluster location manually
        /*val dcl = DemandClusterLocations(
            name = "exp_iota_ws_price_set_c",
            type = LocationType.CLUSTER,
            city = "bangalore",                           //associated city with location and not anchor city
            demandClusterId = "DCU01POSW2BM141BQIHK6BX",  //demand cluster ID, if not exists already, create one
            fulfilmentCenters = listOf(),                 // list of WHs associated, needed only for Staples
            createdBy = "<EMAIL>",
            updatedBy = null
        )
        DemandClusterLocationsRepository.createOrUpdate(dcl)*/
    }
}

class NetworkMigrationJobRunner {
    companion object {
        private val injector = Guice.createInjector(PricingCoreModule())

        @JvmStatic
        fun main(args: Array<String>) {
            System.setProperty("udaan.env", "prod")
            val networkMigrationJob = injector.getInstance(NetworkMigrationJob::class.java)
            JobScope.runBlocking {
                try {
                    networkMigrationJob.process()
                    exitProcess(0)
                } catch (e: Exception) {
                    e.printStackTrace()
                    exitProcess(1)
                }
            }
        }
    }
}
