package com.udaan.pricing.jobs.keda.handlers

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.pricing.contracts.ContractLadderPriceRequest
import com.udaan.pricing.contracts.ContractLadderRequest
import com.udaan.pricing.contracts.ContractPriceRequestType
import com.udaan.pricing.contracts.ContractRequest
import com.udaan.pricing.core.constants.AsyncJobConstants
import com.udaan.pricing.core.controller.contract.ContractController
import com.udaan.pricing.core.helpers.BlobStorageHelper
import com.udaan.pricing.core.managers.signals.AsyncJobController
import com.udaan.pricing.core.models.contracts.ContractCatalogEntity
import com.udaan.pricing.core.models.contracts.ContractReason
import com.udaan.pricing.core.models.contracts.ContractType
import com.udaan.pricing.job.AsyncJob
import com.udaan.pricing.job.AsyncJobStatus
import com.udaan.pricing.jobs.keda.fileutils.FileUtils
import com.udaan.pricing.jobs.models.ProcessedDataInfo
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

@Singleton
class BuyerContractJobHandler @Inject constructor(
    private val asyncJobController: AsyncJobController,
    private val blobStorageHelper: BlobStorageHelper,
    private val fileUtils: FileUtils,
    private val contractController: ContractController
) : AsyncJobHandler() {

    companion object {
        const val UNIT_PRICE_IN_PAISA = "UNIT_PRICE_IN_PAISA"
        const val MRP_MARKDOWN_IN_BPS = "MRP_MARKDOWN_IN_BPS"

        private val logger by logger()
    }

    /**
     * Converts a row to BuyerContractFileInputRow and processes each row to create contract.
     */
    override suspend fun processJob(asyncJob: AsyncJob) {
        try {
            asyncJobController.updateAsyncJobStatusAndMetadata(
                jobId = asyncJob.id,
                status = AsyncJobStatus.IN_PROGRESS,
                updatedBy = "SYSTEM"
            )

            val file = blobStorageHelper.getFileFromBlobStorage(
                containerReference = asyncJob.type.toString().lowercase().replace("_", "-"),
                directoryReference = asyncJob.type.toString().lowercase().replace("_", "-"),
                fileName = asyncJob.id + "_input.xlsx"
            )

            val buyerContractParsedDataInfoList = fileUtils.readExcelFile(
                jobId = asyncJob.id,
                file = file,
                sheetNum = 0,
                headerNum = 0,
                dataRowNum = 1,
                dataClass = BuyerContractFileInputRow::class
            )

            val processedBuyerContractData = buyerContractParsedDataInfoList.parallelMap { buyerContractParsedDataInfo ->
                // Process each row and convert to request
                buyerContractParsedDataInfo.dataRequestDTO?.let {
                    val contractRequest = it.toContractRequest(asyncJob.createdBy)

                    logger.info("Contract request is $contractRequest")
                    try {
                        contractController.createOrUpdateContract(contractRequest)
                        ProcessedDataInfo(
                            row = buyerContractParsedDataInfo.row,
                            parsingExceptionMessage = buyerContractParsedDataInfo.parsingExceptionMessage,
                            processingExceptionMessage = null,
                            processingOutput = BuyerContractProcessResponse(
                                remarks = "Success"
                            )
                        )
                    } catch (e: Exception) {
                        logger.error("Error while creating contract for $contractRequest with error {}", e.message)
                        ProcessedDataInfo(
                            row = buyerContractParsedDataInfo.row,
                            parsingExceptionMessage = buyerContractParsedDataInfo.parsingExceptionMessage,
                            processingExceptionMessage = e.message,
                            processingOutput =  BuyerContractProcessResponse(
                                remarks = "Failure"
                            )
                        )
                    }
                } ?: ProcessedDataInfo(
                    row = buyerContractParsedDataInfo.row,
                    parsingExceptionMessage = buyerContractParsedDataInfo.parsingExceptionMessage,
                    processingExceptionMessage = null,
                    processingOutput = BuyerContractProcessResponse(
                        remarks = "Failure"
                    )
                )
            }

            val successfullyProcessedRowCount = processedBuyerContractData.count {
                it.parsingExceptionMessage == null && it.processingExceptionMessage == null
            }

            val outputFile = fileUtils.updateExcelWithProcessedData(
                jobId = asyncJob.id,
                file = file,
                processedDataInfoList = processedBuyerContractData
            )

            blobStorageHelper.uploadFileToBlobStorage(
                containerReference = asyncJob.type.toString().lowercase().replace("_", "-"),
                directoryReference = asyncJob.type.toString().lowercase().replace("_", "-"),
                file = outputFile
            )

            val remarks = "$successfullyProcessedRowCount rows are success, " +
                    "${buyerContractParsedDataInfoList.size - successfullyProcessedRowCount} rows failed"

            asyncJobController.updateAsyncJobStatusAndMetadata(
                jobId = asyncJob.id,
                status = AsyncJobStatus.COMPLETED,
                remarks = remarks,
                outputFilePath = outputFile.name,
                attributesMap = mapOf(
                    AsyncJobConstants.TOTAL to buyerContractParsedDataInfoList.size.toString(),
                    AsyncJobConstants.SUCCESSFUL to successfullyProcessedRowCount.toString(),
                    AsyncJobConstants.FAILED to (buyerContractParsedDataInfoList.size - successfullyProcessedRowCount).toString()
                ),
                updatedBy = "SYSTEM"
            )
        } catch (e: Exception) {
            logger.error("Error while processing job with error {}", e.message)
            asyncJobController.updateAsyncJobStatusAndMetadata(
                jobId = asyncJob.id,
                status = AsyncJobStatus.FAILED,
                remarks = "Error while processing job with error " + e.message,
                outputFilePath = null,
                updatedBy = "SYSTEM"
            )
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class BuyerContractFileInputRow(
        val buyerOrgId: String,
        val catalogId: String,
        val catalogEntity: ContractCatalogEntity,
        val firstLadderMinQuantity: Int,
        val secondLadderMinQuantity: Int?,
        val thirdLadderMinQuantity: Int?,
        /**
         * TODO: Make it more flexible in future to support more ladders.
         * right now only supporting 3 ladders.
         */
        val firstLadderPrice: Long,
        val secondLadderPrice: Long?,
        val thirdLadderPrice: Long?,
        val priceType: String,
        val expiresAt: String,
        val lockInTill: String?,
        val volumeCommitted: Long,
        val city: String
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class BuyerContractProcessResponse(
        val remarks: String
    )

    private fun BuyerContractFileInputRow.fetchContractLaddersRequest(): ContractLadderPriceRequest {
        val listOfMinLadderQuantities = listOfNotNull(
            this.firstLadderMinQuantity,
            this.secondLadderMinQuantity,
            this.thirdLadderMinQuantity
        )
        return ContractLadderPriceRequest(listOfMinLadderQuantities.mapIndexed { index, ladderMinQuantity ->
            val maxQuantity = (listOfMinLadderQuantities.getOrNull(index + 1)?.let { nextLadderMinQuantity ->
                nextLadderMinQuantity - 1
            } ?: Int.MAX_VALUE)
            ContractLadderRequest(
                minQuantity = ladderMinQuantity,
                maxQuantity = maxQuantity,
                ladderValue = when (index) {
                    0 -> this.firstLadderPrice
                    1 -> this.secondLadderPrice ?: error("Second ladder min quantity is provided, but not price")
                    2 -> this.thirdLadderPrice ?: error("Third ladder min quantity is provided, but not price")
                    else -> throw IllegalArgumentException("Invalid index $index")
                }
            )
        })
    }

    private fun convertDateStringToEpoch(dateString: String?): Long? {
        return if (dateString != null && dateString.isNotBlank()) {
            // Normalize spaces in the input
            val cleanedDate = dateString.trim().replace("\\s+".toRegex(), " ")
            val formatterWithoutSeconds = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")

            try {
                val localDateTime = LocalDateTime.parse(cleanedDate, formatterWithoutSeconds)
                val zonedDateTime = localDateTime.atZone(ZoneId.systemDefault())
                return zonedDateTime.toInstant().toEpochMilli()
            } catch (e: Exception) {
                logger.error("Error parsing date: ${e.message}")
                throw e
            }
        } else {
            null
        }
    }


    private fun BuyerContractFileInputRow.getContractPriceRequestType(): ContractPriceRequestType {
        return when (this.priceType) {
            UNIT_PRICE_IN_PAISA -> ContractPriceRequestType.ABSOLUTE_LADDER_PRICE
            MRP_MARKDOWN_IN_BPS -> ContractPriceRequestType.MRP_MARKDOWN_BPS
            else -> throw IllegalArgumentException("Invalid price type $priceType")
        }
    }

    private fun BuyerContractFileInputRow.toContractRequest(createdBy: String): ContractRequest {
        val ladderPriceRequest = this.fetchContractLaddersRequest()
        val lockInEpoch = convertDateStringToEpoch(this.lockInTill)
        val expiresInEpoch = convertDateStringToEpoch(this.expiresAt)
        val priceType = this.getContractPriceRequestType()
        return ContractRequest(
            buyerOrgId = this.buyerOrgId,
            contractCatalogEntityId = this.catalogId,
            contractCatalogEntity = this.catalogEntity.name,
            price = ladderPriceRequest,
            priceType = priceType,
            type = if (lockInTill != null &&
                this.lockInTill.isNotBlank()
            ) ContractType.LOCK_IN.name else ContractType.EXPIRY.name,
            expiryInEpoch = expiresInEpoch ?: error("Expiry date is not provided"),
            lockInEpoch = lockInEpoch ?: 0L,
            requestedBy = createdBy,
            volumeCommitted = this.volumeCommitted,
            city = this.city,
            reason = ContractReason.VOLUME.name
        )
    }
}
