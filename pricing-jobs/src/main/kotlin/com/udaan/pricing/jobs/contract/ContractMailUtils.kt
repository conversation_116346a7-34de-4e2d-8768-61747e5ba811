package com.udaan.pricing.jobs.contract

import com.udaan.pricing.jobs.models.contracts.ExpiryAlertSummaryForBuyer
import com.udaan.pricing.jobs.models.contracts.QuoteAlertSummaryForBuyer
import com.udaan.pricing.jobs.models.contracts.VolumeAlertSummaryForBuyer

fun generateEmailBody(
    expiryAlert: ExpiryAlertSummaryForBuyer?,
    volumeAlert: VolumeAlertSummaryForBuyer?
): String {
    return """
        <html>
        <head>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    color: #333;
                }
                h2 {
                    color: #333;
                }
                p {
                    margin-bottom: 10px;
                }
            </style>
        </head>
        <body>
            ${expiryAlert?.let {
        """
                <h2>Expiry Alert Summary</h2>
                <p>Buyer: \${it.buyerName} (ID: \${it.buyerOrgId})</p>
                <p>SKUs Live on Contract: \${it.skusLiveOnContract}</p>
                <p>SKUs with Expiry < 5 Days: \${it.skusWithExpiryLessThan5Days}</p>
                """
    } ?: ""}
            
            ${volumeAlert?.let {
        """
                <h2>Volume Alert Summary</h2>
                <p>Buyer: \${it.buyerName} (ID: \${it.buyerOrgId})</p>
                <p>SKUs Live on Contract: \${it.skusLiveOnContract}</p>
                <p>SKUs with 25% Volume Achievement: \${it.skusWith25PercentVolumeAchievement}</p>
                <p>SKUs with 50% Volume Achievement: \${it.skusWith50PercentVolumeAchievement}</p>
                """
    } ?: ""}
        </body>
        </html>
    """.trimIndent()
}


fun generateBulkEmailBody(
    expiryAlerts: List<ExpiryAlertSummaryForBuyer>,
    volumeAlerts: List<VolumeAlertSummaryForBuyer>,
    quoteAlerts: List<QuoteAlertSummaryForBuyer>
): String {
    return """
        <html>
        <head>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    color: #333;
                }
                h2 {
                    color: #333;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }
                th {
                    background-color: #f4f4f4;
                }
                .hidden {
                    display: none;
                }
                .toggle-button {
                    color: blue;
                    cursor: pointer;
                    text-decoration: underline;
                }
            </style>
            <script>
                function toggleVisibility(sectionId) {
                    var section = document.getElementById(sectionId);
                    if (section.style.display === "none") {
                        section.style.display = "table-row-group";
                    } else {
                        section.style.display = "none";
                    }
                }
            </script>
        </head>
        <body>
            <h1>Buyer Alerts Summary</h1>
            
            ${generateTable("Expiry Alert Summary", "expirySection", expiryAlerts) { expiry ->
        """
                <tr>
                    <td>${expiry.buyerName}</td>
                    <td>${expiry.buyerOrgId}</td>
                    <td>${expiry.skusLiveOnContract}</td>
                    <td>${expiry.skusWithExpiryLessThan5Days}</td>
                </tr>
                """
    }}

            ${generateTable("Volume Alert Summary", "volumeSection", volumeAlerts) { volume ->
        """
                <tr>
                    <td>${volume.buyerName}</td>
                    <td>${volume.buyerOrgId}</td>
                    <td>${volume.skusLiveOnContract}</td>
                    <td>${volume.skusWith25PercentVolumeAchievement}</td>
                    <td>${volume.skusWith50PercentVolumeAchievement}</td>
                </tr>
                """
    }}

            ${generateTable("Quote Alert Summary", "quoteSection", quoteAlerts) { quote ->
        """
                <tr>
                    <td>${quote.buyerName}</td>
                    <td>${quote.buyerOrgId}</td>
                    <td>${quote.skusLiveOnContract}</td>
                    <td>${quote.skusWith5PercentBelowQuotePrice}</td>
                    <td>${quote.skusWith5PercentAboveQuotePrice}</td>
                </tr>
                """
    }}
        </body>
        </html>
    """.trimIndent()
}

/**
 * Helper function to generate expandable tables.
 */
fun <T> generateTable(
    title: String,
    sectionId: String,
    alerts: List<T>,
    rowGenerator: (T) -> String
): String {
    if (alerts.isEmpty()) return ""

    val visibleRows = alerts.take(10).joinToString("") { rowGenerator(it) }
    val hiddenRows = alerts.drop(10).joinToString("") { rowGenerator(it) }

    val headerRow = when (title) {
        "Expiry Alert Summary" -> "<tr><th>Buyer Name</th><th>Buyer ID</th><th>SKUs Live</th><th>SKUs Expiry < 5 Days</th></tr>"
        "Volume Alert Summary" -> "<tr><th>Buyer Name</th><th>Buyer ID</th><th>SKUs Live</th><th>25% Volume Achievement</th><th>50% Volume Achievement</th></tr>"
        "Quote Alert Summary" -> "<tr><th>Buyer Name</th><th>Buyer ID</th><th>SKUs Live</th><th>5% Below Quote Price</th><th>5% Above Quote Price</th></tr>"
        else -> ""
    }

    return """
        <h2>$title</h2>
        <table>
            $headerRow
            $visibleRows
            <tbody id="$sectionId" class="hidden">
                $hiddenRows
            </tbody>
        </table>
        ${if (alerts.size > 10) "<p class='toggle-button' onclick=\"toggleVisibility('$sectionId')\">Click to expand</p>" else ""}
    """
}


