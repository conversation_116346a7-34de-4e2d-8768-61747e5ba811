package com.udaan.pricing.jobs.adhoc

import com.google.inject.Guice
import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.JobScope
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.core.managers.signals.SignalWriteManager
import com.udaan.pricing.signalcreation.GenericGidLevelInput
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.jobs.utils.ExcelUtils.cellStringValue
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import java.io.File
import kotlin.collections.iterator
import kotlin.system.exitProcess

class SchemeMultiplierUploadJob @Inject constructor(
    private val signalWriteManager: SignalWriteManager
) {

    companion object {
        private val logger by logger()
        private val injector = Guice.createInjector(PricingCoreModule())

        @JvmStatic
        fun main(args: Array<String>) {
            val fmcgSourcingChannelSyncJob = injector.getInstance(SchemeMultiplierUploadJob::class.java)
            try {
                logger.info("********************** Job STARTED **********************")
                JobScope.runBlocking {
                    fmcgSourcingChannelSyncJob.process()
                }
                logger.info("********************** Job Completed **********************")
            } catch (e: Throwable) {
                e.printStackTrace()
                logger.error("********************** Job FAILED **********************", e)
                exitProcess(1)
            } finally {
                logger.error("********************** Process End **********************")
                exitProcess(0)
            }
        }
    }

    suspend fun process() {
        val file = File("/Users/<USER>/Downloads/Scheme_multiplier.xlsx")
        val inputWb = XSSFWorkbook(file.inputStream())

        val inputSheet = inputWb.getSheet("Sheet0")

        val gidLevelInputs = mutableListOf<GenericGidLevelInput>()
        for (row in inputSheet.rowIterator()) {
            if (row.rowNum > 0) {
                val productGroupId = row.getCell(0).cellStringValue()
                val city = row.getCell(1).cellStringValue()
                val data = row.getCell(2).cellStringValue().toDouble().toBigDecimal()

                val gidLevelInput = GenericGidLevelInput(
                    productGroupId = productGroupId,
                    variableId = VariableId.SCHEME_MULTIPLIER_BPS,
                    data = BigDecimalValue(
                        value = data
                    ),
                    metadata = emptyMap(),
                    location = Location(
                        locationType = LocationType.CITY,
                        locationValue = city
                    ),
                    updatedBy = "DATA_PLATFORM_PROBE"
                )

                gidLevelInputs.add(gidLevelInput)
            }
        }

        gidLevelInputs.forEach {
            signalWriteManager.createSignalFromRawInput(it)
        }
    }
}
