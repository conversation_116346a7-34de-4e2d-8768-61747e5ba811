package com.udaan.pricing.jobs

import com.google.inject.AbstractModule
import com.google.inject.Provides
import com.google.inject.Singleton
import com.udaan.common.client.UdaanClientConfig
import com.udaan.common.server.UdaanServerConfig
import com.udaan.config.Configuration
import com.udaan.dataplatform.client.DataPlatformIngestionClient
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.jobs.cronjobs.botdetection.ADXConfig
import com.udaan.pricing.jobs.cronjobs.botdetection.ADXServiceClientFactory

class PricingJobModule : AbstractModule() {

    override fun configure() {
        install(PricingCoreModule())
    }

    @Provides
    @Singleton
    fun getAdxClient(): ADXServiceClientFactory {
        val azureAdxConfigK8s0 = Configuration.getMap("bot-detection/adx/config")
            ?: throw IllegalStateException("ADX Config with Key 'bot-detection/adx/config' - not found")

        return ADXServiceClientFactory(ADXConfig.fromMap(azureAdxConfigK8s0))
    }

    @Provides
    @Singleton
    fun getDPIngestionClient(): DataPlatformIngestionClient {
        val dpConfig: UdaanClientConfig = UdaanServerConfig["dataplatform-service"]!!
        return DataPlatformIngestionClient(dpConfig)
    }
}
