package com.udaan.pricing.jobs.utils

import org.apache.poi.ss.usermodel.DataFormatter
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import java.io.InputStream
import kotlin.collections.all
import kotlin.collections.any
import kotlin.collections.map
import kotlin.collections.set
import kotlin.io.use
import kotlin.text.isEmpty
import kotlin.text.isNotEmpty
import kotlin.text.trim

object XlsxReader {
    private val dataFormatter = DataFormatter()
    private val colNameIndexMap = mutableMapOf<String, Int>()

    /**
     * This allows reading a worksheet of an excel document.
     * The sheet and cols to read are decided based on passed arg values.
     * This also allows reading of col headers.
     */
    fun read(
        inputStream: InputStream,
        sheetIndex: Int,
        startRow: Int,
        endRow: Int = Int.MAX_VALUE,
        allowEmptyCells: Boolean = true,
        columnIndexList: List<Int>
    ): List<List<String>> {
        inputStream.use {
            val wb = XSSFWorkbook(inputStream)
            val sheet = wb.getSheetAt(sheetIndex)
            val output = mutableListOf<List<String>>()

            for (row in sheet.rowIterator()) {
                if (row.rowNum in startRow..endRow) {
                    if (row.rowNum == 0) {
                        colNameIndexMap.clear()
                        columnIndexList.map { index ->
                            val colName = dataFormatter.formatCellValue(row.getCell(index))
                            if (colName != null && colName.isNotEmpty()) colNameIndexMap[colName]=index
                        }
                    }
                    val cells = columnIndexList.map { dataFormatter.formatCellValue(row.getCell(it)).trim() }
                    if (isEmpty(cells, allowEmptyCells)) output.add(cells)
                }
            }
            return output
        }
    }

    /**
     * This allows reading multiple worksheets of an excel document.
     * The sheet and cols to read are decided based on passed arg values.
     * This doesn't allows reading of col headers out of the box and needs explicit logic to be written by user.
     */
    fun readMultipleSheets(
        inputStream: InputStream,
        startRow: Int,
        endRow: Int = Int.MAX_VALUE,
        allowEmptyCells: Boolean = true,
        sheetColMap: Map<Int, List<Int>>
    ): List<List<List<String>>> {
        val wb = XSSFWorkbook(inputStream)
        val finalOutput = mutableListOf<List<List<String>>>()
        for(sheetIndex in sheetColMap.keys){
            val sheet = wb.getSheetAt(sheetIndex)
            val output = mutableListOf<List<String>>()

            for (row in sheet.rowIterator()) {
                if (row.rowNum in startRow..endRow) {
                    val cells = sheetColMap[sheetIndex]!!.map { dataFormatter.formatCellValue(row.getCell(it)).trim() }
                    if (isEmpty(cells, allowEmptyCells)) output.add(cells)
                }
            }
            finalOutput.add(output)
        }
        return finalOutput
    }

    /**
     * this method allows fetching col headers of the sheet read in read() fun
     */
    fun getColNameIndexMapping(): Map<String, Int> {
        return colNameIndexMap
    }

    /**
     * this is used internally by Excel funs to fetch relevant data
     */
    private fun isEmpty(cells: List<String>, allowEmptyCells: Boolean = false): Boolean {
        return if (!allowEmptyCells)
            cells.any { it.isEmpty() }.not()
        else
            cells.all { it.isEmpty() }.not()
    }
}
