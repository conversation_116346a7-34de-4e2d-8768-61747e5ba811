package com.udaan.pricing.jobs.signals.probesyncer

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.google.inject.Guice
import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.JobScope
import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.core.managers.signals.SignalWriteManager
import com.udaan.pricing.core.svcinterfaces.DpServiceInterface
import com.udaan.pricing.core.utils.signals.BigDecimalUtil.roundToDefaultScale
import com.udaan.pricing.signalcreation.GenericLidLevelInput
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.jobs.utils.PushGatewayUtil
import java.math.BigDecimal
import kotlin.system.exitProcess

class FmcgCogsSyncJob @Inject constructor(
    dpServiceInterface: DpServiceInterface,
    signalWriteManager: SignalWriteManager
) : DPInputProbeSyncer(
    dpServiceInterface,
    signalWriteManager
) {

    companion object {
        private val logger by logger()
        private val injector = Guice.createInjector(PricingCoreModule())
        private val pushGatewayUtil = PushGatewayUtil("FmcgCogsSyncJob", logger)

        @JvmStatic
        fun main(args: Array<String>) {
            val fmcgCogsSyncJob = injector.getInstance(FmcgCogsSyncJob::class.java)
            try {
                logger.info("********************** Job STARTED **********************")
                JobScope.runBlocking {
                    pushGatewayUtil.pushJobStarted()
                    pushGatewayUtil.recordStats {
                        fmcgCogsSyncJob.process()
                    }
                    pushGatewayUtil.pushJobEnded()
                }
                logger.info("********************** Job Completed **********************")
            } catch (e: Throwable) {
                e.printStackTrace()
                logger.error("********************** Job FAILED **********************", e)
                exitProcess(1)
            } finally {
                logger.error("********************** Process End **********************")
                exitProcess(0)
            }
        }
    }

    override val probeId = "exdst7"
    override val chunkSize = 25
    override val delayBetweenChunksInMillis = 1000L

    suspend fun process() {
        val fmcgCogsDPData = getDataFromDataPlatform<FmcgCogsDPData>().filter {
            it.cogs != null
        }

        val convertedLidLevelInputs = fmcgCogsDPData.map {
            GenericLidLevelInput(
                listingId = it.listingId,
                variableId = VariableId.FMCG_COGS_WOT_PAISA_SET,
                data = BigDecimalValue(
                    value = it.cogs!!.multiply(BigDecimal(100)).roundToDefaultScale()
                ),
                metadata = mapOf(
                    "ORDER_DATE" to it.orderDate
                ),
                location = Location(
                    locationType = LocationType.CENTRAL,
                    locationValue = "CENTRAL"
                ),
                updatedBy = "DATA_PLATFORM_PROBE"
            )
        }

        processAndSaveRawInputs(convertedLidLevelInputs)
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class FmcgCogsDPData(
        @JsonProperty("group_title")
        val groupTitle: String,

        @JsonProperty("listing_id")
        val listingId: String,

        @JsonProperty("cogs")
        val cogs: BigDecimal?,

        @JsonProperty("date")
        val orderDate: String
    )
}
