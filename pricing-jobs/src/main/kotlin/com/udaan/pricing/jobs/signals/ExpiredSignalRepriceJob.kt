package com.udaan.pricing.jobs.signals

import com.google.inject.Guice
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.core.managers.signals.SignalReadManager
import com.udaan.pricing.core.managers.signals.SignalWriteManager
import com.udaan.pricing.variable.VariableId
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import javax.inject.Inject
import kotlin.system.exitProcess

class ExpiredSignalRepriceJob @Inject constructor(
    private val signalWriteManager: SignalWriteManager,
    private val signalReadManager: SignalReadManager
) {

    companion object {
        private val logger by logger()

        /**
         * KP follows a different expiry logic, so excluding KP signals from expiry.
         * KP signal will be expired when it last updated time is older than 12 AM that day.
         */
        private val excludeVariablesForExpiry = listOf(VariableId.OFFLINE_COMPETITION_WT_PAISA_UNIT.name)

        @JvmStatic
        fun main(args: Array<String>) {
            val injector = Guice.createInjector(PricingCoreModule())
            val instance = injector.getInstance(ExpiredSignalRepriceJob::class.java)
            try {
                logger.info("JOB START")
                runBlocking {
                    instance.process()
                }
                logger.info("JOB END")
                exitProcess(0)
            } catch (e: Exception) {
                logger.error("JOB FAILED")
                logger.error(e.message)
                exitProcess(1)
            }
        }

    }

    suspend fun process() {
        signalReadManager.getSignalsToMarkExpired().filter {
            excludeVariablesForExpiry.contains(it.variableId).not()
        }.chunked(100).map { signals ->
            delay(1000)
            signals.parallelMap { signal ->
                try {
                    logger.info("Marking signal as expired: ${signal.id}")
                    signalWriteManager.markSignalAsExpired(
                        id = signal.id,
                        partitionKey = signal.partitionKey,
                        updatedBy = "EXPIRY_CRONJOB"
                    )
                    logger.info("Marked signal as expired: ${signal.id}")
                } catch (e: Exception) {
                    logger.error("Failed to mark signal as expired: ${signal.id} with error ", e.cause)
                }
            }
        }
    }
}




