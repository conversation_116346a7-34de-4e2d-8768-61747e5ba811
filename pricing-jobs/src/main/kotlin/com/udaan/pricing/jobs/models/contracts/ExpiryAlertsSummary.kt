package com.udaan.pricing.jobs.models.contracts

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

data class ExpiryAlertSummary(
    val expiryAlertSummaryForBuyerList: List<ExpiryAlertSummaryForBuyer>,
    val expiryDataToRaiseAlert: List<ExpiryAlertOutputFileRow>
)

data class ExpiryAlertSummaryForBuyer(
    val buyerOrgId: String,
    val buyerName: String,
    val skusLiveOnContract: Int,
    val skusWithExpiryLessThan5Days: Int
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ExpiryAlertOutputFileRow(
    val buyerOrgId: String,
    val buyerName: String,
    val city: String,
    val contractCatalogEntityId: String,
    val contractCatalogEntity: String,
    val catalogTitle: String,
    val startTime: String,
    val endTime: String,
    val endTimeInDate: String,
    val currentTime: String,
    val contractPriceInPaisa: Long? = null,
    val contractMrpMarkdownInBps: Long? = null,
    val quotePriceInPaisa: Long? = null,
    val quotePriceInMrpMarkDownBps: Long? = null,
    val cogsUnitPriceInPaisa: Long? = null,
    val volumeCommitted: Long,
    val volumeOrdered: Long,
    val remarks: String
)
