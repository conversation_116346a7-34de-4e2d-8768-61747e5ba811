package com.udaan.pricing.jobs.utils

import org.apache.poi.ss.usermodel.Cell
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.ss.usermodel.Sheet
import org.apache.poi.ss.util.NumberToTextConverter
import org.apache.poi.xssf.usermodel.XSSFWorkbook

object ExcelUtils {
    fun extractCellValueFromRowForHeader(
        row: Row,
        headersToColumnIndexMap: Map<String, Int>,
        header: String
    ): String? {
        return row.getCell(
            headersToColumnIndexMap[header] ?: throw Exception("$header is missing")
        ).cellStringValue().trim().takeIf { it.isNotEmpty() }
    }

    fun Cell.cellStringValue(): String {
        return when(cellType) {
            Cell.CELL_TYPE_STRING -> stringCellValue.trim()
            Cell.CELL_TYPE_NUMERIC -> NumberToTextConverter.toText(numericCellValue).trim()
            Cell.CELL_TYPE_ERROR -> errorCellValue.toString()
            Cell.CELL_TYPE_BLANK -> ""
            Cell.CELL_TYPE_FORMULA -> when(this.cachedFormulaResultType) {
                Cell.CELL_TYPE_NUMERIC -> NumberToTextConverter.toText(this.numericCellValue).trim()
                Cell.CELL_TYPE_STRING -> this.stringCellValue.trim()
                else -> ""
            }
            else -> ""
        }
    }

    fun Row.isRowEmpty():Boolean{
        for (c in this.firstCellNum until this.lastCellNum) {
            val cell = this.getCell(c)
            if (cell != null && cell.cellType != Cell.CELL_TYPE_BLANK) return false
        }
        return true
    }

    fun getOrCreateSheet(workbook: XSSFWorkbook, sheetName: String): Sheet {
        val existingSheet = workbook.getSheet(sheetName)
        if (existingSheet != null) {
            workbook.removeSheetAt(workbook.getSheetIndex(sheetName))
        }
        return workbook.createSheet(sheetName)
    }

    fun getOrCreateRow(sheet: Sheet, rowIndex: Int): Row {
        return sheet.getRow(rowIndex) ?: sheet.createRow(rowIndex)
    }
}
