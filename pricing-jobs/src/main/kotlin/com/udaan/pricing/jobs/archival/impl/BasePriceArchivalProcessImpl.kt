package com.udaan.pricing.jobs.archival.impl

import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.BasePrice
import com.udaan.pricing.core.controller.cache.BasePriceCacheController
import com.udaan.pricing.core.dao.BasePriceRepository
import com.udaan.pricing.core.utils.SlackChannelEnum
import com.udaan.pricing.core.utils.SlackNotifier
import com.udaan.pricing.jobs.archival.abstraction.ArchivalProcess
import com.udaan.pricing.jobs.archival.models.ArchiveCountOverTime
import java.util.*

class BasePriceArchivalProcessImpl @Inject constructor(
    private val basePriceRepository: BasePriceRepository,
    private val basePriceCacheController: BasePriceCacheController
) : ArchivalProcess<BasePrice> {

    private val probeId = "e7bvyj"

    private val slackNotifier = SlackNotifier(SlackChannelEnum.RETAIL_DB_ARCHIVAL_ALERTS.slackUrl)

    private val log by logger()

    override fun getTtl(): Long {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.YEAR, -1);
        return calendar.timeInMillis.div(1000)
    }

    override fun fetchProbeId(): String {
        return probeId
    }

    override fun getTimeStepperInSeconds(): Long {
        /**
         * One minute in seconds
         */
        return 60
    }

    override suspend fun fetchDataCountFromSource(fromTs: Long, toTs: Long): Long {
        val count = basePriceRepository.fetchDataCountByTimeRange(fromTimeStamp = fromTs, toTimeStamp = toTs)
        log.info("Fetched count from source $count with $fromTs and $toTs")
        return count
    }

    override suspend fun fetchDataFromSource(fromTs: Long, toTs: Long): Collection<BasePrice> {
        val data = basePriceRepository.fetchDataByTimeRange(fromTimeStamp = fromTs, toTimeStamp = toTs)
        log.info("Fetched data from source ${data.size} with $fromTs and $toTs")
        return data
    }

    override suspend fun deleteItems(items: Collection<BasePrice>) {
        basePriceRepository.deleteItems(items).also {
            basePriceCacheController.invalidateCachedBasePriceForListings(items.map { it.listingId }.distinct())
        }
    }

    override suspend fun onSourceAndArchivalCountMismatch(
        sourceCount: Long,
        archivalCount: Long,
        item: ArchiveCountOverTime
    ) {
        if (sourceCount != 0L && (sourceCount != archivalCount)) {
            slackNotifier.sendMessage(
                notifyTo = "<@satya.e>",
                message = "prices collection has mismatch between  sourceCount: $sourceCount, " +
                        "archivalCount: $archivalCount for $item"
            )
        }
    }
}