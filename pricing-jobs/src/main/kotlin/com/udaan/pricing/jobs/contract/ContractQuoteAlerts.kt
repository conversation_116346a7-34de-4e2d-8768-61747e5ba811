package com.udaan.pricing.jobs.contract

import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.pricing.contracts.ContractQuoteResponse
import com.udaan.pricing.core.controller.contract.ContractQuoteController
import com.udaan.pricing.core.models.contracts.Contract
import com.udaan.pricing.core.models.contracts.ContractLadderMrpMarkdownBps
import com.udaan.pricing.core.models.contracts.ContractLadderPrice
import com.udaan.pricing.core.models.contracts.toContractQuoteRequest
import com.udaan.pricing.core.strategyevaluator.Constants
import com.udaan.pricing.jobs.models.contracts.QuoteAlertOutputFileRow
import com.udaan.pricing.jobs.models.contracts.QuoteAlertSummary
import com.udaan.pricing.jobs.models.contracts.QuoteAlertSummaryForBuyer
import com.udaan.pricing.jobs.helpers.ContractQuoteTaggingHelper
import com.udaan.proto.models.ModelV1

class ContractQuoteAlerts @Inject constructor(
    private val contractQuoteTaggingHelper: ContractQuoteTaggingHelper,
    private val contractQuoteController: ContractQuoteController
) {

    private val logger by logger()
    private val quotePriceDiffThresholdInBps = 500

    suspend fun process(
        activeContracts: List<Contract>,
        buyerOrgDetailsMap: Map<String, ModelV1.OrgAccount?>,
        catalogTitleDetailsMap: Map<String, String?>
    ): QuoteAlertSummary {
        val defaultCity = "bangalore"
        val quoteResponses = activeContracts.parallelMap { contract ->
            try {
                logger.info("Processing contract: $contract")
                // process contract
                getQuoteForContract(
                    contract = contract,
                    defaultCity = defaultCity
                )
            } catch (e: Exception) {
                logger.error("Not able to generate quote price for $contract with error $e")
                ActiveContractToQuoteResponse(
                    contract = contract,
                    quoteResponse = null,
                    status = QuoteGenerationStatus.FAILED_WITH_ERROR,
                    message = "Failed with error ${e.message}"
                )
            }
        }.map {
            it.toActiveContractQuoteOutputFileRow(
                defaultCity = defaultCity,
                buyerName = buyerOrgDetailsMap[it.contract.buyerOrgId]?.displayName ?: "",
                catalogTitle = catalogTitleDetailsMap[it.contract.catalogEntityId] ?: ""
            )
        }
        return QuoteAlertSummary(
            quoteAlertSummaryForBuyerList = getQuoteAlertSummaryListForBuyer(
                quoteData = quoteResponses,
                buyerOrgDetailsMap = buyerOrgDetailsMap
            ),
            quoteDataToRaiseAlert = quoteResponses
        )
    }

    private fun getQuoteAlertSummaryListForBuyer(
        quoteData: List<QuoteAlertOutputFileRow>,
        buyerOrgDetailsMap: Map<String, ModelV1.OrgAccount?>
    ): List<QuoteAlertSummaryForBuyer> {
        return quoteData.groupBy { it.buyerOrgId }.map {
            val totalSkusLiveOnContract = it.value.size
            val skusWith5PercentAboveQuotePrice = it.value.count {
                (it.quotePriceDiffInBps ?: 0) > quotePriceDiffThresholdInBps
            }
            val skusWith5PercentBelowQuotePrice = it.value.count {
                (it.quotePriceDiffInBps ?: 0) < -quotePriceDiffThresholdInBps
            }
            QuoteAlertSummaryForBuyer(
                buyerOrgId = it.key,
                buyerName = buyerOrgDetailsMap[it.key]?.displayName ?: "",
                skusLiveOnContract = totalSkusLiveOnContract,
                skusWith5PercentAboveQuotePrice = skusWith5PercentAboveQuotePrice,
                skusWith5PercentBelowQuotePrice = skusWith5PercentBelowQuotePrice
            )
        }.sortedByDescending { it.skusLiveOnContract }
    }

    private suspend fun getQuoteForContract(
        contract: Contract,
        defaultCity: String
    ): ActiveContractToQuoteResponse {
        val city = contract.metadata.city ?: defaultCity
        val contractQuoteTagging = contractQuoteTaggingHelper.getContractQuoteTagging(
            contractCatalogEntity = contract.contractCatalogEntity.name,
            contractCatalogEntityId = contract.catalogEntityId,
            city = contract.metadata.city ?: defaultCity
        )
        if (contractQuoteTagging == null) {
            logger.error("Contract Quote Tagging not found for contract: $contract")
            return ActiveContractToQuoteResponse(
                contract = contract,
                quoteResponse = null,
                status = QuoteGenerationStatus.FAILED_WITH_NO_TAGGING,
                message = "No tagging found for ${contract.catalogEntityId} and $city"
            )
        }
        val quoteResponse = contractQuoteController.generateQuote(
            contract.toContractQuoteRequest(defaultCity),
            contractQuoteTagging.strategies.map { it.trim() }
        )
        return ActiveContractToQuoteResponse(
            contract = contract,
            quoteResponse = quoteResponse,
            status = QuoteGenerationStatus.SUCCESS,
            message = "Success"
        )
    }

    private fun ActiveContractToQuoteResponse.toActiveContractQuoteOutputFileRow(
        defaultCity: String,
        buyerName: String,
        catalogTitle: String
    ): QuoteAlertOutputFileRow {
        val contractPriceInPaisa = if (contract.price is ContractLadderPrice) {
            (contract.price as? ContractLadderPrice)?.value?.firstOrNull()?.ladderValue
        } else {
            null
        }
        val contractMrpMarkDownBps = if (contract.price is ContractLadderMrpMarkdownBps) {
            (contract.price as? ContractLadderMrpMarkdownBps)?.value?.firstOrNull()?.ladderValue
        } else {
            null
        }
        val quotePriceDiffInBps = quoteResponse?.quotePriceInPaisa?.let {
            contractPriceInPaisa?.let { contractPrice ->
                (it - contractPrice) * Constants.BPS_NORMALISER / contractPrice
            }
        }
        val quoteMrpMarkDownDiffInBps = quoteResponse?.mrpMarkDownBps?.let {
            contractMrpMarkDownBps?.let { contractMrpMarkDown ->
                (it - contractMrpMarkDown) * Constants.BPS_NORMALISER / contractMrpMarkDown
            }
        }
        return QuoteAlertOutputFileRow(
            buyerOrgId = contract.buyerOrgId,
            buyerName = buyerName,
            city = defaultCity,
            contractCatalogEntityId = contract.catalogEntityId,
            contractCatalogEntity = contract.contractCatalogEntity.name,
            catalogTitle = catalogTitle,
            volumeCommitted = contract.metadata.volumeCommitted,
            targetUnitPriceInPaisa = null,
            quotePriceInPaisa = quoteResponse?.quotePriceInPaisa,
            quotePriceInPaisaWithTax = quoteResponse?.quotePriceInPaisaWithTax,
            mrpMarkDownBps = quoteResponse?.mrpMarkDownBps,
            priceValidity = null,
            customerSavingsInBps = quoteResponse?.customerSavingsInBps,
            bestCompQuoteInPaisa = quoteResponse?.bestCompetitorQuote?.priceInPaisa,
            bestCompQuoteProvider = quoteResponse?.bestCompetitorQuote?.competitor,
            marginInBps = quoteResponse?.marginInBps,
            cogsUnitPriceInPaisa = quoteResponse?.cogsUnitPriceInPaisa,
            contractPrice = if (contract.price is ContractLadderPrice) {
                (contract.price as? ContractLadderPrice)?.value?.firstOrNull()?.ladderValue
            } else {
                null
            },
            contractMrpMarkDownBps = if (contract.price is ContractLadderMrpMarkdownBps) {
                (contract.price as? ContractLadderMrpMarkdownBps)?.value?.firstOrNull()?.ladderValue
            } else {
                null
            },
            quotePriceDiffInBps = quotePriceDiffInBps,
            quoteMrpMarkDownDiffInBps = quoteMrpMarkDownDiffInBps,
            remarks = message
        )
    }

    enum class QuoteGenerationStatus {
        SUCCESS,
        FAILED_WITH_NO_TAGGING,
        FAILED_WITH_ERROR
    }

    data class ActiveContractToQuoteResponse(
        val contract: Contract,
        val quoteResponse: ContractQuoteResponse? = null,
        val status: QuoteGenerationStatus,
        val message: String
    )
}
