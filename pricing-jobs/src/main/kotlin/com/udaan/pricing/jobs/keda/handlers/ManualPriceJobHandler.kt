package com.udaan.pricing.jobs.keda.handlers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.TelemetryScope
import com.udaan.pricing.commons.AbsolutePriceInfo
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.MrpMarkdownInfo
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.constants.AsyncJobConstants
import com.udaan.pricing.core.controller.ManualPriceController
import com.udaan.pricing.core.helpers.BlobStorageHelper
import com.udaan.pricing.core.managers.signals.AsyncJobController
import com.udaan.pricing.job.AsyncJob
import com.udaan.pricing.job.AsyncJobStatus
import com.udaan.pricing.jobs.extensions.isRowEmpty
import com.udaan.pricing.jobs.extensions.value
import com.udaan.pricing.manualprice.ManualPrice
import com.udaan.pricing.manualprice.ManualPriceDeleteRequest
import com.udaan.pricing.manualprice.ManualPriceUpdateRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext
import org.apache.poi.ss.usermodel.Cell
import org.apache.poi.ss.usermodel.DateUtil
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.xssf.usermodel.XSSFSheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*

@Singleton
class ManualPriceJobHandler @Inject constructor(
    private val asyncJobController: AsyncJobController,
    private val blobStorageHelper: BlobStorageHelper,
    private val manualPriceController: ManualPriceController
) : AsyncJobHandler() {

    companion object {
        private val logger by logger()
        const val UNIT_PRICE_IN_PAISA = "UNIT_PRICE_IN_PAISA"
        const val MRP_MARKDOWN_IN_BPS = "MRP_MARKDOWN_IN_BPS"
    }

    override suspend fun processJob(asyncJob: AsyncJob) {
        try {
            asyncJobController.updateAsyncJobStatusAndMetadata(
                jobId = asyncJob.id,
                status = AsyncJobStatus.IN_PROGRESS,
                updatedBy = "SYSTEM"
            )

            val actionType = asyncJob.jobData.attributesMap["ACTION_TYPE"]
                ?: throw Exception("Action Type is missing for job ${asyncJob.id}")

            val file = blobStorageHelper.getFileFromBlobStorage(
                containerReference = asyncJob.type.toString().lowercase().replace("_", "-"),
                directoryReference = asyncJob.type.toString().lowercase().replace("_", "-"),
                fileName = asyncJob.id + "_input.xlsx"
            )

            when (actionType) {
                "CREATE" -> {
                    parseAndProcessManualPriceCreation(
                        job = asyncJob,
                        file = file
                    )
                }
                "DELETE" -> {
                    parseAndProcessManualPriceDeletion(
                        job = asyncJob,
                        file = file
                    )
                }
                else -> {
                    throw Exception("Action Type is not supported for job ${asyncJob.id}")
                }
            }
        } catch (e: Exception) {
            logger.error("Error while processing job with error {}", e.message)
            asyncJobController.updateAsyncJobStatusAndMetadata(
                jobId = asyncJob.id,
                status = AsyncJobStatus.FAILED,
                remarks = "Error while processing job with error " + e.message,
                outputFilePath = null,
                updatedBy = "SYSTEM"
            )
        }
    }

    private suspend fun parseAndProcessManualPriceCreation(
        job: AsyncJob,
        file: File
    ) {
        val parsedDataInfo = parsePriceCreationRowsFromInputSheet(
            job = job,
            inputFile = file,
            ladderInputType = job.jobData.attributesMap["LADDER_INPUT_TYPE"]
                ?: throw Exception("Ladder Input Type is missing for job ${job.id}")
        )

        val distinctDataProcessRequests = parsedDataInfo.filter {
            it.dataRequestDTO != null
        }.distinctBy {
            "${it.dataRequestDTO!!.catalogEntity}:${it.dataRequestDTO.locationType.name}:" +
                    "${it.dataRequestDTO.locationValue}:${it.dataRequestDTO.buyerCohort}".uppercase()
        }

        val processedDataOutput = parsedDataInfo.chunked(5).map { parsedDataInfoChunk ->
            parsedDataInfoChunk.map {
                TelemetryScope.async {
                    if (it.dataRequestDTO != null) {
                        if (it in distinctDataProcessRequests) {
                            try {
                                val updatedManualPrice = manualPriceController.createOrUpdateManualPrice(it.dataRequestDTO)

                                ProcessedDataOutput(
                                    row = it.row,
                                    parseExceptionMessage = it.parseExceptionMessage,
                                    processingExceptionMessage = null,
                                    processResponse = updatedManualPrice
                                )
                            } catch (ex: Exception) {
                                ProcessedDataOutput(
                                    row = it.row,
                                    parseExceptionMessage = it.parseExceptionMessage,
                                    processingExceptionMessage = ex.message,
                                    processResponse = null
                                )
                            }
                        } else {
                            // Duplicate entry
                            ProcessedDataOutput(
                                row = it.row,
                                parseExceptionMessage = it.parseExceptionMessage,
                                processingExceptionMessage = "Duplicate entry, already processed",
                                processResponse = null
                            )
                        }
                    } else {
                        ProcessedDataOutput(
                            row = it.row,
                            parseExceptionMessage = it.parseExceptionMessage,
                            processingExceptionMessage = null,
                            processResponse = null
                        )
                    }
                }
            }.awaitAll()
        }.flatten()

        val outputFile = writeToOutputFile(
            job = job,
            inputFile = file,
            processedDataOutput = processedDataOutput
        )

        val successCount = processedDataOutput.count { it.processResponse != null }
        val failedCount = processedDataOutput.count { it.processResponse == null }

        val remarks = "$successCount rows success, $failedCount rows failed"

        asyncJobController.updateAsyncJobStatusAndMetadata(
            jobId = job.id,
            status = AsyncJobStatus.COMPLETED,
            remarks = remarks,
            outputFilePath = outputFile.name,
            updatedBy = "SYSTEM"
        )
        file.delete()
        outputFile.delete()
    }

    private suspend fun parseAndProcessManualPriceDeletion(
        job: AsyncJob,
        file: File
    ) {
        val parsedDataInfo = parsePriceDeletionRowsFromInputSheet(
            job = job,
            inputFile = file
        )

        val distinctDataProcessRequests = parsedDataInfo.filter {
            it.dataRequestDTO != null
        }.distinctBy {
            "${it.dataRequestDTO!!.catalogEntity}:${it.dataRequestDTO.locationType.name}:" +
                    "${it.dataRequestDTO.locationValue}:${it.dataRequestDTO.buyerCohort}".uppercase()
        }

        val processedDataOutput = parsedDataInfo.chunked(5).map { parsedDataInfoChunk ->
            parsedDataInfoChunk.map {
                TelemetryScope.async {
                    if (it.dataRequestDTO != null) {
                        if (it in distinctDataProcessRequests) {
                            try {
                                val deletedManualPrice = manualPriceController.deleteManualPrice(it.dataRequestDTO)

                                ProcessedDataOutput(
                                    row = it.row,
                                    parseExceptionMessage = it.parseExceptionMessage,
                                    processingExceptionMessage = null,
                                    processResponse = deletedManualPrice
                                )
                            } catch (ex: Exception) {
                                ProcessedDataOutput(
                                    row = it.row,
                                    parseExceptionMessage = it.parseExceptionMessage,
                                    processingExceptionMessage = ex.message,
                                    processResponse = null
                                )
                            }
                        } else {
                            // Duplicate entry
                            ProcessedDataOutput(
                                row = it.row,
                                parseExceptionMessage = it.parseExceptionMessage,
                                processingExceptionMessage = "Duplicate entry, already processed",
                                processResponse = null
                            )
                        }
                    } else {
                        ProcessedDataOutput(
                            row = it.row,
                            parseExceptionMessage = it.parseExceptionMessage,
                            processingExceptionMessage = null,
                            processResponse = null
                        )
                    }
                }
            }.awaitAll()
        }.flatten()

        val outputFile = writeToOutputFile(
            job = job,
            inputFile = file,
            processedDataOutput = processedDataOutput
        )

        val successCount = processedDataOutput.count { it.processResponse != null }
        val failedCount = parsedDataInfo.size - successCount

        val remarks = "$successCount rows success, $failedCount rows failed"

        asyncJobController.updateAsyncJobStatusAndMetadata(
            jobId = job.id,
            status = AsyncJobStatus.COMPLETED,
            remarks = remarks,
            outputFilePath = outputFile.name,
            attributesMap = mapOf(
                AsyncJobConstants.TOTAL to parsedDataInfo.size.toString(),
                AsyncJobConstants.SUCCESSFUL to successCount.toString(),
                AsyncJobConstants.FAILED to failedCount.toString()
            ),
            updatedBy = "SYSTEM"
        )

        file.delete()
        outputFile.delete()
    }

    private suspend fun parsePriceCreationRowsFromInputSheet(
        job: AsyncJob,
        inputFile: File,
        ladderInputType: String
    ): List<ParsedDataInfo<ManualPriceUpdateRequest>> {
        val inputWb = withContext(Dispatchers.IO) {
            XSSFWorkbook(inputFile)
        }
        val inputSheet = inputWb.getSheetAt(0)
        val headersToColumnIndexMap = getPriceCreationHeadersToColumnIndexMap(inputSheet)
        val allParsedDataInfo = mutableListOf<ParsedDataInfo<ManualPriceUpdateRequest>>()

        inputSheet.rowIterator().forEach { row ->
            if (row.rowNum < 1 || row.isRowEmpty()) return@forEach

            val parsedRowData = try {
                val catalogEntity = row.getCell(
                    headersToColumnIndexMap["Catalog Entity"] ?: throw Exception("Catalog Entity is missing")
                ).value()

                val catalogEntityType = row.getCell(
                    headersToColumnIndexMap["Catalog Entity Type"] ?: throw Exception("Catalog Entity Type is missing")
                ).value()?.let {
                    CatalogEntityType.valueOf(it)
                }

                val buyerCohort = row.getCell(
                    headersToColumnIndexMap["Buyer Cohort"] ?: throw Exception("Buyer Cohort is missing")
                )?.value()

                val locationType = row.getCell(
                    headersToColumnIndexMap["Location Type"] ?: throw Exception("Location Type is missing")
                ).value()?.uppercase()?.let {
                    LocationType.valueOf(it)
                }

                val locationValue = row.getCell(
                    headersToColumnIndexMap["Location Value"] ?: throw Exception("Location Value is missing")
                ).value()?.replace("BBCT", "Bhubaneswar", ignoreCase = true)

                val expiryDateCell = row.getCell(headersToColumnIndexMap["Expiry (dd/mm/yy hh:mm a)"]!!)

                val expiryDateEpoch = try {
                    when (expiryDateCell.cellType) {
                        Cell.CELL_TYPE_NUMERIC -> {
                            if (DateUtil.isCellDateFormatted(expiryDateCell)) {
                                expiryDateCell.dateCellValue.time // This will already be in milliseconds (UTC)
                            } else {
                                throw IllegalStateException("Cell is numeric but not a date for row ${row.rowNum}")
                            }
                        }
                        Cell.CELL_TYPE_STRING  -> {
                            // Extract date string and parse it using the correct format
                            val dateString = expiryDateCell.stringCellValue.trim()
                            val dateFormat = SimpleDateFormat("dd/MM/yy hh:mm a", Locale.getDefault())

                            // Set timezone to IST (Asia/Kolkata)
                            dateFormat.timeZone = TimeZone.getTimeZone("Asia/Kolkata")

                            val parsedDate = dateFormat.parse(dateString)
                                ?: throw IllegalStateException("Unable to parse expiry date from string for row ${row.rowNum}")

                            parsedDate.time // Returns epoch in milliseconds
                        }
                        else -> throw IllegalStateException("Unsupported cell type ${expiryDateCell.cellType} for row ${row.rowNum}")
                    }
                } catch (ex: Exception) {
                    logger.error("JobId: {}, Row {}, unable to parse expiry epoch. Ex: {}", job.id, row.rowNum, ex.message)
                    throw ex
                }

                val updateReason = row.getCell(
                    headersToColumnIndexMap["Reason"] ?: throw Exception("Reason is missing")
                ).value()

                // adding check on empty cols which are critical
                if (catalogEntity.isNullOrBlank() || catalogEntityType == null || locationType == null ||
                    locationValue.isNullOrBlank() || updateReason.isNullOrBlank()) {
                    throw IllegalArgumentException("One or more of these are empty - CatalogEntity, CatalogEntityType, " +
                            "LocationType, LocationValue, Reason")
                }

                val ladderInputs = getLadderInputs(
                    row = row,
                    headerMap = headersToColumnIndexMap
                )
                val priceInfo = when (ladderInputType) {
                    UNIT_PRICE_IN_PAISA -> AbsolutePriceInfo(ladderInputs)
                    MRP_MARKDOWN_IN_BPS -> MrpMarkdownInfo(ladderInputs)
                    else -> throw IllegalArgumentException("Input Type not supported")
                }

                val manualPriceUpdateRequest = ManualPriceUpdateRequest(
                    catalogEntity = catalogEntity,
                    catalogEntityType = catalogEntityType,
                    locationType = locationType,
                    locationValue = locationValue,
                    buyerCohort = buyerCohort,
                    priceInfo = priceInfo,
                    expiryTimeInMillis = expiryDateEpoch,
                    jobIdReference = job.id,
                    updatedBy = job.createdBy,
                    updateReason = updateReason
                )
                logger.info(
                    "JobId: {}, Row {}, parsed manual price update request: {}",
                    job.id,
                    row.rowNum,
                    manualPriceUpdateRequest
                )

                ParsedDataInfo(
                    row = row.rowNum,
                    parseExceptionMessage = null,
                    dataRequestDTO = manualPriceUpdateRequest
                )
            } catch (e: Exception) {
                logger.error(
                    "JobId: {}, Row {}, Exception while parsing manual price update request: {}",
                    job.id,
                    row.rowNum,
                    e.message
                )

                ParsedDataInfo<ManualPriceUpdateRequest>(
                    row = row.rowNum,
                    parseExceptionMessage = e.message,
                    dataRequestDTO = null
                )
            }

            allParsedDataInfo.add(parsedRowData)
        }

        inputWb.close()

        return allParsedDataInfo
    }

    private fun getPriceCreationHeadersToColumnIndexMap(sheet: XSSFSheet): Map<String, Int> {
        val headers = mutableMapOf<String, Int>()
        val headerRow = sheet.getRow(0)

        // Assuming maximum ladders to be 10
        val possibleQtyAndDiscountHeaders = (1 .. 10).map {
            listOf("Qty$it", "Input$it")
        }.flatten()

        val allowedHeaders = mutableListOf(
            "Catalog Entity", "Catalog Entity Type", "Location Type", "Location Value",
            "Buyer Cohort", "Expiry (dd/mm/yy hh:mm a)", "Reason"
        ) + possibleQtyAndDiscountHeaders

        for (cell in headerRow.cellIterator()) {
            val header = cell.value()
            if (allowedHeaders.contains(header) && header != null) {
                headers[header] = cell.columnIndex
            }
        }
        logger.info("Found headers with column no : {}", headers)
        return headers
    }

    private suspend fun parsePriceDeletionRowsFromInputSheet(
        job: AsyncJob,
        inputFile: File
    ): List<ParsedDataInfo<ManualPriceDeleteRequest>> {
        val inputWb = withContext(Dispatchers.IO) {
            XSSFWorkbook(inputFile)
        }
        val inputSheet = inputWb.getSheetAt(0)
        val headersToColumnIndexMap = getPriceDeletionHeadersToColumnIndexMap(inputSheet)
        val allParsedDataInfo = mutableListOf<ParsedDataInfo<ManualPriceDeleteRequest>>()

        inputSheet.rowIterator().forEach { row ->
            if (row.rowNum < 1 || row.isRowEmpty()) return@forEach

            val parsedRowData = try {
                val catalogEntity = row.getCell(
                    headersToColumnIndexMap["Catalog Entity"] ?: throw Exception("Catalog Entity is missing")
                ).value()

                val catalogEntityType = row.getCell(
                    headersToColumnIndexMap["Catalog Entity Type"] ?: throw Exception("Catalog Entity Type is missing")
                ).value()?.let {
                    CatalogEntityType.valueOf(it)
                }

                val buyerCohort = row.getCell(
                    headersToColumnIndexMap["Buyer Cohort"] ?: throw Exception("Buyer Cohort is missing")
                )?.value()

                val locationType = row.getCell(
                    headersToColumnIndexMap["Location Type"] ?: throw Exception("Location Type is missing")
                ).value()?.uppercase()?.let {
                    LocationType.valueOf(it)
                }

                val locationValue = row.getCell(
                    headersToColumnIndexMap["Location Value"] ?: throw Exception("Location Value is missing")
                ).value()?.replace("BBCT", "Bhubaneswar", ignoreCase = true)

                val deletionReason = row.getCell(
                    headersToColumnIndexMap["Reason"] ?: throw Exception("Reason is missing")
                ).value()

                // adding check on empty cols which are critical
                if (catalogEntity.isNullOrBlank() || catalogEntityType == null || locationType == null ||
                    locationValue.isNullOrBlank() || deletionReason.isNullOrBlank()) {
                    throw IllegalArgumentException("One or more of these are empty - CatalogEntity, CatalogEntityType, " +
                            "LocationType, LocationValue, Reason")
                }

                val manualPriceDeleteRequest = ManualPriceDeleteRequest(
                    catalogEntity = catalogEntity,
                    catalogEntityType = catalogEntityType,
                    locationType = locationType,
                    locationValue = locationValue,
                    buyerCohort = buyerCohort,
                    jobIdReference = job.id,
                    deletedBy = job.createdBy,
                    deletionReason = deletionReason
                )
                logger.info(
                    "JobId: {}, Row {}, parsed manual price deletion request: {}",
                    job.id,
                    row.rowNum,
                    manualPriceDeleteRequest
                )

                ParsedDataInfo(
                    row = row.rowNum,
                    parseExceptionMessage = null,
                    dataRequestDTO = manualPriceDeleteRequest
                )
            } catch (e: Exception) {
                logger.error(
                    "JobId: {}, Row {}, Exception while parsing manual price delete request: {}",
                    job.id,
                    row.rowNum,
                    e.message
                )

                ParsedDataInfo<ManualPriceDeleteRequest>(
                    row = row.rowNum,
                    parseExceptionMessage = e.message,
                    dataRequestDTO = null
                )
            }

            allParsedDataInfo.add(parsedRowData)
        }

        inputWb.close()

        return allParsedDataInfo
    }

    private fun getPriceDeletionHeadersToColumnIndexMap(sheet: XSSFSheet): Map<String, Int> {
        val headers = mutableMapOf<String, Int>()
        val headerRow = sheet.getRow(0)
        val allowedHeaders = mutableListOf(
            "Catalog Entity", "Catalog Entity Type", "Location Type", "Location Value",
            "Buyer Cohort", "Reason"
        )

        for (cell in headerRow.cellIterator()) {
            val header = cell.value()
            if (allowedHeaders.contains(header) && header != null) {
                headers[header] = cell.columnIndex
            }
        }
        logger.info("Found headers with column no : {}", headers)
        return headers
    }

    private fun getLadderInputs(row: Row, headerMap: Map<String, Int>): List<Ladder> {
        val result = mutableListOf<Ladder>()
        for (i in 1..10) {
            val quantityCell = row.getCell(headerMap["Qty$i"] ?: continue)
            val quantity = if (quantityCell != null && !quantityCell.value().isNullOrBlank()) {
                quantityCell.value()?.toLong()
            } else continue

            val ladderInputCell = row.getCell(headerMap["Input$i"] ?: continue)
            val ladderInput = if (ladderInputCell != null && !ladderInputCell.value().isNullOrBlank()) {
                ladderInputCell.value()?.toBigDecimal()
            } else continue

            if (result.isEmpty()) {
                result.add(
                    Ladder(
                        minQuantity = quantity!!,
                        maxQuantity = Int.MAX_VALUE.toLong(),
                        ladderValue = ladderInput!!
                    )
                )
            } else {
                if (quantity!! > result.last().minQuantity) {
                    result.add(
                        Ladder(
                            minQuantity = quantity,
                            maxQuantity = Int.MAX_VALUE.toLong(),
                            ladderValue = ladderInput!!
                        )
                    )
                }
                else {
                    throw Exception("Row no. ${row.rowNum} Ladder $i quantity is less than or equal to the last ladder")
                }
            }
        }

        if (result.isNotEmpty() && result.first().minQuantity > 1) {
            result.set(
                index = 0,
                element = Ladder(
                    minQuantity = 1,
                    maxQuantity = Int.MAX_VALUE.toLong(),
                    ladderValue = result.first().ladderValue
                )
            )
        }

        for (i in 0 until result.size - 1) {
            result[i] = result[i].copy(
                maxQuantity = result[i+1].minQuantity - 1
            )
        }

        return result.toList()
    }

    private suspend fun writeToOutputFile(
        job: AsyncJob,
        inputFile: File,
        processedDataOutput: List<ProcessedDataOutput<out ManualPrice>>
    ): File {
        /**
         * Init outputWb from input file since, there are no stable available methods to move or rename
         * input file to a different output file name
         */
        val outputWb = withContext(Dispatchers.IO) {
            XSSFWorkbook(inputFile)
        }
        val outputSheet = outputWb.getSheetAt(0)

        val headersToColumnIndexMap = getPriceCreationHeadersToColumnIndexMap(outputSheet)
        outputSheet.getRow(0).createCell(headersToColumnIndexMap.size).setCellValue("Parse Exception (If any)")
        outputSheet.getRow(0).createCell((headersToColumnIndexMap.size + 1)).setCellValue("Process Exception (If any)")
        outputSheet.getRow(0).createCell((headersToColumnIndexMap.size + 2)).setCellValue("Output")

        processedDataOutput.forEach {
            outputSheet.getRow(it.row).createCell(headersToColumnIndexMap.size).setCellValue(it.parseExceptionMessage ?: "N/A")
            outputSheet.getRow(it.row).createCell((headersToColumnIndexMap.size + 1)).setCellValue(it.processingExceptionMessage ?: "N/A")
            outputSheet.getRow(it.row).createCell((headersToColumnIndexMap.size + 2)).setCellValue(it.processResponse?.referenceId ?: "N/A")
        }

        /**
         * Initialise an output file and write data from workbook to this file's output stream
         * Then, close the outputWorkbook and delete the existing input file
         * You shouldn't delete the input file earlier, since outputWb.close() will save all the contents back
         * if it doesn't exist (as, we have used a file i.e; OPCPackge to instantiate it)
         */
        // Write the workbook to a file
        val outputFile = File(job.id + "_output.xlsx")
        withContext(Dispatchers.IO) {
            FileOutputStream(outputFile).use { fos ->
                outputWb.write(fos)
            }
        }
        outputWb.close()

        blobStorageHelper.uploadFileToBlobStorage(
            containerReference = job.type.toString().lowercase().replace("_", "-"),
            directoryReference = job.type.toString().lowercase().replace("_", "-"),
            file = outputFile
        )
        return outputFile
    }

    private data class ParsedDataInfo<S>(
        val row: Int,
        val parseExceptionMessage: String?,
        val dataRequestDTO: S?
    )

    private data class ProcessedDataOutput<T>(
        val row: Int,
        val parseExceptionMessage: String?,
        val processingExceptionMessage: String?,
        val processResponse: T?
    )
}
