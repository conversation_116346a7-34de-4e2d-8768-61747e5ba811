package com.udaan.pricing.jobs.models.contracts

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class ContractQuoteTaggingResponse(
    @JsonProperty("contractCatalogEntityId") val contractCatalogEntityId: String,
    @JsonProperty("contractCatalogEntity") val contractCatalogEntity: String,
    @JsonProperty("city") val city: String,
    @JsonProperty("strategies") val strategies: List<String>
)
