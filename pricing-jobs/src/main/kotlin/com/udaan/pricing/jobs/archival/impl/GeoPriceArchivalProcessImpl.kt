package com.udaan.pricing.jobs.archival.impl

import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.pricing.GeoPricing
import com.udaan.pricing.core.controller.cache.GeoDifferentialPriceCacheController
import com.udaan.pricing.core.dao.GeoPriceRepository
import com.udaan.pricing.core.utils.SlackChannelEnum
import com.udaan.pricing.core.utils.SlackNotifier
import com.udaan.pricing.jobs.archival.abstraction.ArchivalProcess
import com.udaan.pricing.jobs.archival.models.ArchiveCountOverTime
import java.util.*

class GeoPriceArchivalProcessImpl @Inject constructor(
    private val geoPriceRepository: GeoPriceRepository,
    private val geoDifferentialPriceCacheController: GeoDifferentialPriceCacheController
) : ArchivalProcess<GeoPricing> {

    private val probeId = "xn25pq"

    private val slackNotifier = SlackNotifier(SlackChannelEnum.RETAIL_DB_ARCHIVAL_ALERTS.slackUrl)

    private val log by logger()

    override fun getTimeStepperInSeconds(): Long {
        /** one day **/
        return 86400
    }

    override fun getTtl(): Long {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.YEAR, -1);
        return calendar.timeInMillis.div(1000)
    }

    override fun fetchProbeId(): String {
        return probeId
    }

    override suspend fun fetchDataCountFromSource(fromTs: Long, toTs: Long): Long {
        val count = geoPriceRepository.fetchDataCountByTimeRange(fromTimeStamp = fromTs, toTimeStamp = toTs)
        log.info("Fetched count from source $count with $fromTs and $toTs")
        return count
    }

    override suspend fun fetchDataFromSource(fromTs: Long, toTs: Long): Collection<GeoPricing> {
        val data = geoPriceRepository.fetchDataByTimeRange(fromTimeStamp = fromTs, toTimeStamp = toTs)
        log.info("Fetched data from source ${data.size} with $fromTs and $toTs")
        return data
    }

    override suspend fun deleteItems(items: Collection<GeoPricing>) {
        geoPriceRepository.deleteItems(items).also {
            log.info("Deleted items {}", items)
            geoDifferentialPriceCacheController.invalidateGeoDiffForListings(items.mapNotNull {
                it.listingId
            }.distinct())
        }
    }

    override suspend fun onSourceAndArchivalCountMismatch(
        sourceCount: Long,
        archivalCount: Long,
        item: ArchiveCountOverTime
    ) {
        if (sourceCount != 0L && (sourceCount != archivalCount)) {
            slackNotifier.sendMessage(
                notifyTo = "<@satya.e>",
                message = "geo-price collection has mismatch between  sourceCount: $sourceCount, " +
                        "archivalCount: $archivalCount for $item"
            )
        }
    }
}