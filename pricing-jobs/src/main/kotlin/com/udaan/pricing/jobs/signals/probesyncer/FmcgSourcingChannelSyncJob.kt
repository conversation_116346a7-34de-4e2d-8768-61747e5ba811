package com.udaan.pricing.jobs.signals.probesyncer

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.google.inject.Guice
import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.JobScope
import com.udaan.pricing.commons.StringValue
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.core.managers.signals.SignalWriteManager
import com.udaan.pricing.core.svcinterfaces.DpServiceInterface
import com.udaan.pricing.signalcreation.GenericGidLevelInput
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.jobs.utils.PushGatewayUtil
import kotlin.system.exitProcess

class FmcgSourcingChannelSyncJob @Inject constructor(
    dpServiceInterface: DpServiceInterface,
    signalWriteManager: SignalWriteManager
) : DPInputProbeSyncer(
    dpServiceInterface,
    signalWriteManager
) {

    companion object {
        private val logger by logger()
        private val injector = Guice.createInjector(PricingCoreModule())
        private val pushGatewayUtil = PushGatewayUtil("FmcgSourcingChannelSyncJob", logger)

        @JvmStatic
        fun main(args: Array<String>) {
            val fmcgSourcingChannelSyncJob = injector.getInstance(FmcgSourcingChannelSyncJob::class.java)
            try {
                logger.info("********************** Job STARTED **********************")
                JobScope.runBlocking {
                    pushGatewayUtil.pushJobStarted()
                    pushGatewayUtil.recordStats {
                        fmcgSourcingChannelSyncJob.process()
                    }
                    pushGatewayUtil.pushJobEnded()
                }
                logger.info("********************** Job Completed **********************")
            } catch (e: Throwable) {
                e.printStackTrace()
                logger.error("********************** Job FAILED **********************", e)
                exitProcess(1)
            } finally {
                logger.error("********************** Process End **********************")
                exitProcess(0)
            }
        }
    }

    override val probeId = "jhki2h"
    override val chunkSize = 25
    override val delayBetweenChunksInMillis = 1000L

    suspend fun process() {
        val fmcgSourcingChannelDPData = getDataFromDataPlatform<FmcgSourcingChannelDPData>()

        val convertedGidLevelInputs = fmcgSourcingChannelDPData.map {
            GenericGidLevelInput(
                productGroupId = it.gid,
                variableId = VariableId.SOURCING_CHANNEL,
                data = StringValue(
                    value = it.finalChannel.trim()
                ),
                metadata = emptyMap(),
                location = Location(
                    locationType = LocationType.CITY,
                    locationValue = it.city
                ),
                updatedBy = "DATA_PLATFORM_PROBE"
            )
        }

        processAndSaveRawInputs(convertedGidLevelInputs)
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class FmcgSourcingChannelDPData(
        val gid: String,
        val city: String,
        val finalChannel: String
    )
}
