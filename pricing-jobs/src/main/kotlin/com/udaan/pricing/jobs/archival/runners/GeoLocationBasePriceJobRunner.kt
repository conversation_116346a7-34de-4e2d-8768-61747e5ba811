package com.udaan.pricing.jobs.archival.runners

import com.google.inject.Guice
import com.udaan.common.utils.kotlin.logger
import com.udaan.instrumentation.JobScope
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.jobs.archival.impl.GeoLocationBasePriceArchivalProcessImpl
import kotlin.system.exitProcess

object GeoLocationBasePriceJobRunner {
    private val log by logger()
    private val injector = Guice.createInjector(PricingCoreModule())
    private val geoLocationBasePriceArchivalProcessImpl = injector.getInstance(GeoLocationBasePriceArchivalProcessImpl::class.java)

    @JvmStatic
    fun main(args: Array<String>) {
        JobScope.runBlocking {
            try {
                log.info("Job STARTED")
                geoLocationBasePriceArchivalProcessImpl.process()
                log.info("Job SUCCESSFUL")
            } catch (e: Throwable) {
                log.error("Job FAILED", e)
                exitProcess(1)
            } finally {
                exitProcess(0)
            }
        }
    }
}