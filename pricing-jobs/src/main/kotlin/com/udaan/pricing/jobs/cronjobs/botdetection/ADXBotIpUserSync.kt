package com.udaan.pricing.jobs.cronjobs.botdetection

import com.google.inject.Guice
import com.google.inject.Inject
import com.udaan.common.client.exceptions.UdaanTimeoutException
import com.udaan.common.utils.kotlin.logger
import com.udaan.config.Configuration
import com.udaan.dataplatform.client.DataPlatformIngestionClient
import com.udaan.pricing.jobs.PricingJobModule
import com.udaan.resources.lifecycle.LifeCycleObjectRepo
import com.udaan.resources.lifecycle.guice.LifecycleAwareModule
import java.util.concurrent.TimeUnit

class ADXBotIpUserSync {
    companion object {
        private val injector = Guice.createInjector(
            LifecycleAwareModule(PricingJobModule())
        )!!

        @JvmStatic
        fun main(args: Array<String>) {
            val jobConfig = (Configuration.getMap("bot-detection/adx-job/config")
                ?: throw IllegalStateException("No config found by key: bot-detection/adx-job/config")
                    ).let {
                    BotAdxJobConfig.fromMap(it)
                }

            try {
                val runner: Runner = injector.getInstance(Runner::class.java)
                jobs.forEach { job ->
                    runner.executeJob(job, jobConfig)
                }
            } finally {
                LifeCycleObjectRepo.global().close()
            }

        }
    }
}

internal class Runner @Inject constructor(private val kustoClientFactory: ADXServiceClientFactory,
                                          private val dpIngestionClient: DataPlatformIngestionClient) {

    companion object {
        private val LOG by logger()
    }

    fun executeJob(job: BotDetectionAdxQuerySyncJob, jobConfig: BotAdxJobConfig) {
        val marker = job.withContext()
        if (jobConfig.skip) {
            LOG.info(marker, "*** Skipping execution as 'skip' flag is set in the config")
            return
        }

        val kustoClient: ADXServiceClient = kustoClientFactory
            .createInstanceForDB(jobConfig.adxDbName)

        LOG.info("$marker *** ADX Query started $job")
        val result = kustoClient.query(job.query)
        val listOfData = result.to(job.dataKlass)
            .toList()

        LOG.info("$marker *** ADX Query Completed, record_count: ${listOfData.size}")
        LOG.info("$marker Data platform Sync to table: ${job.dpOutputTable} started for ${listOfData.firstOrNull()}, record_count: ${listOfData.size}")

        listOfData.chunked(jobConfig.chunkSize).forEach { chunkedList ->
            try {
                dpIngestionClient.pushToDp(
                    jobConfig.dpTableOwnerEmail,
                    chunkedList,
                    mode = jobConfig.dpIngestionMode,
                    outputTableFullName = job.dpOutputTable,
                    timeoutInSecs = jobConfig.dpTimeoutInSecs
                ).executeSync()
            } catch (e: UdaanTimeoutException) {
                LOG.warn("Data platform ingestion request timed out, ", e)
                // Do nothing as DP will finish the ingestion
            }
        }
        LOG.info("$marker Data platform Sync to table: ${job.dpOutputTable} complete")
    }
}

data class BotAdxJobConfig(
    val dpTimeoutInSecs: Int,
    val dpTableOwnerEmail: String,
    val dpIngestionMode: String,
    val adxDbName: String,
    val skip: Boolean = false,
    val chunkSize: Int = 10000
){
    companion object {
        fun fromMap(configMap: Map<String, Any>): BotAdxJobConfig {
            val dpTimeoutInSecs: Int = configMap["dpTimeoutInSecs"]?.toString()?.toInt() ?: TimeUnit.MINUTES.toSeconds(5).toInt()
            val dpTableOwnerEmail: String by configMap
            val dpIngestionMode: String by configMap
            val adxDbName: String by configMap
            val skip: Boolean = configMap["skip"]?.toString()?.toBoolean() ?: false
            val chunkSize: Int = configMap["chunkSize"]?.toString()?.toInt() ?: 10000

            return BotAdxJobConfig(
                dpTimeoutInSecs = dpTimeoutInSecs,
                dpTableOwnerEmail = dpTableOwnerEmail,
                dpIngestionMode = dpIngestionMode,
                adxDbName = adxDbName,
                skip = skip,
                chunkSize = chunkSize
            )
        }
    }
}

