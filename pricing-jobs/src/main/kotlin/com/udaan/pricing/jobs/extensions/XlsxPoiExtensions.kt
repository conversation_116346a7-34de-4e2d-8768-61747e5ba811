package com.udaan.pricing.jobs.extensions

import org.apache.poi.ss.usermodel.Cell
import org.apache.poi.ss.usermodel.DateUtil
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.ss.util.NumberToTextConverter

/**
 * Returns the value of a cell as a string
 */
fun Cell.value(): String? {
    val cellValue =  when (cellType) {
        Cell.CELL_TYPE_STRING -> stringCellValue.trim()
        Cell.CELL_TYPE_NUMERIC ->  if (DateUtil.isCellDateFormatted(this)) {
            this.dateCellValue.time.toString()
        } else {
            NumberToTextConverter.toText(numericCellValue).trim()
        }
        Cell.CELL_TYPE_FORMULA -> NumberToTextConverter.toText(numericCellValue).trim().uppercase()
        Cell.CELL_TYPE_ERROR -> errorCellValue.toString()
        Cell.CELL_TYPE_BOOLEAN -> booleanCellValue.toString()
        Cell.CELL_TYPE_BLANK -> null
        else -> null
    }
    if (cellValue.isNullOrBlank()) return null
    return cellValue
}

/**
 * Checks if a row is empty
 */
fun Row.isRowEmpty(): Boolean {
    for (c in this.firstCellNum until this.lastCellNum) {
        val cell = this.getCell(c)
        if (cell != null && cell.cellType != Cell.CELL_TYPE_BLANK) return false
    }
    return true
}
