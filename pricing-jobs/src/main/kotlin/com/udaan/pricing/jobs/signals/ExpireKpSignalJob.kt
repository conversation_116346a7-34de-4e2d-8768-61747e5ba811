package com.udaan.pricing.jobs.signals

import com.google.inject.Guice
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.core.managers.signals.SignalReadManager
import com.udaan.pricing.core.managers.signals.SignalWriteManager
import com.udaan.pricing.variable.VariableId
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import java.time.ZoneId
import javax.inject.Inject
import kotlin.system.exitProcess

/**
 * https://www.notion.so/udaantech/Staples-migration-to-new-system-from-legacy-with-VSL-support-1592aa539e938042a92bef0aa66eb686
 * KP follows a different expiry logic, so have to cronjob which runs explicitly at 3 PM to expire KP signals
 */
class ExpireKpSignalJob @Inject constructor(
    private val signalWriteManager: SignalWriteManager,
    private val signalReadManager: SignalReadManager
) {

    companion object {
        private val logger by logger()

        @JvmStatic
        fun main(args: Array<String>) {
            val injector = Guice.createInjector(PricingCoreModule())
            val instance = injector.getInstance(ExpireKpSignalJob::class.java)
            try {
                logger.info("JOB START")
                runBlocking {
                    instance.process()
                }
                logger.info("JOB END")
                exitProcess(0)
            } catch (e: Exception) {
                logger.error("JOB FAILED")
                logger.error(e.message)
                exitProcess(1)
            }
        }

    }

    private fun getTodayMidnightEpochMillis(): Long {
        val zone = ZoneId.systemDefault() // or use a specific zone like ZoneId.of("Asia/Kolkata")
        val midnight = LocalDate.now(zone).atStartOfDay(zone)
        return midnight.toInstant().toEpochMilli()
    }

    suspend fun process() {
        val todayMidNightInEpochMilli = getTodayMidnightEpochMillis()
        logger.info("Today midnight in epoch millis: $todayMidNightInEpochMilli")
        signalReadManager.getSignalsForVariable(VariableId.OFFLINE_COMPETITION_WT_PAISA_UNIT).filter {
            it.updatedAt < todayMidNightInEpochMilli
        }.chunked(100).map { signals ->
            delay(1000)
            signals.parallelMap { signal ->
                try {
                    logger.info("Marking signal as expired: ${signal.id}")
                    signalWriteManager.markSignalAsExpired(
                        id = signal.id,
                        partitionKey = signal.partitionKey,
                        updatedBy = "KP_EXPIRY_CRONJOB",
                        forceExpiry = true
                    )
                    logger.info("Marked signal as expired: ${signal.id}")
                } catch (e: Exception) {
                    logger.error("Failed to mark signal as expired: ${signal.id} with error ", e.cause)
                }
            }
        }
    }
}


