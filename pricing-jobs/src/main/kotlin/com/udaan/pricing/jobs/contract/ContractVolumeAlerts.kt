package com.udaan.pricing.jobs.contract

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.google.inject.Inject
import com.udaan.common.utils.getCurrentMillis
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.pricing.core.controller.contract.ContractController
import com.udaan.pricing.core.models.contracts.Contract
import com.udaan.pricing.core.models.contracts.ContractCatalogEntity
import com.udaan.pricing.core.models.contracts.isValid
import com.udaan.pricing.core.svcinterfaces.DpServiceInterface
import com.udaan.pricing.jobs.models.contracts.VolumeAlertOutputFileRow
import com.udaan.pricing.jobs.models.contracts.VolumeAlertSummary
import com.udaan.pricing.jobs.models.contracts.VolumeAlertSummaryForBuyer
import com.udaan.proto.models.ModelV1
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*
import java.util.concurrent.*

class ContractVolumeAlerts @Inject constructor(
    private val dpServiceInterface: DpServiceInterface,
    private val contractControllerV2: ContractController,
    private val contractAlertsHelper: ContractAlertsHelper
) {

    companion object {
        const val milliSecondsNormaliser = 1000
    }

    private val logger by logger()

    private val volumeOrderedByBuyerProbeId = "hf276o"
    private val defaultCity = "bangalore"
    private val formatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

    suspend fun process(
        contracts: List<Contract>,
        buyerOrgDetailsMap: Map<String, ModelV1.OrgAccount?>,
        catalogTitleDetailsMap: Map<String, String?>
    ): VolumeAlertSummary {
        logger.info("Processing contract volume alerts")
        updateVolumeOrderedForBuyer(contracts)
        val activeContracts = contracts.parallelMap {
            contractControllerV2.getContractByBuyerOrgAndCatalogEntity(
                it.buyerOrgId,
                it.catalogEntityId
            )
        }.filterNotNull().filter { it.isValid() }
        return VolumeAlertSummary(
            volumeAlertSummaryForBuyerList = getVolumeAlertSummaryForBuyer(
                contracts = activeContracts,
                buyerOrgDetailsMap = buyerOrgDetailsMap
            ),
            volumeDataToRaiseAlert = checkContractsForVolumeAlerts(
                contracts = activeContracts,
                buyerOrgDetailsMap = buyerOrgDetailsMap,
                catalogTitleDetailsMap = catalogTitleDetailsMap
            )
        )
    }

    private suspend fun getVolumeAlertSummaryForBuyer(
        contracts: List<Contract>,
        buyerOrgDetailsMap: Map<String, ModelV1.OrgAccount?>
    ): List<VolumeAlertSummaryForBuyer> {
        return contracts.groupBy {
            it.buyerOrgId
        }.entries.parallelMap {
            val buyerOrgName = buyerOrgDetailsMap[it.key]?.displayName ?: ""
            val totalSkusLiveOnContract = it.value.size
            val skusWith25PercentVolumeAchievement = it.value.count {
                it.metadata.volumeOrdered >= it.metadata.volumeCommitted * 0.25
            }
            val skusWith50PercentVolumeAchievement = it.value.count {
                it.metadata.volumeOrdered >= it.metadata.volumeCommitted * 0.5
            }
            VolumeAlertSummaryForBuyer(
                buyerOrgId = it.key,
                buyerName = buyerOrgName,
                skusLiveOnContract = totalSkusLiveOnContract.toLong(),
                skusWith25PercentVolumeAchievement = skusWith25PercentVolumeAchievement.toLong(),
                skusWith50PercentVolumeAchievement = skusWith50PercentVolumeAchievement.toLong()
            )
        }.sortedByDescending { it.skusLiveOnContract }
    }

    private suspend fun checkContractsForVolumeAlerts(
        contracts: List<Contract>,
        buyerOrgDetailsMap: Map<String, ModelV1.OrgAccount?>,
        catalogTitleDetailsMap: Map<String, String?>
    ): List<VolumeAlertOutputFileRow> {
        return contracts.parallelMap { contract ->
            val volumeCommitmentInfo = getVolumeCommitmentInfoForContract(contract)
            if (volumeCommitmentInfo != null &&
                volumeCommitmentInfo.volumeOrderedTillNow < volumeCommitmentInfo.expectedVolumeTillNow
            ) {
                logger.error("Volume committed for contract ${contract.id} is less than expected volume till now")
                contract.toContractVolumeAlertOutputFileRow(
                    volumeCommitmentInfo.expectedVolumeTillNow,
                    buyerOrgDetailsMap[contract.buyerOrgId]?.displayName ?: "",
                    catalogTitleDetailsMap[contract.catalogEntityId] ?: ""
                )
            } else {
                null
            }
        }.filterNotNull()
    }

    private fun getVolumeCommitmentInfoForContract(contract: Contract): VolumeCommitmentInfo? {
        val endTime = if (contract.duration.lockInTime > 0) {
            contract.duration.lockInTime
        } else {
            contract.duration.endTime
        }
        val totalContractDays = (endTime - contract.duration.startTime).div(TimeUnit.DAYS.toMillis(1))
        if (totalContractDays == 0L) {
            logger.error("Total days for contract ${contract.id} is 0")
            return null
        }
        val currentTime = getCurrentMillis()
        val daysElapsed = (currentTime - contract.duration.startTime) / TimeUnit.DAYS.toMillis(1)
        if (daysElapsed <= 0) {
            logger.info("Contract ${contract.id} has not started yet.")
            return null
        }
        if (currentTime >= endTime) {
            logger.info("Contract ${contract.id} has already ended, no alerts needed.")
            return null
        }
        val expectedVolumeCommitmentPercentage = daysElapsed.toDouble() / totalContractDays.toDouble()
        val expectedVolumeTillNow = contract.metadata.volumeCommitted * expectedVolumeCommitmentPercentage
        return VolumeCommitmentInfo(
            buyerOrgId = contract.buyerOrgId,
            catalogEntityId = contract.catalogEntityId,
            city = contract.metadata.city ?: defaultCity,
            volumeCommitted = contract.metadata.volumeCommitted,
            volumeOrderedTillNow = contract.metadata.volumeOrdered,
            volumeOrderedRefreshedTill = contract.metadata.volumeOrderedRefreshedTill?.toString(),
            expectedVolumeTillNow = expectedVolumeTillNow.toLong(),
            expectedVolumePercentage = contract.metadata.volumeOrdered.toDouble() / expectedVolumeTillNow
        )
    }


    /**
     * Fetches active contracts from the probe.
     * Why not fetch from repository directly ?
     * 1. This gives an additional load on database, and the query is fan out query to all partitions.
     */
    private suspend fun updateVolumeOrderedForBuyer(activeContracts: List<Contract>) {
        val volumeOrderedList = dpServiceInterface.getProbeData<VolumeOrderedByBuyer>(
            volumeOrderedByBuyerProbeId
        )
        activeContracts.parallelMap {
            try {
                updateVolumeOrderedForAContract(it, volumeOrderedList)
            } catch (e: Exception) {
                logger.error("Failed to update volume ordered for contract ${it.id} with error ", e)
            }
        }
    }

    private suspend fun updateVolumeOrderedForAContract(
        activeContract: Contract,
        volumeOrderedList: List<VolumeOrderedByBuyer>
    ) {
        try {
            if (activeContract.isValid()) {
                val contractDurationInDates = getDatesBetween(
                    activeContract.duration.startTime,
                    if (activeContract.duration.lockInTime > 0) {
                        activeContract.duration.lockInTime
                    } else {
                        activeContract.duration.endTime
                    }
                )
                val volumesOrderedTillNow = volumeOrderedList.filter {
                    contractDurationInDates.contains(formatter.format(it.orderDate)) &&
                            it.buyerOrgId == activeContract.buyerOrgId
                }.filter {
                    if (activeContract.contractCatalogEntity == ContractCatalogEntity.PRODUCT_GROUP_ID) {
                        it.groupId.trim().uppercase() == activeContract.catalogEntityId
                    } else {
                        it.listingId.trim().uppercase() == activeContract.catalogEntityId
                    }
                }
                val volumeOrderedOnContractDuration = volumesOrderedTillNow.sumOf {
                    if (activeContract.contractCatalogEntity == ContractCatalogEntity.PRODUCT_GROUP_ID) {
                        it.gidUnits.toInt()
                    } else {
                        it.lidUnits.toInt()
                    }
                }.toLong()
                val volumeCapturedTillDate = volumesOrderedTillNow.maxByOrNull { it.orderDate }?.orderDate
                if (volumeOrderedOnContractDuration > 0) {
                    contractControllerV2.updateVolumeOrderedForBuyer(
                        buyerOrgId = activeContract.buyerOrgId,
                        catalogEntityId = activeContract.catalogEntityId,
                        volumeOrdered = volumeOrderedOnContractDuration,
                        volumeOrderedRefreshedTill = volumeCapturedTillDate?.toInstant()?.toEpochMilli()
                    )
                    logger.info(
                        "Updated volume ordered for buyer: ${activeContract.buyerOrgId} " +
                                "and groupId: ${activeContract.catalogEntityId}, $volumeOrderedOnContractDuration"
                    )
                } else {
                    logger.info(
                        "No volume ordered for buyer: ${activeContract.buyerOrgId} " +
                                "and groupId: ${activeContract.catalogEntityId}"
                    )
                }
            } else {
                logger.info(
                    "No valid contract for buyer: ${activeContract.buyerOrgId}" +
                            " and groupId: ${activeContract.catalogEntityId}"
                )
            }
        } catch (e: Exception) {
            logger.error(
                "Failed to update " +
                        "volume ordered for buyer: ${activeContract.buyerOrgId}, ${activeContract.catalogEntityId} with error ",
                e
            )
        }
    }

    private fun getDatesBetween(startEpoch: Long, endEpoch: Long): List<String> {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
        val startDate = Instant.ofEpochSecond(
            startEpoch / milliSecondsNormaliser
        ).atZone(ZoneId.systemDefault()).toLocalDate()
        val endDate = Instant.ofEpochSecond(
            endEpoch / milliSecondsNormaliser
        ).atZone(ZoneId.systemDefault()).toLocalDate()

        return generateSequence(startDate) { it.plusDays(1) }
            .takeWhile { it <= endDate }
            .map { it.format(formatter) }
            .toList()
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class VolumeOrderedByBuyer(
        @JsonProperty("org_id") val buyerOrgId: String,
        @JsonProperty("groupid") val groupId: String,
        @JsonProperty("listing_id") val listingId: String,
        @JsonProperty("order_date") val orderDate: Date,
        @JsonProperty("lid_units") val lidUnits: Double,
        @JsonProperty("gid_units") val gidUnits: Double
    )

    private fun Contract.toContractVolumeAlertOutputFileRow(
        expectedVolumeTillNow: Long,
        buyerName: String,
        catalogTitle: String
    ): VolumeAlertOutputFileRow {
        return VolumeAlertOutputFileRow(
            buyerOrgId = buyerOrgId,
            buyerName = buyerName,
            city = metadata.city ?: defaultCity,
            contractCatalogEntityId = catalogEntityId,
            catalogTitle = catalogTitle,
            contractCatalogEntity = contractCatalogEntity.name,
            volumeCommitted = metadata.volumeCommitted,
            expectedVolumeTillNow = expectedVolumeTillNow,
            volumeOrderedTillNow = metadata.volumeOrdered,
            volumeOrderedRefreshedTill = metadata.volumeOrderedRefreshedTill?.toString() ?: "",
            volumeOrderedRefreshedTillInDate = contractAlertsHelper.convertEpochToReadableFormat(
                metadata.volumeOrderedRefreshedTill
            ) ?: ""
        )
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class VolumeCommitmentInfo(
        val buyerOrgId: String,
        val catalogEntityId: String,
        val city: String?,
        val volumeCommitted: Long,
        val volumeOrderedTillNow: Long,
        val volumeOrderedRefreshedTill: String?,
        val expectedVolumeTillNow: Long,
        val expectedVolumePercentage: Double? = null
    )
}
