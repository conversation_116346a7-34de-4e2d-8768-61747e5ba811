package com.udaan.pricing.jobs.automation

import com.google.inject.Guice
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.instrumentation.JobScope
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.core.managers.TradingPortfolioItemManager
import com.udaan.pricing.core.helpers.PricingNetworkHelper
import javax.inject.Inject
import kotlin.system.exitProcess

class DefaultPortfolioTagger @Inject constructor(
    private val pricingNetworkHelper: PricingNetworkHelper,
    private val tradingPortfolioItemManager: TradingPortfolioItemManager,
    private val gidCityMappingHelper: GidCityMappingHelper
) {
    companion object {
        private val logger by logger()
        private val applicableCities = listOf("bangalore", "hyderabad", "chennai")
    }

    /**
     * Method calls appropriate funs from managers to assign default portfolios.
     */
    private suspend fun assignDefaultTradingPortfolioIfExistingTaggingIsNotDefault(
        groupId: String,
        warehouseId: String
    ) {
        // assigning default trading portfolio
        tradingPortfolioItemManager.assignDefaultTradingPortfolioIfExistingTaggingIsNotDefault(
            groupId = groupId,
            warehouseId = warehouseId,
            createdBy = "autoTaggerJob"
        )
    }

    /**
     *  This method purpose is to tag GID/WH to default trading strategy every day at 3 PM.
     *
     *  Data is read from a probe, which gives the selections(GIDs) for that warehouse.
     *  for each selection/city, we get warehouses mapped to the city and run tagging method for each WHID/GID combination.
     */
    suspend fun process() {
        val data = gidCityMappingHelper.getDataForGroupIdTagging()
        logger.info("Fetched data of size ${data.size}")

        data.filter {
            applicableCities.contains(it.sellerOrgCity.lowercase())
        }.groupBy { it.sellerOrgCity }.entries.map { cityLevelMap ->
            try {
                val city = cityLevelMap.key
                val warehouses = pricingNetworkHelper.fetchWarehousesForAnchorCity(city)
                logger.info("Fetched {} for city {}", warehouses, city)
                val groupIds = cityLevelMap.value.map { it.productGroupId }.distinct()
                groupIds.parallelMap { groupId ->
                    warehouses.parallelMap { warehouseId ->
                        try {
                            assignDefaultTradingPortfolioIfExistingTaggingIsNotDefault(
                                groupId = groupId,
                                warehouseId = warehouseId
                            )
                        } catch (e: Exception) {
                            logger.error("Failed to tag default portfolio for $groupId, $warehouseId with error ${e.message}")
                            e.printStackTrace()
                        }
                    }
                }
            } catch (e: Exception) {
                logger.error("Failed to tag default portfolio for ${cityLevelMap.key} with error ${e.message}")
                // @todo - add alerts for exceptions
            }
        }
    }
}

class DefaultPortfolioTaggerJobRunner {
    companion object {
        private val injector = Guice.createInjector(PricingCoreModule())
        private val defaultPortfolioTagger = injector.getInstance(DefaultPortfolioTagger::class.java)

        @JvmStatic
        fun main(args: Array<String>) {
            JobScope.runBlocking {
                try {
                    defaultPortfolioTagger.process()
                    exitProcess(0)
                } catch (e: Exception) {
                    e.printStackTrace()
                    exitProcess(1)
                }
            }
        }
    }
}


