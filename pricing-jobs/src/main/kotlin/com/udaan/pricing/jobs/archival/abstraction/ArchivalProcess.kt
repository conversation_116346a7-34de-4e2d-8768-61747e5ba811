package com.udaan.pricing.jobs.archival.abstraction

import com.udaan.firstparty.dataplatform.DataPlatformUtil
import com.udaan.pricing.jobs.archival.models.ArchiveCountOverTime

interface ArchivalProcess<T> {

    fun getTtl(): Long

    fun fetchProbeId(): String

    /**
     * Breakdowns the time range based on this step,
     * this helps in fetching data batch wise in-case the rowcount is higher.
     */
    fun getTimeStepperInSeconds(): Long

    suspend fun fetchDataCountFromSource(fromTs: Long, toTs: Long): Long

    suspend fun fetchDataFromSource(fromTs: Long, toTs: Long): Collection<T>

    suspend fun onSourceAndArchivalCountMismatch(sourceCount: Long, archivalCount: Long, item: ArchiveCountOverTime)

    suspend fun deleteItems(items: Collection<T>)

    /**
     * This is standard template to archive data, based on source and archival count between the time range.
     */
    suspend fun process() {
        val probeId = fetchProbeId()
        val probeData = fetchProbeData(probeId)
        probeData.map {
            val sourceCount = fetchDataCountFromSource(fromTs = it.minTs, toTs = it.maxTs)
            val archivalCount = it.rowCount
            if (sourceCount == archivalCount) {
                if (sourceCount < 4000) {
                    deleteDataInSource(fromTs = it.minTs, toTs = it.maxTs)
                } else {
                    deleteDataInBatchesFromSource(fromTs = it.minTs, toTs = it.maxTs)
                }
            } else {
                onSourceAndArchivalCountMismatch(sourceCount, archivalCount, it)
            }
        }
    }


    /**
     * Fetches probe data then after applies
     */
    private suspend fun fetchProbeData(probeId: String): Collection<ArchiveCountOverTime> {
        val data = DataPlatformUtil.getProbeForProbe<ArchiveCountOverTime>(probeId).asSequence().toList()
        val ttl = getTtl()
        return data.filter {
            it.maxTs < ttl
        }
    }

    private suspend fun deleteDataInSource(fromTs: Long, toTs: Long) {
        val data = fetchDataFromSource(fromTs = fromTs, toTs = toTs)
        data.chunked(1000).map { chunkedData ->
            deleteItems(chunkedData)
        }
    }

    private suspend fun deleteDataInBatchesFromSource(fromTs: Long, toTs: Long) {
        val timeStepper = getTimeStepperInSeconds()
        (fromTs + timeStepper..toTs step timeStepper).forEach { currentTs ->
            val data = fetchDataFromSource(
                fromTs = currentTs - timeStepper,
                toTs = if (currentTs + timeStepper > toTs) toTs else currentTs
            )
            data.chunked(1000).forEach { chunkedData ->
                deleteItems(chunkedData)
            }
        }
    }
}