package com.udaan.pricing.jobs

import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.databind.MappingIterator
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.dataformat.csv.CsvMapper
import com.fasterxml.jackson.dataformat.csv.CsvSchema
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.google.inject.Guice
import com.google.inject.Inject
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.instrumentation.JobScope
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.core.PricingCoreModule
import com.udaan.pricing.core.models.SlabPrice
import com.udaan.pricing.core.helpers.BuyerTagHelper
import com.udaan.pricing.core.helpers.StrategyExecutorHelper
import com.udaan.pricing.core.helpers.SellerOrgCityHelper
import com.udaan.pricing.core.svcinterfaces.DpServiceInterface
import com.udaan.pricing.core.svcinterfaces.FpCatalogSvcInterface
import com.udaan.pricing.core.svcinterfaces.VerticalSvcInterface
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.core.helpers.DataPlatformHelper
import com.udaan.pricing.core.helpers.PricingNetworkHelper
import com.udaan.pricing.core.managers.PortfolioPlanManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.InputStream
import java.math.BigDecimal
import kotlin.system.exitProcess

class ListingsSscPriceSyncJob @Inject constructor(
    private val dataPlatformHelper: DataPlatformHelper,
    private val fpCatalogSvcInterface: FpCatalogSvcInterface,
    private val sellerOrgCityHelper: SellerOrgCityHelper,
    private val pricingNetworkHelper: PricingNetworkHelper,
    private val strategyExecutorHelper: StrategyExecutorHelper,
    private val verticalSvcInterface: VerticalSvcInterface,
    private val objectMapper: ObjectMapper,
    private val portfolioPlanManager: PortfolioPlanManager,
    private val buyerTagHelper: BuyerTagHelper
) {

    companion object {
        private const val ACTIVE_PORTFOLIO_ITEM_PROBE = "glyq3i"
        private val csvMapper: CsvMapper = CsvMapper().apply {
            registerKotlinModule()
            this.disable(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY)
        }

        private val schema: CsvSchema = CsvSchema.emptySchema().withHeader().withArrayElementSeparator(";")
        private val logger by logger()
        private val injector = Guice.createInjector(PricingCoreModule())

        @JvmStatic
        fun main(args: Array<String>) {
            val listingsSscPriceSyncJob = injector.getInstance(ListingsSscPriceSyncJob::class.java)
            try {
                logger.info("********************** Job STARTED **********************")
                JobScope.runBlocking {
                    listingsSscPriceSyncJob.process()
                }
                logger.info("********************** Job Completed **********************")
            } catch (e: Throwable) {
                e.printStackTrace()
                logger.error("********************** Job FAILED **********************", e)
                exitProcess(1)
            } finally {
                logger.error("********************** Process End **********************")
                exitProcess(0)
            }
        }
    }

    suspend fun process() {
        val activePortfolioItemsFromDp = getDataFromDataPlatform<PortfolioItemDpData>()

        activePortfolioItemsFromDp.map { portfolioItemDpData ->
            try {
                val listingMarketplaceMappings = fpCatalogSvcInterface.getListingMarketplaceMappingsForGid(portfolioItemDpData.catalogEntity)
                val filteredListingMappings = filterMappingsAsPerPortfolioEntityLocation(
                    listingMarketPlaceMappings = listingMarketplaceMappings,
                    locationType = portfolioItemDpData.locationType,
                    locationValue = portfolioItemDpData.locationValue
                )

                val buyerCohortFromPlans = portfolioPlanManager.getAllPortfolioPlans(
                    portfolioId = portfolioItemDpData.portfolioId
                ).mapNotNull { it.buyerCohort }
                val buyerCohortsDetails = buyerTagHelper.getBuyerDetailsForBuyerCohorts(buyerCohortFromPlans)

                filteredListingMappings.parallelMap {
                    buyerCohortsDetails.map { buyerCohortDetails ->
                        try {
                            val sscPrice = strategyExecutorHelper.getPrice(
                                listingId = it.listingId,
                                salesUnitId = it.salesUnitId,
                                catalogEntity = portfolioItemDpData.catalogEntity,
                                locationsSortedByPriority = listOf(
                                    Location(
                                        locationType = LocationType.valueOf(portfolioItemDpData.locationType.name),
                                        locationValue = portfolioItemDpData.locationValue
                                    )
                                ),
                                allBuyerCohorts = listOf(buyerCohortDetails),
                                verticalCategory = verticalSvcInterface.getVerticalCategoryForLid(it.listingId),
                                mappedFpProductDetails = fpCatalogSvcInterface.getProductForListingId(it.listingId),
                                buyerOrgUnit = null,     // no buyer context present in this flow
                                mappedBenchmarkListingGuardrailPriceDeferred = null
                            )

                            if (sscPrice == null) {
                                logger.error(
                                    "Null ssc price for {} {} and {}",
                                    it.listingId,
                                    portfolioItemDpData.locationValue,
                                    buyerCohortDetails
                                )
                            } else {
                                val convertedSscPrice = SscSlabPriceForListing(
                                    listingId = sscPrice.listingId,
                                    salesUnitId = sscPrice.saleUnitId,
                                    prices = sscPrice.prices.map {
                                        SlabPrice(
                                            minQty = it.minQty,
                                            maxQty = it.maxQty,
                                            priceInPaise = BigDecimal(it.priceInPaisa.basicPrice?.onCODBasePrice ?: 0L)
                                        )
                                    },
                                    sscMetadata = sscPrice.sscMetadata
                                )
                                logger.info(
                                    "Price for listing {} catalogEntity {} location {} and cohort {} is {}",
                                    it.listingId,
                                    portfolioItemDpData.catalogEntity,
                                    portfolioItemDpData.locationValue,
                                    buyerCohortDetails,
                                    convertedSscPrice
                                )
                                dataPlatformHelper.trackEvent(
                                    eventData = objectMapper.convertValue(convertedSscPrice, Map::class.java) as Map<String, Any>,
                                    eventName = DataPlatformHelper.TrackEventName.LISTING_SSC_PRICE_METADATA_AUDIT,
                                    referenceId1 = it.listingId,
                                    referenceId2 = "${portfolioItemDpData.catalogEntity}::${portfolioItemDpData.locationValue}"
                                )
                            }
                        } catch (ex: Exception) {
                            logger.error(
                                "Got exception - {} while fetching ssc price for {} {} and {}",
                                ex.message,
                                it.listingId,
                                portfolioItemDpData.locationValue,
                                buyerCohortDetails
                            )
                        }
                    }
                }
            } catch (ex: Exception) {
                logger.error(
                    "Got exception {} while fetching listings or plans for entity {}",
                    ex.message,
                    portfolioItemDpData
                )
            }
        }


    }

    private suspend fun filterMappingsAsPerPortfolioEntityLocation(
        listingMarketPlaceMappings: List<FpCatalogSvcInterface.ListingMarketPlaceMapping>,
        locationType: LocationType,
        locationValue: String
    ): List<FpCatalogSvcInterface.ListingMarketPlaceMapping> {
        return when (locationType) {
            LocationType.CENTRAL -> listingMarketPlaceMappings
            LocationType.CITY -> {
                listingMarketPlaceMappings.filter {
                    val sellerOrgCity = sellerOrgCityHelper.getCityForSellerOrgId(it.sellerOrgId)
                    sellerOrgCity.equals(locationValue, ignoreCase = true)
                }
            }
            LocationType.CLUSTER, LocationType.WAREHOUSE -> {
                val anchorCity = try {
                    pricingNetworkHelper.getAnchorCityForLocation(locationValue)
                } catch (ex: Exception) {
                    logger.error("Exception in fetching sellerOrgCity / anchor city for {}, Exception {}", locationValue, ex.message)
                    throw ex
                }

                listingMarketPlaceMappings.filter {
                    val sellerOrgCity = sellerOrgCityHelper.getCityForSellerOrgId(it.sellerOrgId)
                    sellerOrgCity.equals(anchorCity, ignoreCase = true)
                }
            }
        }.distinct()
    }

    private suspend inline fun <reified R> getDataFromDataPlatform(): List<R> {
        val inputStream = dataPlatformHelper.getProbe(ACTIVE_PORTFOLIO_ITEM_PROBE)
        return withContext(Dispatchers.IO) {
            inputStream.readCSV<R>().readAll()
        }
    }

    private inline fun <reified R> InputStream.readCSV(): MappingIterator<R> {
        return csvMapper.readerFor(R::class.java)
            .with(schema)
            .readValues<R>(this)
    }

    data class PortfolioItemDpData(
        val catalogEntity: String,
        val locationType: LocationType,
        val locationValue: String,
        val portfolioId: String,
        val referenceId: String
    )

    data class SscSlabPriceForListing(
        val listingId: String,
        val salesUnitId: String,
        val prices: List<SlabPrice>,
        val sscMetadata: Map<String, String>
    )
}
