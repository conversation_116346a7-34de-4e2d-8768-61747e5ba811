package com.udaan.pricing.jobs.keda

import com.google.inject.Guice
import com.udaan.instrumentation.JobScope
import com.udaan.pricing.job.AsyncJobEvent
import com.udaan.pricing.jobs.PricingJobModule
import com.udaan.pricing.jobs.keda.handlers.AsyncJobHandlerFactory
import kotlin.system.exitProcess

/**
 * This is sample method to run the job processor locally.
 */
fun main() {
    JobScope.runBlocking {
        System.setProperty("udaan.env", "prod")
        val injector = Guice.createInjector(PricingJobModule())
        val asyncJobHandlerFactory = injector.getInstance(AsyncJobHandlerFactory::class.java)
        asyncJobHandlerFactory.processJob(AsyncJobEvent("JOBE6YA7Y7XBKJTMGFE1YHA"))
        exitProcess(0)
    }
}
