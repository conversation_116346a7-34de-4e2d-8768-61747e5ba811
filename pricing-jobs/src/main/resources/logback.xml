<!--
    This is NOT for configuring log-levels for Dropwizard.
    Dropwizard will reset all of this and configure logging based on server-config.yml values.
    This configuration is only for CronJobs.
-->
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd'T'HH:mm:ss.SSS'Z', UTC} [%X{TRACE-ID}] [%level] [%thread] [%logger] %msg %n</pattern>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>

    <logger name="com.udaan.pricing.jobs" level="INFO"/>
</configuration>
