user-service:
  baseUrl: "http://user-service"
  httpClientConfig:
    requestTimeout: 60000

catalog-readpath-service:
  baseUrl: "http://catalog-readpath-service"
  httpClientConfig:
    requestTimeout: 60000

trading-service:
  baseUrl: "http://trading-service"
  httpClientConfig:
    requestTimeout: 10000

vertical:
  baseUrl: "http://vertical-service"
  httpClientConfig:
    connectTimeout: 1000
    readTimeout: 5000
    requestTimeout: 5000
    threadPoolName: AsyncHttpClient-vertical


first-party-catalog-service:
  baseUrl: "http://first-party-catalog-service"
  httpClientConfig:
    requestTimeout: 60000

vendor-management-service:
  baseUrl: "http://vendor-management-service"
  httpClientConfig:
    requestTimeout: 60000

dataplatform-service:
  baseUrl: "http://data-platform-file-service"
  httpClientConfig:
    requestTimeout: 300000

pricing-service:
  baseUrl: "http://pricing-service"
  httpClientConfig:
    requestTimeout: 60000

orchestrator-service:
  baseUrl: "http://orchestrator-service"
  httpClientConfig:
    requestTimeout: 60000

promotions-service:
  baseUrl: "http://promotions-service"
  httpClientConfig:
    requestTimeout: 5000

first-party-procurement-service:
  baseUrl: "http://first-party-procurement-service"
  httpClientConfig:
    requestTimeout: 60000

fulfilment-catalog-legacy:
  baseUrl: "http://fulfilment-catalog-legacy-service"
  httpClientConfig:
    requestTimeout: 60000

first-party-service:
  baseUrl: "http://first-party-service"
  httpClientConfig:
    requestTimeout: 60000

config-service:
  baseUrl: "http://config-service"
  httpClientConfig:
    requestTimeout: 5000

tracking-service:
  baseUrl: "http://udaan-tracking-service"
  httpClientConfig:
    requestTimeout: 60000

planning-service:
  baseUrl: "http://planning-service"
  httpClientConfig:
    requestTimeout: 60000

sc-network-service:
  baseUrl: "http://sc-network-service"
  httpClientConfig:
    requestTimeout: 60000

pricing-signals-v2-service:
  baseUrl: "http://pricing-signals-v2-service"
  httpClientConfig:
    requestTimeout: 60000

pricing-network-service:
  baseUrl: "http://pricing-network-service"
  httpClientConfig:
    requestTimeout: 10000

prometheus-pushgateway:
  baseUrl: "prometheus-pushgateway"
  httpClientConfig:
    requestTimeout: 60000

server:
  rootPath: /
  applicationConnectors:
    - type: http
      port: 7000
  adminConnectors:
    - type: http
      port: 7001

dcos:
  cpus: 2 # Per instance of this service, number of cpus needed (e.g. 0.5, or 1.5)
  mem: 2048 # Memory allocated to container in MBs
  servicePort: 17000  # Service port as found on https://sites.google.com/a/udaan.com/tech/services
  instancesDev: 1 # Number of instances to be spawned during deployment
  instancesProd: 1 # Number of instances to be spawned during deployment
  minimumHealthCapacity: 0
  maximumOverCapacity: 0
  jvmArgs: >-
     -server -Xms1536m -Xmx1536m
     -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=9010
     -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false
  mainClass: com.udaan.pricing.jobs.ShouldNotBeCalled_JustHereToSatisfyPackagingSystem
  healthcheck: default
  env:
    dev:
      cluster: cen0
      hpaSpec:
        maxReplicas: 2
        minReplicas: 1
        targetCPUUtilizationPercentage: 60
    prod:
      cluster: sin0
      hpaSpec:
        maxReplicas: 60
        minReplicas: 30
        targetCPUUtilizationPercentage: 60
    containerSpec:
      readinessProbe:
        httpGet:
          path: /healthcheck
          port: admin
        initialDelaySeconds: 10
        periodSeconds: 5
        timeoutSeconds: 3
        failureThreshold: 5
