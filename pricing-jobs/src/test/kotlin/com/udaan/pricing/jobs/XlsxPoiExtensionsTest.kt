package com.udaan.pricing.jobs

import com.udaan.pricing.jobs.extensions.value
import io.mockk.every
import io.mockk.mockk
import org.apache.poi.ss.usermodel.*
import org.apache.poi.xssf.usermodel.XSSFCellStyle
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test

class XlsxPoiExtensionsTest {

    @Test
    fun cellValue_stringCell() {
        val cell = mockk<Cell>()
        every { cell.cellType } returns Cell.CELL_TYPE_STRING
        every { cell.stringCellValue } returns "  Test String  "

        assertEquals("Test String", cell.value())
    }

    @Test
    fun cellValue_numericCell() {
        val cell = mockk<Cell>()
        every { cell.cellType } returns Cell.CELL_TYPE_NUMERIC
        every { cell.numericCellValue } returns 12345.0
        val cellStyle = mockk<XSSFCellStyle>()
        every { cell.cellStyle } returns cellStyle
        every { cellStyle.dataFormat } returns 0
        every { cellStyle.dataFormatString } returns "null"

        assertEquals("12345", cell.value())
    }

    @Test
    fun cellValue_formulaCell() {
        val cell = mockk<Cell>()
        every { cell.cellType } returns Cell.CELL_TYPE_FORMULA
        every { cell.numericCellValue } returns 12345.0

        assertEquals("12345", cell.value())
    }

    @Test
    fun cellValue_errorCell() {
        val cell = mockk<Cell>()
        every { cell.cellType } returns Cell.CELL_TYPE_ERROR
        every { cell.errorCellValue } returns FormulaError.VALUE.code

        assertEquals("15", cell.value())
    }

    @Test
    fun cellValue_booleanCell() {
        val cell = mockk<Cell>()
        every { cell.cellType } returns Cell.CELL_TYPE_BOOLEAN
        every { cell.booleanCellValue } returns true

        assertEquals("true", cell.value())
    }

    @Test
    fun cellValue_blankCell() {
        val cell = mockk<Cell>()
        every { cell.cellType } returns Cell.CELL_TYPE_BLANK

        assertNull(cell.value())
    }

    @Test
    fun cellValue_nullOrBlank() {
        val cell = mockk<Cell>()
        every { cell.cellType } returns Cell.CELL_TYPE_STRING
        every { cell.stringCellValue } returns "   "

        assertNull(cell.value())
    }
}
