package com.udaan.pricing.jobs

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import com.udaan.pricing.jobs.keda.fileutils.FileUtils
import com.udaan.pricing.jobs.models.ProcessedDataInfo
import kotlinx.coroutines.runBlocking
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.io.TempDir
import java.io.File
import java.io.FileOutputStream
import java.nio.file.Path

class FileUtilsTest {

    private val objectMapper = ObjectMapper()
    private val fileUtils = FileUtils(objectMapper)

    @Test
    fun readExcelFile_validData() = runBlocking {
        val file = File("src/test/resources/sample_test_file.xlsx")
        val jobId = "test-job-1"

        val result = fileUtils.readExcelFile(jobId, file, 0, 0, 1, TestData::class)

        assertEquals(4, result.size)
        
        // Verify first row
        assertEquals(1, result[0].row)
        assertNull(result[0].parsingExceptionMessage)
        val data1 = result[0].dataRequestDTO
        assertNotNull(data1)
        assertEquals("Data1", data1?.column1)
        assertEquals(12345.0, data1?.column2)
        assertEquals("01/01/2020", data1?.column3)
        assertEquals(true, data1?.column4)
        assertNull(data1?.column5)

        // Verify second row
        assertEquals(2, result[1].row)
        assertNull(result[1].parsingExceptionMessage)
        val data2 = result[1].dataRequestDTO
        assertNotNull(data2)
        assertEquals("Data2", data2?.column1)
        assertEquals(67890.0, data2?.column2)
        assertEquals("02/02/2021", data2?.column3)
        assertEquals(false, data2?.column4)
        assertEquals("Error", data2?.column5)

        // Verify third row
        assertEquals(3, result[2].row)
        assertNull(result[2].parsingExceptionMessage)
        val data3 = result[2].dataRequestDTO
        assertNotNull(data3)
        assertEquals("Data3", data3?.column1)
        assertEquals(54321.0, data3?.column2)
        assertEquals("03/03/2022", data3?.column3)
        assertEquals(true, data3?.column4)
        assertNull(data3?.column5)

        // Verify fourth row
        assertEquals(4, result[3].row)
        assertNull(result[3].parsingExceptionMessage)
        val data4 = result[3].dataRequestDTO
        assertNotNull(data4)
        assertEquals("Data4", data4?.column1)
        assertEquals(98765.0, data4?.column2)
        assertEquals("1680546600000", data4?.column3)
        assertEquals(false, data4?.column4)
        assertNull(data4?.column5)
    }

    @Test
    fun readExcelFile_emptyFile() = runBlocking {
        val file = File("src/test/resources/empty_test_file.xlsx")
        val jobId = "test-job-2"

        val result = fileUtils.readExcelFile(jobId, file, 0, 0, 1, TestData::class)

        assertTrue(result.isEmpty())
    }

    data class TestData(
        @JsonProperty("column1") val column1: String?,
        @JsonProperty("column2") val column2: Double?,
        @JsonProperty("column3")  val column3: String?,
        @JsonProperty("column4") val column4: Boolean?,
        @JsonProperty("column5") val column5: String?
    )

    data class TestProcessingOutput(
        val outputField1: String?,
        val outputField2: Int?,
        val status: String?
    )

    @Test
    fun updateExcelWithProcessedData_validData(@TempDir tempDir: Path) = runBlocking<Unit> {
        // Create a test Excel file
        val inputFile = createTestExcelFile(tempDir, "test_input.xlsx")
        val jobId = "test-job-update"

        val processedDataList = listOf(
            ProcessedDataInfo(
                row = 1,
                parsingExceptionMessage = null,
                processingExceptionMessage = null,
                processingOutput = TestProcessingOutput("Success1", 100, "COMPLETED")
            ),
            ProcessedDataInfo(
                row = 2,
                parsingExceptionMessage = "Parse error",
                processingExceptionMessage = null,
                processingOutput = TestProcessingOutput("Success2", 200, "COMPLETED")
            )
        )

        val outputFile = fileUtils.updateExcelWithProcessedData<TestProcessingOutput>(jobId, inputFile, processedDataList)

        // Verify output file exists and input file is deleted
        assertTrue(outputFile.exists())
        assertEquals("${jobId}_output.xlsx", outputFile.name)
        assertFalse(inputFile.exists())

        // Verify content of output file
        val workbook = XSSFWorkbook(outputFile.inputStream())
        val sheet = workbook.getSheetAt(0)
        val headerRow = sheet.getRow(0)

        // Check headers (original + result columns)
        assertEquals("column1", headerRow.getCell(0).stringCellValue)
        assertEquals("column2", headerRow.getCell(1).stringCellValue)
        assertEquals("Parse Exception Message", headerRow.getCell(2).stringCellValue)
        assertEquals("Process Exception Message", headerRow.getCell(3).stringCellValue)
        assertEquals("outputField1", headerRow.getCell(4).stringCellValue)
        assertEquals("outputField2", headerRow.getCell(5).stringCellValue)
        assertEquals("status", headerRow.getCell(6).stringCellValue)

        // Check first data row
        val row1 = sheet.getRow(1)
        assertEquals("Data1", row1.getCell(0).stringCellValue)
        assertEquals(123.0, row1.getCell(1).numericCellValue)
        assertEquals("N/A", row1.getCell(2).stringCellValue) // No parsing exception
        assertEquals("N/A", row1.getCell(3).stringCellValue) // No processing exception
        assertEquals("Success1", row1.getCell(4).stringCellValue)
        assertEquals("100", row1.getCell(5).stringCellValue) // Output fields are stored as strings
        assertEquals("COMPLETED", row1.getCell(6).stringCellValue)

        // Check second data row
        val row2 = sheet.getRow(2)
        assertEquals("Data2", row2.getCell(0).stringCellValue)
        assertEquals(456.0, row2.getCell(1).numericCellValue)
        assertEquals("Parse error", row2.getCell(2).stringCellValue)
        assertEquals("N/A", row2.getCell(3).stringCellValue)
        assertEquals("Success2", row2.getCell(4).stringCellValue)
        assertEquals("200", row2.getCell(5).stringCellValue) // Output fields are stored as strings
        assertEquals("COMPLETED", row2.getCell(6).stringCellValue)

        workbook.close()
        outputFile.delete()
    }

    @Test
    fun updateExcelWithProcessedData_withExceptions(@TempDir tempDir: Path) = runBlocking<Unit> {
        val inputFile = createTestExcelFile(tempDir, "test_exceptions.xlsx")
        val jobId = "test-job-exceptions"

        val processedDataList = listOf(
            ProcessedDataInfo(
                row = 1,
                parsingExceptionMessage = "Invalid format",
                processingExceptionMessage = "Processing failed",
                processingOutput = null
            )
        )

        val outputFile = fileUtils.updateExcelWithProcessedData(jobId, inputFile, processedDataList)

        val workbook = XSSFWorkbook(outputFile.inputStream())
        val sheet = workbook.getSheetAt(0)
        val row1 = sheet.getRow(1)

        assertEquals("Invalid format", row1.getCell(2).stringCellValue)
        assertEquals("Processing failed", row1.getCell(3).stringCellValue)
        assertEquals("", row1.getCell(4)?.stringCellValue ?: "") // null processing output
        assertEquals("", row1.getCell(5)?.stringCellValue ?: "")
        assertEquals("", row1.getCell(6)?.stringCellValue ?: "")

        workbook.close()
        outputFile.delete()
    }

    @Test
    fun updateExcelWithProcessedData_emptyList(@TempDir tempDir: Path) = runBlocking<Unit> {
        val inputFile = createTestExcelFile(tempDir, "test_empty.xlsx")
        val jobId = "test-job-empty"

        val outputFile = fileUtils.updateExcelWithProcessedData<TestProcessingOutput>(jobId, inputFile, emptyList<ProcessedDataInfo<TestProcessingOutput>>())

        // File should still be processed and headers added
        assertTrue(outputFile.exists())
        val workbook = XSSFWorkbook(outputFile.inputStream())
        val sheet = workbook.getSheetAt(0)
        val headerRow = sheet.getRow(0)

        // Check that result headers were added
        assertEquals("Parse Exception Message", headerRow.getCell(2).stringCellValue)
        assertEquals("Process Exception Message", headerRow.getCell(3).stringCellValue)

        workbook.close()
        outputFile.delete()
    }

    @Test
    fun updateExcelWithProcessedData_multipleRows(@TempDir tempDir: Path) = runBlocking<Unit> {
        val inputFile = createLargerTestExcelFile(tempDir, "test_multiple.xlsx")
        val jobId = "test-job-multiple"

        val processedDataList = listOf(
            ProcessedDataInfo(
                row = 1,
                parsingExceptionMessage = null,
                processingExceptionMessage = null,
                processingOutput = TestProcessingOutput("Result1", 1, "SUCCESS")
            ),
            ProcessedDataInfo(
                row = 2,
                parsingExceptionMessage = null,
                processingExceptionMessage = "Failed processing",
                processingOutput = TestProcessingOutput("Result2", 2, "FAILED")
            ),
            ProcessedDataInfo(
                row = 3,
                parsingExceptionMessage = "Parse failed",
                processingExceptionMessage = null,
                processingOutput = TestProcessingOutput("Result3", 3, "FAILED")
            )
        )

        val outputFile = fileUtils.updateExcelWithProcessedData(jobId, inputFile, processedDataList)

        val workbook = XSSFWorkbook(outputFile.inputStream())
        val sheet = workbook.getSheetAt(0)

        // Verify all rows were updated correctly
        assertEquals(3, sheet.lastRowNum) // Header + 3 data rows (0-based indexing)

        // Check row 1
        val row1 = sheet.getRow(1)
        assertEquals("N/A", row1.getCell(2).stringCellValue)
        assertEquals("N/A", row1.getCell(3).stringCellValue)
        assertEquals("Result1", row1.getCell(4).stringCellValue)

        // Check row 2
        val row2 = sheet.getRow(2)
        assertEquals("N/A", row2.getCell(2).stringCellValue)
        assertEquals("Failed processing", row2.getCell(3).stringCellValue)
        assertEquals("Result2", row2.getCell(4).stringCellValue)

        // Check row 3
        val row3 = sheet.getRow(3)
        assertEquals("Parse failed", row3.getCell(2).stringCellValue)
        assertEquals("N/A", row3.getCell(3).stringCellValue)
        assertEquals("Result3", row3.getCell(4).stringCellValue)

        workbook.close()
        outputFile.delete()
    }

    private fun createTestExcelFile(tempDir: Path, fileName: String): File {
        val file = tempDir.resolve(fileName).toFile()
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("Sheet1")

        // Create header row
        val headerRow = sheet.createRow(0)
        headerRow.createCell(0).setCellValue("column1")
        headerRow.createCell(1).setCellValue("column2")

        // Create data rows
        val row1 = sheet.createRow(1)
        row1.createCell(0).setCellValue("Data1")
        row1.createCell(1).setCellValue(123.0)

        val row2 = sheet.createRow(2)
        row2.createCell(0).setCellValue("Data2")
        row2.createCell(1).setCellValue(456.0)

        FileOutputStream(file).use { workbook.write(it) }
        workbook.close()
        return file
    }

    private fun createLargerTestExcelFile(tempDir: Path, fileName: String): File {
        val file = tempDir.resolve(fileName).toFile()
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("Sheet1")

        // Create header row
        val headerRow = sheet.createRow(0)
        headerRow.createCell(0).setCellValue("column1")
        headerRow.createCell(1).setCellValue("column2")

        // Create multiple data rows
        for (i in 1..3) {
            val row = sheet.createRow(i)
            row.createCell(0).setCellValue("Data$i")
            row.createCell(1).setCellValue((i * 100).toDouble())
        }

        FileOutputStream(file).use { workbook.write(it) }
        workbook.close()
        return file
    }
}
