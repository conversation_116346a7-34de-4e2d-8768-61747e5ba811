<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.udaan</groupId>
        <artifactId>udaan-parent</artifactId>
        <version>[3.0,4.0)</version>
        <relativePath/>
    </parent>

    <groupId>com.udaan.pricing</groupId>
    <artifactId>pricing-model</artifactId>
    <version>2.0-SNAPSHOT</version>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <name>Udaan Pricing Models</name>

    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
        </dependency>
        <dependency>
            <groupId>com.udaan.incentives</groupId>
            <artifactId>promotions-dto</artifactId>
        </dependency>

        <dependency>
            <groupId>com.udaan.fulfilment</groupId>
            <artifactId>api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.udaan.order-mgt</groupId>
                    <artifactId>order-mgt-model</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.udaan.warehouse</groupId>
                    <artifactId>misc</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.udaan.warehouse</groupId>
                    <artifactId>warehouse-models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.udaan.logistics</groupId>
                    <artifactId>models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.udaan.scnetwork</groupId>
                    <artifactId>sc-network-models</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <!-- Testing -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test-junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>kotlin-maven-plugin</artifactId>
                <groupId>org.jetbrains.kotlin</groupId>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <source>src/main/java</source>
                                <source>src/main/kotlin</source>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <phase>process-test-sources</phase>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <source>src/test/java</source>
                                <source>target/generated-test-sources</source>
                            </sourceDirs>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
