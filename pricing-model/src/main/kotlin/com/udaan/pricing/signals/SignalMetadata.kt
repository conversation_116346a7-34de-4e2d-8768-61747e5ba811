package com.udaan.pricing.signals

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.udaan.pricing.commons.CompQuantityType
import com.udaan.pricing.commons.Ladder

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes(
    JsonSubTypes.Type(value = GenericMetadata::class),
    JsonSubTypes.Type(value = CompSignalMetadata::class)
)
sealed class SignalMetadata

@JsonIgnoreProperties(ignoreUnknown = true)
data class GenericMetadata(
    val metadataMap: Map<String, String>
): SignalMetadata()

@JsonIgnoreProperties(ignoreUnknown = true)
data class CompSignalMetadata @JvmOverloads constructor(
    val mrpInPaisa: Long?,
    val sellingPriceLadder: List<Ladder>,
    val benchmarkRefId: String,
    val quantityPerUnit: Double? = null,
    val quantityType: CompQuantityType? = null,
    val weightPerPcGrams: Double? = null
): SignalMetadata() {
    fun copy(
        mrpInPaisa: Long? = this.mrpInPaisa,
        sellingPriceLadder: List<Ladder> = this.sellingPriceLadder,
        benchmarkRefId: String = this.benchmarkRefId
    ) = CompSignalMetadata(
        mrpInPaisa = mrpInPaisa,
        sellingPriceLadder = sellingPriceLadder,
        benchmarkRefId = benchmarkRefId,
        quantityPerUnit = null,
        quantityType = null,
        weightPerPcGrams = null
    )
}
