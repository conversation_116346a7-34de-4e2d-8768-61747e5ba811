package com.udaan.pricing.signals

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.variable.VariableId

@JsonIgnoreProperties(ignoreUnknown = true)
data class SignalEvent(
    val catalogEntity: String,
    val catalogEntityType: CatalogEntityType,
    val variableId: VariableId,
    val referenceId: String,
    val location: Location,
    val createdAt: Long
)

fun Signal.toSignalEvent(): SignalEvent {
    return SignalEvent(
        catalogEntity = this.catalogEntity,
        catalogEntityType = this.catalogEntityType,
        variableId = VariableId.valueOf(this.variableId),
        referenceId = this.referenceId,
        location = this.location,
        createdAt = this.createdAt
    )
}
