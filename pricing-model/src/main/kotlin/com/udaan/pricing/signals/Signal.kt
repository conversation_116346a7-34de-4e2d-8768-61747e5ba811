package com.udaan.pricing.signals

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.GenericValue
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.utils.Utils.generateId

@JsonIgnoreProperties(ignoreUnknown = true)
data class Signal(
    val catalogEntity: String,
    val catalogEntityType: CatalogEntityType,
    val variableId: String,
    val signalData: GenericValue,
    val metadata: SignalMetadata,
    val location: Location,
    val state: SignalState,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val createdBy: String,
    val updatedBy: String,
    val validTill: Long? = null,
    val referenceId: String = generateId("SIG")
) {
    val id = "$catalogEntity:${location.locationValue}:$variableId".uppercase()
    val partitionKey = "$catalogEntity:${location.locationValue}".uppercase()
}
