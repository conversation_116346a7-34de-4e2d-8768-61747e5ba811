package com.udaan.pricing.portfolio

data class PortfolioRequest(
    val name: String,
    val category: PortfolioCategory,
    val geoPricingEnabled: Boolean = false,
    val tradingEnabled: Boolean = false,
    val createdBy: String
)

fun PortfolioRequest.toPortfolio(): Portfolio {
    return Portfolio(
        name = name,
        flags = mapOf(
            PortfolioFlag.TRADING_ENABLED to this.tradingEnabled,
            PortfolioFlag.GEO_PRICE_ENABLED to this.geoPricingEnabled
        ),
        createdBy = createdBy,
        category = category,
        updatedBy = createdBy
    )
}