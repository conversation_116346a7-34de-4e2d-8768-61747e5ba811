package com.udaan.pricing.portfolio

import com.udaan.pricing.utils.Utils

data class Portfolio(
    val id: String = Utils.generateId("PF"),
    val name: String,
    val state: PortfolioState = PortfolioState.ACTIVE,
    val category: PortfolioCategory,
    /**
     * Right now flags are added for migration purpose,
     * we can go around this remove later point of time.
     */
    val flags: Map<PortfolioFlag, Boolean>,
    val createdAt: Long = System.currentTimeMillis(),
    val createdBy: String,
    val updatedBy: String,
    val updatedAt: Long = System.currentTimeMillis()
)


/**
 * THis can be used for access restriction.
 */
enum class PortfolioCategory {
    FMCG,
    STAPLES,
    FRESH,
    MEAT,
    OTHER
}

enum class PortfolioState {
    ACTIVE,
    DELETED
}

enum class PortfolioFlag {
    TRADING_ENABLED,
    GEO_PRICE_ENABLED
}