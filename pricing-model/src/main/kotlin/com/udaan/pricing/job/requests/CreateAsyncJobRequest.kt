package com.udaan.pricing.job.requests

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.pricing.job.AsyncJob
import com.udaan.pricing.job.AsyncJobData
import com.udaan.pricing.job.AsyncJobStatus
import com.udaan.pricing.job.AsyncJobType
import com.udaan.pricing.job.FilePathReference

@JsonIgnoreProperties(ignoreUnknown = true)
data class CreateAsyncJobRequest(
    val type: AsyncJobType,
    val inputFilePath: String?  = null,
    val attributesMap: Map<String, String>,
    val requestedBy: String
) {
    fun toAsyncJob(): AsyncJob {
        return AsyncJob(
            status = AsyncJobStatus.NEW,
            filePathReference = FilePathReference(inputFilePath = inputFilePath),
            type = type,
            jobData = AsyncJobData(attributesMap = attributesMap),
            createdBy = requestedBy,
            updatedBy = requestedBy
        )
    }
}
