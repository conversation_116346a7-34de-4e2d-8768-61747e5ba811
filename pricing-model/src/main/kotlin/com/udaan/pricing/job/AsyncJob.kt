package com.udaan.pricing.job

import com.udaan.pricing.utils.Utils.generateId

data class AsyncJob(
    val id: String = generateId("JOB"),
    val status: AsyncJobStatus,
    val filePathReference: FilePathReference,
    val type: AsyncJobType,
    val jobData: AsyncJobData,
    val remarks: String? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val createdBy: String,
    val updatedBy: String
)


data class FilePathReference(
    val inputFilePath: String? = null,
    val outputFilePath: String? = null
)
