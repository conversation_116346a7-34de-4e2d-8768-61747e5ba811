package com.udaan.pricing

data class MaxPromotion(
    val id: String,
    val listingId: String,
    val orgId: String?,
    val value: String,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val created_by: String = ""
)

data class MaxPromotionReq(
    val listingId: String,
    val orgId: String?,
    val value: String,
    val created_by: String = ""
)