package com.udaan.pricing

data class BuyerCohortsAndLocationsResponse(
    val buyerCohorts: List<String>,
    val locations: List<AvailableLocationsForListingResponse>
)

data class AvailableLocationsForListingResponse(
    val locationId: String,
    val locationType: String,
    val locationName: String
)

data class PriceListingVisibilityForBuyerResponseDto(
    val listingId: String,
    val salesUnitId: String,
    val productId: String,
    val groupId: String?,
    val title: String,
    val status: String,
    val mrp: String,
    val gst: String,
    val orgId: String,
    val orgUnitId: String,
    val pincode: String,
    val prices : List<PriceListingVisibilityResponse>
)

data class ListingDetailsForPricingResponseDto(
    val listingId: String,
    val salesUnitId: String,
    val productId: String,
    val groupId: String?,
    val title: String,
    val status: String,
    val mrp: String,
    val gst: String
)

data class PriceListingVisibilityResponse(
    val strategyRef: String?,
    val sscMetaData: Map<String, String>,
    val prices: List<PriceDetailsForVisibilityResponse>
)

data class PriceDetailsForVisibilityResponse(
    val minQty: Int,
    val maxQty: Int,
    val priceInPaisa: Long,
    val priceRiders: List<PriceRidersForVisibility>
)

data class PriceRidersForVisibility(
    val riderCode: String,
    val bpsInPercentage: Int,
    val riderDetails: Any?
)
