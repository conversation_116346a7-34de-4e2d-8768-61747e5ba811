package com.udaan.pricing.manualprice

import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.PriceInfo
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.utils.Utils.generateId

data class ManualPrice(
    val catalogEntity: String,
    val catalogEntityType: CatalogEntityType,
    val locationType: LocationType,
    val locationValue: String,
    val buyerCohort: String?,
    val priceInfo: PriceInfo,
    val expiryTimeInMillis: Long,
    val state: ManualPriceState,
    val metadata: Map<String, String>,
    val createdBy: String,
    val createdAt: Long,
    val updatedBy: String,
    val updatedAt: Long,
    val referenceId: String = generateId("MP")
) {
    val id = "$catalogEntity:$locationValue:$buyerCohort".uppercase()
}
