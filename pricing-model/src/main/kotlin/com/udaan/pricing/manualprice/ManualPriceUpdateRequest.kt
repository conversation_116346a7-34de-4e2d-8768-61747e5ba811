package com.udaan.pricing.manualprice

import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.PriceInfo
import com.udaan.pricing.commons.location.LocationType

data class ManualPriceUpdateRequest(
    val catalogEntity: String,
    val catalogEntityType: CatalogEntityType,
    val locationType: LocationType,
    val locationValue: String,
    val buyerCohort: String?,
    val priceInfo: PriceInfo,
    val expiryTimeInMillis: Long,
    val jobIdReference: String?,
    val updatedBy: String,
    val updateReason: String,
    val updatedAt: Long = System.currentTimeMillis()
) {
    fun convert(): ManualPrice {
        return ManualPrice(
            catalogEntity = catalogEntity.uppercase(),
            catalogEntityType = catalogEntityType,
            locationType = locationType,
            locationValue = locationValue.uppercase(),
            buyerCohort = buyerCohort?.uppercase(),
            priceInfo = priceInfo,
            expiryTimeInMillis = expiryTimeInMillis,
            state = ManualPriceState.ACTIVE,
            metadata = mapOf("CREATION_REASON" to updateReason) +
                    (jobIdReference?.let { mapOf("CREATION_JOB_ID_REFERENCE" to it) } ?: emptyMap()),
            createdBy = updatedBy,
            createdAt = updatedAt,
            updatedBy = updatedBy,
            updatedAt = updatedAt
        )
    }
}
