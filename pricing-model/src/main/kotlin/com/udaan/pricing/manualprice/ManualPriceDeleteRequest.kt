package com.udaan.pricing.manualprice

import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.location.LocationType

data class ManualPriceDeleteRequest(
    val catalogEntity: String,
    val catalogEntityType: CatalogEntityType,
    val locationType: LocationType,
    val locationValue: String,
    val buyerCohort: String?,
    val jobIdReference: String?,
    val deletedBy: String,
    val deletionReason: String,
    val deletedAt: Long = System.currentTimeMillis()
)
