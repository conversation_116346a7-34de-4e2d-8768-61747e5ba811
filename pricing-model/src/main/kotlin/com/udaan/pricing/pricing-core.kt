package com.udaan.pricing

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.fulfilment.sku.PackagingType


@JsonIgnoreProperties(ignoreUnknown = true)
data class BuyerContext @JvmOverloads constructor(val orgId: String, val orgUnitId: String?, val pincode: String?, val platform: String?=null, val categoryGroupId: String?="")

@JsonIgnoreProperties(ignoreUnknown = true)
data class TransactionContext(val paymentMode: String, val qty: Int)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PriceInPaisa(
        val onCredit: Long,
        val onCOD: Long,
        val onPrepayment: Long,
        val basicPrice: BasicPrice?,
        val priceRiders: List<PriceRiderForListing?>?
) {
    val defaultPrice = onCOD
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class BasicPrice(val onCreditBasePrice: Long?, val onCODBasePrice: Long?, val onPrepaymentBasePrice: Long?)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PriceRiderForListing @JvmOverloads constructor(
        val listingId: String,
        val saleUnitId: String,
        val bpsInPercentage: Int, //percentage in BPS
        val flatVal: Int = 0,
        val isAdditive: Boolean, //Is the BPS to be added or subtracted
        val riderCode: String?,
        val riderDetails: Any?,
        val riderAdditionalDetails: RiderAdditionalDetails? = null,
        val ladderBreakerMoq: Int? = null
)


@JsonIgnoreProperties(ignoreUnknown = true)
data class ContextualPriceRequest(
        val transactionContext: TransactionContext?,
        val buyerContext: BuyerContext?,
        val refId: String? = null,
        val cluster: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class MetaData @JvmOverloads constructor(
        val mrpInPaisa: Long? = null, val lotId: String? = null, val scheme: String? = null,
        val ptrInPaisa: Long? = null, val batchId: String? = null, val expiryDate: String? = null,
        val priceIntentId: String? = null
)

enum class PriceState { ACTIVE, INACTIVE }

@JsonIgnoreProperties(ignoreUnknown = true)
data class BasePrice(
        val id: String,
        val listingId: String,
        val saleUnitId: String,
        val qtyBasedPrice: List<QtyBasedPrice>,
        val createdAt: Long = System.currentTimeMillis(),
        val updatedAt: Long = System.currentTimeMillis(),
        val state: PriceState = PriceState.ACTIVE,
        val orgId: String = "",
        val metaData:MetaData? =null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class LotPrice(
        val id: String,
        val listingId: String,
        val saleUnitId: String,
        val qtyBasedPrice: List<QtyBasedPrice>,
        val createdAt: Long = System.currentTimeMillis(),
        val updatedAt: Long = System.currentTimeMillis(),
        val state: PriceState = PriceState.ACTIVE,
        val orgId: String = "",
        val metaData:MetaData? =null,
        val lotId:String,
        val schemeDiscountBps:Int = 0,
        val minSchemeUnit:Int = 0
)



fun BasePrice.uniqueKey() ="${this.listingId}::${this.saleUnitId}"


enum class EventChangeType {
    CREATE, UPDATE, DELETE
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class PriceChangeEvent(val listingId: String, val saleUnitId: String, val eventChangeType: EventChangeType, val updatedAt: Long = System.currentTimeMillis(),val geoType: String?=null,val geoVal: String?=null)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ListingInfo @JvmOverloads constructor(
        val listingId: String,
        val saleUnitId: String,
        val refId: String?=null,
        val packagingType: PackagingType? = null
) {
        fun copy(
                listingId: String,
                saleUnitId: String,
                refId: String?
        ): ListingInfo = this.copy(
                listingId = listingId,
                saleUnitId = saleUnitId,
                refId = refId,
                packagingType = null
        )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class PriceMultiReq(
        val listingInfo: List<ListingInfo>,
        val contextualPriceRequest: ContextualPriceRequest
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PricingAudit @JvmOverloads constructor(
        val id: String,
        val refId: String,
        val listingId: String,
        val saleUnitId: String,
        val strategyName: String,
        val strategyRef: String?,
        val prices: List<QtyBasedPrice>,
        val metaData: MetaData? = null,
        val sscMetadata: Map<String, String> = emptyMap(),
        val contractReferenceId: String? = null
) {
        fun copy(
                id: String,
                refId: String,
                listingId: String,
                saleUnitId: String,
                strategyName: String,
                strategyRef: String,
                prices: List<QtyBasedPrice>,
                metaData: MetaData?
        ): PricingAudit = this.copy(
                id = id,
                refId = refId,
                listingId = listingId,
                saleUnitId = saleUnitId,
                strategyName = strategyName,
                strategyRef = strategyRef,
                prices = prices,
                metaData = metaData,
                sscMetadata = emptyMap()
        )


        fun copy(
                id: String,
                refId: String,
                listingId: String,
                saleUnitId: String,
                strategyName: String,
                strategyRef: String,
                prices: List<QtyBasedPrice>,
                metaData: MetaData?,
                sscMetadata: Map<String, String>
        ): PricingAudit = this.copy(
                id = id,
                refId = refId,
                listingId = listingId,
                saleUnitId = saleUnitId,
                strategyName = strategyName,
                strategyRef = strategyRef,
                prices = prices,
                metaData = metaData,
                sscMetadata = sscMetadata,
                contractReferenceId = null
        )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class RawInfoListing(
        val listingId: String,
        val saleUnitId: String
)

fun PriceInPaisa.getPriceWithBestPromotions(): BasicPrice {
        val totalPercentage = 10_000
        val priceRider = priceRiders?.firstOrNull { it?.riderCode == RiderCode.PROMOTION.name }
        val maxDiscountBps = priceRider?.riderAdditionalDetails?.let {
                (it as PromotionDetails).maxBps ?: 0
        } ?: 0
        return BasicPrice(
                onCredit.times(totalPercentage - maxDiscountBps).div(totalPercentage),
                onCOD.times(totalPercentage - maxDiscountBps).div(totalPercentage),
                onPrepayment.times(totalPercentage - maxDiscountBps).div(totalPercentage)
        )
}

fun List<QtyBasedPrice>.getPriceWithBestPromotions(): BasicPrice? {
        return map {
                it.priceInPaisa.getPriceWithBestPromotions()
        }.minByOrNull { it.onCODBasePrice ?: 0 } // onCODBasePrice is not nullable in QtyBasedPrice
}

data class BuyerCohortResponse(
        val buyerNumber : String,
        val buyerOrgId : String?,
        val buyerCohort : String?,
        val buyerServingWarehouse : String?,
        val errorMessage : String?
)

data class SSCPriceVisibilityResponse(
        val listingId: String,
        val salesUnitId: String,
        val pricesForLocationsAndCohorts: List<LocationAndCohortPricingResponseForSSCVisibility>
)

data class LocationAndCohortPricingResponseForSSCVisibility(
        val cohortValue: String,
        val locationValue: String,
        val locationType: String,
        val priceForListing: PriceForListing
)