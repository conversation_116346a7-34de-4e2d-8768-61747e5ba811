package com.udaan.pricing.variable.requestreponse

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.pricing.commons.GenericValue
import com.udaan.pricing.signals.SignalMetadata

@JsonIgnoreProperties(ignoreUnknown = true)
data class ResolvedValue @JvmOverloads constructor(
    val success: Boolean,
    val value: GenericValue?,
    val referenceSignalId: String? = null,
    val resolverLogic: ResolverLogic? = null,
    val exception: String? = null,
    val metadata: SignalMetadata? = null,
    val lastRefreshedAt: Long? = null
) {
    fun copy(
        success: Boolean,
        value: GenericValue?,
        referenceSignalId: String? = null,
        resolverLogic: ResolverLogic? = null,
        exception: String? = null,
        metadata: SignalMetadata? = null
    ): ResolvedValue = ResolvedValue(
        success = success,
        value = value,
        referenceSignalId = referenceSignalId,
        resolverLogic = resolverLogic,
        exception = exception,
        metadata = metadata,
        lastRefreshedAt = null
    )
}