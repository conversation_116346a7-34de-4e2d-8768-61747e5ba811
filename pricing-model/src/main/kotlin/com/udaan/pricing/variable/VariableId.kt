package com.udaan.pricing.variable

enum class VariableId {
    /**Trading **/
    GRANARY_INVENTORY_FLAG,
    TRADING_PRICE_WOT_PAISA_UNIT,

    /** FMCG Scheme **/
    DTR_RETAIL_SCHEME,
    GT_RETAIL_SCHEME,
    GT_WS_SCHEME,
    SOURCING_CHANNEL,

    /** Vendor signals **/
    FPJIT_BEST_VENDOR_PRICE_WOT_PAISA_UNIT,
    VSL_PRICE_WOT_PAISA_UNIT,

    /** competition **/
    JUMBOTAIL_COMP_LADDER_PRICE_WT_PAISA_UNIT,
    OFFLINE_COMPETITION_WT_PAISA_UNIT,
    HYPERPURE_COMP_LADDER_PRICE_WT_PAISA_UNIT,
    METRO_COMP_LADDER_PRICE_WT_PAISA_UNIT,
    NINJACART_COMP_LADDER_PRICE_WT_PAISA_UNIT,
    HYPERPURE_COMP_MARKDOWN_BPS,

    /** Comp dynamic variables (derived from metadata) **/
    JUMBOTAIL_COMP_MRP_WT_PAISA_UNIT,
    NINJACART_QUANTITY_PER_UNIT,
    NINJACART_QUANTITY_TYPE,
    NINJACART_WEIGHT_PER_PIECE_GRAMS,
    HYPERPURE_QUANTITY_PER_UNIT,
    HYPERPURE_QUANTITY_TYPE,
    HYPERPURE_WEIGHT_PER_PIECE_GRAMS,
    METRO_COMP_MRP_WT_PAISA_UNIT,

    /** Dynamic variables **/
    CONVERSION_RATE,
    MRP_WT_PAISA_SET,
    GST_BPS,
    CESS_BPS,
    PACKAGING_UNIT_TYPE,
    QUANTITY_PER_UNIT,
    QUANTITY_TYPE,
    VSL_SELECTED_CHANNEL,

    /** Cogs related signals **/
    STAPLES_LPP_WOT_RUPEES_UNIT,
    FMCG_LPP_WOT_RUPEES_UNIT,
    STAPLES_LIP_WOT_PAISA_UNIT,
    @Deprecated("This variable is now deprecated!")
    FMCG_LIP_WOT_RUPEES_SET,
    FMCG_COGS_WOT_PAISA_SET,

    /** Cohort aware variables **/
    SCHEME_MULTIPLIER_BPS,
    SCHEME_RETENTION_CAP_BPS,
    COHORT_ADJUSTMENT_BPS,
    
    /** FMCG Base Pricing Variable **/
    PTR_BPS,
    BEVS_MOP_WT_PAISA_UNIT,
    BEVS_MARGIN_BPS,
    BUY_SELL_MARGIN_BPS,

    /** Range food pricing variable **/
    // TODO: these variables names can change once data is migrated.
    VSL_MARKUP_BPS,
    DISCOUNT_SLABS,
    DISCOUNT_SLABS_MULTIPLIER,
    KP_ABS_MARKUP_WOT_PAISA_UNIT,
    BMT_GID_BPS,
    BMT_MULTIPLIER,
    COR_WT_PAISA_UNIT,
    COR_ABS_MARKUP_WOT_PAISA_UNIT,

    /** Guardrail associated variables */
    LIP_FLOOR_GUARDRAIL_BPS,
    LIP_CEIL_GUARDRAIL_BPS,
    MRP_CEIL_GUARDRAIL_MD_BPS,

    /** Contract Pricing Variable **/
    VOLUMETRIC_LADDER_DISCOUNT_BPS,
    APPLY_MAX_LADDER_COUNT_THRESHOLD,

    /** FRESH and MEAT variables **/
    FRESH_MANUAL_PRICE_WOT_PAISA_UNIT,
    FRESH_COGS_WOT_PAISA_UNIT,
    MEAT_MANUAL_PRICE_WOT_PAISA_UNIT,
    MEAT_COGS_WOT_PAISA_UNIT,

    /** Benchmark listing price variable **/
    BENCHMARK_LISTING_PRICE_WOT_PAISA_SET
}
