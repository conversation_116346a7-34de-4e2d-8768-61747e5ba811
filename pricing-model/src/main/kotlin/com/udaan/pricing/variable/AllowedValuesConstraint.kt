package com.udaan.pricing.variable

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.udaan.pricing.commons.GenericValue

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes(
    JsonSubTypes.Type(value = RangeValuesConstraint::class),
    JsonSubTypes.Type(value = ListValuesConstraint::class)
)
sealed class AllowedValuesConstraint

data class RangeValuesConstraint(
    val minValue: GenericValue,
    val maxValue: GenericValue
) : AllowedValuesConstraint()

data class ListValuesConstraint(
    val value: List<GenericValue>
) : AllowedValuesConstraint()
