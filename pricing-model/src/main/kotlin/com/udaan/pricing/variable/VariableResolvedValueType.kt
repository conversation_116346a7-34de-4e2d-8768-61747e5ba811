package com.udaan.pricing.variable

import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.commons.GenericValue
import com.udaan.pricing.commons.LadderValue
import com.udaan.pricing.commons.StringValue
import kotlin.reflect.KClass

enum class VariableResolvedValueType(val typeClass: KClass<out GenericValue>) {
    BIG_DECIMAL(BigDecimalValue::class),
    STRING(StringValue::class),
    LADDER(LadderValue::class)
}
