package com.udaan.pricing.variable.requestreponse

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class BmtDataResponse(
    val sourcingModel: SourcingModel,
    val baseBmt: Double,
    val bmtMultiplier: Double?,
    val finalBmtBps: Double,
    val baseBmtStaticDataIdForReference: String?,
    val bmtMultiplierStaticDataIdForReference: String?
)
