package com.udaan.pricing.variable.requestreponse

enum class ResolverLogic {
    VALUE_FROM_SIGNAL,
    VALUE_FROM_FALLBACK,
    NO_VALUE_AS_SIGNAL_ABSENT,
    NO_VALUE_AS_SIGNAL_EXPIRED,
    DEFAULT_VALUE_AS_SIGNAL_ABSENT,
    DEFAULT_VALUE_AS_SIGNAL_EXPIRED,
    DEFAULT_VALUE_AS_SIGNAL_ABSENT_OR_EXPIRED,
    DEFAULT_VALUE_AS_EXCEPTION_OCCURRED,
    NO_VALUE_AS_DOWNSTREAM_SVC_ERROR,
    DEFAULT_VALUE_AS_DOWNSTREAM_SVC_ERROR
}
