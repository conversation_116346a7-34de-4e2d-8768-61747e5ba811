package com.udaan.pricing.variable

import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.GenericValue
import com.udaan.pricing.commons.location.LocationType

data class VariableRequest(
    val id: VariableId,
    val resolvedValueType: VariableResolvedValueType,
    val defaultValue: GenericValue? = null,
    val freshnessDurationInMillis: Long? = null,
    val type: VariableType,
    val hierarchies: List<Pair<CatalogEntityType, LocationType>>,
    val allowedValuesConstraint: AllowedValuesConstraint? = null,
    val createdBy: String,
    val createdAt: Long = System.currentTimeMillis()
)

fun VariableRequest.convert(): Variable {
    return Variable(
        id = this.id,
        resolvedValueType = this.resolvedValueType,
        defaultValue = this.defaultValue,
        freshnessDurationInMillis = this.freshnessDurationInMillis,
        type = this.type,
        hierarchies = this.hierarchies,
        allowedValuesConstraint = this.allowedValuesConstraint,
        state = VariableState.ACTIVE,
        createdBy = this.createdBy,
        createdAt = this.createdAt
    )
}
