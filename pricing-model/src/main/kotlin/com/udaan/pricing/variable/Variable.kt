package com.udaan.pricing.variable

import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.GenericValue
import com.udaan.pricing.commons.location.LocationType

data class Variable(
    val id: VariableId,
    val resolvedValueType: VariableResolvedValueType,
    val defaultValue: GenericValue?,
    val freshnessDurationInMillis: Long?,
    val type: VariableType,
    val hierarchies: List<Pair<CatalogEntityType, LocationType>>,
    val allowedValuesConstraint: AllowedValuesConstraint?,
    val state: VariableState,
    val createdBy: String,
    val createdAt: Long
)
