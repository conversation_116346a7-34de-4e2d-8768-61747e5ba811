package com.udaan.pricing.signalcreation

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.udaan.pricing.commons.CatalogEntityType
import com.udaan.pricing.commons.CompQuantityType
import com.udaan.pricing.commons.GenericValue
import com.udaan.pricing.commons.Ladder
import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.variable.VariableId
import java.math.BigDecimal

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes(
    JsonSubTypes.Type(value = AutomatedSourcingInput::class),
    JsonSubTypes.Type(value = CompSignalInput::class),
    JsonSubTypes.Type(value = SchemeSignalInput::class),
    JsonSubTypes.Type(value = PurchaseOrderInput::class),
    JsonSubTypes.Type(value = GenericGidLevelInput::class),
    JsonSubTypes.Type(value = GenericLidLevelInput::class),
    JsonSubTypes.Type(value = VslPriceInput::class),
    JsonSubTypes.Type(value = FpJitVendorPriceInput::class),
    JsonSubTypes.Type(value = CompOfflineSignalInput::class),
    JsonSubTypes.Type(value = StaplesLipSignalInput::class),
    JsonSubTypes.Type(value = GenericVerticalLevelInput::class),
    JsonSubTypes.Type(value = VolumetricSignalInput::class)
)
sealed class RawSignalInput

@JsonIgnoreProperties(ignoreUnknown = true)
data class FpJitVendorPriceInput(
    val groupId: String,
    val warehouseId: String,
    val unitPrice: Double,
    val vendorId: String
): RawSignalInput()

@JsonIgnoreProperties(ignoreUnknown = true)
data class AutomatedSourcingInput(
    val groupId: String,
    val warehouseId: String,
    val typeOfInput: AutomatedSourcingInputType,
    val value: Double,
    val createdBy: String
): RawSignalInput()

enum class AutomatedSourcingInputType {
    PRICE,
    INVENTORY_FLAG
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class VslPriceInput(
    val groupId: String,
    val warehouseId: String,
    val priceInPaisa: Double,
    val selectedChannel: String,
    val listingId: String,
    val vslDocumentId: String
): RawSignalInput()

@JsonIgnoreProperties(ignoreUnknown = true)
data class CompOfflineSignalInput(
    val productGroupId: String,
    val location: Location,
    val benchmarkRefId: String,
    val value: Double,
    val refreshedAt: Long
): RawSignalInput()

@JsonIgnoreProperties(ignoreUnknown = true)
data class CompSignalInput @JvmOverloads constructor(
    val catalogEntityId: String,
    val catalogEntityType: CatalogEntityType,
    val location: Location,
    val competitionName: String,
    val mrpInPaisa: Long?,
    val sellingLadder: List<Ladder>,
    val unitPriceInPaisa: Double? = null,
    val benchmarkRefId: String,
    val refreshedAt: Long,
    val quantityPerUnit: Double? = null,
    val quantityType: CompQuantityType? = null,
    val weightPerPcGrams: Double? = null
): RawSignalInput() {
    fun copy(
        catalogEntityId: String = this.catalogEntityId,
        catalogEntityType: CatalogEntityType = this.catalogEntityType,
        location: Location = this.location,
        competitionName: String = this.competitionName,
        mrpInPaisa: Long? = this.mrpInPaisa,
        sellingLadder: List<Ladder> = this.sellingLadder,
        unitPriceInPaisa: Double? = this.unitPriceInPaisa,
        benchmarkRefId: String = this.benchmarkRefId,
        refreshedAt: Long = this.refreshedAt
    ) = CompSignalInput(
        catalogEntityId = catalogEntityId,
        catalogEntityType = catalogEntityType,
        location = location,
        competitionName = competitionName,
        mrpInPaisa = mrpInPaisa,
        sellingLadder = sellingLadder,
        unitPriceInPaisa = unitPriceInPaisa,
        benchmarkRefId = benchmarkRefId,
        refreshedAt = refreshedAt,
        quantityPerUnit = null,
        quantityType = null,
        weightPerPcGrams = null
    )
}

/**
 * Data class to enable scheme ingestion, till we move the scheme to signals v2 fully
 * Source and schemeChannel are string, because I don't want to add unnecessary models
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class SchemeSignalInput(
    val productGroupId: String,
    val location: Location,
    val ladder: List<Ladder>,
    val source: String,
    val schemeChannel: String,
    val createdAt: Long,
    val createdBy: String,
    val lastRefreshedAt: Long,
    val lastRefreshedBy: String,
    val additionalMetadata: Map<String, String>
): RawSignalInput()

@JsonIgnoreProperties(ignoreUnknown = true)
data class PurchaseOrderInput(
    val orgId: String,
    val orgUnitId: String,
    val purchaseOrderId: String,
    val productId: String,
    val unitPrice: BigDecimal,
    val vendorId: String,
    val vendorUnitId: String
): RawSignalInput()

@JsonIgnoreProperties(ignoreUnknown = true)
data class GenericGidLevelInput(
    val productGroupId: String,
    val variableId: VariableId,
    val data: GenericValue,
    val metadata: Map<String, String>,
    val location: Location,
    val updatedBy: String
): RawSignalInput()

@JsonIgnoreProperties(ignoreUnknown = true)
data class GenericLidLevelInput(
    val listingId: String,
    val variableId: VariableId,
    val data: GenericValue,
    val metadata: Map<String, String>,
    val location: Location,
    val updatedBy: String
): RawSignalInput()

@JsonIgnoreProperties(ignoreUnknown = true)
data class StaplesLipSignalInput(
    val productGroupId: String,
    val valueInPaise: Double,
    val location: Location,
    val lineItemConsumedId: String,
    val irnId: String,
    val purchaseOrderId: String,
    val productId: String,
    val sellerOrgId: String,
    val createdBy: String,
    val updatedBy: String
): RawSignalInput()

@JsonIgnoreProperties(ignoreUnknown = true)
data class GenericVerticalLevelInput(
    val vertical: String,
    val variableId: VariableId,
    val data: GenericValue,
    val metadata: Map<String, String>,
    val location: Location,
    val updatedBy: String
): RawSignalInput()

@JsonIgnoreProperties(ignoreUnknown = true)
data class VolumetricSignalInput(
    val catalogEntityId: String,
    val catalogEntityType: CatalogEntityType,
    val location: Location,
    val ladders: List<Ladder>,
    val updatedBy: String
): RawSignalInput()
