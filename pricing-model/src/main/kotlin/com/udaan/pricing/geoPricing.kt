package com.udaan.pricing

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

enum class GeoPricingType{
    FLAT,
    PERCENTAGE
}

enum class GeoPricingFlavour(val priority: Int) {
    GENERIC(0),
    LZN(1)
}

/**
 * The natural ordering of this enum is used to determine priority. Please do not change!
 */
enum class GeoLocationType(val flavour: GeoPricingFlavour) {
    REGION(GeoPricingFlavour.GENERIC),
    CLUSTER(GeoPricingFlavour.GENERIC),
    WAREHOUSE(GeoPricingFlavour.GENERIC),
    CITY(GeoPricingFlavour.GENERIC),
    STATE(GeoPricingFlavour.GENERIC),
    ZONAL(GeoPricingFlavour.LZN),
    NATIONAL(GeoPricingFlavour.LZN),
    LOCAL(GeoPricingFlavour.LZN),
    NORTH_EAST_AND_MILITARY(GeoPricingFlavour.LZN)
}

enum class GeoPricingState {
    ACTIVE,
    DELETE
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class GeoPricingReq(val id:String? = null,
                         val orgId:String,
                         val vertical:String?,
                         val listingId:String?,
                         val salesUnitId:String?,
                         val geoTypeId:String,
                         val geoType: GeoLocationType,
                         val priceType: GeoPricingType,
                         val priceValue: Long,
                         val state: GeoPricingState,
                         val additive: Boolean)

@JsonIgnoreProperties(ignoreUnknown = true)
data class GeoPricing(val id:String,
                      val orgId:String,
                      val vertical:String?,
                      val listingId:String?,
                      val salesUnitId:String?,
                      val geoTypeId:String,
                      val geoType: GeoLocationType,
                      val priceType: GeoPricingType,
                      val priceValue: Long,
                      val state: GeoPricingState,
                      val additive: Boolean = true)

@JsonIgnoreProperties(ignoreUnknown = true)
data class FetchGeoPricingRequest(
        val orgId: String,
        val listingId: String,
        val vertical: String?,
        val pincode: String,
        val state: List<GeoPricingState>,
        val cluster: List<String>? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class GeoPricingUpdateReq(val id:String,
                               val orgId:String,
                               val priceType:GeoPricingType,
                               val priceValue: Long,
                               val geoTypeId:String,
                               val state: GeoPricingState)

@JsonIgnoreProperties(ignoreUnknown = true)
data class GeoPricingCreateReq(val orgId:String,
                               val geoTypeId:String,
                               val geoType: GeoLocationType,
                               val priceType:GeoPricingType,
                               val priceValue: Long,
                               val vertical:String? = null,
                               val listingId:String? = null,
                               val salesUnitId:String? = null,
                               val additive: Boolean)

@JsonIgnoreProperties(ignoreUnknown = true)
data class GeoPricingUpsertReq(val id: String? = null,
                               val orgId:String,
                               val geoTypeId:String,
                               val geoType: GeoLocationType,
                               val priceType:GeoPricingType,
                               val priceValue: Long,
                               val vertical:String? = null,
                               val listingId:String? = null,
                               val salesUnitId:String? = null,
                               val additive: Boolean,
                               val state: GeoPricingState)


@JsonIgnoreProperties(ignoreUnknown = true)
data class QueryGeoPricingReq(
    val orgId: String,
    val vertical: String? = null,
    val listingId: String? = null,
    val cascadeDown: Boolean = true,
    val cascadeUp: Boolean = false
)

data class GeoPricingResponse(
    val geoPricingList: List<GeoPricing>
)

abstract class PricingArea(
    private val id: String,
    private val displayName: String,
    private val description: String
)

data class Region(
    val id: String,
    val displayName: String,
    val description: String,
    val listOfStates: List<String>
): PricingArea(id, displayName, description)

data class RegionResponse(
    val regionList: List<Region>
)

val masterPolicyMap = mapOf(
    GeoLocationType.REGION to 10,
    GeoLocationType.STATE to 12,
    GeoLocationType.CITY to 14,
    GeoLocationType.WAREHOUSE to 16,
    GeoLocationType.CLUSTER to 18
)

