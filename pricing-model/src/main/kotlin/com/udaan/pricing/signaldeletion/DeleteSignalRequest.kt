package com.udaan.pricing.signaldeletion

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.udaan.pricing.commons.location.Location

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes(
    JsonSubTypes.Type(value = DeleteCompSignalRequest::class),
    JsonSubTypes.Type(value = DeleteFpJitVendorPriceRequest::class)
)
sealed class DeleteSignalRequest

data class DeleteFpJitVendorPriceRequest(
    val groupId: String,
    val warehouseId: String,
    val createdBy: String
): DeleteSignalRequest()


data class DeleteCompSignalRequest @JvmOverloads constructor(
    val productGroupId: String,
    val competitionName: String,
    val location: Location,
    val refreshedAt: Long,
    val benchmarkRefId: String? = null
): DeleteSignalRequest() {
    fun copy(
        productGroupId: String,
        competitionName: String,
        location: Location,
        refreshedAt: Long
    ): DeleteSignalRequest {
        return this.copy(
            productGroupId = productGroupId,
            competitionName = competitionName,
            location = location,
            refreshedAt = refreshedAt,
            benchmarkRefId = null
        )
    }
}
