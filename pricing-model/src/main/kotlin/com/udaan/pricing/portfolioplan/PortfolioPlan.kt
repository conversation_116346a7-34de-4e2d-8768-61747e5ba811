package com.udaan.pricing.portfolioplan

import com.udaan.pricing.utils.Utils
import com.udaan.pricing.variable.VariableId
import com.udaan.pricing.commons.GenericValue

data class PortfolioPlan(
    val id: String = Utils.generateId("PFP"),
    val portfolioId: String,
    val strategies: List<String>,
    val buyerCohort: String? = null,
    val cohortInputs: List<CohortInput> = emptyList(),
    val state: PortfolioPlanState = PortfolioPlanState.ACTIVE,
    val createdAt: Long = System.currentTimeMillis(),
    val createdBy: String,
    val updatedAt: Long = System.currentTimeMillis(),
    val updatedBy: String
)

data class CohortInput(
    val variableId: VariableId,
    val value: GenericValue
)

enum class PortfolioPlanState {
    ACTIVE,
    DELETED
}

const val DEFAULT_COHORT = "default_cohort"


fun PortfolioPlan.isDefaultCohortPlan(): Boolean {
    return this.buyerCohort.equals(DEFAULT_COHORT, ignoreCase = true)
}

fun PortfolioPlan.isBasePlan(): Boolean {
    return this.buyerCohort == null
}

