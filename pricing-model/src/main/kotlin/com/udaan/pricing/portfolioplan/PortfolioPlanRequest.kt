package com.udaan.pricing.portfolioplan

import com.udaan.pricing.commons.BigDecimalValue
import com.udaan.pricing.variable.VariableId

data class PortfolioPlanRequest(
    val portfolioId: String,
    val strategies: List<String>,
    val buyerCohort: String? = null,
    val cohortInputs: List<CohortInput> = emptyList(),
    val createdBy: String
)


// TODO :- Only BigDecimal support for now
data class PortfolioPlanRequestFromConsole(
    val portfolioId: String,
    val strategies: List<String>,
    val buyerCohort: String? = null,
    val cohortInputs: List<CohortInputFromConsole>,
    val createdBy: String
)

data class CohortInputFromConsole(
    val variableId: String,
    val value: Double
)

fun PortfolioPlanRequestFromConsole.toPortfolioPlanRequest(): PortfolioPlanRequest {
    return PortfolioPlanRequest(
        portfolioId = portfolioId,
        strategies = strategies,
        buyerCohort = buyerCohort,
        cohortInputs = cohortInputs.map { it.toCohortInput() },
        createdBy = createdBy
    )
}

fun CohortInputFromConsole.toCohortInput(): CohortInput {
    return CohortInput(
        variableId = VariableId.valueOf(variableId),
        value = BigDecimalValue(value.toBigDecimal())
    )
}

fun PortfolioPlanRequest.toPortfolioPlan(): PortfolioPlan {
    return PortfolioPlan(
        portfolioId = portfolioId,
        strategies = strategies,
        buyerCohort = buyerCohort,
        cohortInputs = cohortInputs,
        createdBy = createdBy,
        updatedBy = createdBy
    )
}