package com.udaan.pricing.network

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.pricing.commons.location.LocationType

@JsonIgnoreProperties(ignoreUnknown = true)
data class DemandClusterLocationRes(
    val id: String,
    val name: String,
    val type: LocationType,
    val city: String,
    val demandClusterId: String,
    val geoClusterId: String? = null,
    val fulfilmentCenters: List<WarehouseDetails>,
    val anchorCityName: String
)