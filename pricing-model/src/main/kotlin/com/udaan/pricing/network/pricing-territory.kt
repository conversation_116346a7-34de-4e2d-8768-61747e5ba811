package com.udaan.pricing.network

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.sql.Timestamp

@JsonIgnoreProperties(ignoreUnknown=true)
data class TerritoryHex(
    val id: String,
    val hexId: String, // Partition Key
    val resolution: Int,
    val territoryType: TerritoryType,
    val territoryId: String,
    val status: TerritoryStatus,
    val createdAt: Timestamp,
    val updatedAt: Timestamp
)

@JsonIgnoreProperties(ignoreUnknown=true)
data class Territory(
    val id: String,
    val name: String,
    val type: TerritoryType,
    val status: TerritoryStatus,
    val createdAt: Timestamp,
    val updatedAt: Timestamp,
    val createdBy: String,
    val updatedBy: String
)

enum class TerritoryStatus {
    ACTIVE,
    INACTIVE
}

enum class TerritoryType {
    JUMBOTAIL
}

@JsonIgnoreProperties(ignoreUnknown=true)
data class GetTerritoriesRequest(
    val latitude: Double,
    val longitude: Double,
    val territoryTypes: Set<TerritoryType>? = null
)

@JsonIgnoreProperties(ignoreUnknown=true)
data class CreateTerritoryRequest(
    val territoryType: TerritoryType,
    val territoryName: String,
    val points: List<LatLong>,
    val createdBy: String
)

@JsonIgnoreProperties(ignoreUnknown=true)
data class LatLong(
    val latitude: Double,
    val longitude: Double
)