package com.udaan.pricing.network

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.pricing.commons.location.LocationType

@JsonIgnoreProperties(ignoreUnknown = true)
data class DemandClusterReq(
    val demandClusterName: String,
    val anchorCityName: String,
    val fulfilmentCenters: List<WarehouseDetails>,
    val createdBy: String,
    val updatedBy: String?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class DemandClusterLocationReq(
    val name: String,
    val type: LocationType,
    val city: String,
    val createdBy: String,
    val updatedBy: String?
)