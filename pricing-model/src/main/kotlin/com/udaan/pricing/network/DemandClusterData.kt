package com.udaan.pricing.network

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.pricing.commons.location.LocationType
import com.udaan.pricing.utils.Utils.generateId

enum class States {
    ACTIVE,
    DELETED
}

/**
 * This is to save Warehouse details serving to any location
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class WarehouseDetails(
    val whId: String,
    val name: String,
    val city: String,
    val type: String,
    val category: String
)

/**
 * This represents a demand cluster of supply chain network
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class DemandCluster(
    val id: String = generateId("DCU"),
    val demandClusterName: String,
    val anchorCityName: String,
    val fulfilmentCenters: List<WarehouseDetails>,
    val state: States = States.ACTIVE,
    val createdAt: Long = System.currentTimeMillis(),
    val createdBy: String,
    val updatedAt: Long = System.currentTimeMillis(),
    val updatedBy: String?
)

/**
 * This represents pricing locations inside a supply chain demand cluster
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class DemandClusterLocations(
    val id: String = generateId("DCL"),
    val name: String,
    val type: LocationType,
    val city: String,
    val demandClusterId: String,
    val geoClusterId: String? = null,
    val fulfilmentCenters: List<WarehouseDetails>,
    val state: States = States.ACTIVE,
    val createdAt: Long = System.currentTimeMillis(),
    val createdBy: String,
    val updatedAt: Long = System.currentTimeMillis(),
    val updatedBy: String?
)

