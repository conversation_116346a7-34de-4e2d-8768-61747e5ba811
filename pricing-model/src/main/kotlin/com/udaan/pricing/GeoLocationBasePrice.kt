package com.udaan.pricing

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class GeoLocationBasePrice @JvmOverloads constructor(
        val id: String,
        val listingId: String,
        val saleUnitId: String,
        val qtyBasedPrice: List<QtyBasedPrice>,
        val state: PriceState = PriceState.ACTIVE,
        /**
         * Denotes the identifier for the given location type. eg Chennai for LocationType = City.
         */
        val locationTypeId: String,
        val locationType: GeoLocationType,
        val createdAt: Long = System.currentTimeMillis(),
        val updatedAt: Long = System.currentTimeMillis(),
        val currentActive: Int = 1,
        val metaData: MetaData?= null
) {
    init {
        require(listingId.isNotEmpty()) { "Listing id cannot be an empty string" }
        require(saleUnitId.isNotEmpty()) { "Sales unit id cannot be empty" }
        require(locationTypeId.isNotEmpty()) { "Location type id cannot be empty" }

        val sortedPrices = qtyBasedPrice.sortedBy(QtyBasedPrice::minQty)
        var prevMax = 0
        val arePriceLimitsValid = sortedPrices.map { price ->
            val isValid = (price.minQty <= price.maxQty) && (price.minQty - prevMax == 1)
            prevMax = price.maxQty
            isValid
        }.all { it }
        require(arePriceLimitsValid) { "Qty limits are not valid." }
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class GeoLocationBasePriceStateChangeRequest(
        val listingId: String,
        val isActive : Int
) {
    init {
        require(listOf(0, 1).contains(isActive)) { "isActive is needed to be active(1) or inactive(0)" }
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class UpsertGeoLocationBasePriceRequest @JvmOverloads constructor(
        val listingId: String,
        val saleUnitId: String,
        val qtyBasedPrice: List<QtyBasedPriceRequest>,
        val locationTypeId: String,
        val locationType: GeoLocationType,
        val metaData: MetaData? = null
) {
    init {
        require(listingId.isNotEmpty()) { "Listing id cannot be an empty string" }
        require(saleUnitId.isNotEmpty()) { "Sales unit id cannot be empty" }
        require(listingId.startsWith("TL")) { "Listing id must start with TL" }
        require(saleUnitId.startsWith("SU")) { "Sales unit must start with SU" }
        require(qtyBasedPrice.isNotEmpty()) { "At least one price condition must be provided" }
        require(locationTypeId.isNotEmpty()) { "Location type id cannot be empty" }
    }
}

data class GeoLocationBasePriceResponse(
        val id: String,
        val listingId: String,
        val saleUnitId: String,
        val qtyBasedPrice: List<QtyBasedPrice>,
        val state: PriceState = PriceState.ACTIVE,
        /**
         * Denotes the identifier for the given location type. eg Chennai for LocationType = City.
         */
        val locationTypeId: String,
        val locationType: GeoLocationType
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class QtyBasedPriceRequest @JvmOverloads constructor(
    val minQty: Int,
    val maxQty: Int,
    val pricePaise: Long,
    val pricePerKgInPaise: Long? = null,
    val packagingUnit: PUInfo? = null
)

fun List<GeoLocationBasePrice>.convert(): List<GeoLocationBasePriceResponse> {
    val resp = mutableListOf<GeoLocationBasePriceResponse>()
    for (geoBasePrice in this) {
        resp.add(
            GeoLocationBasePriceResponse(
                geoBasePrice.id,
                geoBasePrice.listingId,
                geoBasePrice.saleUnitId,
                geoBasePrice.qtyBasedPrice,
                geoBasePrice.state,
                geoBasePrice.locationTypeId,
                geoBasePrice.locationType
            )
        )
    }
    return resp
}

data class UpdateEmptyActiveGeoBasePriceRequest(
    val listingId: String,
    val saleUnitId: String,
    val locationType: GeoLocationType,
    val locationTypeId: String,
    val priceIntentId: String
)

data class DCsHavingActivePriceResponse(
    val listingId: String,
    val salesUnitWithDemandClusters: List<SalesUnitWithDemandClusters>
)

data class SalesUnitWithDemandClusters(
    val salesUnitId: String,
    val demandClusters: List<String>
)
