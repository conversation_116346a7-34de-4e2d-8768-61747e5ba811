package com.udaan.pricing

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class PriceForListing @JvmOverloads constructor(
    val listingId: String,
    val saleUnitId: String,
    val prices: List<QtyBasedPrice>,
    val strategyRef: String? = null,
    val metaData: MetaData? = null,
    val pricingAuditId: String? = null,
    val sscMetadata: Map<String, String> = emptyMap(),
    /**
     * TODO: This cannot be part of metadata which is used mostly for pharma use-cases.
     *  This would go away once we refactor the API contracts.
     */
    val contractReferenceId: String? = null,
    /**
     * This is used to determine if promotions should be applied on the price or not.
     * In case of contract price this is passed as false, caller services will make sure to not apply promotions.
     */
    val applyPromotions: Boolean? = true,
    val priceBaseStrategy: PriceBaseStrategy? = null
) {
    fun copy(
        listingId: String,
        saleUnitId: String,
        prices: List<QtyBasedPrice>,
        strategyRef: String?,
        metaData: MetaData?
    ): PriceForListing = this.copy(
        listingId = listingId,
        saleUnitId = saleUnitId,
        prices = prices,
        strategyRef = strategyRef,
        metaData = metaData,
        pricingAuditId = null
    )

    fun copy(
        listingId: String,
        saleUnitId: String,
        prices: List<QtyBasedPrice>,
        strategyRef: String?,
        metaData: MetaData?,
        pricingAuditId: String?
    ): PriceForListing = this.copy(
        listingId = listingId,
        saleUnitId = saleUnitId,
        prices = prices,
        strategyRef = strategyRef,
        metaData = metaData,
        pricingAuditId = pricingAuditId,
        sscMetadata = emptyMap()
    )

    fun copy(
        listingId: String,
        saleUnitId: String,
        prices: List<QtyBasedPrice>,
        strategyRef: String?,
        metaData: MetaData?,
        pricingAuditId: String?,
        sscMetadata: Map<String, String>
    ): PriceForListing = this.copy(
        listingId = listingId,
        saleUnitId = saleUnitId,
        prices = prices,
        strategyRef = strategyRef,
        metaData = metaData,
        pricingAuditId = pricingAuditId,
        sscMetadata = sscMetadata,
        contractReferenceId = null
    )

    fun copy(
        listingId: String,
        saleUnitId: String,
        prices: List<QtyBasedPrice>,
        strategyRef: String?,
        metaData: MetaData?,
        pricingAuditId: String?,
        sscMetadata: Map<String, String>,
        contractReferenceId: String?
    ): PriceForListing = this.copy(
        listingId = listingId,
        saleUnitId = saleUnitId,
        prices = prices,
        strategyRef = strategyRef,
        metaData = metaData,
        pricingAuditId = pricingAuditId,
        sscMetadata = sscMetadata,
        contractReferenceId = contractReferenceId,
        applyPromotions = null,
        priceBaseStrategy = null
    )
}

enum class PriceBaseStrategy {
    CONTRACT,
    MANUAL,
    SSC
}