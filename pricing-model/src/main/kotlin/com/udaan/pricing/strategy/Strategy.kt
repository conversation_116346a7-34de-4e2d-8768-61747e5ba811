package com.udaan.pricing.strategy

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.pricing.utils.Utils
import com.udaan.pricing.variable.VariableId

@JsonIgnoreProperties(ignoreUnknown = true)
data class Strategy(
    val id: String = Utils.generateId("STG"),
    val name: String,
    val type: StrategyType,
    val conditionalFormulae: List<ConditionalFormulae>,
    val usedVariables: List<VariableId>,
    val mandatoryVariables: List<VariableId>,
    val state: StrategyState,
    val metadata: Map<String, String>,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * Types here represent:
 * - FORMULA
 * -- This strategy requires definition of formula (conditions, expressions) and evaluation
 * happens basis the definition during run time.
 * - DYNAMIC
 * -- This strategy does not have any formula definitions but only variables defined. The logic
 * to be evaluated is built into code blocks which execute during run time.
 *
 */
enum class StrategyType {
    FORMULA,
    DYNAMIC
}

/**
 * 3 states for Strategy:
 * 1. Staged - When a new strategy is created, it will be staged. You can run soft launch though.
 *             Once verified, it can be moved to Active state.
 *             Strategies in this state can't be tagged to any portfolio.
 *             We can update different attributes of strategy in this state.
 * 2. Active - Once a strategy is verified, it can be moved to this state manually.
 *             You can tag active strategy to any portfolio.
 * 3. Deleted - When a new strategy is created to be used in place of an existing strategy,
 *              the existing strategy will be moved to Deleted state while re-tagging.
 *              We can also explicitly move an ACTIVE or STAGED state strategy to this state.
 */
enum class StrategyState {
    STAGED,
    ACTIVE,
    DELETED
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class ConditionalFormulae(
    val name: String,
    val condition: Formula,
    val formulae: List<Formula>,
    val aggregationType: AggregationType
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Formula(
    val expression: String
)

enum class AggregationType {
    MIN,
    MAX,
    AVERAGE,
    MEDIAN,
    NONE
}
