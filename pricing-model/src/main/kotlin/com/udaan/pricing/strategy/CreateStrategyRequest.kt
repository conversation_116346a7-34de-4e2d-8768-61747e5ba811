package com.udaan.pricing.strategy

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.pricing.variable.VariableId

@JsonIgnoreProperties(ignoreUnknown = true)
data class CreateStrategyRequest(
    val name: String,
    val type: StrategyType,
    val conditionalFormulae: List<ConditionalFormulae>,
    val usedVariables: List<VariableId>,
    val mandatoryVariables: List<VariableId>,
    val createdBy: String
) {
    fun convert(): Strategy {
        return Strategy(
            name = this.name,
            type = this.type,
            conditionalFormulae = this.conditionalFormulae,
            usedVariables = this.usedVariables,
            mandatoryVariables = this.mandatoryVariables,
            state = StrategyState.STAGED,
            metadata = mapOf(
                "CREATED_BY" to this.createdBy
            )
        )
    }
}
