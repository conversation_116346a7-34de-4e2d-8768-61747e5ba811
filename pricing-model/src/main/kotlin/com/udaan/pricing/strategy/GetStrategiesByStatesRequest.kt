package com.udaan.pricing.strategy

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class GetStrategiesByStatesRequest(
    val states: List<String>
) {
    /**
     *  Translates String states in req DTO to StrategyState values.
     *
     *  Throws IllegalArgumentException in case of invalid state String is found.
     */
    fun translateToStrategyEnums() : List<StrategyState> {
        return this.states.map { state ->
            try {
                StrategyState.valueOf(state.uppercase())
            } catch (ex: Exception) {
                throw IllegalArgumentException("Invalid Strategy state $state found in request.")
            }
        }
    }
}