package com.udaan.pricing.portfolioitem

import com.udaan.pricing.commons.location.Location

data class CreatePortfolioItemRequest(
    val catalogEntity: String,
    val entityLevel: CatalogEntityLevel,
    val location: Location,
    val portfolioId: String,
    val createdBy: String,
    val jobIdReference: String,
    val createdAt: Long = System.currentTimeMillis()
) {
    fun convert(): PortfolioItem {
        return PortfolioItem(
            catalogEntity = this.catalogEntity.uppercase(),
            entityLevel = this.entityLevel,
            location = this.location.copy(
                locationValue = this.location.locationValue.uppercase()
            ),
            portfolioId = this.portfolioId,
            state = PortfolioItemState.ACTIVE,
            createdAt = this.createdAt,
            updatedAt = this.createdAt,
            createdBy = this.createdBy,
            updatedBy = this.createdBy,
            metadata = mapOf(
                "JOB_ID" to jobIdReference
            )
        )
    }
}
