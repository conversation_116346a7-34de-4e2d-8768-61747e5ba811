package com.udaan.pricing.portfolioitem

import com.udaan.pricing.commons.location.Location
import com.udaan.pricing.utils.Utils

data class PortfolioItem(
    val catalogEntity: String,
    val entityLevel: CatalogEntityLevel,
    val location: Location,
    val portfolioId: String,
    val state: PortfolioItemState,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val createdBy: String,
    val updatedBy: String,
    val referenceId: String = Utils.generateId("PFI"),
    val metadata: Map<String, String>
) {
    val id = (catalogEntity + ":" + location.locationValue).uppercase()
}

data class PortfolioItemPortfolioTaggingCount(
    val portfolioId: String,
    val countOfPortfolioItemTagged: Long
)
