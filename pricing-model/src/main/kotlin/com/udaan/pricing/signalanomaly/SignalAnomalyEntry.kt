package com.udaan.pricing.signalanomaly

import com.udaan.pricing.signals.Signal
import com.udaan.pricing.utils.Utils

data class SignalAnomalyEntry(
    val newSignal: Signal,
    val oldSignal: Signal?,
    val catalogEntityTitle: String,
    val signalAnomalyType: SignalAnomalyType,
    val guardrail: Guardrail,
    val state: SignalAnomalyAuditState,
    val createdAt: Long,
    val updatedAt: Long,
    val referenceId: String = Utils.generateId("SAE")
) {
    val id = "${newSignal.catalogEntity}:${newSignal.location.locationValue}:${newSignal.variableId}".uppercase()
    val partitionKey = "${newSignal.catalogEntity}:${newSignal.location.locationValue}".uppercase()
}
