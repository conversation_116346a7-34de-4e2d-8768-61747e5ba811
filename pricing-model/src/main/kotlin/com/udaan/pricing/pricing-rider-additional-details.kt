package com.udaan.pricing

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.udaan.promotions.dto.promotion.PromotionsSearchResponseV2

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "riderClass")
@JsonSubTypes(
    JsonSubTypes.Type(value = PromotionDetails::class, name = "PromotionDetails")
)
sealed class RiderAdditionalDetails()

@JsonIgnoreProperties(ignoreUnknown = true)
data class PromotionDetails(
    val maxBps: Int, val promotionDetails: PromotionsSearchResponseV2
): RiderAdditionalDetails()
