package com.udaan.pricing.commons

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes(
    JsonSubTypes.Type(value = AbsolutePriceInfo::class),
    JsonSubTypes.Type(value = MrpMarkdownInfo::class)
)
sealed class PriceInfo

data class AbsolutePriceInfo(
    val priceLadderSlabs: List<Ladder>
): PriceInfo() {
    init {
        priceLadderSlabs.validate()
    }
}

data class MrpMarkdownInfo(
    val markdownLadderSlabs: List<Ladder>
): PriceInfo() {
    init {
        markdownLadderSlabs.validate()
    }
}
