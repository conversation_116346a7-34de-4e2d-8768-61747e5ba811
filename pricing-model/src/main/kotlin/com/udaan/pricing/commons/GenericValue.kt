package com.udaan.pricing.commons

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import java.math.BigDecimal

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes(
    JsonSubTypes.Type(value = BigDecimalValue::class),
    JsonSubTypes.Type(value = StringValue::class),
    JsonSubTypes.Type(value = LadderValue::class)
)
sealed class GenericValue

data class BigDecimalValue(
    val value: BigDecimal
): GenericValue() {
    override fun toString(): String {
        return this.value.stripTrailingZeros().toPlainString()
    }
}

data class StringValue(
    val value: String
): GenericValue() {
    override fun toString(): String {
        return this.value
    }
}

data class LadderValue(
    val value: List<Ladder>
): GenericValue() {
    init {
        val sortedLadders = value.sortedBy(Ladder::minQuantity)
        var prevMaxQuantity = 0L
        val areLaddersQtyValid = sortedLadders.map { ladder ->
            val isValid = (ladder.minQuantity <= ladder.maxQuantity) && (ladder.minQuantity - prevMaxQuantity == 1L)
            prevMaxQuantity = ladder.maxQuantity
            isValid
        }.all { it }

        require(areLaddersQtyValid) { "Ladder Quantity limits are not valid." }
    }

    override fun toString(): String {
        return "[" + this.value.joinToString(", ") {
            "(" + it.minQuantity + "," + it.maxQuantity + "," + it.ladderValue.stripTrailingZeros()
                .toPlainString() + ")"
        } + "]"
    }
}
