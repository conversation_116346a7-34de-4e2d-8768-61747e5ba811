package com.udaan.pricing.commons

import java.math.BigDecimal

data class Ladder(
    val minQuantity: Long,
    val maxQuantity: Long,
    val ladderValue: BigDecimal
) {
    init {
        require(minQuantity > 0) { "minQuantity must be non-negative" }
        require(maxQuantity >= minQuantity) { "maxQuantity must be greater than or equal to minQuantity" }
    }
}

fun List<Ladder>.validate() {
    val sortedLadders = this.sortedBy(Ladder::minQuantity)
    var prevMaxQuantity = 0L
    val areLaddersQtyValid = sortedLadders.map { ladder ->
        val isValid = (ladder.minQuantity <= ladder.maxQuantity) && (ladder.minQuantity - prevMaxQuantity == 1L)
        prevMaxQuantity = ladder.maxQuantity
        isValid
    }.all { it }

    require(areLaddersQtyValid) { "ladder Quantity limits are not valid." }
}
