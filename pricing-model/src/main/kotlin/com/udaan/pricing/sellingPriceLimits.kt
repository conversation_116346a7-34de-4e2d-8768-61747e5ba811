package com.udaan.pricing

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.math.BigDecimal

enum class PRICING_SIGNALS {
    COGS_PROXY, SUGGESTED_MARGIN_BPS, OVERRIDE_PRICE, COMPETITIVE_PRICE_BILL,COMPETITIVE_PRICE_VOC, COMPETITIVE_PRICE_APP,
    COMPETITIVE_PRICE_SCHEMES, COGS_OVERRIDE, FLOOR_PRICE
}

const val sellingPriceLimitsFixedMarginPercentage = 0.02

const val sellingPriceLimitsFixedPercentage = 0.05

@JsonIgnoreProperties(ignoreUnknown = true)
data class ListingPriceAnomalyData(
    val sourceListing: String,
    val sourceSaleUnitId: String,
    val productId: String?,
    val sourceAvgUnitPrice: Double?,
    val sourceMinOrderUnitPrice: Double?,
    val sourceMaxOrderUnitPrice: Double?,
    val sourceMedianOrderUnitPrice: Double?,
    val sourcePriceCurrent: Double?,
    val sellerOrgId: String,
    val mappedMinListingPrice: Double = Double.MAX_VALUE,
    val mappedMaxListingPrice: Double = Double.MIN_VALUE,
    val mappedAvgListingPrice: Double?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PricingSignals(
    val floorPrice: Double?,
    val cogs: Double?,
    val cogsOverride: Double?,
    val suggestedMarginPercentage: Double?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class HsopSignals(
    val min: Double?,
    val max: Double?,
    val avg: Double?,
    val median: Double?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class OlopFpSignals(
    val min: Double?,
    val max: Double?,
    val avg: Double?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class OlopGlobalSignals(
    val min: Double?,
    val max: Double?,
    val avg: Double?,
    val median: Double?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SellingPriceAnomalySignals(
    val pricingSignals: PricingSignals,
    val hsopSignals: HsopSignals,
    val olopFpSignals: OlopFpSignals,
    val olopGlobalSignals: OlopGlobalSignals?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SellingPriceLimits(
    val id: String,
    val listingId: String,
    val saleUnitId: String,
    val productId: String,
    val fpUpperLimit: Double?,
    val fpLowerLimit: Double?,
    val globalUpperLimit: Double?,
    val globalLowerLimit: Double?,
    val currentPrice: Double?,
    val comments: String,
    val anomalySignals: SellingPriceAnomalySignals,
    val createdAt: Long,
    val updatedAt: Long
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Signal(
    val id: String,
    val productId: String,
    val expiresAt: Long?,
    val createdAt: Long = System.currentTimeMillis(),
    val orgId: String,
    val signalName: PRICING_SIGNALS,
    val value: Double
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Limits(
    val fpUpperLimit: BigDecimal? = null,
    val fpLowerLimit: BigDecimal? = null,
    val globalUpperLimit: BigDecimal? = null,
    val globalLowerLimit: BigDecimal? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SellingPriceLimitsCheckRequest(
    val productId: String,
    val price: BigDecimal
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SellingPriceLimitsLog(
    val id: String,
    val statusCode: String,
    val statusComment: String,
    val productId: String,
    val fpUpperLimit: BigDecimal?,
    val fpLowerLimit: BigDecimal?,
    val globalUpperLimit: BigDecimal?,
    val globalLowerLimit: BigDecimal?,
    val intentPrice: BigDecimal?,
    val createdAt: Long
)