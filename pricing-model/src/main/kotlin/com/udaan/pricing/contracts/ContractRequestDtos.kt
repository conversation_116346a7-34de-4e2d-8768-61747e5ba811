package com.udaan.pricing.contracts

data class ContractRequest @JvmOverloads constructor(
    val buyerOrgId: String,
    val contractCatalogEntityId: String,
    val contractCatalogEntity: String,
    val price: ContractLadderPriceRequest,
    val priceType: ContractPriceRequestType,
    val type: String,
    val expiryInEpoch: Long,
    val lockInEpoch: Long = 0L,
    val requestedBy: String,
    val volumeCommitted: Long,
    val reason: String,
    val city: String = ""
)

enum class ContractPriceRequestType {
    MRP_MARKDOWN_BPS,
    ABSOLUTE_LADDER_PRICE
}

data class ContractLadderPriceRequest(
    val value: List<ContractLadderRequest>
)

data class ContractLadderRequest(
    val minQuantity: Int,
    val maxQuantity: Int,
    val ladderValue: Long
)