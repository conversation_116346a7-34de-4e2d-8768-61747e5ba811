package com.udaan.pricing.contracts

data class ContractQuoteResponse(
    val buyerOrgId: String,
    val city: String,
    val contractCatalogEntityId: String,
    val contractCatalogEntity: String,
    val volumeCommitted: Long,
    val targetUnitPriceInPaisa: Long?,
    val quotePriceInPaisa: Long?,
    val quotePriceInPaisaWithTax: Long?,
    val mrpMarkDownBps: Long?,
    val priceValidity: String?,
    val bestCompetitorQuote: BestCompetitorQuote,
    val customerSavingsInBps: Long?,
    val cogsUnitPriceInPaisa: Long?,
    val marginInBps: Long?,
    val contractCreationRemarks: String? = null,
    val referenceListingId: String
)

data class BestCompetitorQuote(
    val competitor: String?,
    val priceInPaisa: Long?
)