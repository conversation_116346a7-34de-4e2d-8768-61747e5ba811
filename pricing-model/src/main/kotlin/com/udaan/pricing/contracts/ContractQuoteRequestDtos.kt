package com.udaan.pricing.contracts

data class ContractQuoteRequest(
    val buyerOrgId: String,
    val city: String,
    val contractCatalogEntityId: String,
    val contractCatalogEntity: String,
    /**
     * Reference listing id for which the contract quote is being requested in case of GID.
     */
    val referenceListingId: String? = null,
    val volumeCommitted: Long,
    val targetUnitPrice: Long?,
    val requestedBy: String,
    val autoCreateContract: Boolean = false,
    val lockInDays: Long = 0,
    val expiryInDays: Long = 0
)
