package com.udaan.pricing.contracts

import com.fasterxml.jackson.annotation.JsonIgnoreProperties


data class ContractResponse(
    val catalogEntityId: String,
    val catalogEntity: String,
    val catalogTitle: String,
    val contractPrice: ContractLadderPriceResponse,
    val contractPriceType: ContractPriceRequestType,
    val duration: ContractDurationResponse,
    val contractType: String,
    val requestedBy: String,
    val volumeCommitted: Long,
    val volumeOrdered: Long,
    val volumeOrderedRefreshedTill: Long,
    val quotePriceInfo: QuotePriceInfoResponse?
)

data class QuotePriceInfoResponse(
    val quotePriceInPaisa: Long?,
    val cogsUnitPriceInPaisa: Long?,
    val quoteMrpInMarkdownBps: Long?,
    val quoteRefreshedAt: Long,
    val listingIdUsedForQuote: String?,
    val bestCompPriceInPaisa: Long?,
    val bestCompName: String?
)

data class ContractVisibilitySummary(
    val buyerNumber: String,
    val buyerOrgId: String,
    val buyerOrgName: String,
    val activeContracts: List<ContractResponse>,
    val expiredContracts: List<ContractResponse>
)

data class ContractDurationResponse(
    val startTime: Long,
    val endTime: Long,
    val lockInTime: Long
)

data class ContractLadderPriceResponse(
    val value: List<ContractLadderResponse>
)

data class ContractLadderResponse(
    val minQuantity: Int,
    val maxQuantity: Int,
    val ladderValue: Long
)


@JsonIgnoreProperties(ignoreUnknown = true)
data class ExceptionContractResponse(
    val buyerOrgId: String,
    val contract: ContractResponse,
    val cogsUnitPrice: Long?,
    val mrpUnitPriceWithNoTax: Long?,
    val message: String,
    val requestedAt: Long
)