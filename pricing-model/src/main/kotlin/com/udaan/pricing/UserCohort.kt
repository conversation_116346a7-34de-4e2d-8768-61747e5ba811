package com.udaan.pricing

import com.fasterxml.jackson.annotation.JsonIgnoreProperties


enum class UserCohortType { PRIVATE, PUBLIC }
@JsonIgnoreProperties(ignoreUnknown = true)
data class UserCohort(
        val id:String,
        val sellerOrgId:String?=null,
        val cohortType:UserCohortType=UserCohortType.PRIVATE,
        val orgId:String,
        val cohortName:String,
        val createdAt: Long,
        val updatedAt: Long
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class UserCohortCreateReq(val sellerOrgId: String?,val orgId:String,
                               val userCohortType: UserCohortType,
                               val cohortName:String)