package com.udaan.pricing

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.udaan.fulfilment.sku.PackagingType

@JsonIgnoreProperties(ignoreUnknown = true)
@Suppress("DataClassContainsFunctions")
data class QtyBasedPrice @JvmOverloads constructor(
    val minQty: Int,
    val maxQty: Int,
    val priceInPaisa: PriceInPaisa,
    val pricePerKgInPaisa: PriceInPaisa?,
    val taxableAmountPaise: Long?,
    val packagingUnit: PUInfo? = null,
    val bulkLadder: Boolean? = null
) {

    fun copy(
        minQty: Int,
        maxQty: Int,
        priceInPaisa: PriceInPaisa,
        pricePerKgInPaisa: PriceInPaisa?,
        taxableAmountPaise: Long?,
        packagingUnit: PUInfo?
    ): QtyBasedPrice = this.copy(
        minQty = minQty,
        maxQty = maxQty,
        priceInPaisa = priceInPaisa,
        pricePerKgInPaisa = pricePerKgInPaisa,
        taxableAmountPaise = taxableAmountPaise,
        packagingUnit = packagingUnit,
        bulkLadder = null
    )

}

@JsonIgnoreProperties(ignoreUnknown = true)
data class PUInfo @JvmOverloads constructor(
    val packagingUnitType: String,
    val assortment: Int,
    val multiplier: Int,
    val packaging: PackagingType? = null
) {
    fun copy(
        packagingUnitType: String,
        assortment: Int,
        multiplier: Int
    ): PUInfo = this.copy(
        packagingUnitType = packagingUnitType,
        assortment = assortment,
        multiplier = multiplier,
        packaging = null
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class PriceRequest(
    val listingId: String, val saleUnitId: String, val price: List<QtyBasedPrice>,
    val state: PriceState, val orgId: String, val metaData: MetaData? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PriceRange(val minPriceInPaisa: Long, val maxPriceInPaisa: Long)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PriceRangeResponse(val priceRange: PriceRange)

@JsonIgnoreProperties(ignoreUnknown = true)
data class LotPricingRequest(
    val listingId: String, val saleUnitId: String, val price: List<QtyBasedPrice>,
    val state: PriceState, val orgId: String, val lotId: String, val metaData: MetaData,
    val schemeDiscountBps: Int, val minSchemeUnit: Int
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class UpdateLotRequest(val listingId: String, val saleUnitId: String, val lotId: String)


@JsonIgnoreProperties(ignoreUnknown = true)
data class GetLotPriceRequest(
    val listingId: String,
    val saleUnitId: String,
    val lotId: String,
    val refId: String? = null
)


@JsonIgnoreProperties(ignoreUnknown = true)
data class GetLotPriceWithSchemeRequest(val listingId:String,val saleUnitId: String,
                                        val lotId: String,val refId:String?=null,val scheme:String?)

@JsonIgnoreProperties(ignoreUnknown = true)
data class DeleteGeoBasePriceRequest(
    val listingId: String,
    val saleUnitId: String,
    val locationTypeId: String,
    val locationType: String
)

/**
 *  Extension functions
 */

fun List<QtyBasedPrice>.isPUPriced(): Boolean {
    return firstOrNull()?.packagingUnit?.let { true } ?: false
}

fun List<QtyBasedPrice>.toDisplayPrice(): List<QtyBasedPrice> {
    return map { qtyPrice ->
        val assortmentSize = qtyPrice.packagingUnit?.multiplier ?: 1
        val price = qtyPrice.priceInPaisa
        val pricePerKg = qtyPrice.pricePerKgInPaisa
        if (assortmentSize > 1) {
            qtyPrice.copy(
                minQty = qtyPrice.minQty.div(assortmentSize),
                maxQty = qtyPrice.maxQty.div(assortmentSize),
                priceInPaisa = price.getMultiplierPrice(assortmentSize),
                pricePerKgInPaisa = pricePerKg?.getMultiplierPrice(assortmentSize)
            )
        } else {
            qtyPrice
        }
    }
}

fun PriceInPaisa.getMultiplierPrice(assortmentSize: Int): PriceInPaisa {
    return PriceInPaisa(
        onCredit = this.onCredit.times(assortmentSize),
        onCOD = this.onCOD.times(assortmentSize),
        onPrepayment = this.onPrepayment.times(assortmentSize),
        basicPrice = BasicPrice(
            onCreditBasePrice = this.basicPrice?.onCreditBasePrice?.times(assortmentSize),
            onCODBasePrice = this.basicPrice?.onCODBasePrice?.times(assortmentSize),
            onPrepaymentBasePrice = this.basicPrice?.onPrepaymentBasePrice?.times(assortmentSize)
        ),
        priceRiders = this.priceRiders
    )
}
