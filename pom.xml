<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>


    <parent>
        <groupId>com.udaan</groupId>
        <artifactId>udaan-parent</artifactId>
        <version>[3.0,4.0)</version>
    </parent>

    <groupId>com.udaan.pricing</groupId>
    <artifactId>pricing-parent</artifactId>
    <version>2.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>Pricing Parent</name>

    <profiles>
        <profile>
            <id>default</id>
            <activation>
                <property>
                    <name>!exclude-default</name>
                </property>
            </activation>
            <modules>
                <module>pricing-model</module>
                <module>pricing-client</module>
            </modules>
        </profile>
    </profiles>

    <modules>
        <module>pricing-core</module>
        <module>pricing-service</module>
        <module>pricing-jobs</module>
        <module>pricing-events</module>
    </modules>

    <properties>
        <versions.ip-subnet-tree>1.0.2</versions.ip-subnet-tree>
        <versions.google.api>1.22.0</versions.google.api>
        <versions.first-party-job-consumer>[1.0,2.0)</versions.first-party-job-consumer>
        <versions.documentdb-bulkexecutor>2.12.5</versions.documentdb-bulkexecutor>
        <versions.slack-api-client>1.45.3</versions.slack-api-client>
        <versions.azure-kusto>3.2.1</versions.azure-kusto>
        <versions.evalEx>2.7</versions.evalEx>
        <versions.poi>3.14</versions.poi>
        <versions.uber-h3>4.0.0</versions.uber-h3>
    </properties>

    <dependencyManagement>
        <dependencies>

            <!-- Udaan pricing -->
            <dependency>
                <groupId>com.udaan.pricing</groupId>
                <artifactId>pricing-model</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency> <!-- ideally should be updated in common-slack -->
                <groupId>com.slack.api</groupId>
                <artifactId>slack-api-client</artifactId>
                <version>${versions.slack-api-client}</version>
            </dependency>

            <dependency>
                <groupId>com.udaan.firstparty</groupId>
                <artifactId>sprinkle-utils</artifactId>
                <version>${versions.first-party-job-consumer}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.udaan.fp-integration</groupId>
                        <artifactId>first-party-external-integrations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.udaan.firstparty</groupId>
                <artifactId>first-party-job-consumer</artifactId>
                <version>${versions.first-party-job-consumer}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.udaan.fp-integration</groupId>
                        <artifactId>first-party-external-integrations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.microsoft.azure</groupId>
                <artifactId>documentdb-bulkexecutor</artifactId>
                <version>${versions.documentdb-bulkexecutor}</version>
            </dependency>

            <dependency>
                <groupId>com.github.x25</groupId>
                <artifactId>ip-subnet-tree</artifactId>
                <version>${versions.ip-subnet-tree}</version>
            </dependency>

            <dependency>
                <groupId>com.microsoft.azure.kusto</groupId>
                <artifactId>kusto-ingest</artifactId>
                <version>${versions.azure-kusto}</version>
            </dependency>


            <dependency>
                <groupId>com.udojava</groupId>
                <artifactId>EvalEx</artifactId>
                <version>${versions.evalEx}</version>
            </dependency>

            <!-- temp netty dependency to unblock operations and functional threads from netty issue -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver-dns</artifactId>
                <version>${versions.netty}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${versions.poi}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${versions.poi}</version>
            </dependency>

            <!-- Uber H3 -->
            <dependency>
                <groupId>com.uber</groupId>
                <artifactId>h3</artifactId>
                <version>${versions.uber-h3}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>

            <!--klint-->
            <plugin>
                <groupId>com.github.gantsign.maven</groupId>
                <artifactId>ktlint-maven-plugin</artifactId>
                <version>1.4.2</version>
                <executions>
                    <execution>
                        <phase>${klint.phase}</phase>
                        <id>format</id>
                        <goals>
                            <goal>format</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
